stages:
  - build-ng
  - build-docker
  - build-ng-local
  - build-docker-local
  

test-build-ng:

  stage: build-ng
  image: trion/ng-cli:7.3.3

  variables:
    NODE_OPTIONS: --max_old_space_size=8192

  before_script:
    - pwd
    - sed -i "s|http://127.0.0.1|https://app-test.med.bot|g" src/app/config/settings.ts
    - sed -i "s|ws://127.0.0.1:9080|wss://app-test.med.bot:10443|g" src/app/config/settings.ts
    - sed -i "s|https://openvidu-test.tivonatestdns.com/|https://vc-test.med.bot/|g" src/app/config/settings.ts
    - sed -i "s/+ ':8004'/ /g" src/app/config/settings.ts
    - sed -i "s/+ ':8000'/ /g" src/app/config/settings.ts
    - sed -i "s/+':8004'/ /g" src/app/config/settings.ts
    - sed -i "s/+ ':8004'/ /g" src/app/config/settings.ts
    - sed -i "s/abc.med.bot/.app-test.med.bot/g" src/app/config/settings.ts
    - npm --version
    - npm install -g npm@6.14.15
    - npm --version
    # - npm uninstall openvidu-angular
    - mv src/assets/plugins/openvidu/openvidu-angular-test.tgz  src/assets/plugins/openvidu/openvidu-angular-2.15.0.tgz
    - npm install src/assets/plugins/openvidu/openvidu-angular-2.15.0.tgz
    - npm ci
  when: manual

  script:
    - ng build --prod
  artifacts:
    paths:
      - dist/   
  only:
    - test

test-build-docker:
  stage: build-docker
  image: docker:19.03.12

  variables:
    DOCKER_TLS_CERTDIR: "/certs"

  services:
    - docker:19.03.12-dind

  before_script:
    - docker info
  when: manual
  
  script:
    - docker build -t fe .
    - docker login -u gitlab-ci-token -p ************************** registry.gitlab.com #$GITLAB_ACCESS_TOKEN
    - docker tag fe registry.gitlab.com/med-bot-projects/platform/fe
    - docker push registry.gitlab.com/med-bot-projects/platform/fe
  only:
    - test

local-build-ng:

  stage: build-ng-local
  image: trion/ng-cli:7.3.3

  before_script:
    - npm --version
    - npm install -g npm@6.14.15
    - npm --version
    - npm install src/assets/plugins/openvidu/openvidu-angular-2.15.0.tgz
    - npm ci
  when: manual

  script:
    - ng build --prod
  artifacts:
    paths:
      - dist/   
  only:
    - test

local-build-docker:
  stage: build-docker-local
  image: docker:19.03.12

  variables:
    DOCKER_TLS_CERTDIR: "/certs"

  services:
    - docker:19.03.12-dind

  before_script:
    - docker info
  when: manual
  
  script:
    - docker build -t fe .
    - docker login -u gitlab-ci-token -p ************************** registry.gitlab.com #$GITLAB_ACCESS_TOKEN 
    - docker tag fe registry.gitlab.com/med-bot-projects/platform/fe-local
    - docker push registry.gitlab.com/med-bot-projects/platform/fe-local
  only:
    - test


staging-build-ng:

  stage: build-ng
  image: trion/ng-cli:7.3.3

  before_script:
    - pwd
    - sed -i "s|http://127.0.0.1|https://app-staging.med.bot|g" src/app/config/settings.ts
    - sed -i "s|ws://127.0.0.1:9080|wss://app-staging.med.bot:10443|g" src/app/config/settings.ts
    - sed -i "s|https://openvidu-test.tivonatestdns.com/|https://vc-staging.med.bot/|g" src/app/config/settings.ts
    - sed -i "s/+ ':8004'/ /g" src/app/config/settings.ts
    - sed -i "s/+ ':8000'/ /g" src/app/config/settings.ts
    - sed -i "s/+':8004'/ /g" src/app/config/settings.ts
    - sed -i "s/+ ':8004'/ /g" src/app/config/settings.ts
    - sed -i "s/abc.med.bot/.app-staging.med.bot/g" src/app/config/settings.ts
    # - npm uninstall openvidu-angular
    - npm --version
    - npm install -g npm@6.14.15
    - npm --version
    - mv src/assets/plugins/openvidu/openvidu-angular-staging.tgz  src/assets/plugins/openvidu/openvidu-angular-2.15.0.tgz
    - npm install src/assets/plugins/openvidu/openvidu-angular-2.15.0.tgz
    - npm ci

  when: manual

  script:
    - ng build --prod
  artifacts:
    paths:
      - dist/   
  only:
    - develop

staging-build-docker:
  stage: build-docker
  image: docker:19.03.12

  variables:
    DOCKER_TLS_CERTDIR: "/certs"

  services:
    - docker:19.03.12-dind

  before_script:
    - docker info
  when: manual

  artifacts:
    paths:
      - dist/   
  script:
    - docker build -t fe .
    - docker login -u gitlab-ci-token -p ************************** registry.gitlab.com #$GITLAB_ACCESS_TOKEN
    - docker tag fe registry.gitlab.com/med-bot-projects/medbot-staging/fe
    - docker push registry.gitlab.com/med-bot-projects/medbot-staging/fe
  only:
    - develop

prod-build-ng:

  stage: build-ng
  image: trion/ng-cli:7.3.3

  before_script:
    - pwd
    - sed -i "s|http://127.0.0.1|https://med.bot|g" src/app/config/settings.ts
    - sed -i "s|ws://127.0.0.1:9080|wss://med.bot:10443|g" src/app/config/settings.ts
    - sed -i "s|https://openvidu-test.tivonatestdns.com/|https://vc-prod.med.bot/|g" src/app/config/settings.ts
    - sed -i "s/+ ':8004'/ /g" src/app/config/settings.ts
    - sed -i "s/+ ':8000'/ /g" src/app/config/settings.ts
    - sed -i "s/+':8004'/ /g" src/app/config/settings.ts
    - sed -i "s/+ ':8004'/ /g" src/app/config/settings.ts
    - sed -i "s/abc.med.bot/.med.bot/g" src/app/config/settings.ts
    - npm --version
    # - npm uninstall openvidu-angular
    - npm --version
    - npm install -g npm@6.14.15
    - npm --version
    - mv src/assets/plugins/openvidu/openvidu-angular-prod.tgz  src/assets/plugins/openvidu/openvidu-angular-2.15.0.tgz
    - npm install src/assets/plugins/openvidu/openvidu-angular-2.15.0.tgz
    - npm ci

  when: manual

  script:
    - ng build --prod
  artifacts:
    paths:
      - dist/   
  only:
    - master

prod-build-docker:
  stage: build-docker
  image: docker:19.03.12

  variables:
    DOCKER_TLS_CERTDIR: "/certs"

  services:
    - docker:19.03.12-dind

  before_script:
    - docker info
  when: manual
  
  script:
    - docker build -t fe .
    - docker login -u gitlab-ci-token -p ************************** registry.gitlab.com #$GITLAB_ACCESS_TOKEN
    - docker tag fe registry.gitlab.com/med-bot-projects/medbot-prod/fe
    - docker push registry.gitlab.com/med-bot-projects/medbot-prod/fe
  only:
    - master
