{"name": "med-bot-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "build-stg": "ng build --configuration=staging && ng serve"}, "private": true, "dependencies": {"@angular/animations": "^9.1.12", "@angular/cdk": "^10.1.3", "@angular/common": "^9.1.12", "@angular/compiler": "^9.1.12", "@angular/core": "^9.1.12", "@angular/elements": "^9.1.12", "@angular/forms": "^9.1.12", "@angular/localize": "^10.0.12", "@angular/material": "^10.1.3", "@angular/platform-browser": "^9.1.12", "@angular/platform-browser-dynamic": "^9.1.12", "@angular/router": "^9.1.12", "@ng-bootstrap/ng-bootstrap": "^7.0.0", "@ng-select/ng-select": "^4.0.4", "@ngx-translate/core": "^12.1.2", "@ngx-translate/http-loader": "^4.0.0", "@types/file-saver": "^2.0.1", "@types/jquery": "^3.5.1", "angular-file-uploader": "^7.0.1", "bootstrap": "^4.5.0", "document-register-element": "^1.7.2", "file-saver": "^2.0.2", "hammerjs": "^2.0.8", "html2canvas": "^1.0.0-rc.7", "jquery": "^3.5.1", "jspdf": "^2.1.1", "moment": "^2.27.0", "ng-multiselect-dropdown": "^0.2.10", "ng-pick-datetime": "^7.0.0", "ng2-ckeditor": "^1.2.9", "ng2-date-picker": "^10.0.1", "ngb-modal": "^2.0.3", "ngx-bootstrap": "^6.2.0", "ngx-cookie-service": "^3.0.4", "ngx-print": "^1.2.0-beta.5", "ngx-toastr": "^12.1.0", "openvidu-angular": "file:src/assets/plugins/openvidu/openvidu-angular-2.15.0.tgz", "rxjs": "~6.5.4", "socket.io": "^3.1.1", "stream-chat": "^2.5.0", "tslib": "^1.10.0", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "^0.901.11", "@angular/cli": "^9.1.11", "@angular/compiler-cli": "^9.1.12", "@types/jasmine": "^3.5.11", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.12.50", "codelyzer": "^5.1.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~5.0.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~2.1.0", "karma-jasmine": "~3.0.1", "karma-jasmine-html-reporter": "^1.4.2", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~3.8.3"}}