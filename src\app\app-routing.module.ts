import { ContactComponent } from './contact/contact.component';
import { AboutComponent } from './about/about.component';
import { HospitalDetailsComponent } from './platform/hospital-details/hospital-details.component';
import { DoctorIdentityDetailsComponent } from './platform/doctor-identity-details/doctor-identity-details.component';
import { PatientIdentityDetailsComponent } from './platform/patient-identity-details/patient-identity-details.component';
import { VerifyOtpLinkComponent } from './auth/verify-otp-link/verify-otp-link.component';
import { ForgotPasswordComponent } from './auth/forgot-password/forgot-password.component';
import { SetPasswordComponent } from './auth/set-password/set-password.component';
import { DoctorDashboardComponent } from './doctor/doctor-dashboard/doctor-dashboard.component';
import { DoctorPublicProfileComponent } from './doctor/doctor-public-profile/doctor-public-profile.component';
import { DoctorBankAccountsComponent } from './doctor/doctor-bank-accounts/doctor-bank-accounts.component';
import { DoctorProfileComponent } from './doctor/doctor-profile/doctor-profile.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule, PreloadAllModules } from '@angular/router';
import { DoctorComponent } from './doctor/doctor.component';
import { PatientComponent } from './patient/patient.component';
import { SignupComponent } from './auth/signup/signup.component';
import { SigninComponent } from './auth/signin/signin.component';
import { VerifyComponent } from './auth/verify/verify.component';
import { DoctorPracticeLocationComponent } from './doctor/doctor-practice-location/doctor-practice-location.component';
import { HomeComponent } from './home/<USER>';
import { TeleConsultComponent } from './tele-consult/tele-consult.component';
import { ConsultationComponent } from './tele-consult/consultation/consultation.component';
import { PatientProfileComponent } from './patient/patient-profile/patient-profile.component';
import { AppointmentComponent } from './patient/appointment/appointment.component';
import { SearchDoctorComponent } from './patient/search-doctor/search-doctor.component';
import { PatientDashboardComponent } from './patient/patient-dashboard/patient-dashboard.component';
import { BookingSuccessComponent } from './patient/booking-success/booking-success.component';
import { PlatformAdminComponent } from './platform/platform-admin/platform-admin.component';
import { ApprovalSuccessComponent } from './platform/approval-success/approval-success.component';
import { PatientConsultationComponent } from './tele-consult/patient-consultation/patient-consultation.component';
import { ConsultingVideoComponent } from './tele-consult/consulting-video/consulting-video.component';
import { DoctorMessagesComponent } from './doctor/doctor-messages/doctor-messages.component';
import { HospitalAdminComponent } from './hospital-admin/hospital-admin.component';
import { AddDoctorComponent } from './hospital-admin/add-doctor/add-doctor.component';
import { HospitalDoctorProfileComponent } from './hospital-admin/hospital-doctor-profile/hospital-doctor-profile.component';
import { UserAdminComponent } from './hospital-admin/user-admin/user-admin.component';
import { DoctorAssistantComponent } from './hospital-admin/doctor-assistant/doctor-assistant.component';
import { HospitalDetailComponent } from './hospital-admin/hospital-detail/hospital-detail.component';
import { UsersComponent } from './hospital-admin/users/users.component';
//import {DoctorAssistantComponent} from './doctor-assistant/doctor-assistant.component';
import { AssistantDashboardComponent } from './doctor-assistant/assistant-dashboard/assistant-dashboard.component';
import { AssistantProfileComponent } from './doctor-assistant/assistant-profile/assistant-profile.component';
import { AdminProfileComponent } from './hospital-admin/admin-profile/admin-profile.component';
import { BankAccountComponent } from './doctor-assistant/bank-account/bank-account.component';
import { PracticeLocationComponent } from './doctor-assistant/practice-location/practice-location.component';
import { ConsultHistoryComponent } from './tele-consult/consult-history/consult-history.component';
import { FeeCollectedComponent } from './doctor/doctor-dashboard/fee-collected/fee-collected.component';
import { MedicalReportComponent } from './tele-consult/medical-report/medical-report.component';
import { EarningReportComponent } from './doctor/doctor-reports/earning-report/earning-report.component';
import { PublicProfileComponent } from './public-profile/public-profile.component'
import { QuickHelpVideos } from './quickhelpvideo'
import { PagenotfoundComponent } from './pagenotfound/pagenotfound.component';
import { HelpComponent } from './help/help.component';
import { BankDetailComponent } from './platform/bank-detail/bank-detail.component';
import { HospitalComponent } from './hospital/hospital.component';
import { pathToFileURL } from 'url';
import { PartnerAdminComponent } from './hospital-admin/partner-admin/partner-admin.component';
import { componentFactoryName } from '@angular/compiler';
import { HaBasicprofileComponent } from './hospital-admin/ha-basicprofile/ha-basicprofile.component';
import { HaEducationComponent } from './hospital-admin/ha-education/ha-education.component';
import { HaDepartmentComponent } from './hospital-admin/ha-department/ha-department.component';
import { HaSpecialityComponent } from './hospital-admin/ha-speciality/ha-speciality.component';
import { HaHomeAddressComponent } from './hospital-admin/ha-home-address/ha-home-address.component';
import { HospitalDoctorFeeComponent } from './hospital-admin/hospital-doctor-fee/hospital-doctor-fee.component';
import { DoctorAssistantProfileComponent } from './hospital-admin/doctor-assistant-profile/doctor-assistant-profile.component';
import { DoctorAssistantAddressComponent } from './hospital-admin/doctor-assistant-address/doctor-assistant-address.component';
import { HaDoctorRegistrationComponent } from './hospital-admin/ha-doctor-registration/ha-doctor-registration.component';
import { PartnerAddressComponent } from './hospital-admin/partner-address/partner-address.component';
import { PartnerProfileComponent } from './hospital-admin/partner-profile/partner-profile.component';
import { AddAsstPatComponent } from './partnerdashboard/add-asst-pat/add-asst-pat.component';
import { AddpatientComponent } from './doctor-asst-dashboard/addpatient/addpatient.component';
import { HadoctorPracticelocationComponent } from './hospital-admin/hadoctor-practicelocation/hadoctor-practicelocation.component';
import { HadoctorConsultatiionComponent } from './hospital-admin/hadoctor-consultatiion/hadoctor-consultatiion.component';
import { HadoctorBookappointmentComponent } from './hospital-admin/hadoctor-bookappointment/hadoctor-bookappointment.component';
import { HaloadingSpinnerComponent } from './hospital-admin/haloading-spinner/haloading-spinner.component';
import { HapatientConsultationComponent } from './hospital-admin/hapatient-consultation/hapatient-consultation.component';
import { HadoctorViewprofileComponent } from './hospital-admin/hadoctor-viewprofile/hadoctor-viewprofile.component';
import { HaDoctorappointmentComponent } from './hospital-admin/ha-doctorappointment/ha-doctorappointment.component';
import { HadashboardComponent } from './hospital-admin/hadashboard/hadashboard.component';
import { BookingStatusComponent } from './hospital-admin/booking-status/booking-status.component';
import { HamedicalReportComponent } from './hospital-admin/hamedical-report/hamedical-report.component';
import { HaconsultationComponent } from './hospital-admin/haconsultation/haconsultation.component';
import { HaconsultingVideoComponent } from './hospital-admin/haconsulting-video/haconsulting-video.component';
import { HaconsultHistoryComponent } from './hospital-admin/haconsult-history/haconsult-history.component';
import { HospitalSettingsComponent } from './hospital-admin/hospital-settings/hospital-settings.component';
import { ConsultationHistoryComponent } from './shared/consultation-history/consultation-history.component';
import { ReferralComponent } from './shared/referral/referral.component';
import { ReportsComponent } from './shared/reports/reports.component';
import { ShareHistoryComponent } from './shared/share-history/share-history.component';
import { ConsultationSummaryComponent } from './platform/consultation-summary/consultation-summary.component';
import { LoginComponent } from './auth/login/login.component';
import { ForgotPassword1Component } from './auth/forgot-password1/forgot-password1.component';
import { DaConsultingVideoComponent } from './tele-consult/da-consulting-video/da-consulting-video.component';
import { title } from 'process';

const routes: Routes = [
  //  { path: '404', component: PagenotfoundComponent },
  // {path:'hadoctor-viewprofile',component:HadoctorViewprofileComponent , data:{title:'hadoctor-viewprofile'}},
  { path: 'hadashboard', component: HadashboardComponent, data: { title: 'Hadashboard' } },
  { path: 'hapatient-consultation', component: HapatientConsultationComponent, data: { title: 'Tele Consult' } },
  { path: 'haloading-spinner', component: HaloadingSpinnerComponent, data: { title: 'haloading-spinner' } },
  { path: 'hadoctor-bookappointment/:page/:query/:patientId', component: HadoctorBookappointmentComponent, data: { title: 'hadoctor-bookappointment' } },
  { path: 'hadoctor-consultatiion/:uuid', component: HadoctorConsultatiionComponent, data: { title: 'hadoctor-consultatiion' } },
  { path: 'hadoctor-practicelocation/:uuid', component: HadoctorPracticelocationComponent, data: { title: 'hadoctor-practicelocation' } },
  { path: 'referedAppointment', component: ReferralComponent, data: { title: 'Refered Appointment' } },
  { path: 'share-history/:patient/:consultId', component: ShareHistoryComponent, data: { title: 'Share History' } },
  { path: 'share-history', component: ShareHistoryComponent, data: { title: 'Share History' } },
  {
    path: 'view-doctor/ha-profile',
    component: HadoctorViewprofileComponent,
    data: { title: 'Doctor Profile' },
  },
  {
    path: 'ha-doctorappointment/:id/:location/:apptId/:patientId',
    component: HaDoctorappointmentComponent,
    data: { title: 'Appointment' },
  },
  {
    path: 'booking-status/:id',
    component: BookingStatusComponent,
    data: { title: 'Booking Status' },
  },

  {
    path: 'hamedic-report/:patient/:doctor/:consultid',
    component: HamedicalReportComponent,
    data: { title: 'Medical Report' }
  },
  {
    path: 'haconsulting-video',
    component: HaconsultingVideoComponent,
    data: { title: 'Platform Admin' }
  },

  {
    path:'daconsulting-video',
    component:DaConsultingVideoComponent,
    data:{title:'Doctor assistant consulting'}
  },

  {
    path: 'haconsultation-history/:patient/:doctor/:consultid',
    component: HaconsultHistoryComponent,
    data: { title: 'Consult History' },
  },

  {
    path: 'haconsultation-history/:patient',
    component: HaconsultHistoryComponent,
    data: { title: 'Consult History' },
  },

  {
    path: 'haconsultation',
    component: HaconsultationComponent,
    data: { title: 'Tele Consult' },
  },
  {
    path: 'prescription',
    component: HaconsultationComponent,
    data: { title: 'Prescription' },
  },

  {
    path: 'reports', component: ReportsComponent, data: { title: 'Reports' },
    // children: [
    //   {
    //     path: 'doctor-consultation-summary/:uuid/view', component: HaconsultationSummaryComponent, data: { title: 'Consultation Summary' },
    //   },
    // ]
  },

  { path: 'addpatient', component: AddpatientComponent, data: { title: 'addpatient' } },
  { path: 'add-asst-pat', component: AddAsstPatComponent, data: { title: 'add-asst-pat' } },
  { path: 'partner-address', component: PartnerAddressComponent, data: { title: 'partner-address' } },
  { path: 'ha-doctor-registration', component: HaDoctorRegistrationComponent, data: { title: 'ha-doctor-registration' } },
  { path: 'partner-profile', component: PartnerProfileComponent, data: { title: 'partner-profile' } },
  { path: 'doctor-assistant-address', component: DoctorAssistantAddressComponent, data: { title: 'doctor-assistant-address' } },
  { path: 'doctor-assistant-profile', component: DoctorAssistantProfileComponent, data: { title: 'doctor-assistant-profile' } },
  { path: 'hospital-doctor-fee', component: HospitalDoctorFeeComponent, data: { title: 'hospital-doctor-fee' } },
  { path: 'ha-deparment', component: HaDepartmentComponent, data: { title: 'ha-department' } },
  { path: 'ha-home-address', component: HaHomeAddressComponent, data: { title: 'ha-home-address' } },
  { path: 'ha-speciality', component: HaSpecialityComponent, data: { title: 'ha-speciality' } },
  { path: 'partner-admin', component: PartnerAdminComponent, data: { title: 'partner-admin' } },
  { path: 'platform-admin', component: PlatformAdminComponent, data: { title: 'platform-admin' } },
  { path: 'assistant-profile', component: AssistantProfileComponent, data: { title: 'AssistantProfile' } },
  { path: 'ha-basicprofile', component: HaBasicprofileComponent, data: { title: 'HaBasicprofile' } },
  { path: 'ha-education', component: HaEducationComponent, data: { title: 'haEduaction' } },
  { path: 'users', component: UsersComponent, data: { title: 'Users' } },

  { path: 'hospital-settings', component: HospitalSettingsComponent, data: { title: 'hospital settings' } },
  { path: 'hospital', component: HospitalComponent, data: { title: 'Hospital' } },
  { path: 'home', component: HomeComponent, data: { title: 'Home' } },
  { path: 'about', component: AboutComponent, data: { title: 'About' } },
  { path: 'contact', component: ContactComponent, data: { title: 'Contact' } },
  { path: 'login', component: SignupComponent, data: { title: 'Signin' } },
  { path: 'help', component: HelpComponent, data: { title: 'help', video: QuickHelpVideos.Doctor['login'] } },
  { path: 'signup', component: SignupComponent, data: { title: 'Signup' } },
  { path: 'verify-otp-link', component: VerifyOtpLinkComponent },
  { path: 'verifcation-pending/:email/:phone/:password', component: VerifyComponent },
  { path: 'forgot-password', component: ForgotPasswordComponent },
  { path: 'set-password', component: SetPasswordComponent },
  { path: 'login1', component: LoginComponent, data: { title: 'Login' } },
  { path: 'forgot-password1', component: ForgotPassword1Component },
  { path: '', redirectTo: '/login', pathMatch: 'full' },
  { path: ':slug', component: PublicProfileComponent },


  {
    path: 'view-doctor/public-profile',
    component: DoctorPublicProfileComponent,
    data: { title: 'Doctor Profile' },
  },

  // {
  //   path: 'doctor',
  //   component: DoctorComponent,
  //   data: { title: 'Doctor' },
  //   children: [
  //     {
  //       path: 'profile',
  //       component: DoctorProfileComponent,
  //       data: { title: 'Profile Settings', video: QuickHelpVideos.Doctor['profile'] },
  //     },
  //     {
  //       path: 'location',
  //       component: DoctorPracticeLocationComponent,
  //       data: { title: 'Location' },
  //     },
  //   ],
  // },
  {
    path: 'tele-consult',
    component: TeleConsultComponent,
    data: { title: 'Tele Consult' },
  },
  {
    path: 'consultation',
    component: ConsultationComponent,
    data: { title: 'Tele Consult' },
  },
  // {
  //   path: 'consultation-history/:patient/:doctor/:consultid',
  //   component: ConsultHistoryComponent,
  //   data: { title: 'Consult History' },
  // },
  {
    path: 'doctor',
    component: DoctorComponent,
    data: { title: 'Doctor' },
    children: [
      {
        path: 'profile',
        component: DoctorProfileComponent,
        data: { title: 'Profile Settings', video: QuickHelpVideos.Doctor['profile'] },
      },
      {
        path: 'bank-accounts',
        component: DoctorBankAccountsComponent,
        data: { title: 'Bank Accounts', video: QuickHelpVideos.Doctor['bankaccounts'] },
      },
      {
        path: 'practice-locations',
        component: DoctorPracticeLocationComponent,
        data: { title: 'Practice Location', video: QuickHelpVideos.Doctor['practicelocations'] },
      },
      {
        path: 'public-profile',
        component: DoctorPublicProfileComponent,
        data: { title: 'Doctor Profile' },
      },
      {
        path: 'dashboard',
        component: DoctorDashboardComponent,
        data: { title: 'Dashboard', video: QuickHelpVideos.Doctor['dashboard'] },
      },
      {
        path: 'consultation',
        component: ConsultationComponent,
        data: { title: 'Consultation' },
      },
      {
        path: 'messages',
        component: DoctorMessagesComponent,
        data: { title: 'Messages' },
      },
      {
        path: 'fee-collection',
        component: FeeCollectedComponent,
        data: { title: 'Fees Collection' },
      },
      {
        path: 'earning-report',
        component: EarningReportComponent,
        data: { title: 'Reports' },
      },
    ],
  },
  {
    path: 'patient',
    component: PatientComponent,
    data: { title: 'Patient' },
    children: [
      {
        path: 'profile',
        component: PatientProfileComponent,
        data: { title: 'Profile Settings' },
      },

      {
        path: 'appointment/:id/:location/:apptId',
        component: AppointmentComponent,
        data: { title: 'Appointment' },
      },

      {
        path: 'appointment/:id/:apptId',
        component: AppointmentComponent,
        data: { title: 'Appointment' },
      },

      {
        path: 'search/:page/:query',
        component: SearchDoctorComponent,
        data: { title: 'Search Doctor' },
      },
      {
        path: 'dashboard',
        component: PatientDashboardComponent,
        data: { title: 'Dashboard' },
      },
      {
        path: 'booked/:id',
        component: BookingSuccessComponent,
        data: { title: 'Booking Success' },
      },


      {
        path: 'consult',
        component: SearchDoctorComponent,
        data: { title: 'Consult Now' }
      },
      {
        path: 'consultation',
        component: PatientConsultationComponent,
        data: { title: 'Tele Consult' },
      },
      {
        path: 'prescription',
        component: ConsultationComponent,
        data: { title: 'Prescription' },
      },

    ],
  },
  {
    path: 'platform-admin/dashboard',
    component: PlatformAdminComponent,
    data: { title: 'Platform Admin' }
  },
  {
    path: 'identity-details/:uuid/view',
    component: DoctorIdentityDetailsComponent,
    data: { title: 'Doctor Profile View' },
  },
  {
    path: 'doctor-consultation-summary/:uuid/view',
    component: ConsultationSummaryComponent,
    data: { title: 'Consultation Summary' },
  },
  {
    path: 'doctor-bankdetail/:uuid/view',
    component: BankDetailComponent,
    data: { title: 'Bank detail' },
  },
  {
    path: 'patient-identity-details/:uuid/view',
    component: PatientIdentityDetailsComponent,
    data: { title: 'Patient Profile View' },
  },
  {
    path: 'hospital-detail/:uuid/view',
    component: HospitalDetailsComponent,
    data: { title: 'Hospital Detail View' },
  },
  {
    path: 'hospital-settings/:uuid/view',
    component: HospitalSettingsComponent,
    data: { title: 'Hospital Settings View' },
  },
  {
    path: 'approval-success',
    component: ApprovalSuccessComponent,
    data: { title: 'Platform Admin' }
  },
  {
    path: 'consulting-video',
    component: ConsultingVideoComponent,
    data: { title: 'Platform Admin' }
  },
  {
    path: 'hospital-admin',
    component: HospitalAdminComponent,
    data: { title: 'hospital Admin' }
  },
  {
    path: 'users',
    component: UsersComponent,
    data: { title: 'users' }
  },
  {
    path: 'doctor-assistant-profile/:uuid',
    component: DoctorAssistantProfileComponent,
    data: { title: 'View Detailsas' }
  },
  {
    path: 'doctor-assistant-address/:id',
    component: DoctorAssistantAddressComponent,
    data: { title: 'doctor-assistant-address' }
  },
  {
    path: 'partner-profile/:uuid',
    component: PartnerProfileComponent,
    data: { title: 'View Detailspartner' }
  },
  {
    path: 'partneraddress/:id',
    component: PartnerAddressComponent,
    data: { title: 'partner-address' }
  },
  {
    path: 'ha-baiscprofile/:hospitalId',
    component: HaBasicprofileComponent,
    data: { title: 'ha-basicprofile' }
  },
  {
    path: 'ha-education/:hospitalId',
    component: HaEducationComponent,
    data: { title: 'ha-education' }
  },
  {
    path: 'ha-department/:id',
    component: HaDepartmentComponent,
    data: { title: 'ha-department' }
  },
  {
    path: 'ha-home-address/:id',
    component: HaHomeAddressComponent,
    data: { title: 'ha-home-address' }
  },
  {
    path: 'ha-doctor-registration/:id',
    component: HaDoctorRegistrationComponent,
    data: { title: 'ha-doctor-registration' }
  },
  {
    path: 'hospital-doctor-fee/:id',
    component: HospitalDoctorFeeComponent,
    data: { title: 'hospital-doctor-fee' }
  },
  {
    path: 'ha-speciality/:id',
    component: HaSpecialityComponent,
    data: { title: 'ha-speciality' }
  },
  {
    path: 'add-doctor/:id',
    component: AddDoctorComponent,
    data: { title: 'Add Doctor' }
  },
  {
    path: 'hospital-doctor-profile/:uuid',
    component: HospitalDoctorProfileComponent,
    data: { title: 'View Details' }
  },
  {
    path: 'doctor-assistant/:id',
    component: DoctorAssistantComponent,
    data: { title: 'Add Assistant' }
  },
  {
    path: 'add-patient/:id',
    component: UserAdminComponent,
    data: { title: 'Add Patient' }
  },
  {
    path: 'partner-admin/:id',
    component: PartnerAdminComponent,
    data: { title: 'Add Partner' }
  },
  {
    path: 'hospital-detail',
    component: HospitalDetailComponent,
    data: { title: 'Hospital' }
  },

  {
    path: 'admin-profile',
    component: AdminProfileComponent,
    data: { title: 'Profile Settings' }
  },
  {
    path: 'assistant',
    component: DoctorAssistantComponent,
    data: { title: 'Assistant' },
    children: [
      {
        path: 'profile',
        component: AssistantProfileComponent,
        data: { title: 'Profile Settings' },
      },
      {
        path: 'dashboard',
        component: AssistantDashboardComponent,
        data: { title: 'Dashboard' },
      },
      {
        path: 'bank-accounts/:id',
        component: BankAccountComponent,
        data: { title: 'Bank Accounts' },
      },
      {
        path: 'practice-locations/:id',
        component: PracticeLocationComponent,
        data: { title: 'Practice Location' },
      },
    ]
  },
  {
    path: 'medic-report/:patient/:doctor/:consultid',
    component: MedicalReportComponent,
    data: { title: 'Medical Report' }
  },
  {
    path: 'consultation-history/:patient/:doctor/:consultid',
    component: ConsultationHistoryComponent,
    data: { title: 'Consultation History' }
  },
  {
    path: 'consultation-history/:patient/:consultid',
    component: ConsultationHistoryComponent,
    data: { title: 'Consultation History' }
  },
  {
    path: 'consultation-history/:patient',
    component: ConsultationHistoryComponent,
    data: { title: 'Consultation History' }
  },

];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: true, scrollPositionRestoration: 'enabled' })],
  exports: [RouterModule],
})
export class AppRoutingModule { }
