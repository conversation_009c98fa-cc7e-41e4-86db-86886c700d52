<ng-template *ngIf="individualLogin; then hospitalLogin; else adminLogin">
</ng-template>

<ng-template #adminLogin>
  <div style="width: 100% !important; height: 100% !important" [ngClass]="{ bgImg: getUserType() == null }">
    <div class="header">
      <app-header></app-header>
    </div>
    <div class="body">
      <router-outlet></router-outlet>
    </div>
    <div class="footer" *ngIf="getUserType() !== null">
      <!-- <app-footer></app-footer> -->
    </div>
  </div>
</ng-template>

<ng-template #hospitalLogin>
  <div>
    <router-outlet></router-outlet>
  </div>
</ng-template>