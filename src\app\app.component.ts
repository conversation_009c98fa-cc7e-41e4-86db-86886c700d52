import { Component } from '@angular/core';
import { AuthService } from './auth/auth.service';
import { SharedService } from '../app/shared/shared.service';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  title = 'MEDBOT-FRONTEND';
  scrollOption: boolean;
  individualLogin: boolean = false;
  currentRoute: string;
  private subscriptions: Subscription;

  getUserType() {
    return localStorage.getItem('user_type');
  }

  loginValue() {
    return this.userService.loggedIn();
  }

  constructor(private userService: AuthService, private sharedService: SharedService, private router: Router) {
    this.subscriptions = this.sharedService.individualLogin$.pipe()
      .subscribe(data => {
        this.setBaseVariables();
      });
  }

  ngOnInit() {
    console.log(window.innerWidth);
    console.log(window.innerHeight);
    this.router.events.subscribe((event) => this.setBaseVariables());
  }

  setBaseVariables() {
    const url = window.location.href;
    const arrURL = url.split('/');
    const arrURLCount = arrURL.length;
    let pathValue = arrURL[arrURLCount - 1];
    if (pathValue == 'login1' || pathValue == 'forgot-password1') {
      this.individualLogin = true;
    } else {
      this.individualLogin = false;
    }

    let hospitalURL = '';
    if (window.location.hostname === 'localhost') {
      hospitalURL = (arrURL[3]) ? arrURL[3] : '';
    } else {
      hospitalURL = (arrURL[2]) ? arrURL[2] : '';
    }
    localStorage.setItem('loginURL', hospitalURL);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

}

