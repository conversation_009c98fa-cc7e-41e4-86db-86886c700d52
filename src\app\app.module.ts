import { RouterModule } from '@angular/router';
import { RefreshTokenInterceptor } from './interceptors/refresh-token.interceptor';
import { ErrorMessageInterceptor } from './interceptors/error-message.interceptor';
import { BrowserModule } from '@angular/platform-browser';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { DoctorModule } from './doctor/doctor.module';
import { PatientModule } from './patient/patient.module';
import { TeleConsultModule } from './tele-consult/tele-consult.module';
import { AdminModule } from './admin/admin.module';
import { SharedModule } from './shared/shared.module';
import { AuthModule } from './auth/auth.module';
import { HttpClientModule, HttpClient, HTTP_INTERCEPTORS } from '@angular/common/http';
import { ToastrModule } from 'ngx-toastr';
import { CommonModule } from '@angular/common';
import {TranslateModule, TranslateLoader} from '@ngx-translate/core';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import { HeaderComponent } from './header/header.component';
import { FooterComponent } from './footer/footer.component';
import { HomeComponent } from './home/<USER>'
import { NgSelectModule } from '@ng-select/ng-select';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AccessTokenInterceptor } from './interceptors/access-token.interceptor';
import { PlatformModule } from './platform/platform.module';
import { OpenviduSessionModule } from 'openvidu-angular';
import {HospitalAdminModule} from './hospital-admin/hospital-admin.module';
import {DoctorAssistantModule} from './doctor-assistant/doctor-assistant.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { DatepickerModule, BsDatepickerModule } from 'ngx-bootstrap/datepicker';

import{ReactiveFormsModule,FormsModule} from '@angular/forms';
import 'hammerjs';
import { AboutComponent } from './about/about.component';
import { ContactComponent } from './contact/contact.component';
import { PublicProfileComponent } from './public-profile/public-profile.component';
import { PagenotfoundComponent } from './pagenotfound/pagenotfound.component';
import { HelpComponent } from './help/help.component';
import { HospitalComponent } from './hospital/hospital.component';
import {DoctorAsstDashboardModule} from './doctor-asst-dashboard/doctor-asst-dashboard.module';
import {PartnerdashboardModule} from './partnerdashboard/partnerdashboard.module';


@NgModule({
  declarations: [
    AppComponent,
    HeaderComponent,
    FooterComponent,
    HomeComponent,
    AboutComponent,
    ContactComponent,
    PublicProfileComponent,
    PagenotfoundComponent,
    HelpComponent,
    HospitalComponent,   
  ],
  imports: [
    CommonModule,
    RouterModule,
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    HttpClientModule,
    SharedModule,
    AuthModule,
    DoctorModule,
    AdminModule,
    PatientModule,
    TeleConsultModule,
    PlatformModule,
    OpenviduSessionModule,
    HospitalAdminModule,
    DoctorAssistantModule,
    ToastrModule.forRoot(),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
    NgSelectModule,
    NgMultiSelectDropDownModule,
    NgbModule,
    BsDatepickerModule.forRoot(),
    DatepickerModule.forRoot(),
    ReactiveFormsModule,
    FormsModule,
    DoctorAsstDashboardModule,
    PartnerdashboardModule
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AccessTokenInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ErrorMessageInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: RefreshTokenInterceptor,
      multi: true
    },
  ],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule { }
export function HttpLoaderFactory(httpClient: HttpClient) {
  return new TranslateHttpLoader(httpClient);
}
