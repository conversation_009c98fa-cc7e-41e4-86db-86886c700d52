import { SharedModule } from './../shared/shared.module';
import { NgModule } from '@angular/core';
import {SigninComponent } from './signin/signin.component';
import {SignupComponent } from './signup/signup.component';
import {VerifyComponent } from './verify/verify.component';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule,ReactiveFormsModule }   from '@angular/forms';
import { RouterModule } from '@angular/router';
import {TranslateModule, TranslateLoader} from '@ngx-translate/core';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import { HttpClientModule, HttpClient } from '@angular/common/http';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import { VerifyOtpLinkComponent } from './verify-otp-link/verify-otp-link.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { SetPasswordComponent } from './set-password/set-password.component';
import { DatepickerModule, BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { LoginComponent } from './login/login.component';
import { ForgotPassword1Component } from './forgot-password1/forgot-password1.component';

@NgModule({
    declarations: [
        SigninComponent,
        SignupComponent,
        VerifyComponent,
        VerifyOtpLinkComponent,
        ForgotPasswordComponent,
        SetPasswordComponent,
        LoginComponent,
        ForgotPassword1Component,
    ],
    imports: [
      CommonModule,
      SharedModule,
      BrowserModule,
      BrowserAnimationsModule,
      FormsModule,
      RouterModule,
      ReactiveFormsModule,
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient]
        }
      }),
      BsDatepickerModule.forRoot(),
      DatepickerModule.forRoot(),
    ],
    exports: [
      TranslateModule
    ],
  })
  export class AuthModule {}

export function HttpLoaderFactory(httpClient: HttpClient) {
  return new TranslateHttpLoader(httpClient);
}
