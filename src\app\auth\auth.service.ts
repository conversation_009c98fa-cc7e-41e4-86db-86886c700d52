import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';
import * as Settings from './../config/settings';
import { delay, map, catchError } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { Router } from '@angular/router';
import { HospitalService } from '../hospital-admin/hospital-admin.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  public picture = '../../../../assets/img/doctors/doctor-thumb-02.png';
  public isLogin = false;
  constructor(private http: HttpClient, private router: Router, private hospitalService: HospitalService) { }

  signUp(data, attribute): Observable<any> {
    data['username'] = data['name'];
    data['password1'] = data['password'];
    data['password2'] = data['password'];
    data['attribute'] = attribute;
    console.log(data);
    return this.http
      .post(`${Settings.API_AUTH_URL_PREFIX}/api/auth/signup/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postVerifyOTP(data): Observable<any> {
    return this.http
      .post(`${Settings.API_AUTH_URL_PREFIX}/api/auth/validate-otp/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  get(uidb64 = null, token = null, otp_type = null): Observable<any> {
    let data = {};
    if (!!uidb64) {
      data['uidb64'] = uidb64;
    }
    if (!!token) {
      data['token'] = token;
    }
    if (!!otp_type) {
      data['otp_type'] = otp_type;
    }
    const params = new HttpParams({ fromObject: data });

    return this.http
      .get(`${Settings.API_AUTH_URL_PREFIX}/api/auth/validate-otp-token-link/`, {
        params: params,
      })
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  sendEmailOtp(data) {
    return this.http
      .post(
        `${Settings.API_AUTH_URL_PREFIX}/api/auth/signup/email_otp_resend/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  sendPhoneOtp(data) {
    return this.http
      .post(
        `${Settings.API_AUTH_URL_PREFIX}/api/auth/signup/phone_otp_resend/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  /**
   *
   *
   * @param {*} username
   * @param {*} password
   * @returns
   * @memberof AuthService
   */
  login(email, password) {
    const url = `${Settings.API_AUTH_URL_PREFIX}/api/auth/token/`;
    const formData = {
      username_or_email: email,
      password: password,
    };
    return this.http
      .post(url, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      .pipe(
        map(
          (data) => {
            if (data) {
              this.setAccessTokenDetails(data);
            }
            return data;
          },
          (err) => {
            console.error(err);
          }
        )
      );
  }

  //auth service start
  /**
   *
   *
   * @param {*} d
   * @memberof AuthService
   */
  setAccessTokenDetails(d) {
    /*
      Undefined expiry time for now.
      Last param '/' Makes cookie valid for entire site, rather than just url/x/..
    */
    // this.cookieService.set('currentUser1', JSON.stringify(d), undefined, "/");
    localStorage.setItem('currentUser', JSON.stringify(d));
  }

  /**
   *
   *
   * @returns
   * @memberof AuthService
   */
  getAccessTokenDetails() {
    //const s = this.cookieService.get('currentUser1');
    const s = localStorage.getItem('currentUser');
    if (!!s) {
      const d = JSON.parse(s);
      return d;
    }
    return null;
  }

  /**
   *
   *
   * @memberof AuthService
   */
  removeAccessTokenDetails() {
    /*
      Last param '/' is to specify the path of cookie.
      Otherwise, it doesn't get deleted correctly.
    */
    //this.cookieService.delete('currentUser1', '/');
    localStorage.removeItem('currentUser');
  }

  /**
   *
   *
   * @returns
   * @memberof AuthService
   */
  refreshAccessToken() {
    const url = `${Settings.API_AUTH_URL_PREFIX}/api/auth/token/refresh/`;
    const refresh = this.getRefreshToken();
    if (refresh) {
      const formData = {
        refresh: refresh,
      };
      return this.http
        .post(url, formData)
        .pipe(delay(Settings.REQUEST_DELAY_TIME), catchError(this.errorHandler));
    }
    else {
      this.logout();
    }
  }

  errorHandler(error: HttpErrorResponse) {
    this.logout();
    return Observable.throw(error.message || "Server Error");
  }

  /**
   *
   *
   * @param {*} token
   * @memberof AuthService
   */
  updateAccessTokenDetails(token) {
    const d = this.getAccessTokenDetails();
    d.access = token.access;
    d.refresh = token.refresh;
    this.setAccessTokenDetails(d);
  }

  check_tokens() {
    const url = `${Settings.API_AUTH_URL_PREFIX}/api/auth/token/verify/`;
    let data = { token: this.getRefreshToken() };
    return this.http.post(url, data).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  /**
   *
   *
   * @memberof AuthService
   */
  logout() {
    this.removeAccessTokenDetails();
    localStorage.clear();
    this.hospitalService.setHospitalDetails();
    this.router.navigate(['/login']);
  }

  /**
   *
   *
   * @returns
   * @memberof AuthService
   */
  getAccessToken() {
    const d = this.getAccessTokenDetails();
    if (d === null) {
      return null;
    }
    return d.access;
  }

  /**
   *
   *
   * @returns
   * @memberof AuthService
   */
  getRefreshToken() {
    const d = this.getAccessTokenDetails();
    if (d === null) {
      return null;
    }
    if (d.refresh) {
      return d.refresh;
    }
  }

  /**
   *
   *
   * @returns
   * @memberof AuthService
   */
  loggedIn() {
    const d = this.getAccessTokenDetails();
    if (d !== null) {
      return true;
    }
    return false;
  }
  //auth part completed

  //userpart
  getUserDetail() {
    return this.http
      .get(`${Settings.API_AUTH_URL_PREFIX}/api/auth/me/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  setPicture(data) {
    this.picture = data;
    console.log(data);
  }

  public getDrPicture(): string {
    return this.picture;
  }

  updatePersonalProfile(formData) {
    return this.http
      .patch(`${Settings.API_AUTH_URL_PREFIX}/api/auth/me/`, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateDoctorProfilePicture(profilePicture) {
    const formData = new FormData();
    formData.append('profile_picture', profilePicture);
    return this.http
      .patch(
        `${Settings.API_AUTH_URL_PREFIX}/api/auth/me/profile_picture/`,
        formData
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  uploadDoctorSignature(signPicture) {
    const formData = new FormData();
    formData.append('file', signPicture);
    // formData.append('data', JSON.stringify({}));
    return this.http
      .post(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/signature/`,
        formData
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorSignature() {
    return this.http
      .get(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/signature/`
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  uploadHaDoctorSignature(signPicture, doc_uuid) {
    const formData = new FormData();
    formData.append('file', signPicture);
    // formData.append('data', JSON.stringify({}));
    return this.http
      .post(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doc_uuid}/signature/`,
        formData
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getHaDoctorSignature(doc_uuid) {
    return this.http
      .get(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doc_uuid}/signature/`
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  setLogin(data) {
    this.isLogin = data;
  }
  reLoadPage() {
    return this.isLogin;
  }
  forgetPassword(data): Observable<any> {
    return this
      .http
      .post(
        `${Settings.API_AUTH_URL_PREFIX}/api/auth/reset-password-request/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }
  setPassword(data): Observable<any> {
    if (data['password1'] == data['password2']) {
      data['password'] = data['password1'];
    }
    return this.http
      .patch(
        `${Settings.API_AUTH_URL_PREFIX}/api/auth/password-reset-complete/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  loginRedirection(data){
    return this.http
      .get(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospital_logo/?hospital_slug=${data}`
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
}
