<div class="container-fluid body-condent">
  <div class="row">
    <div class="col-md-12">
      <div class="login-form-banner">
        <div class="row">
          <div
            class="col-md-6 d-flex flex-column align-items-center justify-content-xl-center bg-white p-3 p-sm-4 p-md-3 px-lg-5 py-lg-0"
            style="min-height: 100vh">
            <h5 class="mx-lg-5 mt-lg-5 mb-lg-0 m-md-0 px-lg-5 p-md-0 lineheight-base">
              Connect with the
              <code class="secondary-color">Best healthcare professionals</code>
              and mange your own digital health account
            </h5>
            <div class="mx-lg-5 mt-lg-0 mb-lg-2 m-md-0 px-lg-5 p-md-0">
              <img src="../../../assets/img/apollo-img.png" width="200" height="200" alt="" />
            </div>
            <div class="mx-lg-5 mt-lg-0 m-md-0 px-lg-5 p-md-0">
              <img src="../../../assets/img/doctorbg-blue.png" width="200" height="auto" alt="" />
            </div>
          </div>
          <div
            class="col-md-6 p-3 p-sm-4 p-md-3 px-lg-5 py-lg-0 d-flex flex-column justify-content-xl-center primary-bg-color">
            <div class="px-lg-5">
              <p class="mt-lg-5 m-md-0 p-md-0 px-lg-5 pb-lg-4 user-input text-white">
                Forgot Password?
              </p>
              <form [formGroup]="passwordFormData" >
                <div class="p-md-0 px-lg-5 pt-lg-3 pb-lg-0">
                  <label class="text-white" for="">Email</label>
                  <input type="email" class="form-control" formControlName="email" placeholder="Email" [readonly]="loadingFormSubmission" />
                </div>
                <div class="py-3 px-md-0 py-md-5 password-reset px-lg-5 py-lg-3 d-flex justify-content-end">
                  <p style="cursor: pointer" class="text-white" [routerLink]="['/login1']">
                    Remember your Password?
                  </p>
                </div>
                <div class="py-2 py-md-0 px-md-0 px-lg-5 py-lg-0">
                  <button class="btn w-100 secondary-bg-color py-2 text-white" style="border-radius: 8px" type="button" (click)="forgotPassword()">
                    Signin
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>