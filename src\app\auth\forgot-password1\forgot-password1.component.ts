import { Component, OnInit } from '@angular/core';
import { AuthService } from '../auth.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { FormBuilder, FormControl, FormControlName, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-forgot-password1',
  templateUrl: './forgot-password1.component.html',
  styleUrls: ['./forgot-password1.component.css']
})
export class ForgotPassword1Component implements OnInit {
  public passwordFormData: FormGroup;
  public loadingFormSubmission = false;
  constructor(
    private authService: AuthService,
    private router: Router,
    private notificationService: ToastrService,
    public fb: FormBuilder
  ) {
    this.passwordFormData = this.fb.group({
      email: new FormControl('', [Validators.required])
    });
  }

  ngOnInit(): void {

  }
  forgotPassword() {
    this.loadingFormSubmission = true;
    this.authService.forgetPassword(
      this.passwordFormData.getRawValue()
    ).subscribe(
      data => {
        console.log(data);
        this.notificationService.success(`${data['message']}`, 'Med.Bot');
        this.router.navigate(['/login1']);
        this.loadingFormSubmission = false;
      }, error => {
        console.log(error);
        this.loadingFormSubmission = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }

      }
    );
  }

}
