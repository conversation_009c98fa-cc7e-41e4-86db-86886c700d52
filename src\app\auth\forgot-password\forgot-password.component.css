.bgImg {
  border-top-color: #77C1F9;
  background-color: #77C1F9;
}


/* Extra small devices (phones, 600px and down) */

@media only screen and (max-width: 600px) {
  .example {
      background: red;
  }
  .btn-signUp {
      border-radius: 25px !important;
      background: #005cb7;
      border: none;
      color: #fff;
  }
  .input-field-border {
      border-radius: 10px !important;
  }
  .connect {
      position: relative;
      font-family: 'Rubik', sans-serif;
      margin-top: 7%;
      font-size: 17px;
      line-height: 30px;
      color: #fff;
      margin-left: 0px;
      font-weight: lighter;
  }
  body {
      overflow-y: auto !important;
  }
  #white-medbot {
      margin-top: -45px;
  }
  .reg-head {
      font-size: 1.8em !important;
      font-family: 'Rubik', sans-serif !important;
      font-weight: lighter;
  }
  /* .form-signup {
      margin-top: -60px;
  } */
}


/* Small devices (portrait tablets and large phones, 600px and up) */

@media only screen and (min-width: 601px) and (max-width:767px) {
  .example {
      background: green;
  }
  .btn-signUp {
      border-radius: 25px !important;
      background: #005cb7;
      border: none;
      color: #fff;
  }
  .input-field-border {
      border-radius: 10px !important;
  }
  .connect {
      font-size: 18px;
      line-height: 30px;
      color: #fff;
      margin-left: 0px;
  }
  .body-condent {
      margin-top: 39%;
  }
}


/* Medium devices (landscape tablets, 768px and up) */

@media only screen and (min-width: 768px) and (max-width: 990px) {
  .example {
      background: blue;
  }
  .btn-signUp {
      border-radius: 25px !important;
      background: #005cb7;
      border: none;
      color: #fff;
  }
  .input-field-border {
      border-radius: 10px !important;
  }
  .connect {
      font-size: 18px;
      line-height: 30px;
      color: #fff;
      margin-left: 0px;
  }
}


/* Large devices (laptops/desktops, 992px and up) */

@media only screen and (min-width: 992px) and (max-width:1000px) {
  .example {
      background: orange;
  }
  .btn-signUp {
      border-radius: 25px !important;
      background: #005cb7;
      border: none;
      color: #fff;
  }
  .input-field-border {
      border-radius: 10px !important;
  }
  .connect {
      font-size: 22px;
      line-height: 30px;
      color: #fff;
      margin-left: 0px;
  }
}

/* mid range laptops */

@media only screen and (min-width: 1200px) {
  .header-right {
      margin-right: -70px;
  }
  .connect {
      font-size: 1.8em !important;
      font-family: 'Rubik', sans-serif !important;
      color: #fff;
      margin-bottom: 0px;
      padding-top: 165px;
      line-height: 35px;
      padding-left: 70px;
      padding-right: 94px;
      font-weight: lighter;
  }
  .example {
      background: pink;
  }
  .btn-signUp {
      border-radius: 25px !important;
      background: #005cb7;
      border: none;
      color: #fff;
  }
  .input-field-border {
      border-radius: 10px !important;
  }
  .form-signup {
      margin-top: -10px;
  }
  .reg-head {
      font-size: 1.8em !important;
      font-family: 'Rubik', sans-serif !important;
  }
  .are-link {
      font-size: 18px;
      font-family: 'Rubik', sans-serif !important;
      font-weight: lighter;
  }
  #white-medbot {
      margin-top: -100px;
      margin-left: -80px;
  }
  .t_c_text {
      color: #fff;
      margin-top: 10px;
      margin-left: 15px;
  }
}
