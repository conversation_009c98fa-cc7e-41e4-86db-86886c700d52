
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<!-- Account Content -->
				<div class="account-content">
					<div class="row align-items-center justify-content-center">
            <div class=" col-lg-7 ">
              <h2 class="connect">Connect with the best healthcare professionals and manage your own digital health account</h2>

                <img  id="white-medbot" src="../../../assets/img/Medbot logo_white_text only_transparent background.png" class="img-fluid" alt="Doccure Register">
            </div>
						<div class=" col-lg-4 ">
							<div class="login-header">
								<h3>Forgot Password?</h3>
								<!-- <p class="small text-muted">Enter your email to get a password reset link</p> -->
							</div>

							<!-- Forgot Password Form -->
							<form #_forgotPasswordFormData="ngForm" (submit)="onSubmit()">
								<fieldset [disabled]="loadingForgotPasswordFormSubmission">
								<div class="form-group form-focus">
									<input type="email" class="form-control floating" name="email"
						              autocomplete="email" required
						              pattern="^\w+([\.+-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$"
						              [(ngModel)]="passwordFormData.email" #_email="ngModel"

						            >
									<label class="focus-label floating">Email</label>
								</div>
								<div class="text-right">
									<a class="forgot-link text-color" [routerLink]="['/login']">Remember your password?</a>
								</div>
								<button class="btn btn-signUp btn-block btn-lg login-btn mb-4" type="submit"
					              [disabled]="_forgotPasswordFormData.invalid || loadingForgotPasswordFormSubmission"
					            >
					                {{ loadingForgotPasswordFormSubmission ? '&nbsp;&nbsp; Loading ... &nbsp;&nbsp;' : '&nbsp;&nbsp; Reset Password &nbsp;&nbsp;'}}
					            </button>
					        </fieldset>
							</form>
							<!-- /Forgot Password Form -->

            </div>
              <div class=" col-lg-1 ">

              </div>
					</div>
				</div>
				<!-- /Account Content -->

			</div>
		</div>

	</div>


