import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../auth.service';
import { ToastrService } from 'ngx-toastr'

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.css']
})
export class ForgotPasswordComponent implements OnInit {

  public loadingForgotPasswordFormSubmission = false;

  public passwordFormData = {
    email: null
  };

  constructor(
  	private authService: AuthService,
  	private _router: Router,
  	private notificationService : ToastrService,
  ) { }

  ngOnInit(): void {

      document.body.style.overflowY = 'auto';
      document.body.style.background='#77C1F9';

  }

  onSubmit() {
    this.loadingForgotPasswordFormSubmission = true;
    this.authService.forgetPassword(
      this.passwordFormData
    ).subscribe(
      data => {
        console.log(data);
        this.loadingForgotPasswordFormSubmission = false;
        this.notificationService.success(`${data['message']}`, 'Med.Bot');
        this._router.navigate(['/login']);


      }, error => {
        this.loadingForgotPasswordFormSubmission = false;
        console.log(error);
        const status = error['status'];
        if(status == 400){
          this.notificationService.error(`${error.error['statusText']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }

      }
    );
  }

  ngOnDestroy(){
    document.body.style.overflowY = 'auto';
    document.body.style.background='#ffffff';
  }

}
