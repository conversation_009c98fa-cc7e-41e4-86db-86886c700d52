<div class="container-fluid body-condent">
  <div class="row">
    <div class="col-md-12">
      <div class="login-form-banner">
        <div class="row">
          <div
            class="col-md-6 d-flex flex-column align-items-center justify-content-xl-center bg-white p-3 p-sm-4 p-md-3 px-lg-5 py-lg-0"
            style="min-height: 100vh">
            <h5 class="mx-lg-5 mt-lg-5 mb-lg-0 m-md-0 px-lg-5 p-md-0 lineheight-base">
              Connect with the
              <code class="secondary-color">Best healthcare professionals</code>
              and mange your own digital health account
            </h5>
            <div class="mx-lg-5 mt-lg-0 mb-lg-2 m-md-0 px-lg-5 p-md-0">
              <img [src]=hospitalLogoUrl width="200" height="200" alt="" />
            </div>
            <div class="mx-lg-5 mt-lg-0 m-md-0 px-lg-5 p-md-0">
              <img src="../../../assets/img/doctorbg-blue.png" width="200" height="auto" alt="" />
            </div>
          </div>
          <div
            class="col-md-6 p-3 p-sm-4 p-md-3 px-lg-5 py-lg-0 d-flex flex-column justify-content-xl-center primary-bg-color">
            <div class="px-lg-5">
              <form [formGroup]="loginForm">
                <p class="mt-lg-5 m-md-0 p-md-0 px-lg-5 pb-lg-4 user-input text-white">
                  {{hospitalName}}
                </p>
                <div class="p-md-0 px-lg-5 py-lg-3">
                  <label class="text-white" for="">Email</label>
                  <input type="email" class="form-control" formControlName="email" placeholder="Email" />
                  <div class="text-danger"
                    *ngIf="loginForm.controls.email.errors?.required && (loginForm.controls.email.touched||loginForm.controls.email.dirty)">
                    Email Required
                  </div>
                  <div class="text-danger" *ngIf="loginForm.controls.email.errors?.email">
                    Invalid Email Format
                  </div>
                </div>
                <div class="p-md-0 px-lg-5 py-lg-0">
                  <label class="text-white" for="">Password</label>
                  <input id="password" type="password" formControlName="password"
                    placeholder="Create Password (minimum 8)" class="form-control input-field-border" />
                    <div class="text-danger"
                    *ngIf="loginForm.controls.password.errors?.required && (loginForm.controls.password.touched||loginForm.controls.password.dirty)">
                    password Required
                  </div>
                </div>
                <div class="py-3 px-md-0 py-md-5 password-reset px-lg-5 py-lg-3 d-flex justify-content-between">
                  <!-- <p class="text-white">
                  <span>
                    <input
                      style="height: 16px; width: 16px"
                      type="checkbox"
                      class="chk hd-inl" /></span
                  >&nbsp;&nbsp;Remeber me
                </p> -->
                  <p style="cursor: pointer" class="text-white" [routerLink]="['/forgot-password1']">
                    Forgot Password?
                  </p>
                </div>
                <div class="py-2 py-md-0 px-md-0 px-lg-5 py-lg-0">
                  <button class="btn w-100 secondary-bg-color py-2 text-white" style="border-radius: 8px" type="button" [disabled]="loginForm.invalid"
                    (click)="login()">
                    Signin
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>