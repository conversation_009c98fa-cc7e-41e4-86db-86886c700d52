import { Component, OnInit, } from '@angular/core';
import { AuthService } from '../auth.service';
import * as Settings from '../../config/settings';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router, NavigationEnd } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { SharedService } from 'src/app/shared/shared.service';
import { DoctorService } from 'src/app/doctor/doctor.service';
import { HospitalService } from 'src/app/hospital-admin/hospital-admin.service';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  hospitalLogoUrl: any = "../../../assets/img/logo.png";
  hospitalName: string = 'Welcome user';
  loginForm: FormGroup;
  public emailPattern = "^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$";
  loading: boolean;
  email: any;
  phoneNumber: any;
  loadingLoginFormSubmission: boolean;
  emailVerified: any;
  phoneVerified: any;
  verified: boolean;
  loadingVerifyEmailOtpFormSubmission: boolean;
  loadingVerifyPhoneOtpFormSubmission: boolean;
  emailOtpVerified: boolean;
  phoneOtpVerified: boolean;
  practice: string;
  bank: string;
  currentLanguage: string;
  individualLogin: boolean = false;

  constructor(private authService: AuthService,
    public formBuilder: FormBuilder,
    private router: Router,
    private notificationService: ToastrService,
    public translate: TranslateService,
    public sharedService: SharedService,
    public doctorService: DoctorService,
    private hospitalService: HospitalService,
  ) {
    let hospitalURL = '';
    if (window.location.hostname === 'localhost') {
      hospitalURL = localStorage.getItem('loginURL');
      hospitalURL = Settings.URLPREFIX + hospitalURL;
    } else {
      hospitalURL = window.location.href;
      const fragmentIndex = hospitalURL.indexOf('#');
      if (fragmentIndex !== -1) {
        const newUrl = hospitalURL.substring(0, fragmentIndex);
        hospitalURL = newUrl;
      }
    }

    this.authService.loginRedirection(hospitalURL).subscribe((result: any) => {
      if (result.logo_large !== null && result.logo_large != undefined) {
        this.hospitalLogoUrl = result.logo_large;
      }
      if (result.hospital_name !== null && result.hospital_name != undefined) {
        this.hospitalName = result.hospital_name;
      }
    }, (error) => {
      console.log(error);
    }
    );
    this.loginForm = this.formBuilder.group({
      email: new FormControl("", [Validators.required, Validators.email]),
      password: new FormControl("", [Validators.required])
    });

  }

  ngOnInit() {
    const lang = localStorage.getItem('pageLanguage');
    const appointment = sessionStorage.getItem('appointment');
    const apturls = sessionStorage.getItem('appointmenturl');
    // console.log(appointment);
    // console.log(apturls);
    this.translate.use(lang);
    this.currentLanguage = lang;
    if (this.authService.loggedIn()) {
      this.sharedService.createWebsocketStream()
      this.loading = true;
      this.authService.getUserDetail().subscribe(
        (data) => {
          this.translate.use(this.currentLanguage);
          const user_type = data['user_type'];
          localStorage.setItem('user_type', user_type);
          this.redirectToDashboard();
        },
        (error) => {
          this.loading = false;
          console.log(error);
          this.notificationService.error(
            `Authentication credentials were not provided.`,
            'Med.Bot'
          );
        }
      );
    } else {
      this.loading = false;
    }

  }

  login() {
    console.log(this.loginForm.value.email, this.loginForm.value.password);
    this.authService
      .login(this.loginForm.value.email, this.loginForm.value.password)
      .subscribe(
        (data) => {
          this.sharedService.updateIndividualLogin(2);
          this.loading = true;
          this.email = data['email'];
          this.phoneNumber = data['phone'];
          this.loadingLoginFormSubmission = false;
          this.emailVerified = data['email_verified'];
          this.phoneVerified = data['phone_verified'];
          if (this.email == null) {
            this.verified = true;
            this.authService.getUserDetail().subscribe(
              (data) => {
                console.log(data);
                const user_type = data['user_type'];
                localStorage.setItem('user_type', user_type);
                if (user_type != 'Doctor') {
                  this.sharedService.createWebsocketStream();
                }
                if (user_type == 'Patient') {
                  this.redirectappointment();
                } else if (user_type == 'Doctor') {
                  this.redirectToDashboard();
                } else if (user_type == 'PlatformAdmin') {
                  this.router.navigate(['/platform-admin']);
                } else if (user_type == 'HospitalAdmin') {
                  this.hospitalService.setHospitalDetails();
                  this.router.navigate(['/hospital-admin']);
                } else if (user_type == 'DoctorAssistant') {
                  this.hospitalService.setHospitalDetails();
                  this.router.navigate(['/assistant/dashboard']);
                } else if (user_type == 'Partner') {
                  this.hospitalService.setHospitalDetails();
                }
                this.notificationService.success(
                  'LoggedIn Successfully',
                  'Med.Bot'
                );
              },
              (error) => {
                console.log(error);
                this.notificationService.error(
                  `Internal server error`,
                  'Med.Bot'
                );
              }
            );
          } else {
            this.authService.setLogin(false);
            this.loading = false;
            this.notificationService.warning(
              'Verification Pending',
              'Med.Bot'
            );
            if (this.emailVerified) {
              this.loadingVerifyEmailOtpFormSubmission = true;
              setTimeout(() => (this.emailOtpVerified = true), 120);
            }
            if (this.phoneVerified) {
              this.loadingVerifyPhoneOtpFormSubmission = true;
              setTimeout(() => (this.phoneOtpVerified = true), 120);
            }
          }
        },
        (error) => {
          this.loadingLoginFormSubmission = false;
          console.log(error);
          this.notificationService.error(
            `${error['error']['non_field_errors']}`,
            'Med.Bot'
          );
        }
      );
  }
  redirectappointment() {
    const appointment = sessionStorage.getItem('appointment');
    const apturls = sessionStorage.getItem('appointmenturl');
    const getappointment = sessionStorage.setItem('getappointmentcheck', appointment);
    const getappturl = sessionStorage.setItem('getapturl', apturls);
    // console.log(appointment);
    // console.log(apturls);
    if (appointment === 'true') {
      this.router.navigate([apturls]);
      // console.log('appointment page');
    } else {
      // console.log('consult now');
      this.router.navigate(['/patient/dashboard']);
    }
  }

  redirectToDashboard() {
    const userType = localStorage.getItem('user_type');
    this.practice = localStorage.getItem('practice');
    this.bank = localStorage.getItem('bank');
    // console.log(this.practice);
    // console.log(this.bank);
    if (userType == 'Doctor') {
      this.doctorService.getDoctorProfile().subscribe(
        (data) => {
          const doctorApproved = data['is_approved'];
          const status = data['approval_request_status'];
          localStorage.setItem(
            'profile_approved_status',
            data['approval_request_status']
          );
          localStorage.setItem('profile_approval_status', doctorApproved);
          if (doctorApproved && status === 'Approved') {
            if (this.practice === 'true' && this.bank === 'true') {
              // console.log(this.practice);
              this.router.navigate(['/doctor/dashboard']);
              this.router.events.subscribe((val) => {
                const nvigationEnd = val instanceof NavigationEnd;
                if (!!nvigationEnd) {
                  location.reload();
                }
              });
            }
            else {
              // console.log('value check ');
              // console.log(this.practice);
              // console.log(this.bank);
              this.router.navigate(['/doctor/practice-locations'])
              if (this.practice === 'false') {
                this.notificationService.warning('Please create your schedule', 'Med.Bot');
              }
            }
          }
          else {
            this.router.navigate(['/doctor/profile']);
          }
          this.loading = false;
        },
        (error) => {
          this.loading = false;
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(
              `${error['statusText']}`,
              'Med.Bot'
            );
          } else {
            this.notificationService.error(
              `${error['statusText']}`,
              'Med.Bot'
            );
          }
          this.router.navigate(['/doctor/profile']);
        }
      );
    } else if (userType == 'Patient') {
      this.router.navigate(['/patient/dashboard']);
      this.router.events.subscribe((val) => {
        const nvigationEnd = val instanceof NavigationEnd;
        if (!!nvigationEnd) {
          location.reload();
        }
      });
    } else if (userType == 'PlatformAdmin') {
      this.router.navigate(['/platform-admin/dashboard']);
    }
    else {
      this.loading = false;
    }
  }
  fm() {
    return this.loginForm.controls;
  }
  changeLanguage(language: string) {
    // console.log('language', language);
    this.translate.use(language);
  }
}
