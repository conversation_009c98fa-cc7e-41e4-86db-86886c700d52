<div class="content bgImg">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-8 offset-md-2">
        <!-- Account Content -->
        <div class="account-content">
          <div class="row align-items-center justify-content-center">
            <div class="col-md-7 col-lg-7 ">
              <h2 class="connect">Connect with the best healthcare professionals and manage your own digital health account</h2>

                <img src="../../../assets/img/Medbot logo_white_text only_transparent background.png" class="img-fluid" alt="Doccure Register">
            </div>
            <div class="col-md-12 col-lg-6 login-right">
              <div class="login-header">
                <h3 translate>Set Password</h3>
                <p class="small text-muted" translate>Set your new password(minimum 8 character)</p>
              </div>
              <div class="form-group "  *ngIf="formError">
                <!-- <label style="color: orangered;" >Form Error</label> -->
                <div class="card">
                  <ng-container *ngFor="let err of errorValue">
                    <p class="text-danger">&nbsp;{{err.value}}</p>
                  </ng-container>
                </div>
                </div>
              <!-- Forgot Password Form -->
              <form [formGroup]="setPasswordFormData" (submit)="onSubmit()">
                <fieldset [disabled]="loadingSetPasswordFormSubmission">
                  <div class="form-group form-focus">
                    <input
                      type="password"
                      class="form-control floating"
                      formControlName="password1"
                      name="password1"
                      required
                      minlength="8"
                      maxlength="20"
                    />
                    <label class="focus-label floating" translate
                      >New Password</label
                    >
                  </div>
                  <div class="form-group form-focus">
                    <input
                      type="password"
                      class="form-control floating"
                      formControlName="password2"
                      name="password2"
                      required
                      minlength="8"
                      maxlength="20"
                      (input)="getConfirmPassword($event)"
                    />
                    <label class="focus-label floating" translate
                      >Confirm New Password</label
                    >
                  </div>
                  <div class="text-right">
                    <a class="forgot-link" [routerLink]="['/login']" translate
                      >Remember your password?</a
                    >
                  </div>
                  <button
                  class="btn btn-primary btn-block btn-lg login-btn"
                  type="submit" *ngIf="showSavebtn"
                  [disabled]="
                    setPasswordFormData.invalid ||
                    loadingSetPasswordFormSubmission
                  "
                >
                  {{
                    loadingSetPasswordFormSubmission
                      ? ("Loading" | translate)
                      : ("Set Password" | translate)
                  }}
                </button>
                  <button *ngIf="!showSavebtn"
                    class="btn btn-primary btn-block btn-lg login-btn"
                    type="submit"
                    [disabled]="true"
                  >
                    {{
                      loadingSetPasswordFormSubmission
                        ? ("Loading" | translate)
                        : ("Set Password" | translate)
                    }}
                  </button>
                </fieldset>
              </form>
              <!-- /Forgot Password Form -->
            </div>
          </div>
        </div>
        <!-- /Account Content -->
      </div>
    </div>
  </div>
</div>
