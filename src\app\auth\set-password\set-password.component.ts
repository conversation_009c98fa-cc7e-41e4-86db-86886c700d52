import { delay, min } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../auth.service';
import { ToastrService } from 'ngx-toastr';
import * as Settings from '../../config/settings';
import { TranslateService } from '@ngx-translate/core';
import {FormGroup,FormControl,Validators} from '@angular/forms';

@Component({
  selector: 'app-set-password',
  templateUrl: './set-password.component.html',
  styleUrls: ['./set-password.component.css'],
})
export class SetPasswordComponent implements OnInit {
  public loadingSetPasswordFormSubmission = false;
  public setPasswordFormData : FormGroup;
  showSavebtn: boolean;
   public formError= false;
   public errorValue =[];
  constructor(
    private authService: AuthService,
    private _router: Router,
    private _route: ActivatedRoute,
    private notificationService: ToastrService,
    private translate: TranslateService
  ) {}

  ngOnInit(): void {
    this.showSavebtn =false;
    this. setPasswordFormData = new FormGroup({
      password1: new FormControl('', [Validators.required, Validators.minLength(8),this.noWhitespaceValidator]),
      password2: new FormControl('',[Validators.required, Validators.minLength(8),this.noWhitespaceValidator]),
      uidb64: new FormControl('', Validators.required),
      token: new FormControl('', Validators.required),
    });

    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this._route.queryParams.subscribe((params) => {
      this.setPasswordFormData.controls['uidb64'].setValue ( params['uidb64']);
      this.setPasswordFormData. controls['token'].setValue ( params['token']);
    });
  }
  getConfirmPassword(value){
    const password1= this.setPasswordFormData.controls[`password1`].value
    const password2 =this.setPasswordFormData.controls[`password2`].value;
    if(password2.length=== password1.length){
      if(this.setPasswordFormData.controls[`password1`].value === this.setPasswordFormData.controls[`password2`].value){
         this.showSavebtn= true;
      }else{
        this.showSavebtn= false;
        this.notificationService.error(`New password and Confirm password not same`, 'Med.Bot');
      }
    }
    console.log(password2.length);

  }
  onSubmit() {
    this.formError= false;
    this.errorValue=[];
    this.loadingSetPasswordFormSubmission = true;
    if(this.setPasswordFormData.controls[`password1`].value === this.setPasswordFormData.controls[`password2`].value){
      this.authService.setPassword(this.setPasswordFormData.value).subscribe(
        (data) => {
          this.loadingSetPasswordFormSubmission = false;
          this._router.navigate(['/login']);
          this.notificationService.success('Password Reset Completed', 'Med.Bot');
        },
        (error) => {

          this.loadingSetPasswordFormSubmission = false;
          console.log(error);
          const status = error['status'];
          if(status == 400){
            this.formError= true;
            const err = error['error']['error_details']['validation_errors'];
            if(err){
              const new_password2=err['new_password2'];
              this.notificationService.error(`${new_password2[0]}`, 'Med.Bot');
              for( let data in new_password2){
                      this.errorValue.push({value:`${new_password2[data]}`})
              }
            }else{
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }

            }
          else{
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }else{

      this.loadingSetPasswordFormSubmission = false;
      this.setPasswordFormData.controls[`password2`].setValue('');
      this.notificationService.error(`New password and Confirm password not same`, 'Med.Bot');
    }
    }
    noWhitespaceValidator(control: FormControl) {
      const isWhitespace = (control.value || '').trim().length === 0;
      const isValid = !isWhitespace;
      return isValid ? null : { 'whitespace': true };
  }



}
