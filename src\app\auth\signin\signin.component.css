p.reset-link {
    text-align: right;
    font-size: 14px;
    cursor: pointer;
    margin-top: 5px;
}

.verify-btn {
    margin-top: 25px;
}
.bgImg{
  /* background-image: url("../../../assets/img/body-bg.jpg"); */
  border-top-color: #77C1F9;
  background-color: #77C1F9;
}

a.navbar-brand.logo{
  margin-right:0px !important;
}
/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .example {background: red;}
  .btn-signUp{
   
    border-radius: 25px !important;
    background: #005cb7;
    border: none;
    color: #fff;
  }
  .input-field-border{
    border-radius: 10px !important;
  }
  .connect {
    font-size: 18px;
    line-height: 30px;
    color: #fff;
    margin-left: 0px;
    }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .example {background: green;}
  .btn-signUp{
  
    border-radius: 25px !important;
    background: #005cb7;
    border: none;
    color: #fff;
  }
  .input-field-border{
    border-radius: 10px !important;
  }
  .connect {
    font-size: 18px;
  
    line-height: 30px;
    color: #fff;
    margin-left: 0px;
    }
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .example {background: blue;}
  .btn-signUp{
  
    border-radius: 25px !important;
    background: #005cb7;
    border: none;
    color: #fff;
  }
  .input-field-border{
    border-radius: 10px !important;
  }
  .connect {
    font-size: 18px;
   
    line-height: 30px;
    color: #fff;
    margin-left: 0px;
    }
} 

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .example {background: orange;}
  .btn-signUp{
   
    border-radius: 25px !important;
    background: #005cb7;
    border: none;
    color: #fff;
  }
  .input-field-border{
    border-radius: 10px !important;
  }
  .connect {
    font-size: 22px;
    line-height: 30px;
    color: #fff;
    margin-left: 0px;
    }
} 

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .example {background: pink;}
  .btn-signUp{
   
    border-radius: 25px !important;
    background: #005cb7;
    border: none;
    color: #fff;
  }
  .input-field-border{
    border-radius: 10px !important;
  }
  /* .connect {
    font-size: 22px;
    line-height: 30px;
    color: #fff;
    margin-left: 30px;
    } */
  
  .connect{
      font-size: 1.8em !important;
      color: #fff;
      margin-bottom: 0px;
      padding-top: 180px;
      line-height: 35px;
      padding-left: 30px;
  }

  a.navbar-brand.logo{
    margin-right:0px !important;
  }

  .login-form-auth{
    margin: 0px;
  }
}

.text-color{
  color: #fff;
}