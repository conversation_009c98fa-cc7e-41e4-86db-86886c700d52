<div *ngIf="loading">
    <app-loading-spinner></app-loading-spinner>
</div>
<div *ngIf="!loading">
    <div *ngIf="refershPage()" class="content bgImg">
        <div class="container-fluid">

            <div class="row">
                <div class="col-md-12">
                    <!-- Login Tab Content -->
                    <!-- <button (click)="showToasterSuccess()" >
        Toaster Check!
      </button> -->
                    <div class="account-content">
                        <div class="row align-items-center justify-content-center">
                          <div class="col-md-6 col-lg-7 ">
                            <h2 class="connect">Connect with the best healthcare professionals and manage your own digital health account</h2>

                              <img src="../../../assets/img/Medbot logo_white_text only_transparent background.png" class="img-fluid" alt="Doccure Register">
                          </div>
                            <div class="col-md-6 col-lg-4  login-form-btm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="login-header">
                                            <h2 translate class="text-color"> Login </h2>
                                        </div>
                                    </div>
                                    <!-- <div class="col-md-6">
                                  <label >Languages
                                      <select #langSelect (change)="changeLanguage(langSelect.value)">
                                        <option *ngFor="let lang of languageList" [value]="lang.id" [selected]="lang.id === .currentLang" >{{ lang.value }}</option>
                                      </select>
                                    </label>
                              </div> -->
                                </div>
                                <form class="login-form-auth" #_loginForm="ngForm" (submit)="onSubmit()">
                                    <fieldset [disabled]="loadingLoginFormSubmission">
                                        <div class="form-group form-focus">
                                            <input type="email" id="email-fld" class="form-control floating" name="email" autocomplete="email" required [pattern]="emailPattern" [(ngModel)]="loginFormData.email" #_email="ngModel" maxlength="50" autocomplete="on">
                                            <label class="focus-label">{{'Email' }}</label>
                                        </div>
                                        <div class="form-group form-focus">
                                            <input type="password" id="pwd-fld" class="form-control floating" name="password" autocomplete="current-password" required [(ngModel)]="loginFormData.password" #_password="ngModel"  maxlength="50" >
                                            <label class="focus-label">{{'Password'}}</label>
                                        </div>
                                        <div class="text-right">
                                            <a class="forgot-link text-color" [routerLink]="['/forgot-password']">{{'Forgot Password'}} ?</a>
                                        </div>
                                        <button class="btn btn-signUp btn-block btn-lg login-btn" id="logn-btn" type="submit" [disabled]="_loginForm.invalid || loadingLoginFormSubmission">
                  {{ loadingLoginFormSubmission ? ('Loading') : 'Sign In'}}
                </button>
                                        <div class="login-or">
                                            <span class="or-line"></span>
                                            <span class="span-or">{{'or' }}</span>
                                        </div>
                                        <!-- <div class="row form-row social-login">
                <div class="col-6">
                  <a href="#" class="btn btn-facebook btn-block"><i class="fab fa-facebook-f mr-1"></i> Login</a>
                </div>
                <div class="col-6">
                  <a href="#" class="btn btn-google btn-block"><i class="fab fa-google mr-1"></i> Login</a>
                </div>
              </div> -->
                                        <div class="text-center dont-have text-color">{{'Don’t have an account'}}? <a [routerLink]="['/signup']">{{'Register'}}</a></div>
                                    </fieldset>
                                </form>
                            </div>
                        </div>
                    </div>
                    <!-- /Login Tab Content -->

                </div>
            </div>

        </div>

    </div>
    <div  class="content bgImg" *ngIf="!refershPage()" >
      <div class="container-fluid">

          <div class="row">
              <div class="col-md-10">

                  <!-- Account Content -->
                  <div class="account-content">
                      <div class="row align-items-center justify-content-center">
                        <div class="col-md-6 col-lg-7 ">
                          <h2 class="connect">Connect with the best healthcare professionals and manage your own digital health account</h2>

                            <img src="../../../assets/img/Medbot logo_white_text only_transparent background.png" class="img-fluid" alt="Doccure Register">
                        </div>
                          <div class="col-md-6 col-lg-4 ">
                              <div class="login-header">
                                  <h3 class="text-color">OTP Verification</h3>
                                  <p *ngIf="!phoneVerified" class="small text-color">Your Phone verification is pending, please check your sms for verification codes. If you do not receive the codes click the resend links. </p>
                                  <p *ngIf="phoneVerified" class="small  text-color">Your Phone verification is completed, click here to <a (click)="navigation()" style="cursor: pointer;color: #20c0f3;">login</a></p>
                                  <!-- <p *ngIf="!emailVerified || !phoneVerified" class="small text-color">Your Email/Phone verification is pending, please check your mail/sms for verification codes. If you do not receive the codes click the resend links. </p> -->
                                  <!-- <p *ngIf="emailVerified && phoneVerified" class="small  text-color">Your Email/Phone verification is completed, click here to <a (click)="navigation()" style="cursor: pointer;color: #20c0f3;">login</a></p> -->
                              </div>

                              <!-- Verify Email OTP Form -->
                              <form #_verifyEmailOtpFormData="ngForm" (submit)="onSubmitEmailOtp()">
                                  <fieldset [disabled]="loadingVerifyEmailOtpFormSubmission">
                                      <div class="form-group form-focus">
                                          <div class="form-group form-focus">
                                              <input [readonly]="true" type="text" class="form-control floating" name="email_or_phone_value" autocomplete="name" required [(ngModel)]="verifyEmailOtpFormData.email_or_phone_value" #_email_or_phone_value="ngModel">
                                              <label class="focus-label float">Email</label>
                                          </div>
                                      </div>
                                      <div *ngIf="!emailVerified" class="form-group form-focus">
                                          <input type="password" id="verify-data-one" class="form-control floating" name="value" required [(ngModel)]="verifyEmailOtpFormData.value" #_value="ngModel" maxlength="35" >
                                          <label class="focus-label float">Email OTP</label>
                                          <p (click)="resendEmailOTP()" class="reset-link text-color">Resend Email OTP?</p>
                                      </div>
                                      <button #emailButton id="email-ver-btn" class="btn btn-signUp btn-block btn-lg verify-btn" type="submit" [disabled]="emailVerified || _verifyEmailOtpFormData.invalid || loadingVerifyEmailOtpFormSubmission ">
                {{ loadingVerifyEmailOtpFormSubmission ? emailOtpVerified ?'&nbsp;&nbsp; Email Verified &nbsp;&nbsp;' :'&nbsp;&nbsp; Verifying ... &nbsp;&nbsp;' : '&nbsp;&nbsp; Verify Email &nbsp;&nbsp;'}}
              </button>
                                  </fieldset>
                              </form>
                              <!-- /Verify Email OTP Form -->
                              <br>
                              <br>
                              <!-- Verify Phone OTP Form -->
                              <form #_verifyPhoneOtpFormData="ngForm" (submit)="onSubmitPhoneOtp()">
                                  <fieldset [disabled]="loadingVerifyPhoneOtpFormSubmission">
                                      <div class="form-group form-focus">
                                          <div class="form-group form-focus">
                                              <input [readonly]="true" type="text" class="form-control floating" name="email_or_phone_value" autocomplete="name" required [(ngModel)]="verifyPhoneOtpFormData.email_or_phone_value" #_email_or_phone_value="ngModel">
                                              <label class="focus-label float">Phone</label>
                                          </div>
                                      </div>
                                      <div *ngIf="!phoneVerified" class="form-group form-focus">
                                          <input type="password" id="verify-data-two" [readonly]="phoneVerified" class="form-control floating" name="value" required [(ngModel)]="verifyPhoneOtpFormData.value" #_value="ngModel" maxlength="6" >
                                          <p (click)="resendPhoneOTP()" class="reset-link text-color" >Resend Phone OTP?</p>
                                          <label class="focus-label float">Phone OTP</label>
                                      </div>
                                      <button #phoneButton id="phone-ver-btn" class="btn btn-signUp btn-block btn-lg verify-btn mb-4" type="submit" [disabled]="phoneVerified || _verifyPhoneOtpFormData.invalid || loadingVerifyPhoneOtpFormSubmission">
                {{ loadingVerifyPhoneOtpFormSubmission ?  phoneOtpVerified ?'&nbsp;&nbsp; Phone Verified &nbsp;&nbsp;' :'&nbsp;&nbsp; Verifying ... &nbsp;&nbsp;' : '&nbsp;&nbsp; Verify Phone &nbsp;&nbsp;'}}
              </button>
                                  </fieldset>
                              </form>
                              <!-- /Verify Phone OTP Form -->
                          </div>
                      </div>
                  </div>
                  <!-- /Account Content -->

              </div>
          </div>

      </div>

    </div>

</div>
