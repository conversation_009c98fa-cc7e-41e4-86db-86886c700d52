import { ToastrService } from 'ngx-toastr';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { AuthService } from '../auth.service';
import { Router, NavigationEnd } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SharedService } from '../../shared/shared.service';
import { DoctorService } from '../../doctor/doctor.service';
import { PublicProfileComponent } from '../../public-profile/public-profile.component';
import { HospitalService } from 'src/app/hospital-admin/hospital-admin.service';

@Component({
  selector: 'app-signin',
  templateUrl: './signin.component.html',
  styleUrls: ['./signin.component.css'],
})
export class SigninComponent implements OnInit {
  @ViewChild('emailButton') emailButton: ElementRef;
  @ViewChild('phoneButton') phoneButton: ElementRef;
  public verified = true;
  public loading = false;
  public emailVerified = false;
  public phoneVerified = false;
  public email = null;
  public phoneNumber = null;
  public loadingVerifyEmailOtpFormSubmission = false;
  public loadingVerifyPhoneOtpFormSubmission = false;

  public verifyEmailOtpFormData = {
    type: 'Email',
    value: null,
    email_or_phone_value: null,
  };

  public verifyPhoneOtpFormData = {
    type: 'Phone',
    value: null,
    email_or_phone_value: null,
    email: null,
  };

  public loginFormData = {
    email: null,
    password: null,
  };

  public emailOtpResendForm = {
    type: 'Email',
    value: null,
    email: null,
    password: null,
  };

  public phoneOtpResendForm = {
    type: 'Phone',
    value: null,
    phone: null,
    email: null,
    password: null,
  };
  public languageList = [
    { id: 'en', value: 'English' },
    { id: 'fr', value: 'French' },
    { id: 'ta', value: 'Tamil' },
  ];
  public loadingLoginFormSubmission = false;
  currentLanguage = '';
  public showAlertMessage = false;
  public phoneOtpVerified = false;
  public emailOtpVerified = false;
  public practice: string;
  public bank: string;
  refershPage() {
    return this.authService.reLoadPage();
  }
  public emailPattern = "^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$";
  constructor(
    private authService: AuthService,
    private router: Router,
    private notificationService: ToastrService,
    public translate: TranslateService,
    public sharedService: SharedService,
    public doctorService: DoctorService,
    public publicprofileComponent: PublicProfileComponent,
    private hospitalService: HospitalService,
  ) { }

  ngOnInit() {
    const lang = localStorage.getItem('pageLanguage');
    const appointment = sessionStorage.getItem('appointment');
    const apturls = sessionStorage.getItem('appointmenturl');
    // console.log(appointment);
    // console.log(apturls);
    this.translate.use(lang);
    this.currentLanguage = lang;
    if (this.authService.loggedIn()) {
      this.sharedService.createWebsocketStream()
      this.loading = true;
      this.authService.getUserDetail().subscribe(
        (data) => {
          this.translate.use(this.currentLanguage);
          const user_type = data['user_type'];
          localStorage.setItem('user_type', user_type);
          this.redirectToDashboard();
        },
        (error) => {
          this.loading = false;
          console.log(error);
          this.notificationService.error(
            `Authentication credentials were not provided.`,
            'Med.Bot'
          );
        }
      );
    } else {
      this.loading = false;
    }
  }

  onSubmit() {
    this.loadingLoginFormSubmission = true;
    this.authService
      .login(this.loginFormData.email, this.loginFormData.password)
      .subscribe(
        (data) => {
          this.loading = true;
          this.email = data['email'];
          this.phoneNumber = data['phone'];
          this.verifyEmailOtpFormData.email_or_phone_value = this.email;
          this.verifyPhoneOtpFormData.email = this.email;
          this.verifyPhoneOtpFormData.email_or_phone_value = this.phoneNumber;
          this.loadingLoginFormSubmission = false;
          this.emailVerified = data['email_verified'];
          this.phoneVerified = data['phone_verified'];
          if (this.email == null) {
            this.verified = true;
            this.authService.getUserDetail().subscribe(
              (data) => {
                console.log(data);
                const user_type = data['user_type'];
                localStorage.setItem('user_type', user_type);
                if (user_type != 'Doctor') {
                  this.sharedService.createWebsocketStream();
                }
                if (user_type == 'Patient') {
                  this.redirectappointment();
                } else if (user_type == 'Doctor') {
                  this.redirectToDashboard();
                } else if (user_type == 'PlatformAdmin') {
                  this.router.navigate(['/platform-admin']);
                } else if (user_type == 'HospitalAdmin') {
                  this.hospitalService.setHospitalDetails();
                  this.router.navigate(['/hospital-admin']);
                } else if (user_type == 'DoctorAssistant') {
                  this.hospitalService.setHospitalDetails();
                  this.router.navigate(['/assistant/dashboard']);
                } else if (user_type == 'Partner') {
                  this.hospitalService.setHospitalDetails();
                }
                this.notificationService.success(
                  'LoggedIn Successfully',
                  'Med.Bot'
                );
              },
              (error) => {
                console.log(error);
                this.notificationService.error(
                  `Internal server error`,
                  'Med.Bot'
                );
              }
            );
          } else {
            this.authService.setLogin(false);
            this.loading = false;
            this.notificationService.warning(
              'Verification Pending',
              'Med.Bot'
            );
            if (this.emailVerified) {
              this.loadingVerifyEmailOtpFormSubmission = true;
              setTimeout(() => (this.emailOtpVerified = true), 120);
            }
            if (this.phoneVerified) {
              this.loadingVerifyPhoneOtpFormSubmission = true;
              setTimeout(() => (this.phoneOtpVerified = true), 120);
            }
          }
        },
        (error) => {
          this.loadingLoginFormSubmission = false;
          console.log(error);
          this.notificationService.error(
            `${error['error']['non_field_errors']}`,
            'Med.Bot'
          );
        }
      );
  }

  onSubmitEmailOtp() {
    this.loadingVerifyEmailOtpFormSubmission = true;
    this.emailOtpVerified = false;
    this.authService.postVerifyOTP(this.verifyEmailOtpFormData).subscribe(
      (data) => {
        this.loadingVerifyEmailOtpFormSubmission = true;
        this.emailOtpVerified = true;
        this.emailVerified = true;
        this.emailButton.nativeElement.innerHTML =
          '&nbsp;&nbsp; Email Verified &nbsp;&nbsp';
        this.notificationService.success(
          'Email Verified Successfully',
          'Med.Bot'
        );
      },
      (error) => {
        this.loading = false;
        this.loadingVerifyEmailOtpFormSubmission = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['error_message']}`,
            'Med.Bot'
          );
        } else if (status == 417) {
          this.notificationService.error(
            `${error['error']['error_message']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );
    if (this.emailVerified && this.phoneVerified) {
      this.navigation();
    }
  }

  onSubmitPhoneOtp() {
    this.loadingVerifyPhoneOtpFormSubmission = true;
    this.phoneOtpVerified = false;
    this.authService.postVerifyOTP(this.verifyPhoneOtpFormData).subscribe(
      (data) => {
        this.loadingVerifyPhoneOtpFormSubmission = true;
        this.phoneOtpVerified = true;
        this.phoneVerified = true;
        console.log('signin');
        this.notificationService.success(
          'Phone Verified Successfully',
          'Med.Bot'
        );
        this.router.navigate(['/login']);
      },
      (error) => {
        this.loadingVerifyPhoneOtpFormSubmission = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else if (status == 417) {
          this.notificationService.error(
            `${error['error']['error_message']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );
    if (this.emailVerified && this.phoneVerified) {
      this.navigation();
    }
  }

  resendEmailOTP() {
    this.loading = true;
    this.emailOtpResendForm.email = this.email;
    this.emailOtpResendForm.value = this.email;
    this.emailOtpResendForm.password = this.loginFormData.password;
    this.authService.sendEmailOtp(this.emailOtpResendForm).subscribe(
      (data) => {
        this.loading = false;
        this.notificationService.success(
          'Email verification code sent',
          'Med.Bot'
        );
      },
      (error) => {
        this.loading = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );
  }

  resendPhoneOTP() {
    this.phoneOtpResendForm.email = this.email;
    this.phoneOtpResendForm.value = this.phoneNumber;
    this.phoneOtpResendForm.phone = this.phoneNumber;
    this.phoneOtpResendForm.password = this.loginFormData.password;
    this.authService.sendPhoneOtp(this.phoneOtpResendForm).subscribe(
      (data) => {
        this.notificationService.success(
          'Phone verification code sent',
          'Med.Bot'
        );
      },
      (error) => {
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );
  }

  changeLanguage(language: string) {
    // console.log('language', language);
    this.translate.use(language);
  }

  redirectToDashboard() {
    const userType = localStorage.getItem('user_type');
    this.practice = localStorage.getItem('practice');
    this.bank = localStorage.getItem('bank');
    // console.log(this.practice);
    // console.log(this.bank);
    if (userType == 'Doctor') {
      this.doctorService.getDoctorProfile().subscribe(
        (data) => {
          const doctorApproved = data['is_approved'];
          const status = data['approval_request_status'];
          localStorage.setItem(
            'profile_approved_status',
            data['approval_request_status']
          );
          localStorage.setItem('profile_approval_status', doctorApproved);
          if (doctorApproved && status === 'Approved') {
            if (this.practice === 'true' && this.bank === 'true') {
              // console.log(this.practice);
              this.router.navigate(['/doctor/dashboard']);
              this.router.events.subscribe((val) => {
                const nvigationEnd = val instanceof NavigationEnd;
                if (!!nvigationEnd) {
                  location.reload();
                }
              });
            }
            else {
              // console.log('value check ');
              // console.log(this.practice);
              // console.log(this.bank);
              this.router.navigate(['/doctor/practice-locations'])
              if (this.practice === 'false') {
                this.notificationService.warning('Please create your schedule', 'Med.Bot');
              }
            }
          }
          else {
            this.router.navigate(['/doctor/profile']);
          }
          this.loading = false;
        },
        (error) => {
          this.loading = false;
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(
              `${error['statusText']}`,
              'Med.Bot'
            );
          } else {
            this.notificationService.error(
              `${error['statusText']}`,
              'Med.Bot'
            );
          }
          this.router.navigate(['/doctor/profile']);
        }
      );
    } else if (userType == 'Patient') {
      this.router.navigate(['/patient/dashboard']);
      this.router.events.subscribe((val) => {
        const nvigationEnd = val instanceof NavigationEnd;
        if (!!nvigationEnd) {
          location.reload();
        }
      });
    } else if (userType == 'PlatformAdmin') {
      this.router.navigate(['/platform-admin/dashboard']);
    }
    else {
      this.loading = false;
    }
  }

  redirectappointment() {
    const appointment = sessionStorage.getItem('appointment');
    const apturls = sessionStorage.getItem('appointmenturl');
    const getappointment = sessionStorage.setItem('getappointmentcheck', appointment);
    const getappturl = sessionStorage.setItem('getapturl', apturls);
    // console.log(appointment);
    // console.log(apturls);
    if (appointment === 'true') {
      this.router.navigate([apturls]);
      // console.log('appointment page');
    } else {
      // console.log('consult now');
      this.router.navigate(['/patient/appointment/98f51c47-4600-4fc7-9eb5-02d6265e4fc5/afb6dd80-7dad-4602-b941-ae42eb65e796/booking']);
    }
  }

  navigation() {
    localStorage.clear();
    this.authService.setLogin(true);
  }

}
