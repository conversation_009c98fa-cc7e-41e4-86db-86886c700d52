<div *ngIf="loading">
    <app-loading-spinner></app-loading-spinner>
</div>
<div *ngIf="!loading">
    <div *ngIf="!formSubmitted" class="content">
        <div class="container-fluid body-condent ">
            <div class="row">
                <div class="col-md-12">
                    <style>
                        input.largerCheckbox {
                            width: 25px;
                            height: 25px;
                            margin-right: 10px
                        }
                    </style>
                    <!-- Register Content -->
                    <div class="account-content">
                        <div class="row align-items-center justify-content-center">
                            <div class="col-md-7 col-lg-7 ">
                                <h2 class="connect">Connect with the best healthcare professionals and manage your own
                                    digital health account
                                </h2>

                                <img id="white-medbot"
                                    src="../../../assets/img/Medbot logo_white_text only_transparent background.png"
                                    class="img-fluid" alt="Doccure Register">
                            </div>
                            <div class="col-lg-4 form-signup">
                                <p *ngIf="userType ==='Doctor'" class="text-right mob-link" style="color: white;"><span
                                        class="fas fa-phone-alt"></span> Support-{{supportNumber}}</p>
                                <div class="login-header mt-1">
                                    <label style="color: white;font-size: 40px;"><b>SIGN UP</b></label><br>
                                    <label style="color: white;font-size: 30px;font-weight: bolder;">as a</label>
                                    <table>
                                        <td>
                                            <input type="checkbox" id="doctor" name="doctor" value="Doctor"
                                                class="largerCheckbox" [checked]="this.doctorChecked"
                                                (click)="getUserType('Doctor')">
                                            <label for="doctor"
                                                style="color: white; font-size: 30px; font-weight: bolder; width: 385px;">DOCTOR</label><br>
                                        </td>
                                        <td>
                                            <input type="checkbox" id="patient" name="patient" value="Patient"
                                                class="largerCheckbox" [checked]="this.patientChecked"
                                                (click)="getUserType('Patient')">
                                            <label for="patient"
                                                style="color: white; font-size: 30px; font-weight: bolder; width: 50px;">PATIENT</label><br>
                                        </td>
                                    </table>
                                    <!-- <h3 class="reg-head" style="color: white;">{{this.userType}} Signup <span *ngIf="userType ==='Patient'"><a class="are-link" style="color: white;" [routerLink]="['/signup']"
                        [queryParams]="{ user_type: 'Doctor' }" (click)="getUserType('Doctor')">Are you a
                        Doctor?</a></span>
                                        <span *ngIf="userType ==='Doctor'"><a class="are-link" [routerLink]="['/signup']"
                        [queryParams]="{ user_type: 'Patient' }" (click)="getUserType('Patient')"
                        style="color: white;">Are you a Patient? </a></span>
                                    </h3>
                                </div> -->
                                    <!-- <input type="checkbox" class="reg-head" id="usertype" style="color: white;" value="Doctor" (click)="getUserType('Doctor')">
                                    <label for="doctor" style="color: white; font-size: 30px; font-weight: bolder; width: 200px"> Doctor</label><br>
                                    <input type="checkbox" class="reg-head" id="usertype" style="color: white;" value="Patient" (click)="getUserType('Patient')">                                    
                                    <label for="patient" style="color: white; font-size: 30px; font-weight: bolder; width: 200px"> Patient</label><br> -->
                                </div>
                                <div class="form-group " *ngIf="formValidationError">
                                    <div class="card">
                                        <ng-container *ngFor="let err of errorValue">
                                            <p class="text-danger">&nbsp;{{err.value}}</p>
                                        </ng-container>
                                    </div>

                                </div>
                                <!-- Register Form -->
                                <form class="signup-form" [formGroup]="signupFormData">
                                    <fieldset [disabled]="loadingSignupFormSubmission">

                                        <div class="form-group ">

                                            <input type="text" class="form-control input-field-border " name="name"
                                                autocomplete="name" formControlName="name" placeholder="Name"
                                                maxlength="25" pattern="^[a-zA-Z ]*$" (input)="nameValidation($event)"
                                                autocomplete="off">
                                            <!-- <span *ngIf="showNameValidationError" class="text-danger">Name Error</span> -->
                                            <span
                                                *ngIf="signupFormData.get('name').errors?.required && signupFormData.get('name').touched"
                                                class="text-danger">Name required</span>
                                            <span *ngIf="signupFormData.get('name').errors?.pattern"
                                                class="text-danger">Name only in Alphabets</span>
                                        </div>

                                        <div class="form-group ">

                                            <input type="email" class="form-control input-field-border"
                                                formControlName="email" placeholder="Email" name="email" maxlength="50"
                                                (input)="emailValidation($event)" autocomplete="off">
                                            <span *ngIf="showEmailValidationError" class="text-danger">Invalid Email
                                                Fromat (eg
                                                :abc&#64;mail.com)</span>
                                            <span
                                                *ngIf="signupFormData.get('email').errors?.required && signupFormData.get('email').touched"
                                                class="text-danger">Email required</span>

                                        </div>
                                        <div class="form-group ">

                                            <input type="text" class="form-control input-field-border "
                                                placeholder="Mobile Number" name="phone" autocomplete="phone"
                                                formControlName="phone" minlength="10" maxlength="15"
                                                (input)="phoneValidation($event)" autocomplete="off">
                                            <!-- <span *ngIf="showPhoneValidationError" class="text-danger">Phone Number should be minimum 10 numbers</span> -->
                                            <span *ngIf="signupFormData.get('phone').errors?.pattern"
                                                class="text-danger">Phone Number is only in Numbers</span>
                                                <span *ngIf="signupFormData.get('phone').errors?.minlength"
                                                class="text-danger">Phone Number minimum 10 Numbers</span>
                                            <span
                                                *ngIf="signupFormData.get('phone').errors?.required && signupFormData.get('phone').touched "
                                                class="text-danger">Phone Number Required</span>

                                        </div>
                                        <div>
                                            <!-- <select id="userType">
                                                <option value="0" disabled selected="selected">Select userType</option>
                                                <option value="Doctor" translate>Doctor</option>
                                                <option value="Patient" translate>Patient</option>
                                            </select> -->
                                        </div>
                                        <div class="form-group ">
                                            <div class="flexContainer">
                                                <input id="password" type="password"
                                                    placeholder="Create Password (minimum 8)"
                                                    class="form-control input-field-border" name="password"
                                                    minlength="8" autocomplete="current-password" required
                                                    formControlName="password" maxlength="25"
                                                    (input)="passwordValidation($event)" autocomplete="off">
                                                <i class="far fa-eye" id="togglePassword" (click)="passwordhideshow()"
                                                    style="margin-top: 14px; margin-left: -30px; cursor: pointer;"></i>
                                                <!-- <button type="submit" (click)="passwordhideshow()"></button> -->
                                                <!-- <button (click)="x.type=x.type=='password'?'text':'password'"></button> -->
                                            </div>
                                            <span *ngIf="showPasswordValidationError" class="text-danger">Password
                                                should be minimum 8 Characters</span>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group ">

                                                    <select class="form-control input-field-border select" name="gender"
                                                        id="gender" formControlName="gender">
                                                        <option value="0" disabled>Select Gender</option>
                                                        <option value="Male" translate>Male</option>
                                                        <option value="Female" translate>Female</option>
                                                        <option value="Prefer not to answer" translate>Prefer not to
                                                            answer</option>
                                                    </select>

                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group ">
                                                    <input type="text" placeholder="DOB" onkeydown="return false"
                                                        class="form-control input-field-border"
                                                        formControlName="date_of_birth" [minDate]="minDate"
                                                        [maxDate]="maxDate" bsDatepicker
                                                        [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
                                                </div>
                                            </div>
                                        </div>
                                        <!-- <div class="text-right">
                                            <a class="forgot-link are-link" style="color: whitesmoke;" [routerLink]="['/login']">Already have an account?&nbsp;Login</a>
                                        </div> -->
                                        <button class="btn btn-signUp btn-block btn-lg login-btn" type="button"
                                            (click)=" confrimation()" [disabled]="!signupFormData.valid">Signup</button>
                                        <p class="t_c_text">By signing up, you agree to Med.Bot's Terms of Use and
                                            Privacy Policy</p>
                                        <div class="login-or">
                                        </div>

                                    </fieldset>
                                </form>
                                <!-- /Register Form -->

                            </div>
                            <div class="col-md-6 col-lg-1"></div>
                        </div>
                    </div>
                    <!-- /Register Content -->

                </div>
            </div>

        </div>
        <br>
        <br>
        <br>
        <br>
    </div>

    <div *ngIf="formSubmitted" class="content bgImg">
        <div class="container-fluid body-condent">
            <div class="row">
                <div class="col-md-12 ">

                    <!-- Account Content -->
                    <!-- Account Content -->
                    <div class="account-content">
                        <div class="row align-items-center justify-content-center">
                            <div class="col-md-7 col-lg-7 ">
                                <h2 class="connect">Connect with the best healthcare professionals and manage your own
                                    digital health account
                                </h2>

                                <img src="../../../assets/img/Medbot logo_white_text only_transparent background.png"
                                    class="img-fluid" alt="Doccure Register">
                            </div>
                            <div class="col-md-12 col-lg-4">
                                <div class="login-header">
                                    <h3 translate>OTP Verification</h3>
                                    <p *ngIf="!phoneVerified" class="small text-muted" translate>Your Phone verification
                                        is pending, please check your sms for verification codes. </p>
                                    <p *ngIf="phoneVerified" class="small text-muted" translate>Your Phone verification
                                        is completed</p>
                                    <!-- <p *ngIf="!emailVerified || !phoneVerified" class="small text-muted" translate>Your Email/Phone verification is pending, please check your mail/sms for verification codes. </p>
                                    <p *ngIf="emailVerified && phoneVerified" class="small text-muted" translate>Your Email/Phone verification is completed</p> -->
                                </div>

                                <!-- Verify Email OTP Form -->
                                <!-- <form #_verifyEmailOtpFormData="ngForm" (submit)="onSubmitEmailOtp()">
                                    <fieldset [disabled]="loadingVerifyEmailOtpFormSubmission">
                                        <div class="form-group form-focus">
                                            <div class="form-group form-focus">
                                                <input [readonly]="true" type="text" class="form-control input-field-border floating" name="email_or_phone_value" autocomplete="name" required [(ngModel)]="verifyEmailOtpFormData.email_or_phone_value" #_email_or_phone_value="ngModel">
                                                <label class="focus-label float" translate>Email</label>
                                            </div>
                                        </div>
                                        <div *ngIf="!emailVerified" class="form-group form-focus">
                                            <input type="password" class="form-control input-field-border floating" name="value" required [(ngModel)]="verifyEmailOtpFormData.value" #_value="ngModel">
                                            <label class="focus-label float" translate>Email OTP</label>
                                            <!--p (click)="resendEmailOTP()" class="reset-link">Resend Email OTP?</p-->
                                <!--  </div>
                                        <button #emailButton class="btn btn-signUp btn-block btn-lg login-btn" type="submit" [disabled]="emailVerified || _verifyEmailOtpFormData.invalid || loadingVerifyEmailOtpFormSubmission ">
                      {{ loadingVerifyEmailOtpFormSubmission ? emailOtpVerified?'&nbsp;&nbsp; Verifying ...&nbsp;&nbsp;' :'&nbsp;&nbsp; Email Verified &nbsp;&nbsp;' : '&nbsp;&nbsp; Verify Email &nbsp;&nbsp;' |translate}}
                    </button>
                                    </fieldset>
                                </form> -->
                                <!-- /Verify Email OTP Form -->
                                <br>
                                <br>
                                <!-- Verify Phone OTP Form -->
                                <form #_verifyPhoneOtpFormData="ngForm" (submit)="onSubmitPhoneOtp()">
                                    <fieldset [disabled]="loadingVerifyPhoneOtpFormSubmission">
                                        <div class="form-group form-focus">
                                            <div class="form-group form-focus">
                                                <input [readonly]="true" type="text"
                                                    class="form-control input-field-border floating"
                                                    name="email_or_phone_value" autocomplete="name" required
                                                    [(ngModel)]="verifyPhoneOtpFormData.email_or_phone_value"
                                                    #_email_or_phone_value="ngModel">
                                                <label class="focus-label float" translate>Phone</label>
                                            </div>
                                        </div>
                                        <div *ngIf="!phoneVerified" class="form-group form-focus">
                                            <input type="password" [readonly]="phoneVerified"
                                                class="form-control input-field-border floating" name="value" required
                                                [(ngModel)]="verifyPhoneOtpFormData.value" #_value="ngModel">
                                            <!-- <p class="reset-link">Resend Phone OTP?</p>  -->
                                            <label class="focus-label float" translate>Phone OTP</label>
                                        </div>
                                        <button #phoneButton class="btn btn-signUp btn-block btn-lg login-btn mb-4"
                                            type="submit"
                                            [disabled]="phoneVerified || _verifyPhoneOtpFormData.invalid || loadingVerifyPhoneOtpFormSubmission">
                                            {{ loadingVerifyPhoneOtpFormSubmission ? phoneOtpVerified ? '&nbsp;&nbsp;
                                            loading... &nbsp;&nbsp;': '&nbsp;&nbsp; Phone Verified &nbsp;&nbsp;' :
                                            '&nbsp;&nbsp; Verify Phone&nbsp;&nbsp;' |translate}}
                                        </button>
                                    </fieldset>
                                </form>
                                <button *ngIf="emailVerified && phoneVerified"
                                    class="btn btn-signUp btn-block btn-lg login-btn mt-2"
                                    (click)="showSignUpForm()">Sign In</button>
                                <!-- /Verify Phone OTP Form -->
                            </div>
                        </div>
                    </div>
                    <!-- /Account Content -->
                    <!-- /Account Content -->

                </div>
            </div>
        </div>
    </div>
</div>


<!-- Modal -->
<div class="modal fade" id="signupModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header ">
                <h5 class="modal-title text-center" id="exampleModalLongTitle">Are you sure?</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body ml-2">

                <p>Display Name : {{signupFormData.controls.name.value}}</p>
                <p>Email : {{signupFormData.controls.email.value}}</p>
                <p>Mobile No : {{signupFormData.controls.phone.value}}</p>
                <small>*Once you submitted the signup form you are not allowed to change the display name,email and
                    mobile number</small>
                <!-- <p>Password  : {{signupFormData.controls.password.value}}</p> -->
                <!-- <p>Gender    : {{signupFormData.controls.gender.value}}</p> -->
                <!-- <p>Date of birth : {{signupFormData.controls.date_of_birth.value | date:'dd-MM-yyyy'}}</p> -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Edit</button>
                <button type="button" class="btn btn-primary" (click)="onSubmit()">Confirm</button>
            </div>
        </div>
    </div>
</div>