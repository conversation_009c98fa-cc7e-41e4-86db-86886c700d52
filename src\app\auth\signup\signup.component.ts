import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute ,NavigationEnd} from '@angular/router';
import { AuthService } from '../auth.service';
import { Observable } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import * as Settings from '../../config/settings';
import { delay } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import * as moment from 'moment';
import  {SharedService} from '../../shared/shared.service';
import  {DoctorService} from '../../doctor/doctor.service';
declare var $;
@Component({
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.css'],
})
export class SignupComponent implements OnInit {
  public loading = true;
  public practice: string;
  public bank: string;
  public formSubmitted = false;
  public emailVerified = false;
  public phoneVerified = false;
  public loadingVerifyEmailOtpFormSubmission = false;
  public loadingVerifyPhoneOtpFormSubmission = false;
  public maxDate: Date;
  public minDate: Date;
  doctorChecked = false;
  patientChecked = false;
  mobNumberPattern = '^((\\+91-?)|0)?[0-9]{10}$';
  public verifyEmailOtpFormData = {
    type: 'Email',
    value: null,
    email_or_phone_value: null,
  };

  public verifyPhoneOtpFormData = {
    type: 'Phone',
    value: null,
    email_or_phone_value: null,
    email:null,


  };
  public userType = null;
  public signupFormData: FormGroup;
  public loadingSignupFormSubmission = false;
  public showAlertMessage = false;
  formValidationError: boolean;
  public errorValue = [];
  public phoneOtpVerified = false;
  public emailOtpVerified = false;
  showNameValidationError: boolean;
  showEmailValidationError: boolean;
  showPhoneValidationError: boolean;
  showPasswordValidationError: boolean;
  supportNumber = Settings.supportNumber;
  constructor(
    private _authService: AuthService,
    private _router: Router,
    private _route: ActivatedRoute,
    private sharedService: SharedService,
    private notificationService: ToastrService,
    private doctorService: DoctorService,
  ) {}

  ngOnInit() {
      document.body.style.overflowY = 'auto';
      document.body.style.background='#77C1F9';

    this.minDate = new Date();
    this.minDate.setDate(this.minDate.getDate() - 36500);
    this.maxDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate() -1);

    this.addFormControl();
    this.loading = false;
    const user_type = localStorage.getItem('user_type');
     if (user_type !==null) {
      this.sharedService.createWebsocketStream()
      this.loading = true;
      this._authService.getUserDetail().subscribe(
        (data) => {
          // this.translate.use(this.currentLanguage);
          const user_type = data['user_type'];
          localStorage.setItem('user_type', user_type);
          this.redirectToDashboard();
        },
        (error) => {
          this.loading = false;
          console.log(error);
          this.notificationService.error(
            `Authentication credentials were not provided.`,
            'Med.Bot'
          );
        }
      );
    } else {
      this.loading = false;
    }

    this.formValidationError = false;
    // this._route.queryParams.subscribe((params) => {
    //   this.userType = params['user_type'] ? params['user_type'] : 'Patient';
    //   // this.userType = this.signupFormData.get('userType').value
    //   if(this.userType ==='Doctor'){
    //     this.maxDate = new Date();
    //     console.log( this.maxDate );
    //     this.maxDate.setDate(this.maxDate.getDate() - 7671);
    //     console.log( this.maxDate );
    //   }else{
    //     this.maxDate = new Date();

    //   }
    // });
  }

  ngOnDestroy(){
    document.body.style.overflowY = 'auto';
    document.body.style.background='#ffffff';
  }

  addFormControl() {
    this.signupFormData = new FormGroup({
      name: new FormControl('', [Validators.required, Validators.maxLength(25),Validators.pattern('^[a-zA-Z ]*$')]),
      email: new FormControl('', [Validators.required,Validators.email]),
      phone: new FormControl('', [Validators.required,Validators.maxLength(15),Validators.minLength(10),Validators.pattern('^[0-9]*$')]),
      password: new FormControl('', [Validators.required,this.noWhitespaceValidator]),
      date_of_birth: new FormControl(null, Validators.required),
      // userType: new FormControl(),
      gender: new FormControl('0', [Validators.required, Validators.minLength(3)]),
    });
  }
  // onSubmit() {
  //   // var inputValue = (<HTMLInputElement>document.getElementById('userType')).value;
  //   // console.log(inputValue);
  //   console.log(this.signupFormData.value);
  //   console.log(this.userType);

  // }
  onSubmit() {
    $('#signupModal').modal('hide');
    this.loading = true;
    this.formValidationError = false;
    this.errorValue = [];
//    var el : HTMLElement = document.getElementById('userType');
    this.loadingSignupFormSubmission = true;
    if (this.userType === 'Patient' || this.userType === 'Doctor') {
      const dateFormate = this.signupFormData.controls['date_of_birth'].value;
      // this.userType = (<HTMLInputElement>document.getElementById('userType')).value;
      const formatedDate = moment(dateFormate).format('YYYY-MM-DD');
      this.signupFormData.controls['date_of_birth'].setValue(formatedDate);
      this._authService
        .signUp(this.signupFormData.value, this.userType)
        .subscribe(
          (data) => {
            this.loading = false;
            this.loadingSignupFormSubmission = false;
            this.verifyEmailOtpFormData.email_or_phone_value = this.signupFormData.controls[
              'email'
            ].value;
            this.verifyPhoneOtpFormData.email = this.signupFormData.controls[
              'email'
            ].value;
            this.verifyPhoneOtpFormData.email_or_phone_value = this.signupFormData.controls[
              'phone'
            ].value;
            this.formSubmitted = true;
            this.notificationService.success('User Registered Successfully!!', 'Med.Bot');
          },
          (error) => {
            console.log(error);
            this.loadingSignupFormSubmission = false;
            this.loading = false;
            const err = error['error']['error_details']['validation_errors'];
            if (err) {
              this.formValidationError = true;
              const email = err['email'];
              const phone = err['phone'];
              const gender=err['gender']
              if (email && phone) {
                const emailError = 'Email : ' + err['email'][0];
                const phoneError = 'Phone : ' + err['phone'][0];
                this.notificationService.error(
                  `${emailError} ${phoneError}`,
                  'Med.Bot'
                );
                this.errorValue.push(
                  { value: `${emailError}` },
                  { value: `${phoneError}` }
                );
              } else if (email) {
                const emailError = 'Email : ' + err['email'][0];
                this.errorValue.push({ value: `${emailError}` });
                this.notificationService.error(`${emailError}`, 'Med.Bot');
              } else if (phone) {
                const phoneError = 'Phone : ' + err['phone'][0];
                this.errorValue.push({ value: `${phoneError}` });
                this.notificationService.error(`${phoneError}`, 'Med.Bot');
              } else if(gender) {
                const genderError = 'gender : ' + 'choose a valid choice.';
                this.errorValue.push({ value: `${genderError}` });
                this.notificationService.error(`${genderError}`, 'Med.Bot');
              }else{
                this.notificationService.error(
                  `${error['statusText']}`,
                  'Med.Bot'
                );
              }
            } else {
              this.notificationService.error(
                `${error['statusText']}`,
                'Med.Bot'
              );
            }
          }
        );
    } else {
      this.notificationService.error('Sorry invalid user', 'Med.Bot');
    }
  }

  onSubmitEmailOtp() {
    this.loading = true;
    this.loadingVerifyEmailOtpFormSubmission = true;
    this.emailOtpVerified=true;
    this._authService.postVerifyOTP(this.verifyEmailOtpFormData).subscribe(
      (data) => {
        this.loading = false;
        this.loadingVerifyEmailOtpFormSubmission = true;
        this.emailOtpVerified= false;
        this.notificationService.success('Email Verified', 'Med.Bot');
        this.emailVerified = true;
      },
      (error) => {
        this.loading = false;
        this.loadingVerifyEmailOtpFormSubmission = false;
        console.log(error);
        const status = error['status'];
        if(status == 400 || status==417){
          this.notificationService.error(`${error['error']['error_message']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  onSubmitPhoneOtp() {
    this.loading = true;
    this.loadingVerifyPhoneOtpFormSubmission = true;
    this.phoneOtpVerified= true;
    this._authService.postVerifyOTP(this.verifyPhoneOtpFormData).subscribe(
      (data) => {
        this.loading = false;
        this.phoneVerified = true;
        this.phoneOtpVerified= false;
        this.notificationService.success('Phone Verified', 'Med.Bot');
        setInterval(() => {
          location.reload();
        },1500);
      },
      (error) => {
        this.loading = false;
        this.loadingVerifyPhoneOtpFormSubmission = false;
        const status = error['status'];
        if(status == 400 || status==417){
          this.notificationService.error(`${error['error']['error_message']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  noWhitespaceValidator(control: FormControl) {
    const isWhitespace = (control.value || '').trim().length === 0;
    const isValid = !isWhitespace;
    return isValid ? null : { 'whitespace': true };
}

getUserType(userType){
  if(userType ==='Doctor'){
    this.maxDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate() - 7665);
    this.userType = "Doctor"
    this.doctorChecked = true;
    this.patientChecked = false;
    console.log(userType)
  }else{
    this.maxDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate() -1);
    this.userType = "Patient"
    this.doctorChecked = false;
    this.patientChecked = true;
    console.log(userType)
  }
}
nameValidation(data){
  const name =this.signupFormData.controls['name'].valid;
  if(name){
    this.showNameValidationError= false;
  }else{
    this.showNameValidationError= true;
  }

}
emailValidation(data){
  const email =this.signupFormData.controls['email'].valid;
  if(email){
    this.showEmailValidationError= false;
  }else{
    this.showEmailValidationError= true;
  }

}
phoneValidation(data){
  const phone =this.signupFormData.controls['phone'].valid;
  if(phone){
    this.showPhoneValidationError= false;
  }else{
    this.showPhoneValidationError= true;
  }

}
passwordValidation(data){
  const password =this.signupFormData.controls['password'].valid;
  if(password){
    this.showPasswordValidationError= false;
  }else{
    this.showPasswordValidationError= true;
  }

}
redirectToDashboard() {

  const userType = localStorage.getItem('user_type');
  if (userType == 'Doctor') {
    this.doctorService.getDoctorProfile().subscribe(
      (data) => {
        const doctorApproved = data['is_approved'];
        const status = data['approval_request_status'];
        localStorage.setItem(
          'profile_approved_status',
          data['approval_request_status']
        );
        this.practice = localStorage.getItem('practice');
        this.bank = localStorage.getItem('bank');

        localStorage.setItem('profile_approval_status', doctorApproved);
        if (doctorApproved && status === 'Approved') {
          if (this.practice === 'true' && this.bank === 'true'){
            this._router.navigate(['/doctor/dashboard']);
            this._router.events.subscribe((val) => {
              const nvigationEnd=val instanceof NavigationEnd;
              if(!!nvigationEnd){
                location.reload();
              }
              });
          }else {
            this._router.navigate(['/doctor/practice-locations'])
            if (this.practice === 'false'){
              this.notificationService.warning('Please create your schedule', 'Med.Bot');
            }
          }  
          } else {
          this._router.navigate(['/doctor/profile']);
        }
        this.loading=false;
      },
      (error) => {
        this.loading=false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
        this._router.navigate(['/doctor/profile']);
      }
    );
    // this.router.navigate(['/doctor/dashboard']);
  }else
  if (userType == 'Patient') {
    this._router.navigate(['/patient/dashboard']);
  }else
  if (userType == 'PlatformAdmin') {
    this._router.navigate(['/platform-admin/dashboard']);
  }else{
    this.loading=false;
  }
}
confrimation(){
  if(this.userType == 'Doctor' || this.userType == 'Patient'){
    $('#signupModal').modal('show');
  }
  else{
    this.notificationService.error(`Please Select User Type`,'Med.Bot');
  }
}
showSignUpForm(){
  this.signupFormData.reset();
  this.formSubmitted= false;
  window.scroll({
    top: 0,
    behavior: 'smooth'
  })
}

passwordhideshow(){
  var x = (<HTMLInputElement>document.getElementById("password")).type;
  console.log(x);
  if (x === "password") {
    (<HTMLInputElement>document.getElementById("password")).type="text"; 
  } else {
    (<HTMLInputElement>document.getElementById("password")).type="password"; 
  }
}


}
