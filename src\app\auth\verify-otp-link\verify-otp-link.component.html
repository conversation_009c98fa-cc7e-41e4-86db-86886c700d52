<div class="loading" *ngIf="loading">
    <app-loading-spinner></app-loading-spinner>
</div>
<div *ngIf="!loading" >
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-12 mt-3">

                <!-- Account Content -->
                <div class="account-content">
                    <div class="row align-items-center justify-content-center">
                      <div class=" col-lg-7 ">
                        <h2 class="connect" >Connect with the best healthcare professionals and manage your own digital health account</h2>

                          <img id="white-medbot" src="../../../assets/img/Medbot logo_white_text only_transparent background.png" class="img-fluid" alt="Doccure Register">
                      </div>
                        <div class=" col-lg-4 ">
                            <div class="login-header">
                                <h3 translate class="text-color">OTP Verification</h3>
                                <p *ngIf="!phoneVerified" class="small text-muted" translate>Your Phone verification is pending, please check your sms for verification codes. If you do not receive the codes click the resend links. </p>
                                <p *ngIf="phoneVerified" class="small text-muted" translate>Your Phone verification is completed, click here to <a routerLink="/login" routerLinkActive="active">login</a></p>
                                <!-- <p *ngIf="!emailVerified || !phoneVerified" class="small text-muted" translate>Your Email/Phone verification is pending, please check your mail/sms for verification codes. If you do not receive the codes click the resend links. </p>
                                <p *ngIf="emailVerified && phoneVerified" class="small text-muted" translate>Your Email/Phone verification is completed, click here to <a routerLink="/login" routerLinkActive="active">login</a></p> -->
                            </div>

                            <!-- Verify Email OTP Form -->
                            <!-- <form #_verifyEmailOtpFormData="ngForm" (submit)="onSubmitEmailOtp()">
                                <fieldset [disabled]="loadingVerifyEmailOtpFormSubmission">
                                    <div class="form-group form-focus mt-1">
                                        <div class="form-group form-focus">
                                            <input [readonly]="true" type="text" class="form-control floating" name="email_or_phone_value" autocomplete="name" required [(ngModel)]="verifyEmailOtpFormData.email_or_phone_value" #_email_or_phone_value="ngModel" >
                                            <label class="focus-label float" translate>Email</label>
                                        </div>
                                    </div>
                                    <div *ngIf="!emailVerified" class="form-group form-focus">
                                        <input type="password" class="form-control floating" name="value" required [(ngModel)]="verifyEmailOtpFormData.value" #_value="ngModel" maxlength="35" >
                                        <label class="focus-label float" translate>Email OTP</label>
                                        <!--p (click)="resendEmailOTP()" class="reset-link">Resend Email OTP?</p-->
                                    <!-- </div>
                                    <button #emailButton class="btn btn-signUp btn-block btn-lg login-btn mb-4" type="submit" [disabled]="emailVerified || _verifyEmailOtpFormData.invalid || loadingVerifyEmailOtpFormSubmission ">
                        {{ loadingVerifyEmailOtpFormSubmission ? emailOtpVerified?'&nbsp;&nbsp; Verifying ... &nbsp;&nbsp;' :'&nbsp;&nbsp; Email Verified  &nbsp;&nbsp;' : '&nbsp;&nbsp; Verify Email &nbsp;&nbsp;' |translate}}
                    </button>
                                </fieldset>
                            </form> -->
                            <!-- /Verify Email OTP Form -->
                            <br>
                            <br>
                            <!-- Verify Phone OTP Form -->
                            <form #_verifyPhoneOtpFormData="ngForm" (submit)="onSubmitPhoneOtp()">
                                <fieldset [disabled]="loadingVerifyPhoneOtpFormSubmission">
                                    <div class="form-group form-focus mt-1">
                                        <div class="form-group form-focus">
                                            <input [readonly]="true" type="text" class="form-control floating" name="email_or_phone_value" autocomplete="name" required [(ngModel)]="verifyPhoneOtpFormData.email_or_phone_value" #_email_or_phone_value="ngModel">
                                            <label class="focus-label float" translate>Phone</label>
                                        </div>
                                    </div>
                                    <div *ngIf="!phoneVerified" class="form-group form-focus">
                                        <input type="password" [readonly]="phoneVerified" class="form-control floating" name="value" required [(ngModel)]="verifyPhoneOtpFormData.value" #_value="ngModel" maxlength="6" >
                                        <!--p (click)="resendPhoneOTP()" class="reset-link">Resend Phone OTP?</p-->
                                        <label class="focus-label float" translate>Phone OTP</label>
                                    </div>
                                    <button #phoneButton class="btn btn-signUp btn-block btn-lg login-btn mb-5" type="submit" [disabled]="phoneVerified || _verifyPhoneOtpFormData.invalid || loadingVerifyPhoneOtpFormSubmission">
                        {{ loadingVerifyPhoneOtpFormSubmission ? phoneOtpVerified ? '&nbsp;&nbsp; loading... &nbsp;&nbsp;': '&nbsp;&nbsp; Phone Verified  &nbsp;&nbsp;' : '&nbsp;&nbsp; Verify Phone &nbsp;&nbsp;'|translate}}
                    </button>
                                </fieldset>
                            </form>
                            <!-- /Verify Phone OTP Form -->
                        </div>
                        <div class="col-lg-1"></div>
                    </div>
                </div>
                <!-- /Account Content -->

            </div>
        </div>

    </div>

</div>
