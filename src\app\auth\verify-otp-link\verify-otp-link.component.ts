import { SharedService } from './../../shared/shared.service';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-verify-otp-link',
  templateUrl: './verify-otp-link.component.html',
  styleUrls: ['./verify-otp-link.component.css'],
})
export class VerifyOtpLinkComponent implements OnInit {
  @ViewChild('emailButton') emailButton: ElementRef;
  @ViewChild('phoneButton') phoneButton: ElementRef;
  @ViewChild('signupButton') signupButton: ElementRef;

  public emailVerified = false;
  public phoneVerified = false;
  public loadingVerifyEmailOtpFormSubmission = false;
  public loadingVerifyPhoneOtpFormSubmission = false;
  public phoneOtpVerified = false;
  public emailOtpVerified = false;
  public email = null;
  public phoneNumber = null;
  public loading = true;
  public verifyOtpFormData = {
    uidb64: 'Email',
    token: null,
    otp_type: null,
  };

  public verifyEmailOtpFormData = {
    type: 'Email',
    value: null,
    email_or_phone_value: null,
  };

  public verifyPhoneOtpFormData = {
    type: 'Phone',
    value: null,
    email_or_phone_value: null,
    email:null
  };
  public userType = null;

  public signupFormData = {
    name: null,
    email: null,
    phone: null,
    password: null,
    terms: null,
  };
  public emailOtpResendForm = {
    type: 'Email',
    value: null,
    email: null,
    password: null,
  };

  public phoneOtpResendForm = {
    type: 'Phone',
    value: null,
    email: null,
    password: null,
  };
  public loadingSignupFormSubmission = false;

  public showAlertMessage = false;
  constructor(
    private _verifyOtpService: SharedService,
    private _router: Router,
    private _route: ActivatedRoute,
    private notificationService: ToastrService,
    private translate: TranslateService
  ) {}

  ngOnInit(): void {

      document.body.style.overflowY = 'auto';
      document.body.style.background='#77C1F9';

    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this._route.queryParams.subscribe((params) => {
      this.verifyOtpFormData.uidb64 = params['uidb64'];
      this.verifyOtpFormData.token = params['token'];
      this.verifyOtpFormData.otp_type = params['otp_type'];
      this.check_link();
    });
  }
  ngOnDestroy(){
    document.body.style.overflowY = 'auto';
    document.body.style.background='#ffffff';
  }
  check_link() {
    console.log(
      this.verifyOtpFormData.uidb64,
      this.verifyOtpFormData.token,
      this.verifyOtpFormData.otp_type
    );
    this._verifyOtpService
      .get(
        this.verifyOtpFormData.uidb64,
        this.verifyOtpFormData.token,
        this.verifyOtpFormData.otp_type
      )
      .subscribe(
        (data) => {
          console.log(data);
          this.loading = false;
          this.verifyEmailOtpFormData.email_or_phone_value = data['email'];
          this.verifyPhoneOtpFormData.email = data['email'];
          this.verifyPhoneOtpFormData.email_or_phone_value = data['phone'];
          if (
            data['email_verified'] == true &&
            data['phone_verified'] == true
          ) {
            this.emailVerified = true;
            this.phoneVerified = true;
            localStorage.clear();
            this.notificationService.success('Verification Completed', 'Med.Bot');
            this._router.navigate(['/login']);
          } else if (data['email_verified'] == true) {
            this.notificationService.success('Email Verified', 'Med.Bot');
            this.loadingVerifyEmailOtpFormSubmission = true;
            this.emailOtpVerified = false;
            this.emailVerified = true;


          } else if (data['phone_verified'] == true) {
            this.loadingVerifyPhoneOtpFormSubmission = true;
            this.notificationService.success('Phone Verified', 'Med.Bot');
            this.phoneOtpVerified = false;
            this.phoneVerified = true;
          }
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(
              `${error['error']['error_message']}`,
              'Med.Bot'
            );
          } else {
            this.notificationService.error(
              `${error['statusText']}`,
              'Med.Bot'
            );
          }
          this._router.navigate(['/login']);
          this.loading = false;
          console.log(error);

        }
      );
  }

  onSubmitEmailOtp() {
    this.loading = true;
    this.loadingVerifyEmailOtpFormSubmission = true;
    this.emailOtpVerified = true;
    this._verifyOtpService.post(this.verifyEmailOtpFormData).subscribe(
      (data) => {
        this.loading = false;
        this.loadingVerifyEmailOtpFormSubmission = true;
        this.emailOtpVerified = false;
        this.notificationService.success('Email verified', 'Med.Bot');
        this.emailVerified = true;
      },
      (error) => {
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['error_message']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
        this.loading = false;
        this.loadingVerifyEmailOtpFormSubmission = false;

      }
    );
  }

  onSubmitPhoneOtp() {
    this.loading = true;
    this.loadingVerifyPhoneOtpFormSubmission = true;
    this.phoneOtpVerified = true;

    this._verifyOtpService.post(this.verifyPhoneOtpFormData).subscribe(
      (data) => {
        this.loading = false;
        this.loadingVerifyPhoneOtpFormSubmission = true;
        this.phoneOtpVerified = false;
        this.phoneVerified = true;
        this.notificationService.success('Phone Number Verified', 'Med.Bot');
      },
      (error) => {
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
        this.loadingVerifyPhoneOtpFormSubmission = false;

      }
    );
  }
}
