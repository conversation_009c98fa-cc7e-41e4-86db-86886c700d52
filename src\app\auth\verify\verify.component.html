
  <div class="container-fluid">

      <div class="row">
          <div class="col-md-12 ">

              <!-- Account Content -->
              <div class="account-content">
                  <div class="row align-items-center justify-content-center">
                    <div class=" col-lg-7 ">
                      <h2 class="connect">Connect with the best healthcare professionals and manage your own digital health account</h2>

                        <img id="white-medbot" src="../../../assets/img/Medbot logo_white_text only_transparent background.png" class="img-fluid" alt="Doccure Register">
                    </div>
                      <div class="col-lg-4">
                          <div class="login-header">
                              <h3>OTP Verification</h3>
                              <p *ngIf="!emailVerified || !phoneVerified" class="small text-muted">Your Email/Phone verification is pending, please check your mail/sms for verification codes. If you do not receive the codes click the resend links. </p>
                              <p *ngIf="emailVerified && phoneVerified" class="small text-muted">Your Email/Phone verification is completed, click here to <a routerLink="/login" routerLinkActive="active">login</a></p>
                          </div>

                          <!-- Verify Email OTP Form -->
                          <!-- <form #_verifyEmailOtpFormData="ngForm" (submit)="onSubmitEmailOtp()">
                              <fieldset [disabled]="loadingVerifyEmailOtpFormSubmission">
                                  <div class="form-group form-focus">
                                      <div class="form-group form-focus">
                                          <input [readonly]="true" type="text" class="form-control floating input-field-border " name="email_or_phone_value" autocomplete="name" required [(ngModel)]="verifyEmailOtpFormData.email_or_phone_value" #_email_or_phone_value="ngModel" >
                                          <label class="focus-label float">Email</label>
                                      </div>
                                  </div>
                                  <div *ngIf="!emailVerified" class="form-group form-focus">
                                      <input type="password" id="verify-data-one" class="form-control floating input-field-border " name="value" required [(ngModel)]="verifyEmailOtpFormData.value" #_value="ngModel" >
                                      <label class="focus-label float">Email OTP</label>
                                      <p (click)="resendEmailOTP()" class="reset-link mt-2 mb-2">Resend Email OTP?</p>
                                  </div>
                                  <button #emailButton id="email-ver-btn" class="btn btn-signUp btn-block btn-lg verify-btn mt-5" type="submit" [disabled]="emailVerified || _verifyEmailOtpFormData.invalid || loadingVerifyEmailOtpFormSubmission ">
            {{ loadingVerifyEmailOtpFormSubmission ? '&nbsp;&nbsp; Verifying ... &nbsp;&nbsp;' : '&nbsp;&nbsp; Verify Email &nbsp;&nbsp;'}}
          </button>
                              </fieldset>
                          </form> -->
                          <!-- /Verify Email OTP Form -->
                          <br>
                          <br>
                          <!-- Verify Phone OTP Form -->
                          <form #_verifyPhoneOtpFormData="ngForm" (submit)="onSubmitPhoneOtp()">
                              <fieldset [disabled]="loadingVerifyPhoneOtpFormSubmission">
                                  <div class="form-group form-focus">
                                      <div class="form-group form-focus">
                                          <input [readonly]="true" type="text" class="form-control floating input-field-border " name="email_or_phone_value" autocomplete="name" required [(ngModel)]="verifyPhoneOtpFormData.email_or_phone_value" #_email_or_phone_value="ngModel">
                                          <label class="focus-label float">Phone</label>
                                      </div>
                                  </div>
                                  <div *ngIf="!phoneVerified" class="form-group form-focus">
                                      <input type="password" id="verify-data-two" [readonly]="phoneVerified" class="form-control input-field-border  floating" name="value" required [(ngModel)]="verifyPhoneOtpFormData.value" #_value="ngModel">
                                      <p (click)="resendPhoneOTP()" class="reset-link mt-2 mb-2">Resend Phone OTP?</p>
                                      <label class="focus-label float">Phone OTP</label>
                                  </div>
                                  <button #phoneButton id="phone-ver-btn" class="btn btn-signUp btn-block btn-lg verify-btn mb-4 mt-5" type="submit" [disabled]="phoneVerified || _verifyPhoneOtpFormData.invalid || loadingVerifyPhoneOtpFormSubmission">
            {{ loadingVerifyPhoneOtpFormSubmission ? '&nbsp;&nbsp; Loading ... &nbsp;&nbsp;' : '&nbsp;&nbsp; Verify Phone &nbsp;&nbsp;'}}
          </button>
                              </fieldset>
                          </form>
                          <!-- /Verify Phone OTP Form -->
                      </div>
                      <div class="col-lg-1"></div>
                  </div>
              </div>
              <!-- /Account Content -->

          </div>
      </div>

  </div>


