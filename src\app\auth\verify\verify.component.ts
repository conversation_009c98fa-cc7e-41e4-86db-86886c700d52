import { ToastrService } from 'ngx-toastr';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { AuthService } from '../auth.service';
import { Router,ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-verify',
  templateUrl: './verify.component.html',
  styleUrls: ['./verify.component.css']
})
export class VerifyComponent implements OnInit {

  @ViewChild('emailButton') emailButton: ElementRef;
  @ViewChild('phoneButton') phoneButton: ElementRef;
  public verified = true;
  public loading = true;
  public emailVerified = false;
  public phoneVerified = false;
  public email = null;
  public phoneNumber = null;
  public loadingVerifyEmailOtpFormSubmission = false;
  public loadingVerifyPhoneOtpFormSubmission = false;

  public verifyEmailOtpFormData = {
    type: 'Email',
    value: null,
    email_or_phone_value: null,
  };

  public verifyPhoneOtpFormData = {
    type: 'Phone',
    value: null,
    email_or_phone_value: null,
    email:null
  };

  public loginFormData = {
    email: null,
    password: null,
  };

  public emailOtpResendForm = {
    type: 'Email',
    value: null,
    email: null,
    password: null,
  };

  public phoneOtpResendForm = {
    type: 'Phone',
    value: null,
    email: null,
    password: null,
    phone:null
  };
  public languageList = [
    { id: 'en', value: 'English' },
    { id: 'fr', value: 'French' },
    { id: 'ta', value: 'Tamil' },
  ];
  public loadingLoginFormSubmission = false;
  currentLanguage = '';
  public showAlertMessage = false;
  constructor(
    private authService: AuthService,
    private router: Router,
    private notifyService: ToastrService,
    public translate: TranslateService,
    private activatedRoute:ActivatedRoute
  ) {}

  ngOnInit(): void {

      document.body.style.overflowY = 'auto';
      document.body.style.background='#77C1F9';

    this.activatedRoute.params.subscribe(parms => {
      this.verifyEmailOtpFormData.email_or_phone_value = parms['email'];
      this.verifyPhoneOtpFormData.email=parms['email'];
      this.verifyPhoneOtpFormData.email_or_phone_value = parms['phone'];

      this.email= parms['email'];
      this.phoneNumber=parms['phone'];
      this.loginFormData.password=parms['password'];

    })
    this.notifyService.warning(
      'Verification Pending',
      'Med.Bot'
    );
    if (this.emailVerified) {
      setTimeout(
        () =>
          (this.emailButton.nativeElement.innerHTML =
            '&nbsp;&nbsp; Email Verified &nbsp;&nbsp'),
        120
      );
    }
    if (this.phoneVerified) {
      setTimeout(
        () =>
          (this.phoneButton.nativeElement.innerHTML =
            '&nbsp;&nbsp; Phone Verified &nbsp;&nbsp'),
        120
      );
    }
  }

  ngOnDestroy(){
    document.body.style.overflowY = 'auto';
    document.body.style.background='#ffffff';
  }
  onSubmitEmailOtp() {
    this.loading = true;
    this.loadingVerifyEmailOtpFormSubmission = true;
    this.authService.postVerifyOTP(this.verifyEmailOtpFormData).subscribe(
      (data) => {
        this.loading = false;
        this.loadingVerifyEmailOtpFormSubmission = false;
        this.emailVerified = true;
        this.emailButton.nativeElement.innerHTML =
          '&nbsp;&nbsp; Email Verified &nbsp;&nbsp';

        this.notifyService.success(
          'Email Verified Successfully',
          'Med.Bot'
        );
      },
      (error) => {
        this.loading = false;
        this.loadingVerifyEmailOtpFormSubmission = false;
        this.notifyService.error('Invalid Verification Code', 'Med.Bot');
      }
    );
    if(this.emailVerified && this.phoneVerified){
      this.redirectToDashboard();
    }
  }

  onSubmitPhoneOtp() {
    this.loading = true;
    this.loadingVerifyPhoneOtpFormSubmission = true;
    this.authService.postVerifyOTP(this.verifyPhoneOtpFormData).subscribe(
      (data) => {
        this.loading = false;
        this.phoneButton.nativeElement.innerHTML =
          '&nbsp;&nbsp; Phone Verified &nbsp;&nbsp';
        this.loadingVerifyPhoneOtpFormSubmission = false;
        this.phoneVerified = true;
        this.notifyService.success(
          'Phone Verified Successfully',
          'Med.Bot'
        );
      },
      (error) => {
        this.loading = false;
        this.loadingVerifyPhoneOtpFormSubmission = false;
        this.notifyService.error('Invalid Verification Code', 'Med.Bot');
      }
    );
    if(this.emailVerified && this.phoneVerified){
      this.redirectToDashboard();
    }
  }

  resendEmailOTP() {
    this.loading = true;
    this.emailOtpResendForm.email = this.email;
    this.emailOtpResendForm.value = this.email;
    this.emailOtpResendForm.password = this.loginFormData.password;
    this.authService.sendEmailOtp(this.emailOtpResendForm).subscribe(
      (data) => {
        this.loading = false;
        this.notifyService.success(
          'Email verification code sent',
          'Med.Bot'
        );
      },
      (error) => {
        this.loading = false;
        console.log(error);
        this.notifyService.error("Sorry Email not sent");
      }
    );
  }

  resendPhoneOTP() {
    this.loading = true;
    this.phoneOtpResendForm.email = this.email;
    this.phoneOtpResendForm.value = this.phoneNumber;
    this.phoneOtpResendForm.phone = this.phoneNumber;
    this.phoneOtpResendForm.password = this.loginFormData.password;
    this.authService.sendPhoneOtp(this.phoneOtpResendForm).subscribe(
      (data) => {
        this.loading = false;
        this.notifyService.success(
          'Phone verification code sent',
          'Med.Bot'
        );
      },
      (error) => {
        this.loading =false;
        console.log(error);
        this.notifyService.error("Sorry Message not sent");
      }
    );
  }
  redirectToDashboard(){
    const user_type = localStorage.getItem('user_type');
    if(user_type == 'Doctor'){
      this.router.navigate(['/doctor/dashboard']);
    }
    if(user_type == 'Patient'){
      this.router.navigate(['/patient/dashboard']);
    }
    if(user_type == 'PlatformAdmin'){
      this.router.navigate(['/platform-admin/dashboard']);
    }
  }
}
