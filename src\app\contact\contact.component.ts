import { Component, OnInit } from '@angular/core';
import * as Settings from './../config/settings';
@Component({
  selector: 'app-contact',
  templateUrl: './contact.component.html',
  styleUrls: ['./contact.component.css']
})
export class ContactComponent implements OnInit {
  supportNumber = Settings.supportNumber;

  constructor() { }

  ngOnInit(): void {
    document.body.style.overflowY = 'auto';
    document.body.style.background='#77C1F9';
  }
  ngOnDestroy(){
    document.body.style.overflowY = 'auto';
    document.body.style.background='#ffffff';
  }

}
