/* .appt-time {
    text-align: center;
} */

.modal-img {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 30%;
}

#no-data,
#no-msg-data {
  margin: 5px;
  text-align: center;
}

.ms {
  display: inline;
}

.ms-btn {
  float: right;
}

.ms-tab {
  margin-top: 25px;
}

.db-ch-title {
  margin-top: 15px;
}

.img-fluid {
  cursor: pointer;
}

hr {
  margin-left: -20px;
  margin-right: -20px;
}

.fas {
  color: #20c0f3;
  cursor: pointer;
}

.app-btn {
  margin: 5px;
}

#add-notes {
  color: #20c0f3;
  cursor: pointer;
}

#practice-location-ms {
  margin-bottom: 5px;
  width: 20%;
}

textarea {
  width: 100%;
}

.loc-label {
  float: right;
  margin: 5px;
}

#confirmTitle {
  text-align: center;
}

.btn-md.mx-3 {
  width: 145px;
}

.other-activities {
  margin-top: 25px;
}

.h-btn {
  height: 60px;
  margin-bottom: 10px;
}

.chk {
  float: right;
}

.hd-inl {
  display: inline;
}

.hd-inl.txt {
  margin-left: 10px;
  margin-bottom: 0px;
}

.chk {
  display: inline;
}

.chk-bx {
  float: left;
  margin-right: 10px;
  margin-top: 4px;
}

th {
  text-align: center;
}

#avail-checkbox {
  margin-top: 5px;
}

.en-size {
  font-size: 1.5em;
}

.nm-size {
  font-size: 1em;
}

.sm-size {
  font-size: 0.8em;
}

.largerCheckbox {
  width: 20px;
  height: 20px;
}


/* tbody {
  pointer-events: none;
} */

.btn-msg {
  cursor: pointer !important;
}

.btn-flw-up {
  pointer-events: none;
}

.admin-det p {
  margin-bottom: 0px;
}

td.text-warning {
  cursor: pointer;
}