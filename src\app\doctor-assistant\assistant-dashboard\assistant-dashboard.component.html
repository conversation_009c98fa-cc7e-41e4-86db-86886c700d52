<!-- Page Content -->
<!-- <div class="content"> -->
<!-- <div class="container-fluid"> -->
<div class="row">
  <div class="col-md-12">

      <div class="col-md-3 co-lg-3 mx-1" >
        <div class="form-group">
          <h4 translate>Doctor List</h4>
          <form [formGroup]="doctorForm">
            <select
            class="form-control select"
            name="doctor"
            id="doctor"
            formControlName="doctorId"
            (ngModelChange)="getSelectedDoctorDatails($event)"
          >
            <option
              *ngFor="let doctor of associatedDotorList"
              [value]="doctor.uuid"
            >
              {{ doctor.user.username }}
            </option>
          </select>
          </form>

        </div>

    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="card dash-card">
      <div>
        <div class="row">
          <div class="col-md-12 col-lg-4" style="margin-left: 18%">
            <div class="dash-widget dct-border-rht">
              <div class="circle-bar circle-bar2">
                <div class="circle-graph2" data-percent="100">
                  <img
                    id="ds-img-1"
                    src="assets/img/appointments-today.png"
                    (click)="openAppointmentsTab()"
                    class="img-fluid"
                    alt="Patient"
                  />
                </div>
              </div>
              <div class="dash-widget-info text-success">
                <h6 id="appt-today-title" class="text-success en-size">
                  Appointments Today
                </h6>
                <h3 id="appt-today-cnt" class="text-center">
                  {{ appointmentsToday.length }}
                </h3>
              </div>
            </div>
          </div>
          <div class="col-md-12 col-lg-4 text-center">
            <div class="dash-widget">
              <div class="circle-bar circle-bar1">
                <div class="circle-graph1" data-percent="100">
                  <img
                    id="ds-img-2"
                    src="assets/img/icon-01.png"
                    (click)="openAppointmentsTab()"
                    class="img-fluid"
                    alt="patient"
                  />
                </div>
              </div>
              <div class="dash-widget-info">
                <h6 id="appt-total-title" class="text-success en-size">
                  Total Appointments
                </h6>
                <h3 id="appt-total-cnt">{{ appointmentsCount }}</h3>
              </div>
            </div>
          </div>
          <!-- <div class="col-md-12 col-lg-4">
                              <div class="dash-widget">
                                  <div class="circle-bar circle-bar3">
                                      <div class="circle-graph3" data-percent="50">
                                          <img src="assets/img/icon-03.png" class="img-fluid" alt="Patient">
                                      </div>
                                  </div>
                                  <div class="dash-widget-info">
                                      <h6>Appoinments</h6>
                                      <h3>85</h3>
                                      <p class="text-muted">06, Apr 2019</p>
                                  </div>
                              </div>
                          </div> -->
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row mb-5">
  <div class="col-md-12">
    <div>
      <h4 id="dash-activity-title" class="mb-4">Your Activities</h4>
      <!-- <button (click)="initiateVConsult()" id="dash-video-cnslt" class="btn btn-info btn-lg mx-3 h-btn"><i class="fas fa-desktop"></i>  Initiate Video Consult</button> -->
      <button
        id="dash-ms"
        (click)="manageScheduleFn()"
        class="btn btn-info btn-lg mx-3 h-btn"
      >
        <i class="far fa-clock"></i> Manage Schedules
      </button>
      <button
        id="dash-ma"
        (click)="manageAppointmentsFn()"
        class="btn btn-info btn-lg mx-3 h-btn"
      >
        <i class="far fa-calendar-check"></i> Manage Appointments
      </button>
      <button class="btn btn-info btn-lg mx-3 h-btn">
        <i class="fas fa-user-injured"></i> Patients Consulted Today - 0
      </button>
      <button class="btn btn-info btn-lg mx-3 h-btn">
        <i class="far fa-money-bill-alt"></i> Fees Collected Today - 0
      </button>
      <button class="btn btn-info btn-lg mx-3 h-btn">
        <i class="far fa-calendar-minus"></i> Reports
      </button>
      <button
        id="dash-rts" [disabled]="disabledDoctordetailsBtn"
        class="btn btn-info btn-lg mx-3 h-btn"
        (click)="doctorProfileView()"
      >
        <i class="far fa-user"></i>Doctor Profile Settings
      </button>
      <button
        id="dash-rtsh"
        class="btn btn-info btn-lg mx-3 h-btn" [disabled]="disabledDoctordetailsBtn"
        (click)="bankAccountRouting()"
      >
        <i class="far fa-hospital"></i> Bank Account
      </button>
      <button
        id="dash-rtsh"
        class="btn btn-info btn-lg mx-3 h-btn"
        (click)="praticeLocationNavigation()" [disabled]="disabledDoctordetailsBtn"
      >
        <i class="far fa-hospital"></i> Practice Location
      </button>
    </div>
  </div>
</div>
<div class="row mb-5">
  <div class="col-md-12">
    <div>
      <h4 id="dash-activity-title" class="mb-4">New Messages</h4>
    </div>
    <div class="card card-table mb-0">
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-hover table-center mb-0">
            <thead>
              <tr>
                <th class="text-left">
                  <h5>#</h5>
                </th>
                <th class="text-left">
                  <h5>Patient Name</h5>
                </th>
                <th class="text-left">
                  <h5>Message</h5>
                </th>
                <th class="text-left">
                  <h5>Action</h5>
                </th>

                <th class="text-left">Mark as Read</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let msg of messages; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ msg["sender_type"] }} {{ i + 1 }}</td>
                <td>******</td>
                <td>
                  <button class="btn btn-primary btn-sm btn-msg">
                    View Message
                  </button>
                </td>
                <td></td>
                <td></td>
              </tr>
            </tbody>
          </table>
          <p
            id="no-msg-data"
            class="nm-size"
            *ngIf="messages?.length == 0 || !messages"
          >
            No New Messages
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-12">
    <h4 id="dash-booked-appt-title" class="mb-4 hd-inl">Booked Appoinments</h4>
    <h5 class="chk hd-inl txt">I am available now for Instant Consultation</h5>
    <!-- <input type="checkbox" [(ngModel)]="this.available_now" [checked]="this.available_now" class="chk hd-inl" id="avail-checkbox" (click)="modifyAvailableNow($event)"> -->
    <div class="appointment-tab">
      <!-- Appointment Tab -->
      <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
        <li class="nav-item">
          <a
            id="dash-ba-today"
            class="nav-link active nm-size"
            href="#today-appointments"
            data-toggle="tab"
          >
            <h5>Today</h5>
          </a>
        </li>
        <li class="nav-item">
          <a
            id="dash-ba-upcomming"
            class="nav-link nm-size"
            href="#upcoming-appointments"
            data-toggle="tab"
          >
            <h5>Upcoming</h5>
          </a>
        </li>
      </ul>
      <!-- TODO NEED to add ID for the below elements-->
      <!-- /Appointment Tab -->
      <div class="tab-content">
        <!-- Today Appointment Tab -->
        <div class="tab-pane show active" id="today-appointments">
          <div class="card card-table mb-0">
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-hover table-center mb-0">
                  <thead>
                    <tr>
                      <th class="text-left">
                        <h5>Time</h5>
                      </th>
                      <th class="text-left">
                        <h5>Patient Name</h5>
                      </th>
                      <th class="text-left">
                        <h5>Contact No</h5>
                      </th>
                      <!-- <th>Status</th> -->
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let appt of appointmentsToday">
                      <!-- <td class="ml-4" style="font-size: 1.5em;"><input type="checkbox" (click)="selectAppointment(appt,$event)" class="chk chk-bx mt-2 largerCheckbox" style="size: 3em;">{{appt['appt_time']}}</td> -->
                      <td class="ml-4" style="font-size: 1.5em">
                        {{ appt["appt_time"] }}
                      </td>
                      <td class="ml-4">
                        <img
                          class="avatar avatar-sm mr-2 avatar-img rounded-circle text-center"
                          [src]="appt['patient_user_json']['profile_picture']"
                        />
                        <h2
                          class="table-avatar nm-size"
                          style="font-size: 1.5em"
                        >
                          {{ appt["patient_user_json"]["username"] }}
                        </h2>
                      </td>
                      <td class="ml-4" style="font-size: 1.3em">
                        {{ appt["patient_user_json"]["phone"] }}
                      </td>
                      <!-- <td>{{appt['status']}}</td> -->
                      <td class="text-left en-size">
                        <div class="table-action">
                          <a
                            href="javascript:void(0);"
                            (click)="
                              viewPatient(appt['patient_user_json']['uuid'])
                            "
                            class="btn app-btn btn-sm bg-info-light sm-size"
                            data-toggle="modal"
                            data-target="#patientModal"
                          >
                            <i class="far fa-eye"></i> View Patient
                          </a>
                          <a
                            href="javascript:void(0);"
                            (click)="onConsult(appt)"
                            class="btn app-btn btn-sm bg-success-light sm-size"
                          >
                            <i class="fa fa-notes-medical"></i> Consult
                          </a>
                          <a
                            href="javascript:void(0);"
                            (click)="onCaseHistory()"
                            class="btn app-btn btn-sm bg-warning-light sm-size"
                          >
                            <i class="fa fa-file"></i> Case History
                          </a>
                          <a
                            href="javascript:void(0);"
                            (click)="cancelAppointment(appt['uuid'], 'today')"
                            class="btn app-btn btn-sm bg-danger-light sm-size"
                            data-toggle="modal"
                            data-target="#confirmModal"
                          >
                            <i class="fas fa-times"></i> Cancel
                          </a>
                          <label
                            class="btn app-btn btn-sm bg-danger btn-flw-up sm-size"
                            disabled
                          >
                            <i class="far fa-arrow-alt-circle-right"></i> Follow
                            Up
                          </label>
                        </div>
                      </td>
                      <!-- <td class="text-left">
                                                      <div class="table-action">

                                                      </div>
                                                  </td> -->
                    </tr>
                  </tbody>
                </table>
                <p
                  id="no-data"
                  class="nm-size"
                  *ngIf="appointmentsToday.length == 0"
                >
                  No Appoinments Available
                </p>
              </div>
            </div>
          </div>
        </div>
        <!-- /Today  Appointment Tab -->
        <!-- Upcoming Appointment Tab -->
        <div class="tab-pane" id="upcoming-appointments">
          <div class="card card-table mb-0">
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-hover table-center mb-0">
                  <thead>
                    <tr>
                      <th>Date|Time</th>
                      <th>Patient Name</th>
                      <th>Contact No</th>
                      <!-- <th>Status</th> -->
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let appt of upcommingAppointments">
                      <td *ngIf="upcommingAppointments.length == 0">
                        No Appoinments Available
                      </td>
                      <td>
                        {{ appt["appt_date"]
                        }}<span class="d-block text-info">{{
                          appt["appt_time"]
                        }}</span>
                      </td>
                      <td>
                        <h2 class="table-avatar">
                          <img
                            class="avatar-img rounded-circle avatar avatar-sm mr-2"
                            [src]="appt['patient_user_json']['profile_picture']"
                          />
                          {{ appt["patient_user_json"]["username"] }}
                        </h2>
                      </td>
                      <td>{{ appt["patient_user_json"]["phone"] }}</td>
                      <!-- <td>{{appt['status']}}</td> -->
                      <td class="text-left">
                        <div class="table-action">
                          <a
                            href="javascript:void(0);"
                            (click)="
                              viewPatient(appt['patient_user_json']['uuid'])
                            "
                            class="btn btn-sm bg-info-light"
                            data-toggle="modal"
                            data-target="#patientModal"
                          >
                            <i class="far fa-eye"></i> View Patient
                          </a>
                          <a
                            href="javascript:void(0);"
                            (click)="onCaseHistory()"
                            class="btn app-btn btn-sm bg-warning-light"
                            data-toggle="modal"
                            data-target="#confirmModal"
                          >
                            <i class="fa fa-file"></i> Case History
                          </a>
                          <a
                            href="javascript:void(0);"
                            (click)="
                              cancelAppointment(appt['uuid'], 'upcomming')
                            "
                            class="btn app-btn btn-sm bg-danger-light"
                            data-toggle="modal"
                            data-target="#exampleModal"
                          >
                            <i class="fas fa-times"></i> Cancel
                          </a>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <p id="no-data" *ngIf="upcommingAppointments.length == 0">
                  No Appoinments Available
                </p>
              </div>
            </div>
          </div>
        </div>
        <!-- /Upcoming Appointment Tab -->
      </div>
      <!-- <div class="other-activities"> -->
      <!-- <h4 class="mb-4">For other activities, refer to speaker notes</h4> -->
      <!-- <button class="btn btn-info btn-lg mx-3 h-btn"><i class="fas fa-user-injured"></i>  Patients Consulted Today - 0</button>
                          <button class="btn btn-info btn-lg mx-3 h-btn"><i class="far fa-money-bill-alt"></i>  Fees Collected Today - 0</button>
                          <button class="btn btn-info btn-lg mx-3 h-btn"><i class="far fa-calendar-minus"></i>  Reports </button> -->
      <!-- <button (click)="editProfile()" class="btn btn-info btn-lg mx-3 h-btn"><i class="far fa-user-circle"></i>  Edit Profile</button> -->
      <!-- </div> -->
      <div class="modal" id="patientModal">
        <div class="modal-dialog">
          <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
              <h4 class="modal-title">{{ patientDetail["username"] }}</h4>
              <button type="button" class="close" data-dismiss="modal">
                &times;
              </button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
              <img
                class="avatar-img modal-img rounded-circle"
                [src]="patientDetail['profile_picture']"
              />
              <p>
                Email:
                <a href="javascript:void(0);">{{ patientDetail["email"] }}</a>
              </p>
              <p>Contact No: {{ patientDetail["phone"] }}</p>
              <p>Age : {{ patientDetail["age"] }}</p>
              <p>Gender : {{ patientDetail["gender"] }}</p>
              <p>Pre Consultation Notes : General Checkup - Fever</p>
              <p *ngIf="!notes" (click)="showNotes()" id="add-notes">
                <i class="fa fa-plus-circle"></i>Add Notes
              </p>
              <textarea *ngIf="notes" placeholder="Notes for Doctor"></textarea>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer text-center">
              <button type="button" class="btn btn-danger" data-dismiss="modal">
                Close
              </button>
              <button
                *ngIf="notes"
                type="button"
                class="btn btn-primary"
                data-dismiss="modal"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- Modal -->
      <div
        class="modal fade"
        id="confirmModal"
        tabindex="-1"
        role="dialog"
        #confirmModal
      >
        <div class="modal-dialog" role="document">
          <div class="modal-content">
            <div class="modal-header justify-content-center">
              <h5 class="modal-title" id="confirmTitle">
                Are You Sure To Cancel the Appointment?
              </h5>
              <!-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                              </button> -->
            </div>
            <div class="modal-body">
              <p>Patient-Name: {{ apptDetails["username"] }}</p>
              <p>Appoinment Date: {{ apptDetails["date"] }}</p>
              <p>Appoinment Time: {{ apptDetails["time"] }}</p>
            </div>
            <div class="modal-footer text-center">
              <button
                type="button"
                id="close-modal"
                #closemodal
                class="btn btn-secondary"
                data-dismiss="modal"
              >
                Close
              </button>
              <button
                type="button"
                (click)="confirmCancel(apptDetails['uuid'])"
                class="btn btn-primary"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!--Hospital List-->
<div class="row mb-5">
  <div class="col-md-12">
    <div>
      <h4 id="hsptl-list" class="mb-4">Associated Hospitals List</h4>
    </div>
    <div class="card card-table mb-0">
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-hover table-center mb-0">
            <thead>
              <tr>
                <th class="text-left">
                  <h5>#</h5>
                </th>
                <th class="text-left">
                  <h5>Hospital Name</h5>
                </th>
                <th class="text-left">
                  <h5>Registrations</h5>
                </th>
                <th class="text-left">
                  <h5>Admin Details</h5>
                </th>

                <th class="text-left">
                  <h5>Status</h5>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let hsp of hospitalAssReq; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ hsp["hospital_data"]?.name }}</td>
                <td>{{ hsp["hospital_data"]?.med_hospital_id }}</td>
                <td>
                  <div class="admin-det">
                    <p>
                      <b>Name : </b
                      >{{ hsp["hospital_data"]?.contact_person_name }}
                    </p>
                    <p><b>Email : </b>{{ hsp["hospital_data"]?.email }}</p>
                    <p>
                      <b>Phone : </b>{{ hsp["hospital_data"]?.phone_numbers }}
                    </p>
                  </div>
                </td>
                <td *ngIf="hsp['status'] != 'Pending'">{{ hsp["status"] }}</td>
                <td *ngIf="hsp['status'] == 'Pending'" class="text-warning">
                  <u
                    data-toggle="modal"
                    data-target="#hspAssConfirmModal"
                    (click)="updateHspAppReq(hsp['uuid'])"
                    >{{ hsp["status"] }}</u
                  >
                </td>
              </tr>
            </tbody>
          </table>
          <p
            id="no-msg-data"
            class="nm-size"
            *ngIf="hospitalAssReq?.length == 0 || !messages"
          >
            No Data
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
<!--Manage Schedules-->
<div *ngIf="activity == 'manage schedules'" class="card">
  <div class="card-body">
    <div class="row">
      <div class="col-md-12">
        <h4 class="mb-4 ms">
          <i
            class="fas fa-chevron-circle-left"
            (click)="openAppointmentsTab()"
          ></i
          ><span> Manage Schedules</span>
        </h4>
        <ng-select
          class="ms ms-btn"
          (change)="getChforLocation($event)"
          [ngModel]="selectedLocationCh"
          id="practice-location-ms"
          [searchable]="false"
          [clearable]="false"
          [items]="practiceLocationList"
          bindLabel="name"
          placeholder="{{ 'Select Location' | translate }}"
        ></ng-select>
        <h5 class="ms loc-label">Location</h5>
        <hr />
      </div>
      <div class="col-md-12">
        <h5 class="db-ch-title card-title" translate>Consulting Hours</h5>
        <!-- <div class="card">
                  <div class="card-body"> -->
        <div class="profile-box">
          <app-doctor-consulting-hours
            [selectedLocation]="selectedLocationCh"
          ></app-doctor-consulting-hours>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- </div> -->
<!--Manage Schedules-->
<app-manage-appointments
  *ngIf="activity == 'manage appoinments'"
  (activity)="getActivity($event)"
></app-manage-appointments>
<!-- </div> -->

<div
  class="modal fade"
  id="hspAssConfirmModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="hspAssConfirmModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="hspAssConfirmModalLabel">
          Hospital Association Approval
        </h5>
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">Please confirm the request from the hospital</div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-primary"
          (click)="modifyHspAppReq(true)"
          data-dismiss="modal"
        >
          Approve
        </button>
        <button type="button" class="btn btn-danger" data-dismiss="modal">
          Decline
        </button>
      </div>
    </div>
  </div>
</div>
<!-- /Page Content -->
