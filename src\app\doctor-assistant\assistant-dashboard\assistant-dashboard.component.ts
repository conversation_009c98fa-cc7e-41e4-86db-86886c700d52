import { PlatformService } from './../../platform/platform.service';
import { DoctorService } from '../../doctor/doctor.service';
import { Component, OnInit, Output, EventEmitter, ViewChild, ElementRef, Input } from '@angular/core';
import * as moment from 'moment';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import {DoctorAssistantService} from '../doctor-assistant-service'
import { FormControl, FormGroup } from '@angular/forms';
declare var $;
@Component({
  selector: 'app-assistant-dashboard',
  templateUrl: './assistant-dashboard.component.html',
  styleUrls: ['./assistant-dashboard.component.css']
})
export class AssistantDashboardComponent implements OnInit {
  @ViewChild('closemodal') confirmModal: ElementRef;
  public practiceLocationList = [];
  public appointments = [];
  public upcommingAppointments = [];
  public appointmentsToday = [];
  public patientDetailList = {};
  public patientDetail = {};
  public appointmentsCount: any;
  public manageSchedules = false;
  public apptDetails = {};
  public notes = false;
  public activity = 'No Activity';
  public selectedAppointment = [];
  public initiateConsult = false;
  public disabledDoctordetailsBtn =true;
  @Output() consult: EventEmitter<boolean> = new EventEmitter();
  doctorInstantAvailability: any;
  public selectedLocationCh = {};
  screenOptions: NgbModalOptions = {
    keyboard: false,
    centered: true
  };
  hospitalAssReq = [];
  public messages: any;
  public available_now = JSON.parse(localStorage.getItem('available_now'));
  hospitalsList = [];
  selectedReqUuid = '';
  public doctorUuid: string;
  public doctorData:string;
  public doctorForm:FormGroup;
public associatedDotorList= [];
  constructor(
    private doctorService: DoctorService,
    private router: Router,
    private platformService: PlatformService,
    private doctorAssistantService: DoctorAssistantService,
    private notificationService: ToastrService
  ) {}

  ngOnInit(): void {
    this.doctorForm = new FormGroup({
      doctorId: new FormControl('')
    });
    this.doctorService.checkProfileCompletion();
   // this.getAppointmentsData();
  //  this.getPracticeLocation();
    this.getDoctorList();
    // console.log(typeof(JSON.parse(localStorage.getItem('available_now'))));
    setTimeout(()=>
      this.chckBoxFn(),300
    );
    // this.doctorService.getConsultationMessages().subscribe(data=>{
    //   this.messages = data;
    //
    // });
   // this.getHspAssociationReq();
  }

  viewPatient(uuid){
    console.log(this.patientDetailList);
    const data = this.patientDetailList[uuid];
    this.notes = false;
    const yr = data['date_of_birth'].split('-');
    data['age'] = new Date().getFullYear() - parseInt(yr[0]);
    this.patientDetail = data;
  }

  getAppointmentsData(){
    const todayDate = new Date();
    const date = moment(new Date()).format('YYYY-MM-DD');
    const tomorrowDate = new Date(todayDate);
    tomorrowDate.setDate(tomorrowDate.getDate()+1);
    const tomDate = moment(tomorrowDate).format('YYYY-MM-DD');
    const todaySearchParams = '?start_datetime=' + date +'&end_datetime=' + date;
    // console.log(todaySearchParams);
    const upcommingSearchParams =  '?start_datetime=' + tomDate;
    this.doctorService.getDoctorAppointments(todaySearchParams).subscribe(
      data => {
        this.appointmentsToday = data['results'];
        console.log(this.appointmentsToday);
        this.appointmentsToday = this.appointmentsToday.filter(
          (obj) => obj.status === "Booked"
        );
        this.appointmentsCount = this.appointmentsToday.length;
        for(let i=0; i<this.appointmentsToday.length; i++){
          this.appointmentsToday[i]['appt_date'] = moment(this.appointmentsToday[i]['start_datetime']).format('DD MMM YYYY');
          this.appointmentsToday[i]['appt_time'] = moment(this.appointmentsToday[i]['start_datetime']).format('h:mm A');
          const uuid = this.appointmentsToday[i]['patient_user_json']['uuid'];
          this.patientDetailList[uuid] = this.appointmentsToday[i]['patient_user_json'];
          // this.patientDetailList[uuid]['appDetail'] = this.appointmentsToday[i]['patient_user_json']
          // this.doctorService.getConsultationMessage(this.appointmentsToday[i]['consultation_uuid']).subscribe(
          //   data => {
          //
          //   }
          // );
        }
        this.defaultPatientImage();
      }
      );
      this.doctorService.getDoctorAppointments(upcommingSearchParams).subscribe(
        data => {
          this.upcommingAppointments = data['results'];
          this.upcommingAppointments = this.upcommingAppointments.filter(
            (obj) => obj.status === "Booked"
          );
          this.appointmentsCount = this.appointmentsCount + this.upcommingAppointments.length;
          for(let i=0; i<this.upcommingAppointments.length; i++){
            if(this.upcommingAppointments[i]['status'] == "Booked"){
            this.upcommingAppointments[i]['appt_date'] = moment(this.upcommingAppointments[i]['start_datetime']).format('DD MMM YYYY');
            this.upcommingAppointments[i]['appt_time'] = moment(this.upcommingAppointments[i]['start_datetime']).format('h:mm A');
            const uuid = this.upcommingAppointments[i]['patient_user_json']['uuid'];
            this.patientDetailList[uuid] = this.upcommingAppointments[i]['patient_user_json'];
          }
          this.defaultPatientImage();
        }
      }
    );
  }

  getPracticeLocation() {
    this.doctorService.getDoctorPracticeLocations().subscribe(
      (data) => {
        if(data['count'] != 0){
          const resp_data = data['results'];
          this.practiceLocationList = Object.values(resp_data);
          this.selectedLocationCh = this.practiceLocationList[0];
          sessionStorage.setItem(
                'practice_location',
                this.practiceLocationList[0]['uuid']
              );
        }
      },
      (err) => {
        console.log(err);
      }
    );
  }

  getChforLocation(event){
    this.selectedLocationCh = event;
  }

  manageScheduleFn(){
    this.manageSchedules = true;
    this.activity = 'manage schedules';
  }

  openAppointmentsTab(){
    this.manageSchedules = false;
    this.activity = 'No Activity';
  }

  manageAppointmentsFn(){
    this.activity = 'manage appoinments';
  }

  onConsult(appt){
    console.log('consultation id:::'+appt['consultation_uuid']);
    this.doctorService.joinConsultation(appt['consultation_uuid']).subscribe(
      data=>{

        this.router.navigate(['/doctor/consultation'], { queryParams: { consultationId: appt['consultation_uuid'] } });
      }
    );
  }
  cancelAppointment(appt_uuid,day){

    let data = {};
    if(day == "today"){
      data = this.appointmentsToday.filter(
        (obj) => obj.uuid === appt_uuid
      );
    }
    if(day == "upcomming"){
      data = this.upcommingAppointments.filter(
        (obj) => obj.uuid === appt_uuid
      );
    }

    this.apptDetails['username'] = data[0]['patient_user_json']['username'];
    this.apptDetails['date'] = data[0]['appt_date'];
    this.apptDetails['time'] = data[0]['appt_time'];
    this.apptDetails['uuid'] = data[0]['uuid'];
  }

  confirmCancel(uuid){
    // console.log(uuid);
    this.doctorService.cancelPatientAppointment(uuid).subscribe(
      data => {
        this.notificationService.success('Appoinment Cancelled', 'Med.Bot');
        this.getAppointmentsData();
         this.confirmModal.nativeElement.click();
      }
    );

  }

  showNotes(){
    this.notes = true;
  }

  onCaseHistory(){
    this.router.navigateByUrl('/case-history-visit-dates');

    // this.modalService.open(CaseHistoryComponent, this.screenOptions).result.then((result) => {
    // });
  }

  getActivity(event){
    this.activity = event;
  }

  selectAppointment(data,event){
    if(event.target.checked){
        this.selectedAppointment.push(data);
    }
    else{
      this.selectedAppointment = this.selectedAppointment.filter(
        (appt) => appt === data
      );
    }
    this.initiateConsult = true;
  }

  initiateVConsult(){
    const len = this.selectedAppointment.length;
    if(len != 0){
      if(len == 1){
        console.log('selected appointment...');
        console.log(this.selectedAppointment[0]['uuid']);
        this.router.navigate(['consultation'],{ queryParams: { appId: this.selectedAppointment[0]['uuid'] } });
      }
      else{
        this.notificationService.error('Please select single Appoinment');
      }
    }
    else{
      this.notificationService.error('Select an appointment');
    }
  }

  editProfile(){
    this.router.navigate(['/doctor/profile']);
  }

  modifyAvailableNow(event){
    if(event.target.checked){
      this.doctorService.doctorAvailableNow().subscribe(
        data=>
        {
          localStorage.setItem('available_now', 'true');
          this.available_now = true;
          this.notificationService.success('You are available for Instant Consultation');
        }
      );
    }
    else{
      this.doctorService.doctorUnAvailableNow().subscribe(
        data=>{
          localStorage.setItem('available_now', 'false');
          this.available_now = false;
          this.notificationService.success('Not available for Instant Consultation');
        }
      );
    }
  }

  defaultPatientImage(){
    Object.values(this.patientDetailList).forEach(obj => {
      if(obj['profile_picture']== null){
        obj['profile_picture'] = 'assets/img/patients/patient.jpg';
      }
    });
    console.log(this.patientDetailList);
  }

  chckBoxFn(){
    console.log(this.available_now);
    this.available_now = JSON.parse(localStorage.getItem('available_now'));
    if(this.available_now){
      this.available_now = true;
      console.log('avalilable');
    }
  }

  trimMsg(strng){
    strng.substring(0,300);
  }

  getHospital(uuid,req){
    this.platformService.getHospitalDetail(uuid).subscribe(
      data=>{
        this.hospitalsList.push(data);
      }
    );
  }
  getHspAssociationReq(){
    this.doctorService.getHospitalAssociationRequest().subscribe(
      data=>{

        this.hospitalAssReq = data['results'];
        const hspData = this.hospitalsList[0];
        // this.hospitalsList = [];
        for (let i=0; i<this.hospitalAssReq.length; i++){
          this.platformService.getHospitalDetail(this.hospitalAssReq[i]?.hospital).subscribe(
            data=>{
              this.hospitalAssReq[i]['hospital_data'] = data;
              if(!this.hospitalAssReq[i]['is_approved_by_doctor']){
                this.hospitalAssReq[i]['status'] = 'Pending';
              }
              else{
                this.hospitalAssReq[i]['status'] = 'Approved';
              }
            }
          );
        }
        console.log(this.hospitalAssReq);
      }
    );
  }

  updateHspAppReq(uuid){
    this.selectedReqUuid = uuid;
  }

  modifyHspAppReq(status){

    this.doctorService.approveAssociateReq(this.selectedReqUuid,status).subscribe(
      data=>{
        this.notificationService.success('Request Approved', 'Med.Bot');
        this.getHspAssociationReq();
        // $('#hspAssConfirmModal').hide();
      }
    );
  }
  getSelectedDoctorDatails(value){
    this.doctorUuid =value;
    console.log(value);
  }
  doctorProfileView(){
this.router.navigate([`/identity-details/${this.doctorUuid}/view`]);
  }
  praticeLocationNavigation(){
    this.router.navigate([`/assistant/practice-locations/${this.doctorUuid}`]);

  }
  bankAccountRouting(){
    this.router.navigate([`/assistant/bank-accounts/${this.doctorUuid}`]);
  }
  getDoctorList(){
    this.doctorAssistantService.getAssociatedDoctor().subscribe(
      data=>{
        this.associatedDotorList = data['results'];
        if(this.associatedDotorList.length>0){
          this.disabledDoctordetailsBtn= false;
          this.doctorUuid = this.associatedDotorList[0].uuid;
          this.doctorForm.controls['doctorId'].setValue(this.associatedDotorList[0].uuid);
        }


        // $('#hspAssConfirmModal').hide();
      }
    );
  }
}
