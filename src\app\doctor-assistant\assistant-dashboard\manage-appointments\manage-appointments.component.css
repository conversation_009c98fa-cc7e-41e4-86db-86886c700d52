.ma {
    display: inline;
}

.date-label {
    float: right;
    padding-top: 10px;
}

hr {
    margin: 0px;
}

.card-body.top-ma {
    padding-bottom: 10px;
}

th {
    font-size: 18px;
    font-weight: lighter;
    text-transform: uppercase;
}

.sm-head {
    display: block;
    color: #757575;
    font-size: 14px;
    font-weight: lighter;
    text-align: center;
}

.time-box {
    margin-top: 10px;
    margin-left: 10px;
    border: 1px solid #e9e9e9;
    border-radius: 3px;
    display: block;
    font-size: 14px;
    margin-bottom: 10px;
    padding: 5px 5px;
    text-align: center;
    position: relative;
    pointer-events: none;
    margin-right: 10px;
    text-align: center;
}

.booked {
    /* /padding: 5px; */
    /* display: flex; */
    justify-content: center;
    align-items: center
}

.btn-booked {
    /* background-color: #4CAF50; */
    border: none;
    color: white;
    padding: 12px 38px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    margin-left: 10px;
    margin-right: 10px;
}

.date-field {
    width: 80%;
}

tr.no-hr td.no-border {
    border: 0;
}

tr:hover {
    background-color: #ffff !important;
}

#back-appt {
    color: #20c0f3;
    cursor: pointer;
}

.availCss {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.bookedCss {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.noSlotCss {
    color: #212529;
    background-color: #F8F9CB;
    border-color: #f8f9fa;
    pointer-events: none;
}

.selectedCss {
    color: #212529;
    background-color: #20c0f3 !important;
    border-color: 2px solid #20c0f3 !important;
}

.blockedCss {
    color: #fff;
    background-color: #bd2130;
    ;
    border-color: #bd2130;
    pointer-events: none;
}

#not-booked {
    color: #6c757d;
}

#booked {
    color: #28a745;
}

#no-slot {
    color: #F8F9CB !important;
}

#unavail {
    color: #bd2130;
}

#selected {
    color: #20c0f3;
}

.legend {
    display: inline;
}

.card {
    margin-bottom: 5px !important;
}

.slt-btn {
    margin: 10px;
}

.text-center {
    margin-bottom: 25px;
}

.chk-box {
    text-align: center;
}

th {
    text-align: center;
    /* center checkbox horizontally */
    vertical-align: middle;
}

td {
    text-align: center;
    /* center checkbox horizontally */
    vertical-align: middle;
}

#mtsa-btn2 {
    margin-top: -22px;
    /* margin-left: 162px; */
    float: right;
    margin-bottom: auto;
}

.ma {
    /* padding-left: 20px; */
}

.legend-cont {
    padding-right: 10px;
    padding-left: 15px;
}