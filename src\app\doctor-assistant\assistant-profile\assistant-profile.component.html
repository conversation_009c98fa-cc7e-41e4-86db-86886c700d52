<div class="card m-5">
    <div class="card-body">
        <!-- <h4 class="card-title" translate>Profile Picture</h4> -->
        <div class="row form-row">
            <div class="col-md-12">
                <div class="form-group">
                    <div class="change-avatar">
                        <div class="profile-img">
                            <img [src]="doctorProfilePictureUrl" alt="User Image">
                        </div>
                        <div class="upload-img">
                            <div class="change-photo-btn">
                                <span><i class="fa fa-upload"></i> {{ profileUpload ? ('Upload Photo'|translate):
                                    'Uploading'|translate}}</span>
                                <input type="file" class="upload" id="profile-picture"
                                    [disabled]="disabledUploadPhotoBtn" (change)="doctorProfilePictureChange($event)"
                                    accept=".jpg, .png,">
                            </div>
                            <small class="form-text text-muted" translate>Allowed JPG, GIF or PNG. Max size of
                                2MB</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <form [formGroup]="personalProfileForm">
            <h4 class="card-title" translate>Personal Profile <i *ngIf="disabled" (click)="editProfile()"
                class="fa fa-edit"></i></h4>
            <div class="row form-row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label translate>Full Name<span class="text-danger">*</span></label>
                        <input id="fullname" class="form-control" type="text" name="username"
                            formControlName="username" maxlength="25" pattern="[a-zA-Z0-9 ]*" readonly>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label translate>Email <span class="text-danger">*</span></label>
                        <input id="email" type="email" name="email" class="form-control"
                            formControlName="email" maxlength="50" readonly>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label translate>First Name</label>
                        <input id="firstname" type="text" class="form-control" name="first_name"
                            formControlName="first_name" maxlength="25" pattern="[a-zA-Z ]*"
                            autocomplete="off" [readonly]="disabled">
                        <div *ngIf="personalProfileForm.controls.first_name.invalid && (personalProfileForm.controls.first_name.dirty || personalProfileForm.controls.first_name.touched)"
                            class="alert alert-danger">{{alphabetsError}}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label translate>Middle Name</label>
                        <input id="middlename" type="text" class="form-control" name="middle_name"
                            formControlName="middle_name" maxlength="25" pattern="[a-zA-Z ]*"
                            autocomplete="off" [readonly]="disabled">
                        <div *ngIf="personalProfileForm.controls.middle_name.invalid && (personalProfileForm.controls.middle_name.dirty || personalProfileForm.controls.middle_name.touched)"
                            class="alert alert-danger">{{alphabetsError}}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label translate>Last Name</label>
                        <input id="lastname" type="text" class="form-control" name="last_name"
                            formControlName="last_name" maxlength="25" pattern="[a-zA-Z ]*"
                            autocomplete="off" [readonly]="disabled">
                        <div *ngIf="personalProfileForm.controls.last_name.invalid && (personalProfileForm.controls.last_name.dirty || personalProfileForm.controls.last_name.touched)"
                            class="alert alert-danger">{{alphabetsError}}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label translate>Phone Number<span class="text-danger">*</span></label>
                        <input id="phone" type="text" class="form-control" formControlName="phone"
                            pattern="[0-9]*" name="phone" maxlength="15" readonly>
                        <div *ngIf="personalProfileForm.controls.phone.invalid && (personalProfileForm.controls.phone.dirty || personalProfileForm.controls.phone.touched)"
                            class="alert alert-danger">{{numberError}}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label translate>Gender<span class="text-danger">*</span></label>
                        <select class="form-control" name="gender" id="gender" formControlName="gender">
                            <option *ngFor="let data of gender" [value]="data.value ">{{data.name}}
                            </option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-0 ">
                        <label translate>Date of Birth<span class="text-danger">*</span></label>
                        <input [maxDate]="maxDate" [minDate]="minDate" placeholder="DOB"
                            onkeydown="return false" class="form-control"
                            formControlName="date_of_birth" [readonly]="disabled" bsDatepicker
                            [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }"
                            [ngClass]="{'bs-datepicker':disabled==true}" required>
                    </div>
                </div>
            </div>
            <div class="form-group float-right">
                <button *ngIf="!disabled" type="button" id="save-btn" id="per-prof-btn"
                    class="btn btn-primary" translate (click)="onSubmit()"
                    [disabled]="!personalProfileForm.valid">Save</button>
                <button *ngIf="!disabled" type="button" id="cancel-btn" (click)="cancelUpdate()"
                    class="btn btn-secondary cancel-btn" translate>Cancel</button>

            </div>
            <!-- /Basic Information -->

        </form>
        
        <!-- <form #_personalProfileForm="ngForm" (ngSubmit)="_personalProfileForm.form.valid && onSubmit()" novalidate>
            <fieldset #profileFieldset [disabled]="disabled">
                <h4 class="card-title" translate>Personal Profile <i *ngIf="disabled" (click)="editProfile()"
                        class="fa fa-edit"></i></h4>
                <div class="row form-row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Full Name<span class="text-danger">*</span></label>
                            <input id="fullname" class="form-control" type="text" name="username"
                                [(ngModel)]="userData['username']" #_username="ngModel" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Email <span class="text-danger">*</span></label>
                            <input id="email" type="email" name="email" [(ngModel)]="userData['email']"
                                #_email="ngModel" class="form-control" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>First Name</label>
                            <input id="firstname" type="text" class="form-control" name="first_name"
                                [(ngModel)]="userData['first_name']" #_first_name="ngModel"
                                [ngClass]="{ 'is-invalid': _personalProfileForm.submitted && _first_name.invalid }">
                            <div *ngIf="_first_name.invalid && (_first_name.dirty || _first_name.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>

                            <div *ngIf="_first_name.invalid" class="alert alert-danger">
                                <div *ngIf="_first_name.errors.required">_first_name is required</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Middle Name</label>
                            <input id="middlename" type="text" class="form-control" name="middle_name"
                                [(ngModel)]="userData['middle_name']" #_middle_name="ngModel">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Last Name</label>
                            <input id="lastname" type="text" class="form-control" name="last_name"
                                [(ngModel)]="userData['last_name']" #_last_name="ngModel">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Phone Number<span class="text-danger">*</span></label>
                            <input id="phone" type="text" class="form-control" name="phone"
                                [(ngModel)]="userData['phone']" #_phone="ngModel" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Gender</label>
                            <select class="form-control select" name="gender" id="gender"
                                [(ngModel)]="userData['gender']" #_gender="ngModel">
                                <option>Select</option>
                                <option value="Male" translate>Male</option>
                                <option value="Female" translate>Female</option>
                                <option value="Other" translate>Other</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-0">
                            <label translate>Date of Birth</label>
                            <input type="date" id="dob" class="form-control" name="date_of_birth" [max]="maxDate"
                                [(ngModel)]="userData['date_of_birth']" #_date_of_birth="ngModel" max>
                        </div>
                    </div>
                </div>
                <div class="form-group float-right">
                    <button *ngIf="!disabled" type="submit" id="save-btn" id="per-prof-btn"
                        class="save-btn basic-data-btn" translate>Save</button>
                    <button *ngIf="!disabled" type="button" id="cancel-btn" (click)="cancelUpdate()"
                        class="btn btn-secondary cancel-btn" translate>Cancel</button>

                </div>
            </fieldset>
        </form> -->
        <!-- /Basic Information -->
    </div>
</div>