
import { AuthService } from '../../auth/auth.service';
import {
  Component,
  OnInit,
  Output,
  EventEmitter,
} from '@angular/core';
import * as Settings from '../../config/settings';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  NgForm,
  FormBuilder,
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
declare var $: any;
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import { SharedService } from '../../shared/shared.service';
import { DoctorAssistantService } from '../doctor-assistant-service';
import * as moment from 'moment';

@Component({
  selector: 'app-assistant-profile',
  templateUrl: './assistant-profile.component.html',
  styleUrls: ['./assistant-profile.component.css']
})
export class AssistantProfileComponent implements OnInit {
  public changed = false;
  public profileUpload = true;
  public disabledUploadPhotoBtn = false;
  // public personalProfileForm: NgForm;
  public personalProfileForm: FormGroup;
  public userData = {};
  public disabled = true;
  doctorProfilePictureUrl = 'assets/img/doctors/doctor-thumb-02.png';
  public cancelbtn = false;
  profileFileSizeLarge = false;
  @Output() messageEvent: EventEmitter<string> = new EventEmitter<string>();
  userID: any;
  public isLoading = false;
  public maxDate: Date;
  public minDate: Date;
  gender = [
    { value: '', name: 'Select' },
    { value: 'Male', name: 'Male' },
    { value: 'Female', name: 'Female' },
    { value: 'Prefer not to answer', name: 'Prefer not to answer' },
  ];
  public doc_uuid = '';
  public userDetails: any = {
    username: null,
    email: null,
    phone: null,
    gender: null,
    first_name: null,
    middle_name: null,
    last_name: null,
    date_of_birth: null,
    uuid: null
  };
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  numberError = Settings.numberError;

  constructor(private userService: AuthService,
    private formBuilder: FormBuilder,
    private router: Router,
    public translate: TranslateService,
    private sharedService: SharedService,
    private notificationService: ToastrService,
    private assistant: DoctorAssistantService,
    private location: Location) { }

  ngOnInit(): void {
    this.sharedService.setActiveLink('assistant-profile');
    this.maxDate = new Date();
    this.minDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate() - 7672);
    this.minDate.setDate(this.minDate.getDate() - 36500);
    this.addProfileFromControl(null);
    this.getuserData();
  }

  getuserData() {
    this.userService.getUserDetail().subscribe(
      (data) => {
        this.userData = data;
        console.log(this.userData);
        if (this.userData['profile_picture'] !== null) {
          this.doctorProfilePictureUrl = this.userData['profile_picture'];
        }
        this.addProfileFromControl(this.userData);
        this.isLoading = false;
      },
      (error) => {
        console.log(error);
        this.isLoading = true;
      }
    );
  }

  onSubmit() {
    const dob = this.personalProfileForm.controls[`date_of_birth`].value;
    this.userDetails.username = this.personalProfileForm.controls[`username`].value;
    this.userDetails.email = this.personalProfileForm.controls[`email`].value;
    this.userDetails.phone = this.personalProfileForm.controls[`phone`].value;
    this.userDetails.gender = this.personalProfileForm.controls[`gender`].value;
    this.userDetails.first_name = this.personalProfileForm.controls[`first_name`].value;
    this.userDetails.last_name = this.personalProfileForm.controls[`last_name`].value;
    this.userDetails.middle_name = this.personalProfileForm.controls[`middle_name`].value;
    this.userDetails.date_of_birth = moment(dob, 'DD-MM-YYYY').format('YYYY-MM-DD');
    // this.userDetails.uuid = this.doc_uuid;
    this.userService.updatePersonalProfile(this.userDetails).subscribe(
      (data) => {
        this.notificationService.success('Profile Update', 'Med.Bot');
        this.disabled = true;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          const err = error['error']['error_details']['validation_errors'];
          if (err) {
            const gender = err['gender'];
            const dob = err['date_of_birth'];
            if (gender && dob) {
              ;
              const genderError = 'Gender : ' + gender[0];
              const dobError = 'DOB : ' + dob[0];
              this.notificationService.error(
                `${genderError} ${dobError}`,
                'Med.Bot'
              );
            } else if (gender) {
              const genderError = 'Gender : ' + gender[0];
              this.notificationService.error(`${genderError}`, 'Med.Bot');
            } else if (dob) {
              const dobError = 'DOB : ' + dob[0];
              this.notificationService.error(` ${dobError}`, 'Med.Bot');
            } else {
              this.notificationService.error('Updation Error', 'Med.Bot');
            }
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  onChange() {
    this.changed = true;
  }

  editProfile() {
    this.disabled = false;
    this.personalProfileForm.get('gender').enable();
  }

  cancelUpdate() {
    this.personalProfileForm.get('gender').disable();
    this.disabled = true;
    this.addProfileFromControl(this.userDetails);
  }

  doctorProfilePictureChange(event) {
    const file = event.target.files;

    if (file.length > 0) {
      this.profileFileSizeLarge = false;
      const selectedProfilePicture = file[0];
      console.log(selectedProfilePicture);
      if (
        selectedProfilePicture.size < 2000000 &&
        (selectedProfilePicture.type === 'image/jpeg' ||
          selectedProfilePicture.type === 'image/jpg' ||
          selectedProfilePicture.type === 'image/png')
      ) {
        this.disabledUploadPhotoBtn = true;
        this.profileUpload = false;
        this.userService
          .updateDoctorProfilePicture(selectedProfilePicture)
          .subscribe(
            (data) => {
              this.userData = data;
              this.doctorProfilePictureUrl = this.userData['profile_picture'];
              this.sharedService.setPicture(this.doctorProfilePictureUrl);
              this.profileUpload = true;
              this.notificationService.success(
                'Profile Picture Update',
                'Med.Bot'
              );
            },
            (error) => {
              this.profileUpload = true;
              this.disabledUploadPhotoBtn = false;
              console.log(error);
              this.notificationService.error(
                'Error In Updating profile picture',
                'Med.Bot'
              );
            }
          );
      } else {
        this.profileFileSizeLarge = true;
      }
    } else {
      this.profileUpload = true;
      this.disabledUploadPhotoBtn = false;
      this.notificationService.error(
        'Please select  profile picture',
        'Med.Bot'
      );
    }
  }

  addProfileFromControl(data) {
    if (data === null) {
      this.disabled = false;
      this.personalProfileForm = new FormGroup({
        username: new FormControl('', [
          Validators.required,
          Validators.maxLength(50),
        ]),
        email: new FormControl('', [Validators.required, Validators.email]),
        first_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        middle_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        last_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        phone: new FormControl('', [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl('', [
          Validators.required,
          Validators.maxLength(10),
        ]),
        date_of_birth: new FormControl('', [
          Validators.required,
          Validators.maxLength(20),
        ]),
      });
    } else {
      this.disabled = true;
      this.personalProfileForm.get('gender').disable();
      this.personalProfileForm = new FormGroup({
        username: new FormControl(data.username, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        email: new FormControl(data.email, [
          Validators.required,
          Validators.email,
        ]),
        first_name: new FormControl(data.first_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')
        ]),
        middle_name: new FormControl(data.middle_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')
        ]),
        last_name: new FormControl(data.last_name, [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        phone: new FormControl(data.phone, [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl(data.gender, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        date_of_birth: new FormControl(
          moment(data.date_of_birth).format('DD-MM-YYYY'),
          [Validators.required, Validators.maxLength(25)]
        ),
      });
      this.personalProfileForm.get('gender').disable();
    }
  }
}
