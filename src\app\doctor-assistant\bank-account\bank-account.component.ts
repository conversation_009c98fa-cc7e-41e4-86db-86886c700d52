import { Component, OnInit } from '@angular/core';
import { DoctorService } from '../../doctor/doctor.service';
import { ToastrService } from 'ngx-toastr';

import { delay } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { FormBuilder, FormGroup, Validators, FormArray, FormControl } from '@angular/forms';

import * as Settings from '../../config/settings';
@Component({
  selector: 'app-bank-account',
  templateUrl: './bank-account.component.html',
  styleUrls: ['./bank-account.component.css']
})
export class BankAccountComponent implements OnInit {





  public bankAccountDetailForm: FormGroup;
  public bankAccountDetailArray: FormArray;
  public edit = false;
  public newForm = false;
  public showAddMore = true;
  public bankAccountList = [];
  public accountTypes = ['Savings Account', 'Current Account'];
  public doctor = {};
  constructor(
    private formBuilder:FormBuilder,
    private httpClient: HttpClient,
    private doctorService: DoctorService,
    private notificationService: ToastrService
  ) { }

  ngOnInit(): void {
    this.doctorService.checkProfileCompletion();
    this.addArrayForm();
    this.loadBankData();
  }

  addArrayForm(){
    this.bankAccountDetailForm = this.formBuilder.group({
      bankAccountDetailArray: this.formBuilder.array([]),
    });
  }

  trackFn(index: any) {
    return index;
  }

  getBankAccountDetails(){
    return this
      .httpClient
        .get(
          `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/bank_accounts/`
        )
        .pipe(delay(Settings.REQUEST_DELAY_TIME))
    ;
  }

  createBankAccountDetailForm(data: Object){
    this.bankAccountDetailArray = this.bankAccountDetailForm.get('bankAccountDetailArray') as FormArray;
    if(data == null){
      this.bankAccountDetailArray.push(
        this.formBuilder.group({
          uuid: null,
          account_type: [null, Validators.required],
          account_name: [null, Validators.required],
          account_number: [null, Validators.required],
          bank_name: [null, Validators.required],
          branch_name: [null, Validators.required],
          ifsc_code: [null, Validators.required],
          edit: true,
          // deleted: false,
        })
      );
      this.showAddMore = false;
      this.newForm = true;
      }
    else{
      this.bankAccountDetailArray.push(
        this.formBuilder.group({
          uuid: data['uuid'],
          account_type: [{value:data['account_type'],disabled:true},Validators.required],
          account_name: [{value:data['account_name'], disabled:true}, Validators.required],
          account_number: [{value:data['account_number'], disabled:true}, Validators.required],
          bank_name: [{value:data['bank_name'], disabled:true}, Validators.required],
          branch_name: [{value:data['branch_name'], disabled:true}, Validators.required],
          ifsc_code: [{value:data['ifsc_code'], disabled:true}, Validators.required],
          edit: false,
          // deleted:false,
        })
      );
    }
  }

  removeBankAccount(i){
    const data = this.bankAccountDetailForm.get('bankAccountDetailArray').value[i];
    const uuid = data['uuid'];
    if(uuid){
      this.deleteBankAccount(uuid).subscribe(
        data => {
          this.addArrayForm();
          this.loadBankData();
          // this.bankAccountDetailArray.at(i).get('deleted').setValue(true);
          this.showAddMore = true;
          this.notificationService.success('Bank Account Deleted', 'Med.Bot');
        },
        error => {
          console.log(error);
          this.notificationService.error('Bank Account Deletion Failed', 'Med.Bot');
        }
      );
    }
    else{
      this.bankAccountDetailArray.removeAt(i);
      this.showAddMore = true;
    }
  }

  deleteBankAccount(uuid){
    return this.httpClient.delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/bank_accounts/${uuid}/`
    )
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateAccountDetail(data,uuid){
    if(uuid){
      return this
      .httpClient
        .patch(
          `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/bank_accounts/${uuid}/`, data
        )
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
    else{
      return this
      .httpClient
        .post(
          `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/bank_accounts/`, data
        )
        .pipe(delay(Settings.REQUEST_DELAY_TIME))
    ;
    }
  }

  saveAccountDetail(i){
    const data = this.bankAccountDetailForm.get('bankAccountDetailArray').value[i];
    const api_data = {
      'account_type':data['account_type'],
      'account_name': data['account_name'],
      'account_number': data['account_number'],
      'bank_name': data['bank_name'],
      'branch_name': data['branch_name'],
      'ifsc_code': data['ifsc_code']
    };
    if(data['uuid']){
      this.updateAccountDetail(api_data,data['uuid']).subscribe(
        data =>{
          const control = this.bankAccountDetailArray.at(i);
          control.get('edit').setValue(false);
          control.get('account_type').disable();
          control.get('account_name').disable();
          control.get('account_number').disable();
          control.get('bank_name').disable();
          control.get('branch_name').disable();
          control.get('ifsc_code').disable();
          this.showAddMore = true;
          this.notificationService.success('Bank Account Added', 'Med.Bot');
        },
        error => {
          console.log(error);
          this.notificationService.error('Sorry', 'Med.Bot');
        }
        );
      }
      else{
        this.newForm = false;
        this.updateAccountDetail(api_data,null).subscribe(
          data =>{
            const control = this.bankAccountDetailArray.at(i);
            control.get('uuid').setValue(data['uuid']);
            control.get('edit').setValue(false);
            control.get('account_type').disable();
            control.get('account_name').disable();
            control.get('account_number').disable();
            control.get('bank_name').disable();
            control.get('branch_name').disable();
            control.get('ifsc_code').disable();
            this.showAddMore = true;

            this.notificationService.success('Bank Account Added', 'Med.Bot');
      },
      error => {
        console.log(error);
        this.notificationService.error('Sorry', 'Med.Bot');
      }
    );
    }

  }

  emptyFormArray(){
      const control = this.bankAccountDetailForm.get(
        'bankAccountDetailArray'
      ) as FormArray;
      for(let i = control.length-1; i >= 0; i--) {
        control.removeAt(i);
      }
  }

  editBankAccountDetail(i: any){
    this.newForm = false;
    const control = this.bankAccountDetailArray.at(i);
    control.get('edit').setValue(true);
    control.get('account_type').enable();
    control.get('account_name').enable();
    control.get('account_number').enable();
    control.get('bank_name').enable();
    control.get('branch_name').enable();
    control.get('ifsc_code').enable();
    this.showAddMore = false;
  }

  cancelEdit(i: any){
    const control = this.bankAccountDetailArray.at(i);
    const uuid = control.get('uuid').value;
    if(uuid == null){
      this.removeBankAccount(i);
    }
    else{
      const control = this.bankAccountDetailArray.at(i);
      control.get('edit').setValue(false);
      control.get('account_type').disable();
      control.get('account_name').disable();
      control.get('account_number').disable();
      control.get('bank_name').disable();
      control.get('branch_name').disable();
      control.get('ifsc_code').disable();
    }
    this.showAddMore = true;
  }

  loadBankData(){
    this.getBankAccountDetails().subscribe(
      data=>{
        this.bankAccountList = data['results'];
        // if(this.bankAccountList.length <= 2){
        //   this.showAddMore = false;
        // }
        const banks = this.bankAccountList;
        for(let i=banks.length; i>0; i-- ){
          this.createBankAccountDetailForm(banks[i-1]);
        }
      }
    );
  }
}


