
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as Settings from './../config/settings';
import { delay } from 'rxjs/operators';
@Injectable({
  providedIn: 'root',
})
export class DoctorAssistantService {
  constructor(private httpClient: HttpClient) {}

  getdoctorProfile(id){
    return this
    .httpClient
    .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/profile/${id}/`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getCountryDetail(){
    return this
    .httpClient
    .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/countries/`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorQualifications(){
    return this
    .httpClient
    .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorLanguages(){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/languages/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorProfile(){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/profile/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorPracticeLocations(){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/practice_locations/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsultingHours(){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/consulting_hours_group/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorFees(){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctersByConsultNow(page){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/?page=${page}&consult_now=true`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctersBypage(page){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/?page=${page}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getAllDocters(){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorUnavailability(){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/unavailability/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  searchDoctors(searchParams){
      return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${searchParams}`
       ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorAppointments(searchParams){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/appointments/${searchParams}/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorAllAppointments(){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/appointments/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorRegistrations() {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/registrations/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorAppointmentSlot(id , searchParams) {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${id}/appointment_slots/calendar/${searchParams}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  bookAppointment(id) {
     return this.httpClient
    .post(
      `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/appointment_slots/${id}/booking/`, null
    )
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorListByPageNumber(num){
      return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/?page=${num}`
      ).pipe(delay(Settings.REQUEST_DELAY_TIME));

  }

  cancelPatientAppointment(uuid){
      const data = {status: 'Cancelled'};
      return this.httpClient.patch(`${Settings.API_URL_PREFIX}/api/doctor/me/appointments/${uuid}/`, data
      ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getAppointmentSlots(searchParams){
      return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/appointment_slots/?${searchParams}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getSpeciality(){
      return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/specialities/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  blockAppointmentSlot(uuid){
      const data = {status: 'Blocked'};
      return this.httpClient.patch(`${Settings.API_URL_PREFIX}/api/doctor/me/appointment_slots/${uuid}/`, data
      ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postDoctorFee(data){
      return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/`,data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchDoctorFee(uuid,data){
      return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/${uuid}/`,data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  doctorAvailableNow(){
      return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_appointment_slot/open/`,{}
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  doctorUnAvailableNow(){
      return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_appointment_slot/close/`,{}
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postConsultingHours(data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/consulting_hours_group/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchConsultingHours(data,uuid) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/consulting_hours_group/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postUnavailability(data){
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/unavailability/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  patchUnavailability(uuid,data){
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/unavailability/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  deleteUnavailabilityData(uuid){
    return this.httpClient.delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/unavailability/${uuid}/`,
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchDoctorProfile(data){
    const formData = {'professional_summary':data};
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/profile/`, formData
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  checkInstantAppointmentAvailability(){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_requests/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateInstantAppointmentStatus(uuid,status){
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_requests/${uuid}/`, status
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  initiateConsultation(appointment_uuid){
    const data = {};
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}api/doctor/me/appointments/${appointment_uuid}/consultations/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getInstantAppointment(uuid){
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_requests/${uuid}/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  joinConsultation(uuid){
    const data = {};
    return this.httpClient.post(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${uuid}/join/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getConsultationMessage(uuid){
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${uuid}/messages/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getConsultationMessages(){
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/me/messages/?seen=False&recipient_type=Doctor`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  checkProfileCompletion(){
    const profile_completion = localStorage.getItem('profile_completion');
    if(parseInt(profile_completion) < 80){
      // this.notificationService.warning('Please complete the profile to access other pages');
      // this.router.navigate(['/doctor/profile']);
    }
  }

  submitForApproval(){
    const data ='';
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/profile/approval_request/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateTermsAndCondtion(id) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/terms/acceptance/`, id)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postDoctorQualification(data) {
    return this
      .httpClient
      .post(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/`, data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }

  patchDoctorQualification(qual_uuid,data){
    return this
      .httpClient
      .patch(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/${qual_uuid}/`, data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }

  deleteDoctorQualification(qual_uuid){
    return this
      .httpClient
      .delete(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/${qual_uuid}/`
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }
  getHospitalAssociationPendingRequest(){
    return this
      .httpClient
      .get(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/h/me/associations/pending/`
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }

  getHospitalAssociationRequest(){
    return this
      .httpClient
      .get(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/h/me/associations/`
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }

  approveAssociateReq(uuid){
    const data = {"is_approved_by_doctor": true,'approved':true};
    return this
      .httpClient
      .patch(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/h/me/associations/pending/${uuid}/`, data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }
  getAssociatedDoctor(){
    return this
    .httpClient
    .get(
      `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/a-doctors/`
    )
    .pipe(delay(Settings.REQUEST_DELAY_TIME))
    ;
  }
  saveNotes(data,id,consultMsgId){
    return this
    .httpClient
    .patch(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${id}/messages/${consultMsgId}`, data)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
}
