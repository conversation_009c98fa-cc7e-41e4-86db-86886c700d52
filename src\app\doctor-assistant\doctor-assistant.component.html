<!--BreadCrumbs-->
<div class="breadcrumb-bar">
  <div class="container-fluid">
    <div class="row align-items-center">
      <div class="col-md-12 col-12">
        <nav aria-label="breadcrumb" class="page-breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.html">Home</a></li>
            <li
              class="breadcrumb-item active"
            >
              {{ breadcrumbHeader }}
            </li>
          </ol>
        </nav>
        <h2  class="breadcrumb-title">
          {{ breadcrumbHeader}}
        </h2>
      </div>
      <div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 text-aline"
      *ngIf="breadcrumbHeader=='Consultation' &&showProfilePic" style="display: inline;">
      <div class="avatar avatar-teleconsult avatar-sm mr-4 ">
          <img [src]="profilePicture" class="avatar-img rounded-circle" alt="User Image">
      </div>
      <h5 class="doc-name first-name" style="color: white; display: inline;">
          {{personalInfo['username']}}&nbsp;&nbsp;</h5><br>
          <h6 class="doc-name first-name" style="color: white; display: inline;">
              ID:{{patientId}}&nbsp;&nbsp;</h6><br>
      <p style="color: white;display: inline"><span class="fas fa-phone-alt"></span>
          Support-{{supportNumber}}</p>
  </div>
    </div>
  </div>
</div>
<!--BreadCrumbs Ends-->
<!-- Page Content -->
<div class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12 col-lg-12 col-xl-12">
        <div >
         <router-outlet (activate)="onActivate($event)"></router-outlet>
      </div>
      </div>
    </div>
  </div>
</div>

