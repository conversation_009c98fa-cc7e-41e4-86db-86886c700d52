import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from './../auth/auth.service';
import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SharedService } from '../shared/shared.service';
import { DoctorAssistantService } from './doctor-assistant-service';
import { DoctorService } from '../doctor/doctor.service';
import { TeleConsultService } from '../tele-consult/tele-consult.service';

@Component({
  selector: 'app-doctor-assistant',
  templateUrl: './doctor-assistant.component.html',
  styleUrls: ['./doctor-assistant.component.css'],
})
export class DoctorAssistantComponent implements OnInit {
  breadcrumbHeader: string;
  personalInfo: { username: null };
  patientId: any;
  profilePicture = 'assets/img/doctors/doctor-thumb-02.png';
  showProfilePic: boolean;


  constructor(
    private translate: TranslateService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private sharedService: SharedService,
    private userService: AuthService,
    private DoctorAssistantService: DoctorAssistantService,
    private doctorService: DoctorService,
    private teleConsultService: TeleConsultService,

  ) {}

  ngOnInit(): void {
    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this.userService.getUserDetail().subscribe(
      (data) => {
        this.sharedService.setUserName(data['username']);
        if (data['profile_picture'] !== null) {
          this.sharedService.setPicture(data['profile_picture']);
        }
      },
      (error) => {
        console.log(error);
      }
    );
    this.breadcrumbHeader = this.activatedRoute.snapshot.children[0].data[
      'title'
    ];
  }
  onActivate(event) {
    const url = this.router.url;
    const ar = url.split('/');
    const id = ar[ar.length - 1];
    console.log(id);
    this.sharedService.setActiveLink(id);
    this.breadcrumbHeader = this.activatedRoute.snapshot.children[0].data[
      'title'
    ];


    if (url.includes('consultation?consultationId')) {
      const routerurl = this.router.url;
      const ar = routerurl.split('=');
      console.log(ar);
      const id = ar[ar.length - 1];
      console.log(id)
      this.teleConsultService
        .getConsultationData(id)
        .subscribe((data) => {
          const patientId = data['patient_uuid'];
          localStorage.setItem('consult_patient_id', patientId);
          this.getPatientProfile(patientId)
        }, error => { })
    }
  }
  getPatientProfile(id) {
    this.doctorService.getPatientProfile(id).subscribe(
      (data) => {
        this.personalInfo = data['user'];
        this.patientId= data['med_patient_id'];
        console.log(this.personalInfo);
        if (this.personalInfo['profile_picture']) {
          this.profilePicture = this.personalInfo['profile_picture'];
        }
        setTimeout(() => {
          this.showProfilePic = true;
        }, 1000);
      },
      (error) => {
        this.showProfilePic = true;
        console.log(error);
      }
    );
  }
}
