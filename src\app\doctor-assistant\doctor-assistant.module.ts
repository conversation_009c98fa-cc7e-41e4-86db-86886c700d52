import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ModalModule } from 'ngb-modal';
import { CKEditorModule } from 'ng2-ckeditor';
import { DoctorAssistantComponent } from './doctor-assistant.component';
import { AssistantProfileComponent } from './assistant-profile/assistant-profile.component';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DoctorAssistantService } from './doctor-assistant-service';
import { PracticeLocationComponent } from './practice-location/practice-location.component';
import { BankAccountComponent } from './bank-account/bank-account.component';
import { DoctorConsultingHoursComponent } from './practice-location/doctor-consulting-hours/doctor-consulting-hours.component';
import { ManageAppointmentsComponent } from './assistant-dashboard/manage-appointments/manage-appointments.component';
import { AssistantDashboardComponent } from './assistant-dashboard/assistant-dashboard.component';
import { DatepickerModule, BsDatepickerModule, BsDatepickerConfig } from 'ngx-bootstrap/datepicker';

@NgModule({
  declarations: [
    DoctorAssistantComponent,
    AssistantProfileComponent,
    PracticeLocationComponent,
    BankAccountComponent,
    DoctorConsultingHoursComponent,
    ManageAppointmentsComponent,
    AssistantDashboardComponent
  ],
  imports: [
    CommonModule,
    NgMultiSelectDropDownModule,
    NgSelectModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    BrowserModule,
    BrowserAnimationsModule,
    BsDatepickerModule.forRoot(),
    DatepickerModule.forRoot(),
  ],
  providers: [DoctorAssistantService, { provide: BsDatepickerConfig, useFactory: getDatepickerConfig }],
})
export class DoctorAssistantModule { }
export function HttpLoaderFactory(httpClient: HttpClient) {
  return new TranslateHttpLoader(httpClient);
}
export function getDatepickerConfig(): BsDatepickerConfig {
  return Object.assign(new BsDatepickerConfig(), {
    dateInputFormat: 'DD-MM-YYYY'
  });
}
