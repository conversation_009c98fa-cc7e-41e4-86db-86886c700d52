.vl {
    margin-top: 28px;
    /* width: 10px; */
    margin-left: -40px;
    border-left: 1px solid rgba(0, 0, 0, .125);
    height: 130px;
    padding: 0px;
}

.edit-btn {
    float: right;
}

.time-marg {
    margin-left: -48px;
    /* margin-bottom: -14px; */
}

#text-area {
    height: 100px;
    resize: none;
    align-content: left;
    border-radius: 2px;
}

#text-area:focus {
    outline: none !important;
    border-color: #719ECE;
    box-shadow: 0 0 10px #719ECE;
}

.add-more {
    margin-top: 15px;
    margin-bottom: -5px;
}

.modal-content {
    width: 780px;
    /* height: 320px; */
}

.week-val label {
    padding: 10px;
}

.save-modal-btn {
    margin-top: 18px;
    margin-bottom: 5px;
    text-align: center;
}

.week-val label input {
    margin-right: 3px;
}

label {
    color: #757575;
}

.form-vl {
    margin-top: 28px;
    /* width: 10px; */
    margin-left: -200px;
    border-right: 1px solid rgba(0, 0, 0, .125);
    height: 130px;
    margin-right: 15px;
    /* margin-left: 20px; */
}

.footer-time {
    float: left;
}

.m-btn {
    margin-top: 30px;
}

.rend-form {
    background-color: #DCDCC1;
}

p.edit-link {
    cursor: pointer;
}

.add-more {
    color: #20c0f3;
    cursor: pointer;
}

#no-ch-text {
    color: #495057;
}

.hl {
    margin-top: 0px;
}

.fa-trash-alt {
    color: #d11a2a;
    margin-top: 2px;
    cursor: pointer;
}

.date-label {
    margin-left: 18px;
    margin-right: 10px;
}

.uad {
    background-color: #DCDCC1;
    outline: none;
    width: 100%;
    border: 0px;
    margin-right: -110px;
}

#del-unavail-icon {
    margin-top: 12px;
}

@media only screen and (max-width: 479px) {
    .form-vl {
        display: none !important;
    }
}