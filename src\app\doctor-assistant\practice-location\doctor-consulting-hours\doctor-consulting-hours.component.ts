import { HttpClient } from '@angular/common/http';
import { delay } from 'rxjs/operators';
import {
  Validators,
  FormGroup,
  FormBuilder,
  FormArray,
  FormControl,
} from '@angular/forms';
import { DoctorService } from '../../../doctor/doctor.service';
import { Component, OnInit, Input,OnChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import * as Settings from '../../../config/settings';
import { Router } from '@angular/router';
declare var $: any;
import * as moment from 'moment';


@Component({
  selector: 'app-doctor-consulting-hours',
  templateUrl: './doctor-consulting-hours.component.html',
  styleUrls: ['./doctor-consulting-hours.component.css'],
})
export class DoctorConsultingHoursComponent implements OnInit,OnChanges {
  @Input() selectedLocation: any;
  public consultingForm: FormGroup;
  public daysForm: FormGroup;
  public unavailabilityForm: FormGroup;
  public locations = [];
  public doctor = {};
  public location_data: any = [];
  public todayDate = new Date();
  public dataAvailable = false;
  public editAvailability = true;
  public time = [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60];
  public selectedTime = 30;
  public days = [];
  public key = 'practice_location';
  public formDisable = false;
  public consultationForm: FormGroup;
  public chArray: FormArray;
  public editConsultingHour = false;
  public consultationData = {
    uuid: null,
    time_from: null,
    time_to: null,
    effective_from: '',
    effective_upto: '',
    consultation_duration: null,
  };
  public weekDays = {
    Sunday: false,
    Monday: false,
    Tuesday: false,
    Wednesday: false,
    Thursday: false,
    Friday: false,
    Saturday: false
  };
  public hourData = [];
  public unavailabilityData = [];
  public daysModel = {};
  public unavailabilityDates = {
    uuid:'',
    fromDate:'',
    endDate:''
  };
  public unavailDataAvailable = false;
  @Input() feeDetailAvailable: any;
  public selectedIndex : any;
  public noCreate = false;
  constructor(
    private doctorService: DoctorService,
    private formBuilder: FormBuilder,
    //private notificationService: NotificationService,
    private route: Router,
    private translate: TranslateService,
    private httpClient: HttpClient
  ) { }

  ngOnInit(): void {
    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this.addForm();
    this.formatTodayDate();
    this.createDateTime("12:05:00");
    this.doctorService.getDoctorPracticeLocations().subscribe(
      data => {
        data = data['results'];
        this.location_data.push(Object.values(data));
        this.location_data = this.location_data[0];
        const len = this.location_data.length;
        for (let i = 0; i < len; i++) {
          let data = this.location_data[i];
          if (data['name'] != null) {
            this.locations.push(data);
          }
        }
        this.locations = Object.values(this.locations);
        this.selectedLocation = this.locations[0];
        this.updateLocation(event, this.selectedLocation);
      }
    );
    this.consultingForm = this.formBuilder.group({
      doctor: [this.doctor['uuid'], Validators.required],
      uuid: [null, Validators.required],
      practice_location: [null, Validators.required],
      time_from: [null, Validators.required],
      time_to: [null, Validators.required],
      effective_from: [null, Validators.required],
      effective_upto: [null, Validators.required],
      days_of_the_week: [[], Validators.required],
      consultation_duration: [null, Validators.required],
    });

    this.daysForm = this.formBuilder.group({
      Sunday: [null, Validators.required],
      Monday: [null, Validators.required],
      Tuesday: [null, Validators.required],
      Wednesday: [null, Validators.required],
      Thursday: [null, Validators.required],
      Friday: [null, Validators.required],
      Saturday: [null, Validators.required],
    });
    this.unavailabilityForm = this.formBuilder.group({
      uuid: null,
      practice_location: this.selectedLocation.uuid,
      doctor: this.doctor['uuid'],
      consulting_hours_group: [null, Validators.required],
      start_datetime: [null, Validators.required],
      end_datetime: [null, Validators.required],
    });
    this.doctorService.getDoctorUnavailability().subscribe(
      data => {
        data = data['results'];
         const len = Object.values(data).length;
         for (let i = 0; i < len; i++) {
            let sep_data = data[i];
            this.unavailabilityData.push(sep_data);
        }
      });
      this.doctorService.getDoctorProfile().subscribe(data=>{
        this.doctor = data;
      },
      error=>{
        console.log(error);
      });
      if(this.route.url == '/doctor/practice-locations'){
        this.noCreate = false;
      }
      else{
        this.noCreate = true;
      }
  }

  ngOnChanges(){
    this.updateLocation(event, this.selectedLocation);
  }

  //api calls ends
  submitConsultingForm(id) {
    $('#consultModal').modal('hide');
    let effective_from = moment(this.consultingForm.controls['effective_from'].value).startOf('day').format('YYYY-MM-DD');
    let effective_upto = moment(this.consultingForm.controls['effective_upto'].value).startOf('day').format('YYYY-MM-DD');
    if(id === null){
      this.consultingForm.setValue({
        doctor: this.doctor['uuid'],
        uuid: null,
        time_from: this.consultingForm.controls['time_from'].value,
        time_to: this.consultingForm.controls['time_to'].value,
        effective_from: effective_from,
        effective_upto: effective_upto,
        days_of_the_week: this.days,
        practice_location: this.selectedLocation['uuid'],
        consultation_duration: this.consultingForm.controls[
          'consultation_duration'
        ].value,
      });
      this.doctorService.postConsultingHours(this.consultingForm.value).subscribe(
        data => {

          $('#consultModal').modal('hide');
          //this.notificationService.showSuccess('Posted', 'testing');
          this.updateLocation(event, this.selectedLocation);
        },
        (error) => {
          console.log(error);
        }
      );
    }
    else{
      console.log(this.consultingForm.value);
      this.consultingForm.patchValue({
        uuid: id,
        doctor: this.doctor['uuid'],
        time_from: this.consultingForm.controls['time_from'].value,
        time_to: this.consultingForm.controls['time_to'].value,
        effective_from: effective_from,
        effective_upto: effective_upto,
        days_of_the_week: this.days,
        practice_location: this.selectedLocation['uuid'],
        consultation_duration: this.consultingForm.controls[
          'consultation_duration'
        ].value,
      });
      this.doctorService.patchConsultingHours(this.consultingForm.value,id).subscribe(
        data => {
          // this.notificationService.showSuccess('Posted', 'testing');
          $('#consultModal').modal('hide');
          this.updateLocation(event, this.selectedLocation);
        },
        (error) => {
          console.log(error);
        }
      );
    }
  }

  updateDays() {
    const obj = this.daysForm.value;
    this.days = [];
    this.days = Object.keys(obj).filter((key) => obj[key] === true);
  }

  updateLocation(event, location = null) {
    if (!location) {
      this.selectedLocation = event;
    }
    this.doctorService.getConsultingHours().subscribe(
      data => {
        const dt = this.getDataWithId(data, this.key, this.selectedLocation.uuid);
        if (dt.length == 0) {
          this.dataAvailable = false;
        } else {
          this.emptyFormArray();
          this.consultationForm = this.formBuilder.group({
            chArray: this.formBuilder.array([]),
          });
          this.chArray = this.consultationForm.get('chArray') as FormArray;
          for (let i = 0; i < dt.length; i++) {
            let checkedValue = this.formControlDays(dt[i]['days_of_the_week']);
            const ud = this.getDataWithId(this.unavailabilityData,'consulting_hours_group',dt[i]['uuid']);
            let uad = {};
            if(ud.length <= 0 || ud[0]== undefined){
              uad['uuid'] = null;
              uad['start_datetime'] = null;
              uad['end_datetime'] = null;
              uad['data_available'] = false;
              this.unavailDataAvailable = false;
            }
            else{
              this.unavailDataAvailable = true;
              uad['uuid'] = ud[0].uuid;
              uad['start_datetime'] = this.formatDate(ud[0].start_datetime);
              uad['end_datetime'] = this.formatDate(ud[0].end_datetime);
              uad['data_available'] = true;
            }
            this.chArray.push(
              this.formBuilder.group({
                doctor: this.doctor['uuid'],
                uuid: dt[i]['uuid'],
                time_from: this.formatConsultationData(dt[i]['time_from']),
                time_to: this.formatConsultationData(dt[i]['time_to']),
                effective_from: dt[i]['effective_from'],
                effective_upto: dt[i]['effective_upto'],
                consultation_duration: dt[i]['consultation_duration'],
                sunday: checkedValue['Sunday'],
                monday: checkedValue['Monday'],
                tuesday: checkedValue['Tuesday'],
                wednesday: checkedValue['Wednesday'],
                thursday: checkedValue['Thursday'],
                friday: checkedValue['Friday'],
                saturday: checkedValue['Saturday'],
                ua_uuid: uad['uuid'],
                ua_start_datetime: uad['start_datetime'],
                ua_end_datetime: uad['end_datetime'],
                ua_data_available: uad['data_available']
              })
            );
          }
          this.dataAvailable = true;
          this.formDisable = true;
        }
        this.formDisable = true;
      });
  }

  //formarray
  addForm() {
    this.consultationForm = this.formBuilder.group({
      chArray: this.formBuilder.array([]),
    });
  }

  addConsultingHour() {
    this.chArray = this.consultationForm.get('chArray') as FormArray;
    this.chArray.push(
      this.formBuilder.group({
        time_from: '',
        time_to: '',
        effective_from: '',
        effective_upto: '',
        consultation_duration: '',
      })
    );
  }

  emptyFormArray() {
    const control = this.consultationForm.get('chArray') as FormArray;
    for (let i = control.length - 1; i >= 0; i--) {
      control.removeAt(i);
    }
  }

  formControlDays(days) {
    const daysObj = {
      Sunday: false,
      Monday: false,
      Tuesday: false,
      Wednesday: false,
      Thursday: false,
      Friday: false,
      Saturday: false,
    };
    for (let i = 0; i < days.length; i++) {
      const key = days[i];
      daysObj[key] = true;
    }
    return daysObj;
  }

  editConsultingHours(i,edit:boolean) {
    // $('#consultModal').modal('show');
    if(edit){
      this.editAvailability = false;
      this.editConsultingHour = true;
    }
    else{
      this.editAvailability = true;
    }
    const data = this.consultationForm.controls.chArray['controls'][i].value;
    const time_from = data['time_from'];
    const time_to = data['time_to'];
    const effectivefrom = data['effective_from'];
    const effective_upto = data['effective_upto'];
    const consultation_duration = data['consultation_duration'];
    this.consultationData.uuid = data['uuid'];
    this.consultationData.effective_from = effectivefrom;
    this.consultationData.effective_upto = effective_upto;
    this.consultationData.consultation_duration = consultation_duration;
    this.consultationData.time_from = this.toTwentyFourHours(time_from);
    this.consultationData.time_to = this.toTwentyFourHours(time_to);
    this.daysModel = {
      Sunday: false,
      Monday: false,
      Tuesday: false,
      Wednesday: false,
      Thursday: false,
      Friday: false,
      Saturday: false
    };
    this.daysModel['Sunday'] = data['sunday'];
    this.daysModel['Monday'] = data['monday'];
    this.daysModel['Tuesday'] = data['tuesday'];
    this.daysModel['Wednesday'] = data['wednesday'];
    this.daysModel['Thursday'] = data['thursday'];
    this.daysModel['Friday'] = data['friday'];
    this.daysModel['Saturday'] = data['saturday'];
  }



  //Unavailability
  markUnavailability(i) {
    this.selectedIndex = i;
    this.editAvailability = true;
    const chData = this.consultationForm.controls.chArray['controls'][i].value;
    this.consultationData = chData;
    this.consultationData.time_from = this.toTwentyFourHours(chData['time_from']);
    this.consultationData.time_to = this.toTwentyFourHours(chData['time_to']);
    console.log(this.consultationData,this.toTwentyFourHours(chData['time_from']));
    this.editConsultingHours(i,false);
    const un_dt = this.getDataWithId(this.unavailabilityData,'consulting_hours_group',chData['uuid']) ;
    let data = un_dt[0] || null;
    console.log(chData['ua_uuid']);

    if(chData['ua_uuid'] != null){
      this.unavailabilityForm.setValue({
        uuid: chData['ua_uuid'],
        doctor: this.doctor['uuid'],
        consulting_hours_group:chData['uuid'],
        practice_location: this.selectedLocation['uuid'],
        start_datetime: this.reverseDate(chData['ua_start_datetime']),
        end_datetime: this.reverseDate(chData['ua_end_datetime']),
      });
      console.log(this.unavailabilityForm.value);
      this.editConsultingHour = true;
    }
    else{
      this.unavailabilityDates.fromDate = null;
      this.unavailabilityDates.endDate = null;
      this.editConsultingHour = true;
    }
  }

  saveUnavailability(){
    const uuid = this.unavailabilityForm.controls['uuid'].value;
    const controls = this.chArray.at(this.selectedIndex);
    // console.log(controls);
    if(uuid == null){
      this.unavailabilityForm.patchValue({
        start_datetime: new Date(this.unavailabilityForm.controls['start_datetime'].value),
        end_datetime: new Date(this.unavailabilityForm.controls['end_datetime'].value)
      });
      const data = this.unavailabilityForm.value;
      console.log(uuid);
      this.doctorService.postUnavailability(data).subscribe(
        data=>{
          controls.get('ua_data_available').setValue(true);
          controls.get('ua_uuid').setValue(data['uuid']);
          controls.get('ua_start_datetime').setValue(this.formatDate(data['start_datetime']));
          controls.get('ua_end_datetime').setValue(this.formatDate(data['end_datetime']));
          $('#consultModal').modal('hide');
        }
      );
      } else{
        this.unavailabilityForm.patchValue({
          start_datetime: new Date(this.unavailabilityForm.controls['start_datetime'].value),
          end_datetime: new Date(this.unavailabilityForm.controls['end_datetime'].value)
        });
        const data = this.unavailabilityForm.value;

        this.doctorService.patchUnavailability(uuid,data).subscribe(
          data=>{
            controls.get('ua_start_datetime').setValue(this.formatDate(data['start_datetime']));
            controls.get('ua_end_datetime').setValue(this.formatDate(data['end_datetime']));
            $('#consultModal').modal('hide');
          }
        );
      }
  }

  deleteUnavailability(i){
    const data = this.consultationForm.controls.chArray['controls'][i].value;
    this.doctorService.deleteUnavailabilityData(data['ua_uuid']).subscribe(
      data=>{

        this.consultationForm.controls.chArray['controls'][i].patchValue({
          ua_data_available: false,
          ua_uuid: null
        });
        this.unavailabilityForm.patchValue({
          start_datetime: null,
          end_datetime: null,
        });
      }
    );
  }
  //Unavailability ends

  newConsultingModal() {
    $('#consultModal').modal('show');
    console.log('its working');
    this.editAvailability = false;
    this.daysModel = {
      Sunday: false,
      Monday: false,
      Tuesday: false,
      Wednesday: false,
      Thursday: false,
      Friday: false,
      Saturday: false
    };
    this.consultationData = {
      uuid:null,
      time_from: null,
      time_to: null,
      effective_from: null,
      effective_upto: null,
      consultation_duration: null,
    };
    this.editAvailability = false;
    this.editConsultingHour = false;
  }



  formatTodayDate() {
    this.todayDate = new Date();
    // const month = this.formatDateMonth(todaydate.getMonth());
    // const date = this.formatDateMonth(todaydate.getDate());
    // this.todayDate = todaydate.getFullYear() + '-' + month + '-' + date;
    // console.log(this.todayDate);
  }

  formatDateMonth(val) {
    if (val < 10) {
      const result = '0' + val;
      return result;
    } else {
      return val;
    }
  }

  formatConsultationData(data) {
    let time_data = data.split(":");
    if (parseInt(time_data[0]) < 12) {
      if(parseInt(time_data[0]) == 0){
        time_data[0] = "12";
      }
      time_data = time_data[0] + ':' + time_data[1] + ' AM';
    }
    else if(parseInt(time_data[0]) == 12){
      time_data = time_data[0] + ':' + time_data[1] + ' PM';
    }
    else {
      let convertTime = parseInt(time_data[0]) - 12;
      if(parseInt(time_data[0]) > 9){
        time_data = convertTime.toString() + ':' + time_data[1] + ' PM';
      }
      else{
        time_data = '0'+convertTime.toString() + ':' + time_data[1] + ' PM';
      }
    }
    return time_data;
  }

  createTimeObj(data) {

    const hours = data.getHours();
    const minutes = data.getMinutes();
    const time = hours + ':' + minutes;
    return time;
  }

  getDataWithId(data, key, value) {
    this.hourData = [];
    for (let i = 0, length = data.length; i < length; i++) {
      if (data[i][key] == value) {
        this.hourData.push(data[i]);
      }
    }
    return this.hourData;
  }

  createDateTime(time) {
    let time_arr = time.split(':');
    let date = new Date();
    date.setHours(time_arr[0]);
    date.setMinutes(time_arr[1]);
  }

  toTwentyFourHours(date) {
    const type = date.split(' ');
    const  timeType = type[1];
    if (timeType == 'AM') {
      const time = type[0] + ' AM';
      return time;
    }
    if (timeType == 'PM') {
      const tm = type[0].split(':');
      if(tm[0] == 12){
        const time2 = tm[0] + ':' + tm[1] + ' PM';
        return time2;
      }
      else{
        const time2 = '0'+parseInt(tm[0]) + ':' + tm[1] + ' PM';
        return time2;
      }
    }
  }

  formatDate(date) {
    let d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

    if (month.length < 2)
        month = '0' + month;
    if (day.length < 2)
        day = '0' + day;
    return [ day, month, year].join('/');
  }

  reverseDate(date){
    let d = date.split('/');
    return [ d[2], d[1], d[0]].join('-');
  }

}
