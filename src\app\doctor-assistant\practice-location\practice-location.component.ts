import { ToastrService } from 'ngx-toastr';
import { HttpClient } from '@angular/common/http';
import { delay } from 'rxjs/operators';
import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import {
  FormGroup,
  FormControl,
  FormArray,
  Validators,
  FormBuilder,
} from '@angular/forms';
import { DoctorService } from '../../doctor/doctor.service';
// import { NotificationService } from '../../../../services/notification.service';
import { TranslateService } from '@ngx-translate/core';
import * as Settings from '../../config/settings';

@Component({
  selector: 'app-practice-location',
  templateUrl: './practice-location.component.html',
  styleUrls: ['./practice-location.component.css']
})
export class PracticeLocationComponent implements OnInit {
  practiceLocationList = [];
  showAllForms: boolean;
  locationForm: FormGroup;
  public practiceLocationId: string;
  public practiceType = [
    { name: 'Own Practice', value: 'own_practice' },
    { name: 'Hospital Based', value: 'hospital_based' },
  ];
  public selectedLocation = {
    name: '',
    practice_type: '',
  };
  public selectedLocationCh = {};
  public edit = false;
  public saved = false;
  public create = false;
  public showLocationForm = false;
  public doctor_uuid = '';
  public location_data = false;
  public newlocationForm: FormGroup;
  practice_location: string;
  public feeForm: FormGroup;
  public PracticeLocationList = [
    {
      uuid: '009db27e-e3fe-40de-8006-03f2279a3e14',
      name: 'Murthy Clinic2',
      practice_type: null,
      hospital_uuid: null,
      doctor: 'bf1ef8f0-0375-43c8-a5c4-a4088e6cd5f7',
    },
  ];
  public feesFormFieldValues = {
    practice_location: null,
    doctor: null,
    amount: null,
    effective_from: null,
    effective_upto: null,
  };
  showFeesForm = true;
  public feesList = [];
  feesId = null;
  minDate = new Date();
  maxDate = new Date(2015, 12, 30);
  public feesEditBtnShow = false;
  feesUUId = null;
  feeFormReadyOnly = false;
  cancelBtnShow = false;
  public clinicAddressForm: FormGroup;
  practiceLocation: string;
  public countryList: any = [];
  doctorClinicAddressList: any = [];
  clinicAddressReadOnly: boolean;
  public cancleBtnShow = false;
  public feeDetailAvailable = false;
  constructor(
    private doctorService: DoctorService,
    private _notificationService: ToastrService,
    private translate: TranslateService,
    private httpClient: HttpClient,
    private cd:ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.doctorService.checkProfileCompletion();
    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this.getPracticeLocation();
    this. getCountry();
    this.addFormControl(null);
    this.addFormControlData(null);
    this.getDoctorAddress();
    //this.getFeesDetails();
    this.locationForm = new FormGroup({
      practiceLocation: new FormControl(null),
    });
    // this.createNewLocationForm();
  }
 // tslint:disable-next-line: typedef
 setParacticeLocationId(event) {
    this.selectedLocationCh = event;
    this.practiceLocationId = event.uuid;
    this.practiceLocation = event.uuid;
    let singleLocation = [];
    sessionStorage.setItem('practice_location', `${event.uuid}`);
    const uuid = sessionStorage.getItem('practice_location');
    this.getSelectedLocationData(event);
    this.showLocationForm = false; // creation/edit form
    if (this.doctorClinicAddressList.length > 0) {
      singleLocation = this.doctorClinicAddressList.filter(obj => obj.practice_location === this.practiceLocation);
      if (singleLocation.length > 0) {

        this.addFormControlData(singleLocation[0]);
      } else {
        this.addFormControlData(null);
      }
    }
    else {
      this.addFormControlData(null);
    }

    let singleFees = [];
    if (this.feesList.length > 0) {
      singleFees = this.feesList.filter(obj => obj.practice_location === this.practiceLocation);
      if (singleFees.length > 0) {
        this.addFormControl(singleFees[0]);
        this.feeDetailAvailable = true;
      } else {
        this.addFormControl(null);
      }
    } else {
      this.addFormControl(null);
    }

  }
  getPracticeLocation() {
    this.doctorService.getDoctorPracticeLocations().subscribe(
      (data) => {
        const resp_data = data['results'];
        this.practiceLocationList = Object.values(resp_data);
        if (this.practiceLocationList.length > 0) {
          this.locationForm.controls['practiceLocation'].setValue(
            this.practiceLocationList[0].uuid
            );
          this.setParacticeLocationId(this.practiceLocationList[0]);
          this.selectedLocationCh = this.practiceLocationList[0];
          this.practiceLocation = this.practiceLocationList[0].uuid;
          sessionStorage.setItem(
            'practice_location',
            this.practiceLocationList[0].uuid
          );
          this.selectedLocationCh = this.practiceLocationList[0];
          this.practiceLocationId = this.practiceLocationList[0].uuid;
          this.doctor_uuid = this.practiceLocationList[0].doctor;
          this.getSelectedLocationData(this.practiceLocationList[0]);
          this.showAllForms = true;
          this.location_data = true;
        } else {
          this.showAllForms = false;
          this.location_data = false;
        }
      },
      (err) => {
        console.log(err);
      }
    );
  }

  getSelectedLocationData(event) {
    this.selectedLocation.name = event.name;
    const val = this.practiceType.find(
      ({ value }) => value === event.practice_type
    );
    this.selectedLocation.practice_type = val.name;
  }
  createNewLocationForm() {
    this.showLocationForm = true;
    this.newlocationForm = new FormGroup({
      uuid: new FormControl(null),
      name: new FormControl(null),
      practice_type: new FormControl(null),
    });
    this.edit = false;
    this.create = true;
  }

  postPracticeLocation(data) {
    return this.httpClient
      .post(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/practice_locations/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  createLocation() {
    console.log(this.newlocationForm.value);
    this.postPracticeLocation(this.newlocationForm.value).subscribe(
      (data) => {

        this.getPracticeLocation();
        this.addFormControlData(null);
        this._notificationService.success('Location Created', 'Med.Bot');
        this.showLocationForm = false;
        this.showAllForms = true;
      },
      (error) => {
        console.log(error);
        this._notificationService.error('Sorry!', 'Med.Bot');
      }
    );
  }

  editLocation() {
    this.showLocationForm = true;
    this.edit = true;
    this.create = false;
    const location_id = sessionStorage.getItem('practice_location');
    console.log(location_id);
    const location = this.practiceType.find(
      ({ name }) => name === this.selectedLocation.practice_type
    );
    this.newlocationForm = new FormGroup({
      doctor: new FormControl(this.doctor_uuid),
      uuid: new FormControl(this.practiceLocationId),
      name: new FormControl(this.selectedLocation.name),
      practice_type: new FormControl(location.value),
      location: new FormControl(location_id),
    });
  }

  cancelLocation() {
    this.showLocationForm = false;
  }

  patchPracticalLocation(data,uuid) {
    return this.httpClient
      .patch(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/practice_locations/${uuid}/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  saveEditLocation() {
    const uuid = this.newlocationForm.controls['uuid'].value;
    this.patchPracticalLocation(this.newlocationForm.value,uuid).subscribe(
      (data) => {

        this.showLocationForm = false;
        this._notificationService.success('Location Updated', 'Med.Bot');
      },
      (error) => {
        //console.log(error);
        console.log(error);
        this._notificationService.error('Sorry!', 'Med.Bot');
      }
    );
  }

  submitNewLocationForm(type) {
    if (type == 'create') {
      this.createLocation();
    }
    if (type == 'edit') {
      this.saveEditLocation();
    }
  }
  // feess
  addFormControl(data) {
    if (data === null) {
      this.practiceLocation = sessionStorage.getItem('practice_location');
      this.feeForm = new FormGroup({
        practice_location: new FormControl(
          this.practice_location,
          Validators.required
        ),
        effective_upto: new FormControl(this.minDate, Validators.required),
        amount: new FormControl('', Validators.required),
        effective_from: new FormControl('', Validators.required),
        doctor: new FormControl(),
      });
      this.feesEditBtnShow = false;
      this.feeFormReadyOnly = false;
      this.feesId = null;
    } else {
      this.feesEditBtnShow = true;
      this.feeFormReadyOnly = true;
      this.feesId = data.uuid;
      this.feeForm = new FormGroup({
        practice_location: new FormControl(
          data.practice_location,
          Validators.required
        ),
        effective_upto: new FormControl(
          data.effective_upto,
          Validators.required
        ),
        amount: new FormControl(data.amount, Validators.required),
        effective_from: new FormControl(
          data.effective_from,
          Validators.required
        ),
        doctor: new FormControl(data.doctor),
      });
    }
  }

  updateDoctorFessDetail(data, id) {
    if (id === null) {
      return this.httpClient
        .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    } else {
      console.log(data['effective_upto']);
      const editDate = { effective_upto: data['effective_upto'] };
      return this.httpClient
        .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/${id}/`, editDate)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
  }

  saveFees() {
    this.practiceLocation = sessionStorage.getItem('practice_location');
    console.log(' this.practiceLocation ', this.practiceLocation );
    this.feeForm.controls['practice_location'].setValue( this.practiceLocation);
    const paraticeLocationId = this.feeForm.controls['practice_location'].value;
    const data = this.practiceLocationList.filter(
      obj => obj.uuid === paraticeLocationId
    );
    //this.selectedLocationCh = null;
    this.feeForm.controls['doctor'].setValue(data[0].doctor);
    this.updateDoctorFessDetail(this.feeForm.value, this.feesId).subscribe(
      data => {
        this.feesList.push(data);
        this.feesEditBtnShow = true;
        this.feeFormReadyOnly = true;
        this.cancelBtnShow = false;
        this.feeDetailAvailable = true;
        console.log('saveee');
        const updateLoc = this.practiceLocationList.filter(
          obj => obj.uuid === paraticeLocationId
        );
        console.log('hoo',updateLoc[0]);
        this.selectedLocationCh = this.PracticeLocationList[0];
        this.selectedLocationCh = updateLoc[0];
        this._notificationService.success('Fees Updated', 'Med.Bot');
      },
      (err) => {
        console.log(err);
        this._notificationService.error(
          'Fees Updation Faild',
          'Med.Bot'
        );
      }
    );
  }
  editFees() {
    this.feesEditBtnShow = false;
    this.cancelBtnShow = true;
  }
  CancelFees() {
    const singleFees = this.feesList.filter(
      (obj) => obj.practice_location === this.practiceLocation
    );
    if (singleFees.length > 0) {
      this.addFormControl(singleFees[0]);
    } else {
      this.addFormControl(null);
    }
    this.cancelBtnShow = false;
    this.feesEditBtnShow = true;
  }

  // getFeesDetails() {
  //   this.doctorService.getDoctorFees().subscribe(
  //     (data) => {
  //       data = data['results'];
  //
  //       this.feesList = Object.values(data);
  //       if (this.feesList.length > 0) {
  //         const singleFees = this.feesList.filter(
  //           (obj) => obj.practice_location === this.practiceLocation
  //         );
  //         if (singleFees.length > 0) {
  //           this.addFormControl(singleFees[0]);
  //         } else {
  //           this.addFormControl(null);
  //         }
  //       }
  //     },
  //     (err) => {
  //       console.log(err);
  //     }
  //   );
  // }


  addFormControlData(data) {
    if (data === null) {
      this.practiceLocation = sessionStorage.getItem('practice_location');
      this.clinicAddressReadOnly = false;
      this.cancleBtnShow = false;
      this.clinicAddressForm = new FormGroup({
        uuid: new FormControl(null),
        address_type: new FormControl('Clinic'),
        practice_location: new FormControl(this.practiceLocation),
        line_1: new FormControl(),
        line_2: new FormControl(),
        city_town_village: new FormControl(),
        district: new FormControl(),
        taluk: new FormControl(),
        state: new FormControl(),
        country: new FormControl(),
        postal_code: new FormControl(),
      });
    } else {
      this.clinicAddressReadOnly = true;
      this.cancleBtnShow = false;
      this.clinicAddressForm = new FormGroup({
        uuid: new FormControl(data.uuid),
        address_type: new FormControl(data.address_type),
        practice_location: new FormControl(data.practice_location),
        line_1: new FormControl(data.line_1),
        line_2: new FormControl(data.line_2),
        city_town_village: new FormControl(data.city_town_village),
        district: new FormControl(data.district),
        taluk: new FormControl(data.taluk),
        state: new FormControl(data.state),
        country: new FormControl(data.country),
        postal_code: new FormControl(data.postal_code),
      });
    }
  }

  editClinicAddress() {
    this.clinicAddressReadOnly = false;
    this.cancleBtnShow = true;

  }

  saveAddress(data) {
    if (data.uuid === null) {
      return this.httpClient
        .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/addresses/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    } else {
      return this.httpClient
        .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/addresses/${data.uuid}/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
  }


  saveClinicAddress() {
    this.practiceLocation = sessionStorage.getItem('practice_location');
    this.clinicAddressForm.controls['practice_location'].setValue(this.practiceLocation);
    const practiceLocationVale = this.clinicAddressForm.controls['practice_location'].value;
    if (practiceLocationVale !== null) {
      this.saveAddress(this.clinicAddressForm.value).subscribe(
        (data) => {
          this.doctorClinicAddressList.push(data);
          data = [data];
          const addressList = Object.values(data);
          const singleLocation = addressList.filter(obj => obj.practice_location === this.practiceLocation);
          this.addFormControlData(singleLocation[0]);
          this._notificationService.success(
            'Clinic Address Added Successfully',
            'Med.Bot'
          );
        },
        (err) => {
          console.log(err);
          this._notificationService.error(
            'Clinic Address Updation Failed',
            'Med.Bot'
          );
        }
      );
    } else {
      console.log('Null value');
      this._notificationService.error(
        'Clinic Address Updation Failed',
        'Med.Bot'
      );
    }

  }
  cancelClinicAddress() {
    this.cancleBtnShow = false;
    if (this.doctorClinicAddressList.length > 0) {
      const singleLocation = this.doctorClinicAddressList.filter(obj => obj.practice_location === this.practiceLocation);
      if (singleLocation.length > 0) {
        this.addFormControlData(singleLocation[0]);
      } else {
        this.addFormControlData(null);
      }

    } else {
      this.addFormControlData(null);
    }

  }

  getAddressDetail() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/addresses/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorAddress() {
    this.getAddressDetail().subscribe(
      (data) => {
        data = data[`results`];
        const doctorAddress = Object.values(data);
        this.doctorClinicAddressList = doctorAddress.filter(obj => obj.address_type === 'Clinic' && obj.practice_location !== null);
        if (this.doctorClinicAddressList.length > 0) {
          const singleLocation = this.doctorClinicAddressList.filter(obj => obj.practice_location === this.practiceLocation);
          if (singleLocation.length > 0) {
            this.addFormControlData(singleLocation[0]);
          } else {
            this.addFormControlData(null);
          }

        }
      },
      (err) => {
        console.log('err', err);
      }
    );
  }
  getCountry() {
    this.doctorService.getCountryDetail().subscribe(
      data => {
        this.countryList = data; //Object.values(data);
      },
      error => {
        console.log(error);
      }
    );
  }
}

