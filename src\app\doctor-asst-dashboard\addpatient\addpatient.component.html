<div class="container-fluid">
  <div class="breadcrumb-bar banner">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col-md-12 col-12">
          <nav aria-label="breadcrumb" class="page-breadcrumb">
            <ol class="breadcrumb">
              <li class="breadcrumb-item"><a href="javascript:void(0);">{{'DoctorAssistant DashBoard'|translate}}</a>
              </li>
              <li #listHeader class="breadcrumb-item active" aria-current="page">{{'Dashboard' |translate}}</li>
            </ol>
          </nav>
          <h2 #header class="breadcrumb-title">{{'DoctorAssistant DashBoard' |translate}}</h2>
        </div>
      </div>
    </div>
  </div>
  <div class="card mt-5">
    <div class="card-body">
      <div class="col-md-12">
        <button class="btn btn-info mx-2 btn-height" id="add-patient" (click)="addAdmin()">Add Patient</button>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="tab-content pt-0">
      <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded my-2 mx-1">
        <li class="nav-item">
          <a class="nav-link active" href="#patient-test" data-toggle="tab">Patient List
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#doctor-list" data-toggle="tab">Doctor List
          </a>
        </li>
      </ul>

      <div class="tab-content">
        <!--patient list-->
        <div class="tab-pane show active" id="patient-test">
          <app-patient-list></app-patient-list>
        </div>

        <!-- doctor list start -->
        <div class="tab-pane" id="doctor-list">
          <div class="tab-pane show active" id="doctor-list">
            <div *ngIf="doctorCount>0" class="container-fluid">
              <input type="text" id="name" style="cursor:pointer" class="form-control form-control-height col-md-3"
                [(ngModel)]="searchDoctorName" placeholder="Name">

              <div class="text-right col-md-3">
                <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-0" (click)="searchDoctor()">Search
                  Doctors</button>
              </div>
            </div>

            <div>
              <div *ngIf="!doctorisLoading">
                <div class="col-md-12 float-right pagination-zindex">
                  <div class="float-right mt-3">
                    <nav aria-label="Page navigation example" *ngIf="this.doctorTotalPage > 1">
                      <ul class="pagination">
                        <li class="page-item" (click)="firstDoctorPageList()"
                          [ngClass]="{ 'disabled-pagination': doctorCurrentPage === 1 }">
                          <a class="page-link">&lt;&lt;</a>
                        </li>
                        <li class="page-item" (click)="previousDoctorPageList()"
                          [ngClass]="{ 'disabled-pagination': doctorCurrentPage === 1 }">
                          <a class="page-link">&lt;</a>
                        </li>
                        <li class="page-item">
                          <a class="page-link">page &nbsp;{{ doctorCurrentPage }}&nbsp;of&nbsp; {{ doctorTotalPage
                            }}</a>
                        </li>
                        <li class="page-item" (click)="nextDoctorPageList()"
                          [ngClass]="{'disabled-pagination': doctorurrentPage === doctorTotalPage}">
                          <a class="page-link">&gt;</a>
                        </li>
                        <li class="page-item" (click)="lastDoctorPageList()"
                          [ngClass]="{'disabled-pagination': doctorCurrentPage === doctorTotalPage}">
                          <a class="page-link">&gt;&gt;</a>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
                <div class="col-md-10">
                  <h4 class="text-success">Doctors List</h4>
                  <h4>Totaldoctor {{ doctorCount }}</h4>
                  <div class="card card-table mb-0">
                    <div class="card-body">
                      <div class="table-responsive" *ngIf="doctorList.length > 0">
                        <table class="table table-hover table-center mb-0">
                          <thead>
                            <tr>
                              <th>#</th>
                              <th>Doctor Name</th>
                              <th>Speciality</th>
                              <th>System of Medicine</th>
                              <th>Schedule</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let data of doctorList; let i = index">
                              <th scope="row"><a>{{ doctorSerialNumber + i + 1 }}</a></th>
                              <td><a class="text-primary">{{ data.username }}</a></td>
                              <td>
                                <ng-container *ngFor="let speciality of data.speciality;">
                                  {{speciality.value}}
                                </ng-container>
                              </td>
                              <td>{{data.som}}</td>
                              <td><button class="btn-primary btn-md mr-2"
                                  (click)="addSchedule(data.uuid)">Schedule</button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div class="centered" *ngIf="doctorisLoading">
          <app-loading-spinner></app-loading-spinner>
          </div> -->
              </div>
            </div>
          </div>

        </div>
      </div>


      <!-- approved Tab -->
    </div>
  </div>

  <!-- Button trigger modal -->


  <!-- Modal -->
  <div class="modal fade" id="assistantModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Add Doctor Assistant</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <h4 translate>Doctor List</h4>
            <select class="form-control select" name="doctor" id="doctor"
              (change)="getAssisatantDatails($event.target.value)">
              <option>Select</option>
              <option *ngFor="let assist of assistantList" value={{assist.uuid}}> {{assist.username}}</option>

            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          <button type="button" class="btn btn-primary" (click)="assignDoctorAssistant()">Add Assistant</button>
        </div>
      </div>
    </div>
  </div>