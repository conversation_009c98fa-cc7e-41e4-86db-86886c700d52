<h3>Bank Accounts</h3>
<div class="card" *ngIf="!loading">
    <div class="card-body">
        <form [formGroup]="bankAccountDetailForm">
            <div formArrayName="bankAccountDetailArray">
                <ng-container *ngFor="let data of this.bankAccountDetailForm.controls.bankAccountDetailArray.value; let i=index; trackBy:trackFn" [formGroupName]="i">
                    <h4 class="card-title" id="accnt-det">Account Detail {{i+1}}
                    </h4>
                    <i id="editIcon{{i}}" *ngIf="!data['edit']" (click)="editBankAccountDetail(i)" class="fa fa-edit"></i>
                    <i id="delete{{i}}" *ngIf="data['edit'] && !newForm" (click)="removeBankAccount(i)" class="fa fa-trash-alt" aria-hidden="true"></i>
                    <div class="row form-row">
                        <input type="text" class="form-control" formControlName="uuid"  maxlength="50" hidden>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Account Name<span class="text-danger">*</span></label>
                                <input id="accnt-name" type="text" class="form-control" formControlName="account_name" pattern="[a-zA-Z ]*" (input)="validation($event,i)" maxlength="50" required autocomplete="off" >
                                <span *ngIf="accntNameError" class="text-danger">Alphabets only allowed!</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Account Number<span class="text-danger">*</span></label>
                                <input id="accnt-num" type="text" class="form-control" formControlName="account_number" pattern="[0-9 ]*" (input)="validation($event,i)" maxlength="50" required autocomplete="off">
                                <span *ngIf="accntNumberError" class="text-danger">Numbers only allowed!</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Account Type<span class="text-danger">*</span></label>
                                <ng-select id="accnt-type" formControlName="account_type" [items]="accountTypes" [searchable]="false" bindLabel="accountTypes" placeholder="Select" required>
                                </ng-select>
                            </div>
                        </div>
                    </div>
                    <div class="row form-row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Bank Name<span class="text-danger">*</span></label>
                                <input id="bank-name" type="text" class="form-control" formControlName="bank_name" pattern="[a-zA-Z ]*" (input)="validation($event,i)" maxlength="50" required autocomplete="off">
                                <span *ngIf="bankNameError" class="text-danger">Alphabets only allowed!</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Branch Name<span class="text-danger">*</span></label>
                                <input id="branch-name" type="text" class="form-control" formControlName="branch_name" pattern="[a-zA-Z ]*" (input)="validation($event,i)" maxlength="50" required autocomplete="off">
                                <span *ngIf="branchNameError" class="text-danger">Alphabets only allowed!</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>IFSC Code<span class="text-danger">*</span></label>
                                <input type="text" id="ifsc-code" class="form-control" formControlName="ifsc_code" pattern="[a-zA-Z0-9 ]*" (input)="validation($event,i)" maxlength="50" required autocomplete="off" >
                                <span *ngIf="ifscCodeError" class="text-danger">No special characters only allowed!</span>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="data['edit']" id="edit{{i}}" class="form-group dsp">
                        <button id="ba-save" type="submit" (click)="saveAccountDetail(i)" class="btn btn-primary save-btn" [disabled]="!bankAccountDetailArray.at(i).valid">Save</button>
                        <button id="ba-canc" type="button" (click)="cancelEdit(i)" class="btn btn-secondary cancel-btn">Cancel</button>
                    </div>
                </ng-container>
            </div>
        </form>
        <div *ngIf="showAddMore && bankAccountList.length===0" class="add-more">
            <p id="add-ba" (click)="createBankAccountDetailForm(null)" ><i class="fa fa-plus-circle"></i> Add Bank Account</p>
        </div>
    </div>
</div>

<div *ngIf="loading">
  <app-loading-spinner></app-loading-spinner>
</div>
