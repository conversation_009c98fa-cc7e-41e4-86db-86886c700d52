import { DoctorService } from './../doctor.service';
import { ToastrService } from 'ngx-toastr';
//import { NotificationService } from './../../../services/notification.service';
import { delay } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { FormBuilder, FormGroup, Validators, FormArray, FormControl } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import * as Settings from '../../config/settings';

@Component({
  selector: 'app-doctor-bank-accounts',
  templateUrl: './doctor-bank-accounts.component.html',
  styleUrls: ['./doctor-bank-accounts.component.css']
})
export class DoctorBankAccountsComponent implements OnInit {
  public bankAccountDetailForm: FormGroup;
  public bankAccountDetailArray: FormArray;
  public edit = false;
  public newForm = false;
  public showAddMore = true;
  public bankAccountList = [];
  public accountTypes = ['Savings Account', 'Current Account'];
  public doctor = {};
  public loading= false;
  public accntNameError = false;
  public accntNumberError = false;
  public bankNameError = false;
  public branchNameError = false;
  public ifscError = false;
  constructor(
    private formBuilder: FormBuilder,
    private httpClient: HttpClient,
    private doctorService: DoctorService,
    private notificationService: ToastrService
  ) { }

  ngOnInit(): void {
    this.doctorService.checkProfileCompletion();
    this.addArrayForm();
    this.loadBankData();
  }

  // reload(){
  //   window.location.reload()
  //   console.log("reload works");
  // }

  addArrayForm() {
    this.bankAccountDetailForm = this.formBuilder.group({
      bankAccountDetailArray: this.formBuilder.array([]),
    });
  }

  trackFn(index: any) {
    return index;
  }



  createBankAccountDetailForm(data: Object) {
    this.bankAccountDetailArray = this.bankAccountDetailForm.get('bankAccountDetailArray') as FormArray;
    if (data == null) {
      this.bankAccountDetailArray.push(
        this.formBuilder.group({
          uuid: null,
          account_type: [null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]],
          account_name: [null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]],
          account_number: [null, [Validators.required, Validators.maxLength(50), Validators.pattern('[0-9 ]*')]],
          bank_name: [null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]],
          branch_name: [null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]],
          ifsc_code: [null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9 ]*')]],
          edit: true,
          // deleted: false,
        })
      );
      this.showAddMore = false;
      this.newForm = true;
    }
    else {
      this.bankAccountDetailArray.push(
        this.formBuilder.group({
          uuid: data['uuid'],
          account_type: [{ value: data['account_type'], disabled: true }, [Validators.required, Validators.maxLength(50), Validators.pattern('.*\\S.*[a-zA-Z]')]],
          account_name: [{ value: data['account_name'], disabled: true }, [Validators.required, Validators.maxLength(50), Validators.pattern('.*\\S.*[a-zA-Z]')]],
          account_number: [{ value: data['account_number'], disabled: true }, [Validators.required, Validators.maxLength(50), Validators.pattern('.*\\S.*[0-9]')]],
          bank_name: [{ value: data['bank_name'], disabled: true }, [Validators.required, Validators.maxLength(50), Validators.pattern('.*\\S.*[a-zA-Z]')]],
          branch_name: [{ value: data['branch_name'], disabled: true }, [Validators.required, Validators.maxLength(50), Validators.pattern('.*\\S.*[a-zA-Z]')]],
          ifsc_code: [{ value: data['ifsc_code'], disabled: true }, [Validators.required, Validators.maxLength(50), Validators.pattern('.*\\S.*[A-Z0-9]')]],
          edit: false,
          // deleted:false,
        })
      );
    }
  }

  removeBankAccount(i) {
    const data = this.bankAccountDetailForm.get('bankAccountDetailArray').value[i];
    const uuid = data['uuid'];
    if (uuid) {
      this.doctorService.deleteBankAccount(uuid).subscribe(
        data => {
          this.emptyFormArray
          this.addArrayForm();
          this.loadBankData();
          // this.bankAccountDetailArray.at(i).get('deleted').setValue(true);
          if(this.bankAccountList.length===0){
            this.showAddMore = true;
          }
          this.notificationService.success('Bank Account Deleted', 'Med.Bot');
        },
        error => {
          console.log(error);
          this.notificationService.error('Bank Account Deletion Failed', 'Med.Bot');
        }
      );
    }
    else {
      this.bankAccountDetailArray.removeAt(i);
      if(this.bankAccountList.length===0){
        this.showAddMore = true;
      }
    }
  }





  saveAccountDetail(i) {
    const data = this.bankAccountDetailForm.get('bankAccountDetailArray').value[i];
    const api_data = {
      'account_type': data['account_type'],
      'account_name': data['account_name'],
      'account_number': data['account_number'],
      'bank_name': data['bank_name'],
      'branch_name': data['branch_name'],
      'ifsc_code': data['ifsc_code']
    };
    if (data['uuid']) {
      this.doctorService.updateAccountDetail(api_data, data['uuid']).subscribe(
        data => {
          this.bankAccountList = data['results'];
          const control = this.bankAccountDetailArray.at(i);
          control.get('edit').setValue(false);
          control.get('account_type').disable();
          control.get('account_name').disable();
          control.get('account_number').disable();
          control.get('bank_name').disable();
          control.get('branch_name').disable();
          control.get('ifsc_code').disable();
          // this.showAddMore = true;
          this.notificationService.success('Bank Account Added', 'Med.Bot');
          localStorage.setItem('bank', 'true');
        },
        error => {
          console.log(error);
          this.notificationService.error('Sorry', 'Med.Bot');
        }
      );
    }
    else {
      this.newForm = false;
      this.doctorService.updateAccountDetail(api_data, null).subscribe(
        data => {
          this.bankAccountList = data['results'];
          const control = this.bankAccountDetailArray.at(i);
          control.get('uuid').setValue(data['uuid']);
          control.get('edit').setValue(false);
          control.get('account_type').disable();
          control.get('account_name').disable();
          control.get('account_number').disable();
          control.get('bank_name').disable();
          control.get('branch_name').disable();
          control.get('ifsc_code').disable();
          // this.showAddMore = true;
          this.notificationService.success('Bank Account Added', 'Med.Bot');
          localStorage.setItem('bank', 'true');
        },
        error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }

  }

  emptyFormArray() {
    const control = this.bankAccountDetailForm.get(
      'bankAccountDetailArray'
    ) as FormArray;
    for (let i = control.length - 1; i >= 0; i--) {
      control.removeAt(i);
    }
  }

  editBankAccountDetail(i: any) {
    this.newForm = false;
    const control = this.bankAccountDetailArray.at(i);
    control.get('edit').setValue(true);
    control.get('account_type').enable();
    control.get('account_name').enable();
    control.get('account_number').enable();
    control.get('bank_name').enable();
    control.get('branch_name').enable();
    control.get('ifsc_code').enable();
    this.showAddMore = false;
  }

  cancelEdit(i: any) {
    const control = this.bankAccountDetailArray.at(i);
    const uuid = control.get('uuid').value;
    if (uuid == null) {
      this.removeBankAccount(i);
    }
    else {
      const control = this.bankAccountDetailArray.at(i);
      control.get('edit').setValue(false);
      control.get('account_type').setValue(this.bankAccountList[i]['account_type']);
      control.get('account_name').setValue(this.bankAccountList[i]['account_name']);
      control.get('account_number').setValue(this.bankAccountList[i]['account_number']);
      control.get('bank_name').setValue(this.bankAccountList[i]['bank_name']);
      control.get('branch_name').setValue(this.bankAccountList[i]['branch_name']);
      control.get('ifsc_code').setValue(this.bankAccountList[i]['ifsc_code']);
      control.get('account_type').disable();
      control.get('account_name').disable();
      control.get('account_number').disable();
      control.get('bank_name').disable();
      control.get('branch_name').disable();
      control.get('ifsc_code').disable();
    }
    if(this.bankAccountList.length===0){
      this.showAddMore = true;
    }

  }

  loadBankData() {
    this.loading= true;
    this.doctorService.getBankAccountDetails().subscribe(
      data => {
        this.bankAccountList = data['results'];
        if(this.bankAccountList.length >0){
          this.showAddMore = false;
        }else{
          this.showAddMore = true;
          console.log("add bank account detail");
          this.notificationService.warning(`Please add your bank account`, 'Med.Bot');

        }
        const banks = this.bankAccountList;
        for (let i = banks.length; i > 0; i--) {
          this.createBankAccountDetailForm(banks[i - 1]);
        }
        this.loading= false;
      },error=>{
          this.loading= false;
          console.log(error);
          const status = error['status'];
          if(status == 400){
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          else{
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
      }
    );
  }

  validation(event,i){
    let id  = event.srcElement.id;
    console.log(id);
    const control = this.bankAccountDetailArray.at(i);
    if(id == "accnt-name"){
      if(control.get('account_name').errors){
        this.accntNameError = true;
      }
      else{
        this.accntNameError = false;
      }
    }
    if(id == "accnt-num"){
      if(control.get('account_number').errors){
        this.accntNumberError = true;
      }
      else{
        this.accntNumberError = false;
      }
    }
    if(id == "bank-name"){
      if(control.get('bank_name').errors){
        this.bankNameError = true;
      }
      else{
        this.bankNameError = false;
      }
    }
    if(id == "branch-name"){
      if(control.get('branch_name').errors){
        this.branchNameError = true;
      }
      else{
        this.branchNameError = false;
      }
    }
    if(id == "ifsc-code"){
      if(control.get('ifsc_code').errors){
        this.ifscError = true;
      }
      else{
        this.ifscError = false;
      }
    }

  }
}

