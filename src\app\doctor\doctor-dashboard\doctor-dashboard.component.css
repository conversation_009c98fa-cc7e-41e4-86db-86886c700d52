/* .appt-time {
    text-align: center;
} */

.modal-img {
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 30%;
}

#no-data,
#no-msg-data {
    margin: 5px;
    text-align: center;
}

.ms {
    display: inline;
}

.ms-btn {
    float: right;
}

.ms-tab {
    margin-top: 25px;
}

.db-ch-title {
    margin-top: 15px;
}

.img-fluid {
    cursor: pointer;
}

hr {
    margin-left: -20px;
    margin-right: -20px;
}

.fas {
    font-size: 16px !important;
    cursor: pointer;
}

.app-btn {
    margin: 5px;
}

#add-notes {
    color: #20c0f3;
    cursor: pointer;
}

#practice-location-ms {
    margin-bottom: 5px;
    width: 20%;
}

textarea {
    width: 100%;
}

.loc-label {
    float: right;
    margin: 5px;
}

#confirmTitle {
    text-align: center;
}

.btn-md.mx-3 {
    width: 145px;
}

.other-activities {
    margin-top: 25px;
}

.h-btn {
    height: 60px;
    margin-bottom: 10px;
}

.chk {
    float: right;
}


.hd-inl.txt {
    margin-left: 10px;
    margin-bottom: 0px;
}



.chk-bx {
    float: left;
    margin-right: 10px;
    margin-top: 4px;
}

th {
    text-align: center;
}

#avail-checkbox {
    margin-top: 5px;
}

.en-size {
    font-size: 1.5em;
}

.nm-size {
    font-size: 1em;
}

.sm-size {
    font-size: 0.8em;
}

.largerCheckbox {
    width: 20px;
    height: 20px;
}


/* tbody {
    pointer-events: none;
} */

.btn-msg {
    cursor: pointer !important;
}

.btn-flw-up {
    pointer-events: none;
}

.admin-det p {
    margin-bottom: 0px;
}

td.text-warning {
    cursor: pointer;
}
.disabled-pagination{
  color: darkgray !important;
  pointer-events: none !important;
}
.fifteen_chars{
  display: block;
  white-space: nowrap;
  width: 12em;
  overflow: hidden;
  text-overflow: ellipsis;
}
.bg-danger-light {
  background-color: rgba(242,17,54,.12)!important;
  color: #e63c3c!important;
}
.diable-btn-danger {
  background-color: rgba(169, 169, 169, 0.842)!important;
  color:#000000!important;
}
.bg-success-light{
  background-color: rgba(15, 183, 107, 0.12) !important;
  color: #26af48 !important;
}
.circle-bar-top{
  margin-top: 8px !important;
}
.appointment-tabs{
  margin-top: 15px !important;;
}
@media only screen and (max-width: 600px) {
  .txt-size{
    font-size: small;
  }
  .circle_position{
    margin-left: 0%
  }
  .hd-inl {
    display: block;
}
.chk {
  display: block;
}
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .txt-size{
    font-size: small;
  }
  .circle_position{
    margin-left: 0%
  }
  .hd-inl {
    display: block;
}
.chk {
  display: block;
}
.h5, h5 {
  font-size: 0.85rem !important;
}
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .txt-size{
    font-size: medium;
  }
  .pager_position{

  }
  .circle_position{
    margin-left: 0%
  }
  .hd-inl {
    display: block;
}
.chk {
  display: block;
}
.h5, h5 {
  font-size: 1rem !important;
}
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .txt-size{
    font-size: medium;
  }
  .pager_position{

  }
  .tab_pager_position{

  }
  .circle_position{
    margin-left: 10%
  }
  .hd-inl {
    display: inline;
}
.chk {
  display: inline;
}
.h5, h5 {
  font-size: 1rem ;
}
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .txt-size{
    font-size: large;
  }
  .pager_position{
    margin-top: -72px;
  }
  .tab_pager_position{
    float:  right !important;
  }
  .circle_position{
    margin-left: 18%
  }
  .hd-inl {
    display: inline;
}
.chk {
  display: inline;
}
.h5, h5 {
  font-size: 1.25rem;
}
}
