<!-- Page Content -->
<!-- <div class="content"> -->
<!-- <div class="container-fluid"> -->
<div *ngIf="isLoading">
  <app-loading-spinner></app-loading-spinner>
</div>
<div *ngIf="!isLoading">
  <div class="row">
    <div class="col-md-12">
      <div class="card dash-card">
        <div class="card-body">
          <div class="row">
            <div class="col-md-12 col-lg-3 col-md-3">
              <div class="dash-widget dct-border-rht">
                <div class="circle-bar circle-bar1">
                  <div class="circle-graph1" data-percent="75">
                    <img src="../../../assets/img/appointments-today.png" class="img-fluid" alt="patient">
                  </div>
                </div>
                <div class="dash-widget-info">
                  <h6>Appointments Today</h6>
                  <h3>
                    <!-- {{completedAppointmentsCount+startedAppointmentsCount+pendingAppointmentsCount+missedAppointmentsCount}} -->
                    {{todayAppointmentsCount}}
                  </h3>

                </div>
              </div>
            </div>

            <div class="col-md-12 col-lg-3 col-md-3">
              <div class="dash-widget dct-border-rht">
                <div class="circle-bar circle-bar2">
                  <div class="circle-graph2" data-percent="65">
                    <img src="../../../assets/img/icon-01.png" class="img-fluid" alt="Patient">
                  </div>
                </div>
                <div class="dash-widget-info">
                  <h6>Appointments Completed Today</h6>
                  <h3> {{ completedAppointmentsCount }}</h3>
                </div>
              </div>
            </div>


            <div class="col-md-12 col-lg-3 col-md-3">
              <div class="dash-widget dct-border-rht">
                <div class="circle-bar circle-bar3">
                  <div class="circle-graph3" data-percent="65">
                    <img src="../../../assets/img/icon-02.png" class="img-fluid" alt="Patient">
                  </div>
                </div>
                <div class="dash-widget-info">
                  <h6>Pending Appointments Today</h6>
                  <h3>{{pendingAppointmentsCount}}</h3>
                </div>
              </div>
            </div>
            <div class="col-md-12 col-lg-3 col-md-3">
              <div class="dash-widget">
                <div class="circle-bar circle-bar4">
                  <div class="circle-graph4" data-percent="50">
                    <img src="../../../assets/img/icon-03.png" class="img-fluid" alt="Patient">
                  </div>
                </div>
                <div class="dash-widget-info">
                  <h6>Total Consultation</h6>
                  <!-- <h3> {{ appointmentsCount+pastInstantAppointmentsCount}}</h3> -->
                  <h3> {{ totalAppointmentsCount}}</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- <div class="row">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
         <h6 id="appt-today-title" class="en-size ">
                              Appointments
                          </h6>
            <div class="card ">
              <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                  <div class="row">

                      <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                        <div class="text-success">
                          <h6 id="appt-today-title" class="text-success en-size text-center">
                               Today-Pending
                          </h6>
                          <h3 id="appt-today-cnt" class="text-center">
                              {{ appointmentsTodayCount}}
                          </h3>
                      </div>
                      </div>
                      <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                        <div class="text-success">
                          <h6 id="appt-today-title" class="text-success en-size text-center" >
                               Today-Started
                          </h6>
                          <h3 id="appt-today-cnt" class="text-center">
                              {{ startedAppointmentsCount}}
                          </h3>
                      </div>
                      </div>
                      <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                        <div class="text-success">
                          <h6 id="appt-today-title" class="text-success en-size text-center">
                              Today-Completed
                          </h6>
                          <h3 id="appt-today-cnt" class="text-center">
                              {{ this.completedAppointmentsCount }}
                          </h3>
                      </div>
                      </div>
                      <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                        <div class="text-success">
                          <h6 id="appt-today-title" class="text-success en-size text-center">
                          Total
                          </h6>
                          <h3 id="appt-today-cnt" class="text-center">
                            {{ appointmentsCount+pastInstantAppointmentsCount }}
                          </h3>
                      </div>
                      </div>
                    </div>
                  </div>
            </div>
        </div>
    </div> -->
  <div class="row mb-5">
    <div class="col-md-12">
      <div>
        <h4 id="dash-activity-title" class="mb-4">Your Activities</h4>
        <!-- <button (click)="initiateVConsult()" id="dash-video-cnslt" class="btn btn-info btn-lg mx-3 h-btn"><i class="fas fa-desktop"></i>  Initiate Video Consult</button> -->
        <button id="dash-ms" (click)="createAppointment()" class="btn btn-info btn-lg mx-3 h-btn"
          *ngIf="checkDoctorIsPublic()">
          <i class="far fa-clock"></i> Create Schedules
        </button>
        <button id="dash-ms" (click)="manageScheduleFn()" class="btn btn-info btn-lg mx-3 h-btn"
          *ngIf="checkDoctorIsPublic()">
          <i class="far fa-clock"></i> Manage Schedules
        </button>
        <button id="dash-ma" (click)="manageAppointmentsFn()" class="btn btn-info btn-lg mx-3 h-btn"
          *ngIf="checkDoctorIsPublic()">
          <i class="far fa-calendar-check"></i> Manage Appointments
        </button>
        <button class="btn btn-info btn-lg mx-3 h-btn">
          <i class="fas fa-user-injured"></i> Patients Consulted Today - {{completedAppointments.length}}
        </button>
        <button class="btn btn-info btn-lg mx-3 h-btn" [routerLink]="['/doctor/fee-collection']"
          routerLinkActive="router-link-active" *ngIf="checkDoctorIsPublic()">
          <i class="far fa-money-bill-alt"></i> Fees Collected Today
        </button>
        <!-- <button class="btn btn-info btn-lg mx-3 h-btn" [routerLink]="['/medical-report']" routerLinkActive="router-link-active" >
          <i class="far fa-calendar-minus"></i> Reports
        </button> -->
        <!-- <button id="dash-rts" class="btn btn-info btn-lg mx-3 h-btn"><i class="far fa-user"></i>  Refer to a Specialist</button>
                  <button id="dash-rtsh" class="btn btn-info btn-lg mx-3 h-btn"><i class="far fa-hospital"></i>  Refer to a Super Speciality Hosp</button> -->
      </div>
    </div>
  </div>

  <!-- <div *ngIf="activity == 'No Activity'" class="row mb-5">
    <div class="col-md-12">
      <div>
        <h4 id="dash-activity-title" class="mb-4">New Messages</h4>
      </div>
      <div class="col-md-12 float-right">
        <div class="float-right">
          <nav
            aria-label="Page navigation example"
            *ngIf="this.messageTotalPage > 1"
          >
            <ul class="pagination">
              <li
                class="page-item"
                (click)="messageFirstPageList()"
                [ngClass]="{
                  'disabled-pagination':
                    messageCurrentPage === 1
                }"
              >
                <a class="page-link">&lt;&lt;</a>
              </li>
              <li
                class="page-item"
                (click)="messagePreviousPageList()"
                [ngClass]="{
                  'disabled-pagination':
                    messageCurrentPage === 1
                }"
              >
                <a class="page-link">&lt;</a>
              </li>
              <li class="page-item">
                <a class="page-link"
                  >page &nbsp;{{
                    messageCurrentPage
                  }}&nbsp;of&nbsp; {{ messageTotalPage }}</a
                >
              </li>
              <li
                class="page-item"
                (click)="messageNextPageList()"
                [ngClass]="{
                  'disabled-pagination':
                    messageCurrentPage ===
                    messageTotalPage
                }"
              >
                <a class="page-link">&gt;</a>
              </li>
              <li
                class="page-item"
                (click)="messageLastPageList()"
                [ngClass]="{
                  'disabled-pagination':
                    messageCurrentPage ===
                    messageTotalPage
                }"
              >
                <a class="page-link">&gt;&gt;</a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
      <div *ngIf="messageLoading">
        <app-loading-spinner></app-loading-spinner>
    </div>
      <div class="card card-table mb-0"  *ngIf="!messageLoading">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover table-center mb-0">
              <thead>
                <tr>
                  <th class="text-left">
                    <h5>#</h5>
                  </th>
                  <th class="text-left">
                    <h5>Patient Name</h5>
                  </th>
                  <th class="text-left">
                    <h5>Message</h5>
                  </th>
                  <th class="text-left">
                    <h5>Action</h5>
                  </th>

                  <!-- <th class="text-left">Mark as Read</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let msg of messages; let i = index">
                  <td>{{ i +messageSerialNumber+ 1 }}</td>
                  <td>{{ msg["consultation"]['patient_json']['username'] }} </td>
                  <td class="fifteen_chars">{{ msg.text }}</td>
                  <td>
                    <button class="btn btn-primary btn-sm btn-msg" (click)="viewMessage(msg['text'],msg['uuid'],msg['consultation']['uuid'])" data-toggle="modal" data-target="#viewMessageModal">
                      View Message
                    </button>
                  </td>
                  <td></td>
                  <td></td>
                </tr>
              </tbody>
            </table>
            <p
              id="no-msg-data"
              class="nm-size"
              *ngIf="messages?.length == 0 || !messages"
            >
              No New Messages
            </p>
          </div>
        </div>
      </div>
      <div class="col-md-12 float-right mt-3">
        <div class="float-right">
          <nav
            aria-label="Page navigation example"
            *ngIf="this.messageTotalPage > 1"
          >
            <ul class="pagination">
              <li
                class="page-item"
                (click)="messageFirstPageList()"
                [ngClass]="{
                  'disabled-pagination':
                    messageCurrentPage === 1
                }"
              >
                <a class="page-link">&lt;&lt;</a>
              </li>
              <li
                class="page-item"
                (click)="messagePreviousPageList()"
                [ngClass]="{
                  'disabled-pagination':
                    messageCurrentPage === 1
                }"
              >
                <a class="page-link">&lt;</a>
              </li>
              <li class="page-item">
                <a class="page-link"
                  >page &nbsp;{{
                    messageCurrentPage
                  }}&nbsp;of&nbsp; {{ messageTotalPage }}</a
                >
              </li>
              <li
                class="page-item"
                (click)="messageNextPageList()"
                [ngClass]="{
                  'disabled-pagination':
                    messageCurrentPage ===
                    messageTotalPage
                }"
              >
                <a class="page-link">&gt;</a>
              </li>
              <li
                class="page-item"
                (click)="messageLastPageList()"
                [ngClass]="{
                  'disabled-pagination':
                    messageCurrentPage ===
                    messageTotalPage
                }"
              >
                <a class="page-link">&gt;&gt;</a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div> -->
  <div *ngIf="activity == 'No Activity'" class="row">

    <div class="col-md-12">
      <h4 id="dash-booked-appt-title" class="mb-4 hd-inl">Appointments</h4>
      <div *ngIf="checkDoctorIsPublic()">
        <h5 class="chk hd-inl txt txt-size">
          I am available now for Instant Consultation
        </h5>
        <input type="checkbox" [(ngModel)]="this.available_now" [checked]="this.available_now" class="chk hd-inl"
          id="avail-checkbox" (click)="modifyAvailableNow($event)" />
      </div>
      <br>
      <app-appointment-list></app-appointment-list>
    </div>
  </div>
</div>

<!--Hospital List-->
<!-- <div *ngIf="activity == 'No Activity'" class="row mb-5">
    <div class="col-md-12">
        <div>
            <h4 id="hsptl-list" class="mb-4">Associated Hospitals List</h4>
        </div>
        <div class="card card-table mb-0">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-center mb-0">
                        <thead>
                            <tr>
                                <th class="text-left">
                                    <h5>#</h5>
                                </th>
                                <th class="text-left">
                                    <h5>Hospital Name</h5>
                                </th>
                                <th class="text-left">
                                    <h5>Registrations</h5>
                                </th>
                                <th class="text-left">
                                    <h5>Admin Details</h5>
                                </th>

                                <th class="text-left">
                                    <h5>Status</h5>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let hsp of hospitalAssReq;let i = index">
                                <td>{{i+1}}</td>
                                <td>{{hsp['hospital_data']?.name}}</td>
                                <td>{{hsp['hospital_data']?.med_hospital_id}}</td>
                                <td>
                                    <div class="admin-det">
                                        <p><b>Name : </b>{{hsp['hospital_data']?.contact_person_name}}</p>
                                        <p><b>Email : </b>{{hsp['hospital_data']?.email}}</p>
                                        <p><b>Phone : </b>{{hsp['hospital_data']?.phone_numbers}}</p>
                                    </div>
                                </td>
                                <td *ngIf="hsp['status'] != 'Pending'">{{hsp['status']}}</td>
                                <td *ngIf="hsp['status'] == 'Pending'" class="text-warning"><u data-toggle="modal" data-target="#hspAssConfirmModal" (click)="updateHspAppReq(hsp['uuid'])">{{hsp['status']}}</u></td>
                            </tr>
                        </tbody>
                    </table>
                    <p id="no-msg-data" class="nm-size" *ngIf="hospitalAssReq?.length == 0">No Data</p>
                </div>
            </div>

        </div>
    </div>
</div> -->
<!--Manage Schedules-->
<div *ngIf="activity == 'manage schedules'" class="card">
  <div class="card-body">
    <div class="row">
      <div class="col-md-12">
        <h4 class="mb-4 ms">
          <i class="fas fa-chevron-circle-left" (click)="openAppointmentsTab()"></i><span> Manage Schedules</span>
        </h4>
        <ng-select class="ms ms-btn" (change)="getChforLocation($event)" [ngModel]="selectedLocationCh"
          id="practice-location-ms" [searchable]="false" [clearable]="false" [items]="practiceLocationList"
          bindLabel="name" placeholder="{{ 'Select Location' | translate }}"></ng-select>
        <h5 class="ms loc-label">Location</h5>
        <br />
        <hr />
      </div>
      <div class="col-md-12">
        <h5 class="db-ch-title card-title" translate>Consulting Hours</h5>
        <!-- <div class="card">
                  <div class="card-body"> -->
        <div class="profile-box">
          <app-doctor-consulting-hours [selectedLocation]="selectedLocationCh"></app-doctor-consulting-hours>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- </div> -->
<!--Manage Schedules-->
<app-manage-appointments *ngIf="activity == 'manage appointments'" (activity)="getActivity($event)">
</app-manage-appointments>
<!-- </div> -->

<div class="modal fade" id="hspAssConfirmModal" tabindex="-1" role="dialog" aria-labelledby="hspAssConfirmModalLabel"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="hspAssConfirmModalLabel">
          Hospital Association Approval
        </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">Please confirm the request from the hospital</div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" (click)="modifyHspAppReq(true)" data-dismiss="modal">
          Approve
        </button>
        <button type="button" class="btn btn-danger" (click)="modifyHspAppReq(false)" data-dismiss="modal">
          Decline
        </button>
      </div>
    </div>
  </div>
</div>
<!-- /Page Content -->
<!-- Button trigger modal -->


<!-- Modal -->
<div class="modal fade" id="viewMessageModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Message</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <P>{{patientNotes}}</P>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" (click)="markAsRead()">Mark As Read</button>
      </div>
    </div>
  </div>
</div>
<div class="modal" tabindex="-1" role="dialog" id="consultation-complete">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Pending Consultation</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>Please complete/end the pending consultation to proceed to new consultation</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="endConsultation()">End
          Consultation</button>
        <button type="button" class="btn btn-primary" (click)="onConsult(consultationUuid,'virtual','null')">Resume
          Consultation</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade bd-example-modal-lg" id="reports" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="col-md-12 mt-2">
        <div class="card">
          <table class="table table-hover table-responsive table-center mb-0">
            <thead>
              <tr>
                <th>No</th>
                <th>Report Type</th>
                <th>File Name</th>
                <th>Report Generated On</th>
                <th>Uploaded On</th>
                <th>Action</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let file of reportFiles; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ file.medical_report_type}}</td>
                <td>{{ file.file_name }}</td>
                <td>{{ file.report_generated_on | date:'mediumDate'}}</td>
                <td>{{ file.created_at | date:'mediumDate'}}</td>
                <td>
                  <button class="btn btn-primary btn-sm btn-msg" (click)="openFile(file['file'])" data-toggle="modal">
                    View Report
                  </button>
                </td>
                <td></td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>