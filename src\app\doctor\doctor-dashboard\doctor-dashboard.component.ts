import { filter } from 'rxjs/operators';
import { PlatformService } from './../../platform/platform.service';
import { ManageAppointmentsComponent } from './manage-appointments/manage-appointments.component';
import { DoctorService } from './../doctor.service';
import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  Input,
} from '@angular/core';
import * as moment from 'moment';
import { Router, ActivatedRoute, NavigationEnd, NavigationStart } from '@angular/router';
import { ModalManager } from 'ngb-modal';
import { ToastrService } from 'ngx-toastr';
import { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import * as Settings from '../../config/settings';
import { TeleConsultService } from '../../tele-consult/tele-consult.service';
declare var $;
@Component({
  selector: 'app-doctor-dashboard',
  templateUrl: './doctor-dashboard.component.html',
  styleUrls: ['./doctor-dashboard.component.css'],
})
export class DoctorDashboardComponent implements OnInit {
  @ViewChild('closemodal') confirmModal: ElementRef;
  public practiceLocationList = [];
  public appointments = [];
  public upcommingAppointments = [];
  public appointmentsToday = [];
  public patientDetailList = [];
  public patientDetail = {};
  public appointmentsCount: number = 0;
  public manageSchedules = false;
  public apptDetails = {};
  public notes = false;
  public activity = 'No Activity';
  public selectedAppointment = [];
  public initiateConsult = false;
  @Output() consult: EventEmitter<boolean> = new EventEmitter();
  doctorInstantAvailability: any;
  public selectedLocationCh = {};
  screenOptions: NgbModalOptions = {
    keyboard: false,
    centered: true,
  };
  hospitalAssReq = [];
  public messages: any;
  public available_now = JSON.parse(localStorage.getItem('available_now'));
  hospitalsList = [];
  selectedReqUuid = '';
  doctorTodayAppointmentCurrentPage = 1;
  doctorTodayAppointmentTotalPage: number;
  doctorUpcomingAppointmentCurrentPage = 1;
  doctorUpcomingAppointmentTotalPage: number;
  serialNumber: number;
  pastAppointments = [];
  startedAppointments = [];
  missedAppointments = [];
  completedAppointments = [];
  cancelBtnDisabled: boolean;
  isLoading = false;
  patientNotes: string;
  todayAppointmentData: any = [];
  doctorPastAppointmentCurrentPage: number;
  doctorPastAppointmentTotalPage: number;
  messageCurrentPage: number = 1;
  messageTotalPage: number;
  messageSerialNumber: number;
  messageLoading: boolean;
  instantAppontmentList: any = [];
  todayInstantAppontmentList: any = [];
  messageUuid: any;
  consultationUuid: any;
  appointmentsTodayCount: number;
  defalutPicture = "assets/img/doctors/doctor-thumb-02.png";
  pastInstantAppointments: any = [];
  doctorPastInstantAppointmentTotalPage: number;
  doctorPastInstantAppointmentCurrentPage: number;
  pastInstantAppointmentsCount: number = 0;
  cancelAppointmentId: string;
  upcommingAppointmentId: string;
  startedAppointmentsCount: number = 0;
  missedAppointmentsCount: number = 0;
  completedAppointmentsCount: number = 0;
  doctorStartedAppointmentCurrentPage: number;
  doctorStartedAppointmentTotalPage: any;
  doctorMissedAppointmentCurrentPage: any;
  doctorMissedAppointmentTotalPage: any;
  doctorCompletedAppointmentCurrentPage: any;
  doctorCompletedAppointmentTotalPage: any;
  doctorPendingAppointmentTotalPage: any;
  doctorPendingAppointmentCurrentPage: any;
  pendingAppointmentsCount: number = 0;
  reportFiles: any = [];
  isPublicDoctor: boolean;
  todayAppointmentsCount: number = 0;
  totalAppointmentsCount: number = 0;

  constructor(
    private doctorService: DoctorService,
    private router: Router,
    private platformService: PlatformService,
    private modalService: NgbModal,
    private notificationService: ToastrService,
    private teleConsultService: TeleConsultService
  ) { }

  ngOnInit(): void {
    this.patientNotes = 'null';
    this.messageSerialNumber = 0;
    // this.doctorService.checkProfileCompletion();
    this.getAppointmentsData();
    this.getPracticeLocation();
    this.getAllInstantAppointmentData();
    this.appointmentCount();
    setTimeout(() => this.chckBoxFn(), 3000);
    this.getmessageList(this.messageCurrentPage);
    setInterval(() => {
      const url = this.router.url;
      if (url === '/doctor/dashboard') {
        this.appointmentButtonEnableAndDisable();
        this.doctorPendingAppointment(1);
        this.appointmentCount();
      }
    }, 60000);
    this.getHspAssociationReq();
    this.doctorService.getDoctorProfile().subscribe(
      (data) => {
        this.available_now = data['instant_appointment_slot_available'];
        localStorage.setItem('available_now', data['instant_appointment_slot_available']);
      });
    localStorage.removeItem('consult_patient_id');
  }

  checkDoctorIsPublic() {
    const hospital_id = localStorage.getItem('hospital_id');
    if (hospital_id != null) {
      this.isPublicDoctor = false;
    }
    else {
      this.isPublicDoctor = true;
    }
    return this.isPublicDoctor;
  }

  viewPatient(userData, consultationid) {
    this.patientDetail = userData;
    this.patientNotes = 'Nil';
    this.doctorService.getPatientNotes(consultationid).subscribe((data) => {
      const result = data['results']
      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          if (result[i].consultation == consultationid) {
            this.patientNotes = data['results'][i].text;
          }
        }
        // this.patientNotes = data['results'][0]?.text;
      }

      $('#patientModal').modal('show');
    }, error => {
      $('#patientModal').modal('show');
      console.log(error);
      const status = error['status'];
      if (status == 400) {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
      else {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
    })

    this.notes = false;
    // const data = this.patientDetailList.filter(obj=> obj.uuid==uuid);

  }

  getAppointmentsData() {
    const page = 1;
    this.doctorPastAppointmentCount();
    this.doctorPastInstantAppointment(page);
    this.getTodayCompletedAppointments(page);
    this.doctorPendingAppointment(page);
    this.getAppointmentByStatus();
    this.doctorUpcomingAppointment(page);
    this.doctorPastAppointment(page);

  }
  doctorPendingAppointment(page) {
    this.isLoading = true;
    this.doctorPendingAppointmentTotalPage = 0;
    const date = moment(new Date()).format('YYYY-MM-DD');
    const todaySearchParams =
      '?start_datetime=' + date + '&end_datetime=' + date + '&sort_by=Descending&status=Booked&fulfilment_status=Not Started' + '&page=' + page;
    this.doctorService
      .getDoctorAppointments(todaySearchParams)
      .subscribe((data) => {
        const results = data['results'];
        this.doctorPendingAppointmentTotalPage = data['total_pages'];
        this.doctorPendingAppointmentCurrentPage = data['page_number'];
        const appointment = results.filter(obj => obj.fulfilment_status == 'Not Started');
        this.todayAppointmentData = results.filter(obj => obj.status == 'Booked');
        const currentDate = moment();
        let index = 0;
        this.appointmentsToday = [];
        this.patientDetailList = []
        for (const value of appointment) {
          this.cancelBtnDisabled = true;
          const patientName = value.patient_user_json.username;
          const patientUuid = value.patient_user_json.uuid;
          const patientProfilePic = value.patient_user_json.profile_picture;
          const patientPhoneNumber = value.patient_user_json.phone;
          const patient_user_json = value.patient_user_json;
          const consultation_type = value.consultation_type;
          const startDate = moment(currentDate).format('YYYY-MM-DD');
          const day = moment(value.start_datetime);
          let minutes = currentDate.diff(day, 'minutes');
          const endTime = moment(value.end_datetime)
          let maxTime = endTime.diff(day, 'minutes');
          const parent_consultation_uuid = value.parent_consultation_uuid;
          if (value.status === 'Booked') {
            const uuid = value.uuid;
            this.patientDetailList.push(value.patient_user_json);
            if (minutes > Settings.consultaion_cancel_margin) {
              this.cancelBtnDisabled = true;
            } else
              if (day > currentDate) {
                this.cancelBtnDisabled = false;
              }
            if (minutes > Settings.consultaion_left_margin && maxTime >= minutes) {
              // tslint:disable-next-line: max-line-length
              this.appointmentsToday.push({
                start_datetime: `${value.start_datetime}`,
                end_datetime: `${value.end_datetime}`, patientName: `${patientName}`, patientProfilePic: `${patientProfilePic}`, doctor_appointment_uuid: `${value.doctor_appointment_uuid}`,
                disable: false, status: `${value.status}`, cancelButtonDisabled: this.cancelBtnDisabled, patientUuid: patientUuid,
                consultationUuid: `${value.consultation_uuid}`, patientPhoneNumber: patientPhoneNumber, patient_user_json: patient_user_json,
                uuid: `${value.uuid}`, fulfilment_status: value.fulfilment_status, patient: `${value.patient}`, doctor: `${value.doctor}`, consultation_type: consultation_type, parent_consultation_uuid: parent_consultation_uuid
              });
            } else {
              // tslint:disable-next-line: max-line-length
              this.appointmentsToday.push({
                start_datetime: `${value.start_datetime}`,
                end_datetime: `${value.end_datetime}`, patientName: `${patientName}`, patientProfilePic: `${patientProfilePic}`, doctor_appointment_uuid: `${value.doctor_appointment_uuid}`,
                disable: true, status: `${value.status}`, cancelButtonDisabled: this.cancelBtnDisabled, patientUuid: patientUuid,
                consultationUuid: `${value.consultation_uuid}`, patientPhoneNumber: patientPhoneNumber, patient_user_json: patient_user_json,
                uuid: `${value.uuid}`, fulfilment_status: value.fulfilment_status, patient: `${value.patient}`, doctor: `${value.doctor}`, consultation_type: consultation_type, parent_consultation_uuid: parent_consultation_uuid
              });

            }
          }
          index = index + 1;
        }
        this.pendingAppointmentsCount = data['count'];

        this.isLoading = false;
      }, error => {
        this.isLoading = false;
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }
  doctorPendingAppointmentNextPage() {
    this.doctorPendingAppointmentCurrentPage = this.doctorPendingAppointmentCurrentPage + 1;
    if (this.doctorPendingAppointmentTotalPage >= this.doctorPendingAppointmentCurrentPage) {
      this.serialNumber = (this.doctorPendingAppointmentCurrentPage - 1) * 10;
      this.doctorPendingAppointment(this.doctorPendingAppointmentCurrentPage);

    } else {
      this.doctorPendingAppointmentCurrentPage = this.doctorPendingAppointmentCurrentPage - 1;
    }
  }
  doctorPendingAppointmentLastPage() {
    this.serialNumber = (this.doctorPendingAppointmentTotalPage - 1) * 10;
    this.doctorPendingAppointment(this.doctorPendingAppointmentTotalPage);
  }
  doctorPendingAppointmentFirstPage() {
    this.serialNumber = 0;
    this.doctorPendingAppointmentCurrentPage = 1;
    this.doctorPendingAppointment(this.doctorPendingAppointmentCurrentPage);
  }
  doctorPendingAppointmentPreviousPage() {
    this.doctorPendingAppointmentCurrentPage = this.doctorPendingAppointmentCurrentPage - 1;
    if (this.doctorPendingAppointmentTotalPage >= this.doctorPendingAppointmentCurrentPage && this.doctorPendingAppointmentCurrentPage > 0) {
      this.serialNumber = (this.doctorPendingAppointmentCurrentPage - 1) * 10;
      this.doctorPendingAppointment(this.doctorPendingAppointmentCurrentPage);
    } else {
      this.doctorPendingAppointmentCurrentPage = this.doctorPendingAppointmentCurrentPage + 1;
    }
  }
  doctorUpcomingAppointment(page) {
    this.doctorUpcomingAppointmentTotalPage = 0;
    const todayDate = new Date();
    const tomorrowDate = new Date(todayDate);
    tomorrowDate.setDate(tomorrowDate.getDate() + 1);
    const tomDate = moment(tomorrowDate).format('YYYY-MM-DD');
    const upcommingSearchParams = '?start_datetime=' + tomDate + '&status=Booked&fulfilment_status=Not Started' + '&page=' + page;
    this.doctorService
      .getDoctorAppointments(upcommingSearchParams)
      .subscribe((data) => {
        this.upcommingAppointments = data['results'];
        this.doctorUpcomingAppointmentTotalPage = data['total_pages'];
        this.doctorUpcomingAppointmentCurrentPage = data['page_number'];
        this.upcommingAppointments = this.upcommingAppointments.filter(
          (obj) => obj.status === 'Booked'
        );
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }
  getPracticeLocation() {
    this.doctorService.getDoctorPracticeLocations().subscribe(
      (data) => {
        if (data['count'] != 0) {
          const resp_data = data['results'];
          this.practiceLocationList = Object.values(resp_data);
          this.selectedLocationCh = this.practiceLocationList[0];
          sessionStorage.setItem(
            'practice_location',
            this.practiceLocationList[0]['uuid']
          );
        }
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  getChforLocation(event) {
    this.selectedLocationCh = event;
  }

  manageScheduleFn() {
    this.manageSchedules = true;
    this.activity = 'manage schedules';
  }

  openAppointmentsTab() {
    this.manageSchedules = false;
    this.activity = 'No Activity';
  }

  manageAppointmentsFn() {
    this.activity = 'manage appointments';
  }

  onConsult(consultation_uuid, consultation_type, parent_consultation_uuid) {
    console.log(consultation_type);
    localStorage.setItem('doc_consultation_type', consultation_type);
    if (parent_consultation_uuid != null) {
      localStorage.setItem('parent_consultation_uuid', parent_consultation_uuid);
    }
    this.doctorService
      .joinConsultation(consultation_uuid)
      .subscribe((data) => {
        this.router.navigate(['/doctor/consultation'], {
          queryParams: { consultationId: consultation_uuid },
        });
        this.router.events.subscribe((val) => {
          const nvigationEnd = val instanceof NavigationEnd;
          if (!!nvigationEnd) {
            location.reload();
          }

        });
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          const errorData = error['error']['error_details']['consultations'];
          if (errorData.length > 0) {
            this.consultationUuid = errorData[0]['uuid'];
            $('#consultation-complete').modal({ backdrop: 'static', keyboard: false });
            $('#consultation-complete').modal('show');
          }
          const errMessage = error['error']['error_message']
          this.notificationService.error(errMessage, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }

  viewConsultHistory(patient_uuid, status, doctor_uuid, consultation_uuid) {
    if (status == 'Completed' || status == 'Suspended') {
      this.router.navigate(['/consultation-history/', patient_uuid, doctor_uuid, consultation_uuid]);
    }
    else {
      this.notificationService.warning('No consultation data', 'Med.Bot')
    }

  }
  cancelAppointment(appt_uuid, day, index, cancelButtonDisabled, disable) {
    let data = {};
    if (day == 'today') {
      this.cancelAppointmentId = "#today_apt_cancel" + index;

      if (!cancelButtonDisabled && disable) {
        data = this.appointmentsToday.filter((obj) => obj.uuid === appt_uuid);
        this.apptDetails['username'] = data[0]['patientName'];
        this.apptDetails['date'] = moment(data[0]['start_datetime']).format('DD-MM-YYYY');
        this.apptDetails['time'] = moment(data[0]['start_datetime']).format('hh:mm a');
        this.apptDetails['uuid'] = data[0]['uuid'];
        this.apptDetails['day'] = 'today';
        $('#confirmModal').modal('show');
      } else {
        this.notificationService.error('You can cancel before 10 minutes of your consultation time only ');
      }

    }
    if (day == 'upcomming') {
      this.upcommingAppointmentId = "#upcomming_apt_cancel" + index;
      data = this.upcommingAppointments.filter((obj) => obj.uuid === appt_uuid);
      this.apptDetails['username'] = data[0]['patient_user_json']['username'];
      this.apptDetails['date'] = moment(data[0]['start_datetime']).format('DD-MM-YYYY');
      this.apptDetails['time'] = moment(data[0]['start_datetime']).format('hh:mm a');
      this.apptDetails['uuid'] = data[0]['uuid'];
      this.apptDetails['day'] = 'upcomming';
    }


  }

  confirmCancel(uuid, day) {
    $(`${this.upcommingAppointmentId}`).prop('disabled', true);
    $(`${this.cancelAppointmentId}`).prop('disabled', true);

    $('#confirmModal').modal('hide');
    this.notificationService.warning('Please wait appointment  cancel is processing', 'Med.Bot');
    this.doctorService.cancelPatientAppointment(uuid).subscribe((data) => {
      this.notificationService.success('Appointment Cancelled', 'Med.Bot');
      if (day == 'today') {
        this.appointmentsToday = this.appointmentsToday.filter((obj) => obj.uuid !== uuid);
      } else {
        this.upcommingAppointments = this.upcommingAppointments.filter((obj) => obj.uuid !== uuid);
      }
      this.confirmModal.nativeElement.click();
      $(`${this.upcommingAppointmentId}`).prop('disabled', false);
      $(`${this.cancelAppointmentId}`).prop('disabled', false);
    }, error => {
      console.log(error);

      const status = error['status'];
      if (status == 400) {
        const errMessage = error['error']['error_message']
        if (errMessage == 'The appointment has been already cancelled') {
          this.notificationService.error(`${errMessage}`, 'Med.Bot');
          if (day == 'today') {
            this.appointmentsToday = this.appointmentsToday.filter((obj) => obj.uuid !== uuid);
          } else {
            this.upcommingAppointments = this.upcommingAppointments.filter((obj) => obj.uuid !== uuid);
          }
        } else if (errMessage) {
          this.notificationService.error(`${errMessage}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
      else {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
      $(`${this.upcommingAppointmentId}`).prop('disabled', false);
      $(`${this.cancelAppointmentId}`).prop('disabled', false);
    });
  }

  showNotes() {
    this.notes = true;
  }

  onCaseHistory() {
    this.router.navigateByUrl('/case-history-visit-dates');

    // this.modalService.open(CaseHistoryComponent, this.screenOptions).result.then((result) => {
    // });
  }

  getActivity(event) {
    this.activity = event;
  }

  selectAppointment(data, event) {
    if (event.target.checked) {
      this.selectedAppointment.push(data);
    } else {
      this.selectedAppointment = this.selectedAppointment.filter(
        (appt) => appt === data
      );
    }
    this.initiateConsult = true;
  }

  initiateVConsult() {
    const len = this.selectedAppointment.length;
    if (len != 0) {
      if (len == 1) {
        this.router.navigate(['consultation'], {
          queryParams: { appId: this.selectedAppointment[0]['uuid'] },
        });
      } else {
        this.notificationService.error('Please select single Appointment');
      }
    } else {
      this.notificationService.error('Select an appointment');
    }
  }

  editProfile() {
    this.router.navigate(['/doctor/profile']);
  }

  modifyAvailableNow(event) {
    if (event.target.checked) {
      const doctorId = localStorage.getItem('Doctor');
      this.teleConsultService.checkOpenConsultation(doctorId).subscribe(data => {
        const results = data['results']
        if (results.length > 0) {
          this.consultationUuid = results[0]['uuid'];
          $('#consultation-complete').modal({ backdrop: 'static', keyboard: false });
          $('#consultation-complete').modal('show');
        } else {
          this.doctorService.doctorAvailableNow().subscribe((data) => {
            localStorage.setItem('available_now', 'true');
            this.available_now = true;
            this.notificationService.success(
              'You are available for Instant Consultation'
            );
          });
        }
      });

    } else {
      this.doctorService.doctorUnAvailableNow().subscribe((data) => {
        localStorage.setItem('available_now', 'false');
        this.available_now = false;
        this.notificationService.success(
          'Not available for Instant Consultation'
        );
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
    }
  }

  defaultPatientImage() {

  }

  chckBoxFn() {
    this.available_now = JSON.parse(localStorage.getItem('available_now'));
    if (this.available_now) {
      this.available_now = true;
    }
  }

  trimMsg(strng) {
    strng.substring(0, 300);
  }

  getHospital(uuid, req) {
    this.platformService.getHospitalDetail(uuid).subscribe((data) => {
      this.hospitalsList.push(data);
    });
  }

  getHspAssociationReq() {
    this.doctorService.getHospitalAssociationRequest().subscribe((data) => {

      this.hospitalAssReq = data['results'];
      const hspData = this.hospitalsList[0];
      // this.hospitalsList = [];
      for (let i = 0; i < this.hospitalAssReq.length; i++) {
        this.platformService
          .getHospitalDetail(this.hospitalAssReq[i]?.hospital)
          .subscribe((data) => {
            this.hospitalAssReq[i]['hospital_data'] = data;
            if (
              !this.hospitalAssReq[i]['is_approved_by_doctor'] &&
              !this.hospitalAssReq[i]['is_declined_by_doctor']
            ) {
              this.hospitalAssReq[i]['status'] = 'Pending';
            } else if (this.hospitalAssReq[i]['is_declined_by_doctor']) {
              this.hospitalAssReq[i]['status'] = 'Declined';
            } else {
              this.hospitalAssReq[i]['status'] = 'Approved';
            }
          }, error => {
            console.log(error);
            const status = error['status'];
            if (status == 400) {
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
            else {
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          });
      }
    });
  }

  updateHspAppReq(uuid) {
    this.selectedReqUuid = uuid;
  }

  modifyHspAppReq(status) {
    this.doctorService
      .approveAssociateReq(this.selectedReqUuid, status)
      .subscribe((data) => {
        if (status) {
          this.notificationService.success('Request Approved', 'Med.Bot');
        } else {
          this.notificationService.success('Request Declined', 'Med.Bot');
        }
        this.getHspAssociationReq();

      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }

  doctorUpcomingAppointmentNextPage() {
    this.doctorUpcomingAppointmentCurrentPage = this.doctorUpcomingAppointmentCurrentPage + 1;
    if (this.doctorUpcomingAppointmentTotalPage >= this.doctorUpcomingAppointmentCurrentPage) {
      this.serialNumber = (this.doctorUpcomingAppointmentCurrentPage - 1) * 10;
      this.doctorUpcomingAppointment(this.doctorUpcomingAppointmentCurrentPage);

    } else {
      this.doctorUpcomingAppointmentCurrentPage = this.doctorUpcomingAppointmentCurrentPage - 1;
    }
  }
  doctorUpcomingAppointmentLastPage() {
    this.serialNumber = (this.doctorUpcomingAppointmentTotalPage - 1) * 10;
    this.doctorUpcomingAppointment(this.doctorUpcomingAppointmentTotalPage);
  }
  doctorUpcomingAppointmentFirstPage() {
    this.serialNumber = 0;
    this.doctorUpcomingAppointmentCurrentPage = 1;
    this.doctorUpcomingAppointment(this.doctorUpcomingAppointmentCurrentPage);
  }
  doctorUpcomingAppointmentPreviousPage() {
    this.doctorUpcomingAppointmentCurrentPage = this.doctorUpcomingAppointmentCurrentPage - 1;
    if (this.doctorUpcomingAppointmentTotalPage >= this.doctorUpcomingAppointmentCurrentPage && this.doctorUpcomingAppointmentCurrentPage > 0) {
      this.serialNumber = (this.doctorUpcomingAppointmentCurrentPage - 1) * 10;
      this.doctorUpcomingAppointment(this.doctorUpcomingAppointmentCurrentPage);
    } else {
      this.doctorUpcomingAppointmentCurrentPage = this.doctorUpcomingAppointmentCurrentPage + 1;
    }
  }
  appointmentButtonEnableAndDisable() {
    const currentDate = moment();
    const result = this.appointmentsToday;
    let index = 0;
    this.appointmentsToday = [];
    for (const value of result) {
      this.cancelBtnDisabled = true;
      const patientName = value.patientName;
      const patientUuid = value.patientUuid;
      const patientProfilePic = value.patientProfilePic;
      const patientPhoneNumber = value.patientPhoneNumber;
      const patient_user_json = value.patient_user_json;
      const consultation_type = value.consultation_type;
      const day = moment(value.start_datetime);
      let minutes = currentDate.diff(day, 'minutes');
      const parent_consultation_uuid = value.parent_consultation_uuid;
      if (value.status === 'Booked') {
        if (minutes > Settings.consultaion_cancel_margin) {
          this.cancelBtnDisabled = true;
        } else
          if (day > currentDate) {
            this.cancelBtnDisabled = false;
          }

        if (minutes > -10 && 30 > minutes) {
          // tslint:disable-next-line: max-line-length
          this.appointmentsToday.push({
            start_datetime: `${value.start_datetime}`,
            end_datetime: `${value.end_datetime}`, patientName: `${patientName}`, patientProfilePic: `${patientProfilePic}`, doctor_appointment_uuid: `${value.doctor_appointment_uuid}`,
            disable: false, status: `${value.status}`, cancelButtonDisabled: this.cancelBtnDisabled, patientUuid: patientUuid,
            consultationUuid: `${value.consultationUuid}`, patientPhoneNumber: patientPhoneNumber, patient_user_json: patient_user_json,
            uuid: `${value.uuid}`, fulfilment_status: value.fulfilment_status, patient: `${value.patient}`, doctor: `${value.doctor}`, consultation_type: consultation_type, parent_consultation_uuid: parent_consultation_uuid
          });
        } else {
          // tslint:disable-next-line: max-line-length
          this.appointmentsToday.push({
            start_datetime: `${value.start_datetime}`,
            end_datetime: `${value.end_datetime}`, patientName: `${patientName}`, patientProfilePic: `${patientProfilePic}`, doctor_appointment_uuid: `${value.doctor_appointment_uuid}`,
            disable: true, status: `${value.status}`, cancelButtonDisabled: this.cancelBtnDisabled, patientUuid: patientUuid,
            consultationUuid: `${value.consultationUuid}`, patientPhoneNumber: patientPhoneNumber, patient_user_json: patient_user_json,
            uuid: `${value.uuid}`, fulfilment_status: value.fulfilment_status, patient: `${value.patient}`, doctor: `${value.doctor}`, consultation_type: consultation_type, parent_consultation_uuid: parent_consultation_uuid
          });

        }
      }
      index = index + 1;
    }

  }

  getAppointmentByStatus() {
    this.getTodayStartedAppointments(1)
    this.getTodayMissedAppointments(1)
    this.getTodayInstantAppointmentData();
    this.getOngoingInstantAppointment();
  }
  getTodayStartedAppointments(page) {
    const date = moment(new Date()).format('YYYY-MM-DD');
    const todayStartedSearchParams =
      '?start_datetime=' + date + '&end_datetime=' + date + '&sort_by=Descending&status=Booked&fulfilment_status=Started' + '&page=' + page;
    this.doctorService
      .getDoctorAppointments(todayStartedSearchParams)
      .subscribe((data) => {
        this.startedAppointments = data['results'];
        this.startedAppointmentsCount = data['count'];
      }
        , error => {

        });
  }
  doctorStartedAppointmentNextPage() {
    this.doctorStartedAppointmentCurrentPage = this.doctorStartedAppointmentCurrentPage + 1;
    if (this.doctorStartedAppointmentTotalPage >= this.doctorStartedAppointmentCurrentPage) {
      this.serialNumber = (this.doctorStartedAppointmentCurrentPage - 1) * 10;
      this.getTodayStartedAppointments(this.doctorStartedAppointmentCurrentPage);

    } else {
      this.doctorStartedAppointmentCurrentPage = this.doctorStartedAppointmentCurrentPage - 1;
    }
  }
  doctorStartedAppointmentLastPage() {
    this.serialNumber = (this.doctorStartedAppointmentTotalPage - 1) * 10;
    this.getTodayStartedAppointments(this.doctorStartedAppointmentTotalPage);
  }
  doctorStartedAppointmentFirstPage() {
    this.serialNumber = 0;
    this.doctorStartedAppointmentCurrentPage = 1;
    this.getTodayStartedAppointments(this.doctorStartedAppointmentCurrentPage);
  }
  doctorStartedAppointmentPreviousPage() {
    this.doctorStartedAppointmentCurrentPage = this.doctorStartedAppointmentCurrentPage - 1;
    if (this.doctorStartedAppointmentTotalPage >= this.doctorStartedAppointmentCurrentPage && this.doctorStartedAppointmentCurrentPage > 0) {
      this.serialNumber = (this.doctorStartedAppointmentCurrentPage - 1) * 10;
      this.getTodayStartedAppointments(this.doctorStartedAppointmentCurrentPage);
    } else {
      this.doctorStartedAppointmentCurrentPage = this.doctorStartedAppointmentCurrentPage + 1;
    }
  }
  getTodayMissedAppointments(page) {
    const date = moment(new Date()).format('YYYY-MM-DD');
    const todayMissedSearchParms = '?start_datetime=' + date + '&end_datetime=' + date + '&sort_by=Descending&status=Booked&fulfilment_status=Both Missed%2BPatient Missed%2BDoctor Missed' + '&page=' + page;
    this.doctorService
      .getDoctorAppointments(todayMissedSearchParms)
      .subscribe((data) => {
        this.missedAppointments = data['results'];
        this.missedAppointmentsCount = data['count'];
      }
        , error => {

        });
  }
  doctorMissedAppointmentNextPage() {
    this.doctorMissedAppointmentCurrentPage = this.doctorMissedAppointmentCurrentPage + 1;
    if (this.doctorMissedAppointmentTotalPage >= this.doctorMissedAppointmentCurrentPage) {
      this.serialNumber = (this.doctorMissedAppointmentCurrentPage - 1) * 10;
      this.getTodayMissedAppointments(this.doctorMissedAppointmentCurrentPage);

    } else {
      this.doctorMissedAppointmentCurrentPage = this.doctorMissedAppointmentCurrentPage - 1;
    }
  }
  doctorMissedAppointmentLastPage() {
    this.serialNumber = (this.doctorMissedAppointmentTotalPage - 1) * 10;
    this.getTodayMissedAppointments(this.doctorMissedAppointmentTotalPage);
  }
  doctorMissedAppointmentFirstPage() {
    this.serialNumber = 0;
    this.doctorMissedAppointmentCurrentPage = 1;
    this.getTodayMissedAppointments(this.doctorMissedAppointmentCurrentPage);
  }
  doctorMissedAppointmentPreviousPage() {
    this.doctorMissedAppointmentCurrentPage = this.doctorMissedAppointmentCurrentPage - 1;
    if (this.doctorMissedAppointmentTotalPage >= this.doctorMissedAppointmentCurrentPage && this.doctorMissedAppointmentCurrentPage > 0) {
      this.serialNumber = (this.doctorMissedAppointmentCurrentPage - 1) * 10;
      this.getTodayMissedAppointments(this.doctorMissedAppointmentCurrentPage);
    } else {
      this.doctorMissedAppointmentCurrentPage = this.doctorMissedAppointmentCurrentPage + 1;
    }
  }
  getTodayCompletedAppointments(page) {
    const date = moment(new Date()).format('YYYY-MM-DD');
    const todayCompletedSearchParms = '?start_datetime=' + date + '&end_datetime=' + date + '&sort_by=Descending&status=Booked&fulfilment_status=Suspended%2BCompleted' + '&page=' + page;
    this.doctorService
      .getDoctorAppointments(todayCompletedSearchParms)
      .subscribe((data) => {
        const results = data['results'];
        for (let val in results) {
          this.completedAppointments.push(results[val]);
        }
        this.completedAppointmentsCount = data['count'];
        this.appointmentsCount = this.appointmentsCount + this.completedAppointmentsCount;
      }
        , error => {

        });
  }
  createAppointment() {
    this.router.navigate(['/doctor/practice-locations']);
  }

  doctorCompletedAppointmentNextPage() {
    this.doctorCompletedAppointmentCurrentPage = this.doctorCompletedAppointmentCurrentPage + 1;
    if (this.doctorCompletedAppointmentTotalPage >= this.doctorCompletedAppointmentCurrentPage) {
      this.serialNumber = (this.doctorCompletedAppointmentCurrentPage - 1) * 10;
      this.getTodayCompletedAppointments(this.doctorCompletedAppointmentCurrentPage);

    } else {
      this.doctorCompletedAppointmentCurrentPage = this.doctorCompletedAppointmentCurrentPage - 1;
    }
  }
  doctorCompletedAppointmentLastPage() {
    this.serialNumber = (this.doctorCompletedAppointmentTotalPage - 1) * 10;
    this.getTodayCompletedAppointments(this.doctorCompletedAppointmentTotalPage);
  }
  doctorCompletedAppointmentFirstPage() {
    this.serialNumber = 0;
    this.doctorCompletedAppointmentCurrentPage = 1;
    this.getTodayCompletedAppointments(this.doctorCompletedAppointmentCurrentPage);
  }
  doctorCompletedAppointmentPreviousPage() {
    this.doctorCompletedAppointmentCurrentPage = this.doctorCompletedAppointmentCurrentPage - 1;
    if (this.doctorCompletedAppointmentTotalPage >= this.doctorCompletedAppointmentCurrentPage && this.doctorCompletedAppointmentCurrentPage > 0) {
      this.serialNumber = (this.doctorCompletedAppointmentCurrentPage - 1) * 10;
      this.getTodayCompletedAppointments(this.doctorCompletedAppointmentCurrentPage);
    } else {
      this.doctorCompletedAppointmentCurrentPage = this.doctorCompletedAppointmentCurrentPage + 1;
    }
  }
  getOngoingInstantAppointment() {
    const date = moment().format('YYYY-MM-DD')
    this.doctorService.getOngoingInstantAppointment(date).subscribe((data) => {
      const result = Object.values(data)
      this.startedAppointmentsCount = this.startedAppointmentsCount + result.length;
      for (let value in result)
        this.startedAppointments.push(result[value]);
    }, error => {

    })
  }
  doctorPastAppointment(page) {
    this.doctorPastAppointmentTotalPage = 0;
    const date = moment().add(-1, 'days');
    const formateDay = moment(date).format('YYYY-MM-DD');
    const pastSearchParams =
      '?end_datetime=' + formateDay + '&page=' + page;

    this.doctorService
      .getDoctorAppointments(pastSearchParams)
      .subscribe((data) => {
        this.pastAppointments = data['results'];
        for (let value of this.pastAppointments) {
          this.patientDetailList.push(value.patient_user_json);
        }
        this.doctorPastAppointmentTotalPage = data['total_pages'];
        this.doctorPastAppointmentCurrentPage = data['page_number'];
      }, error => {
        console.log(error);

      })

  }
  doctorPastAppointmentCount() {
    this.doctorPastAppointmentTotalPage = 0;
    const date = moment().add(-1, 'days');
    const formateDay = moment(date).format('YYYY-MM-DD');
    const pastSearchParams =
      '?end_datetime=' + formateDay + '&status=Booked&fulfilment_status=Suspended%2BCompleted';

    this.doctorService
      .getDoctorAppointments(pastSearchParams)
      .subscribe((data) => {
        this.appointmentsCount = this.appointmentsCount + data['count'];
      }, error => {

      })
  }
  doctorPastAppointmentNextPage() {
    this.doctorPastAppointmentCurrentPage = this.doctorPastAppointmentCurrentPage + 1;
    if (this.doctorPastAppointmentTotalPage >= this.doctorPastAppointmentCurrentPage) {
      this.serialNumber = (this.doctorPastAppointmentCurrentPage - 1) * 10;
      this.doctorPastAppointment(this.doctorPastAppointmentCurrentPage);

    } else {
      this.doctorPastAppointmentCurrentPage = this.doctorPastAppointmentCurrentPage - 1;
    }
  }
  doctorPastAppointmentLastPage() {
    this.serialNumber = (this.doctorPastAppointmentTotalPage - 1) * 10;
    this.doctorPastAppointment(this.doctorPastAppointmentTotalPage);
  }
  doctorPastAppointmentFirstPage() {
    this.serialNumber = 0;
    this.doctorPastAppointmentCurrentPage = 1;
    this.doctorPastAppointment(this.doctorPastAppointmentCurrentPage);
  }
  doctorPastAppointmentPreviousPage() {
    this.doctorPastAppointmentCurrentPage = this.doctorPastAppointmentCurrentPage - 1;
    if (this.doctorPastAppointmentTotalPage >= this.doctorPastAppointmentCurrentPage && this.doctorPastAppointmentCurrentPage > 0) {
      this.serialNumber = (this.doctorPastAppointmentCurrentPage - 1) * 10;
      this.doctorPastAppointment(this.doctorPastAppointmentCurrentPage);
    } else {
      this.doctorPastAppointmentCurrentPage = this.doctorPastAppointmentCurrentPage + 1;
    }
  }

  doctorPastInstantAppointment(page) {
    this.doctorPastInstantAppointmentTotalPage = 0;
    let todayDate = new Date();
    const formateDay = moment(todayDate).add(-1, 'days').format('YYYY-MM-DD');
    let pastInstant = `date=` + formateDay + `&page=${page}&status=Booked&sort_by=Descending`;

    this.doctorService.getPastInstantAppointment(pastInstant).subscribe((data) => {
      this.pastInstantAppointments = data['results'];
      this.doctorPastInstantAppointmentTotalPage = data['total_pages'];
      this.doctorPastInstantAppointmentCurrentPage = data['page_number'];
      this.pastInstantAppointmentsCount = data['count'];
    }, error => { })
  }
  
  doctorPastInstantAppointmentNextPage() {
    this.doctorPastInstantAppointmentCurrentPage = this.doctorPastInstantAppointmentCurrentPage + 1;
    if (this.doctorPastInstantAppointmentTotalPage >= this.doctorPastInstantAppointmentCurrentPage) {
      this.serialNumber = (this.doctorPastInstantAppointmentCurrentPage - 1) * 10;
      this.doctorPastInstantAppointment(this.doctorPastInstantAppointmentCurrentPage);

    } else {
      this.doctorPastInstantAppointmentCurrentPage = this.doctorPastInstantAppointmentCurrentPage - 1;
    }
  }
  doctorPastInstantAppointmentLastPage() {
    this.serialNumber = (this.doctorPastInstantAppointmentTotalPage - 1) * 10;
    this.doctorPastInstantAppointment(this.doctorPastInstantAppointmentTotalPage);
  }
  doctorPastInstantAppointmentFirstPage() {
    this.serialNumber = 0;
    this.doctorPastInstantAppointmentCurrentPage = 1;
    this.doctorPastInstantAppointment(this.doctorPastInstantAppointmentCurrentPage);
  }
  doctorPastInstantAppointmentPreviousPage() {
    this.doctorPastInstantAppointmentCurrentPage = this.doctorPastInstantAppointmentCurrentPage - 1;
    if (this.doctorPastAppointmentTotalPage >= this.doctorPastAppointmentCurrentPage && this.doctorPastAppointmentCurrentPage > 0) {
      this.serialNumber = (this.doctorPastAppointmentCurrentPage - 1) * 10;
      this.doctorPastInstantAppointment(this.doctorPastInstantAppointmentCurrentPage);
    } else {
      this.doctorPastInstantAppointmentCurrentPage = this.doctorPastInstantAppointmentCurrentPage + 1;
    }
  }

  messageNextPageList() {
    this.messageCurrentPage = this.messageCurrentPage + 1;
    if (this.messageTotalPage >= this.messageCurrentPage) {
      this.getmessageList(this.messageCurrentPage);
      this.messageSerialNumber = (this.messageCurrentPage - 1) * 10;
    } else {
      this.messageCurrentPage = this.messageCurrentPage - 1;
    }
  }

  messageLastPageList() {
    this.messageSerialNumber = (this.messageTotalPage - 1) * 10;
    this.getmessageList(this.messageTotalPage);

  }
  messageFirstPageList() {
    this.messageCurrentPage = 1;
    this.messageSerialNumber = 0;
    this.getmessageList(this.messageCurrentPage);
  }
  messagePreviousPageList() {
    this.messageCurrentPage = this.messageCurrentPage - 1;
    if (this.messageTotalPage >= this.messageCurrentPage && this.messageCurrentPage > 0) {
      this.getmessageList(this.messageCurrentPage);
      this.messageSerialNumber = (this.messageCurrentPage - 1) * 10;
    } else {
      this.messageCurrentPage = this.messageCurrentPage + 1;
    }
  }
  getmessageList(page) {
    this.messageLoading = true;
    this.messageTotalPage = 0;
    this.doctorService.getConsultationMessages(page).subscribe((data) => {
      this.messages = data['results'];
      this.messageTotalPage = data['total_pages'];
      this.messageCurrentPage = data['page_number'];
      this.messageLoading = false;
      //   ;
    }, error => {
      this.messageLoading = false;
      const status = error['status'];
      if (status == 400) {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
      else {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
    });
  }
  viewMessage(text, msgId, consultationUuid) {
    this.consultationUuid = consultationUuid;
    this.messageUuid = msgId;
    this.patientNotes = text;
  }
  getAllInstantAppointmentData() {

    this.doctorService.getAllInstantAppointment().subscribe((data) => {
      this.instantAppontmentList = data;
    }, error => {
      console.log(error);
    })
  }
  getTodayInstantAppointmentData() {
    const date = moment().format('YYYY-MM-DD');
    this.doctorService.getTodayInstantAppointment(date).subscribe((data: any) => {
      this.todayInstantAppontmentList = data['results'];
      this.completedAppointmentsCount = this.completedAppointmentsCount + data['count'];
      this.appointmentsCount = this.appointmentsCount + data['count'];
      for (let value of this.todayInstantAppontmentList) {
        this.patientDetailList.push(value.patient_user_json);
        this.completedAppointments.push({
          start_datetime: `${value.request_datetime}`, patient_user_json: {
            uuid: value.patient_user_json.uuid,
            phone: value.patient_user_json.phone, username: value.patient_user_json.username,
            profile_picture: value.patient_user_json.profile_picture, email: value.patient_user_json.email,
            gender: value.patient_user_json.gender, age: value.patient_user_json.age
          },
          fulfilment_status: `${value.fulfilment_status}`, consultation_uuid: `${value.consultation_uuid}`, patient: `${value.patient}`, doctor: `${value.doctor}`
        })
      }
    }, error => {
      console.log(error);
    })
  }
  markAsRead() {
    this.doctorService.changeMessageStatus(this.consultationUuid, this.messageUuid).subscribe((data) => {
      $('#viewMessageModal').modal('hide');
      this.messages = this.messages.filter(
        msg => msg.uuid != this.messageUuid
      );
      this.notificationService.success(`Message Marked as Read`, 'Med.Bot');

    }, error => {

      console.log(error);
      const status = error['status'];
      if (status == 400) {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
      else {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
    })
  }

  endConsultation() {
    this.teleConsultService.completeConsultation(this.consultationUuid).subscribe(data => {
      this.available_now = false;
      $('#consultation-complete').model('hide');
    },
      error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          const errMessage = error['error']['error_message']
          this.notificationService.error(errMessage, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }

  viewReport(patirnt_uuid, doctor_uuid, consultid) {
    this.router.navigate(['/medic-report', patirnt_uuid, doctor_uuid, consultid])
  }
  getReports(id) {
    this.doctorService.getMedicalReportsById(id).subscribe(data => {
      this.reportFiles = Object.values(data['results']);
      if (this.reportFiles.length > 0) {
        $('#reports').modal('show');
      } else {
        this.notificationService.warning('Patient did not upload any report');
      }

    }, error => {
      console.log(error
      )
    })
  }
  openFile(file) {
    window.open(file);
  }
  tabSelection(val: string) {

    switch (val) {
      case 'today-pending':
        this.doctorPendingAppointment(1);
        break;
      case 'ongoing':
        this.getTodayStartedAppointments(1);
        break;
      case 'today-missed':
        this.getTodayMissedAppointments(1);
        break;
      case 'today-completed':
        this.getTodayCompletedAppointments(1);
        break;
      case 'upcoming':
        this.doctorUpcomingAppointment(1);
        break;
      case 'past-appointments':
        this.doctorPastAppointment(1);
        break;
      case 'past-instant':
        this.doctorPastInstantAppointment(1);
        break;
    }
  }

  appointmentCount() {
    const date = moment(new Date()).format('YYYY-MM-DD');
    const query = 'start_datetime=' + date + `&end_datetime=` + date;
    this.doctorService.getDoctorAppointmentCount(query).subscribe((data: any) => {
      console.log(data);
      this.todayAppointmentsCount = data.appointments_today;
      this.completedAppointmentsCount = data.completed_appointments_today;
      this.pendingAppointmentsCount = data.pending_appointments_today;
      this.totalAppointmentsCount = data.total_consultations;
    });
  }
}
