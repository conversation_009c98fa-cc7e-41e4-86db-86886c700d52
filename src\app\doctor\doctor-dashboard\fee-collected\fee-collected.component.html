
 <h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" style=" color: #20C0F3;" (click)="back()"></i>Back</h5><div class="row mb-5">
    <div class="col-md-12">
        <div>
            <h4 id="dash-activity-title" class="mb-4">Today Fees</h4>
        </div>
        <div class="col-md-12 float-right">
            <div class="float-right">
                <nav aria-label="Page navigation example" *ngIf="this.feeTotalPage > 1">
                    <ul class="pagination">
                        <li class="page-item" (click)="feeFirstPageList()" [ngClass]="{
                'disabled-pagination':
                  feeCurrentPage === 1
              }">
                            <a class="page-link">&lt;&lt;</a>
                        </li>
                        <li class="page-item" (click)="feePreviousPageList()" [ngClass]="{
                'disabled-pagination':
                  feeCurrentPage === 1
              }">
                            <a class="page-link">&lt;</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link">page &nbsp;{{
                  feeCurrentPage
                }}&nbsp;of&nbsp; {{ feeTotalPage }}</a
              >
            </li>
            <li
              class="page-item"
              (click)="feeNextPageList()"
              [ngClass]="{
                'disabled-pagination':
                  feeCurrentPage ===
                  feeTotalPage
              }"
            >
              <a class="page-link">&gt;</a>
                        </li>
                        <li class="page-item" (click)="feeLastPageList()" [ngClass]="{
                'disabled-pagination':
                  feeCurrentPage ===
                  feeTotalPage
              }">
                            <a class="page-link">&gt;&gt;</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
        <div *ngIf="feeLoading">
            <app-loading-spinner></app-loading-spinner>
        </div>
        <div class="card card-table mb-0" *ngIf="!feeLoading">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-center mb-0">
                        <thead>
                            <tr>
                                <th class="text-left">
                                    <h5>#</h5>
                                </th>

                                <th class="text-left">
                                    <h5>Patient Name</h5>
                                </th>
                                <th class="text-left">
                                  <h5>Consultation Date</h5>
                              </th>
                              <th class="text-left">
                                <h5>Consultation Time</h5>
                            </th>
                                <th class="text-left">
                                    <h5>Fee</h5>
                                </th>


                                <!-- <th class="text-left">Mark as Read</th> -->
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let fee of fees; let i = index">
                                <td>{{ i +feeSerialNumber+ 1 }}</td>
                                <td>{{ fee['customer_name'] }} </td>
                                <td>{{ fee['start_datetime']|date:'dd-MM-yyyy' }} </td>
                                <td>{{fee['start_datetime']|date:'hh:mm a'}}-{{ fee['end_datetime']|date:'hh:mm a' }}</td>
                                <td> &#8377;{{ fee.amount==null?0:fee.net_amount }}</td>

                                <td></td>
                                <td></td>
                            </tr>
                            <tr *ngIf="fees.length>0">
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>Total</td>
                                <td>&#8377;{{totalFee}}.00</td>
                                <td></td>

                            </tr>
                        </tbody>
                    </table>
                    <p id="no-msg-data" class="nm-size " *ngIf="fees?.length == 0 || !fees">
                        No New Fees Data
                    </p>
                </div>
                <div class="col-md-12 float-right mt-3">
                    <div class="float-right">
                        <nav aria-label="Page navigation example" *ngIf="this.feeTotalPage > 1">
                            <ul class="pagination">
                                <li class="page-item" (click)="feeFirstPageList()" [ngClass]="{
                'disabled-pagination':
                  feeCurrentPage === 1
              }">
                                    <a class="page-link">&lt;&lt;</a>
                                </li>
                                <li class="page-item" (click)="feePreviousPageList()" [ngClass]="{
                'disabled-pagination':
                  feeCurrentPage === 1
              }">
                                    <a class="page-link">&lt;</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link">page &nbsp;{{
                  feeCurrentPage
                }}&nbsp;of&nbsp; {{ feeTotalPage }}</a
              >
            </li>
            <li
              class="page-item"
              (click)="feeNextPageList()"
              [ngClass]="{
                'disabled-pagination':
                  feeCurrentPage ===
                  feeTotalPage
              }"
            >
              <a class="page-link">&gt;</a>
                                </li>
                                <li class="page-item" (click)="feeLastPageList()" [ngClass]="{
                'disabled-pagination':
                  feeCurrentPage ===
                  feeTotalPage
              }">
                                    <a class="page-link">&gt;&gt;</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        </div>
        </div>
