import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { FeeCollectedComponent } from './fee-collected.component';

describe('FeeCollectedComponent', () => {
  let component: FeeCollectedComponent;
  let fixture: ComponentFixture<FeeCollectedComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ FeeCollectedComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FeeCollectedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
