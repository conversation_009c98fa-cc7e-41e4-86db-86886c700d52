import { Component, OnInit } from '@angular/core';
import { DoctorService } from '../../doctor.service';
import * as moment from 'moment';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
@Component({
  selector: 'app-fee-collected',
  templateUrl: './fee-collected.component.html',
  styleUrls: ['./fee-collected.component.css'],
})
export class FeeCollectedComponent implements OnInit {
  feeCurrentPage: number = 1;
  feeTotalPage: number = 0;
  feeSerialNumber: number = 0;
  feeLoading: boolean;
  fees: any = [];
  totalFee: number = 0;

  constructor(
    private doctorService: DoctorService,
    private notificationService: ToastrService,
    private location: Location
  ) {}

  ngOnInit(): void {
    this.getfeeList(this.feeCurrentPage);
  }
  feeNextPageList() {
    this.feeCurrentPage = this.feeCurrentPage + 1;
    if (this.feeTotalPage >= this.feeCurrentPage) {
      this.getfeeList(this.feeCurrentPage);
      this.feeSerialNumber = (this.feeCurrentPage - 1) * 10;
    } else {
      this.feeCurrentPage = this.feeCurrentPage - 1;
    }
  }

  feeLastPageList() {
    this.feeSerialNumber = (this.feeTotalPage - 1) * 10;
    this.getfeeList(this.feeTotalPage);
  }
  feeFirstPageList() {
    this.feeCurrentPage = 1;
    this.feeSerialNumber = 0;
    this.getfeeList(this.feeCurrentPage);
  }
  feePreviousPageList() {
    this.feeCurrentPage = this.feeCurrentPage - 1;
    if (this.feeTotalPage >= this.feeCurrentPage && this.feeCurrentPage > 0) {
      this.getfeeList(this.feeCurrentPage);
      this.feeSerialNumber = (this.feeCurrentPage - 1) * 10;
    } else {
      this.feeCurrentPage = this.feeCurrentPage + 1;
    }
  }
  getfeeList(page) {
    this.feeLoading = true;
    this.feeTotalPage = 0;
    const id = localStorage.getItem('Doctor');
    const date = moment(new Date()).format('YYYY-MM-DD');
    const tomorrowDate = new Date(date);
    tomorrowDate.setDate(tomorrowDate.getDate() + 1);
    const tomDate = moment(tomorrowDate).format('YYYY-MM-DD');
    const todaySearchParams =
      '?from_date=' + date + '&to_date=' + tomDate + '&page=' + page;
    this.doctorService.getEarningData(todaySearchParams, id).subscribe(
      (data) => {
        this.fees = data['results'];
        this.feeTotalPage = data['total_pages'];
        this.feeCurrentPage = data['page_number'];
        this.feeLoading = false;
        for (let data of this.fees) {
          this.totalFee = this.totalFee + parseInt(data.net_amount);
        }
      },
      (error) => {
        this.feeLoading = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  viewfee(text) {}
  back() {
    this.location.back();
  }
}
