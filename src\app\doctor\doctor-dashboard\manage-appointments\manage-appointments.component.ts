import { ToastrService } from 'ngx-toastr';
import { DoctorService } from './../../doctor.service';
import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import  * as moment from 'moment';
declare var $:any;
@Component({
  selector: 'app-manage-appointments',
  templateUrl: './manage-appointments.component.html',
  styleUrls: ['./manage-appointments.component.css']
})

export class ManageAppointmentsComponent implements OnInit {
  @Output() activity = new EventEmitter();
  public selectedDate: any;
  public todayDate: any;
  public endDate: any;
  public weekDays = [];
  public weekData = [];
  public weekInfo = [];
  public searchParams = '';
  public doctor: any;
  public calendarData: any;
  public slots = [];
  // public slotCss = "btn btn-secondary btn-booked";
  // public bookedCss = "btn btn-failed btn-booked";
  public slotsAvailable = false;
  public selectedIdList = [];
  public appointments = [];
  public bookedAppointments=[];
  public blockedAppointments=[];
  public maxDate:Date;
  public minDate:Date;
  public isLoading= false;
  constructor(
    private doctorService: DoctorService,
    private notificationService: ToastrService
  ) { }

  ngOnInit(): void {
    this.maxDate = new Date();
    this.minDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate()+30);
    this.doctorService.getDoctorProfile().subscribe(
      data => {
        this.doctor = data;
        this.todayDate = moment().format('YYYY-MM-DD');
        this.selectedDate = moment();
        this.getSelectedDateDays(this.selectedDate);
      });

  }

  getSelectedDateDays(selectedDate){
    this.isLoading= true;
  this.selectedDate = selectedDate;
    this.weekDays = [];
    this.weekData = [];
    this.weekInfo = [];
    this.slots = [];
    this.weekDays.push(moment(this.selectedDate).format('dddd'));
    this.weekData.push(moment(this.selectedDate).format('MMM D YYYY'));
    let date = new Date(this.selectedDate);
    let tomorrowDate = new Date(date);
    for(let i=0; i<6; i++){
      tomorrowDate.setDate(tomorrowDate.getDate()+1);
      this.weekDays.push(moment(tomorrowDate).format('dddd'));
      this.weekData.push(moment(tomorrowDate).format('MMM D YYYY'));
    }
    for (let i=0; i<this.weekDays.length; i++){
        let data  = [];
        data['day'] = this.weekDays[i];
        data['date'] = this.weekData[i];
        this.weekInfo.push(data);
    }
    this.getSlots(this.selectedDate);
  }

  getSlots(dateSelected){
    this.slots = [];
    let date = new Date(dateSelected);
    let dateValueList = [];
    let setdateVal = '';
    let dateInc = new Date(date);
    for (let i=0; i<7; i++){
      let dateValue = parseInt(moment(dateInc).format('D'));
      let monthValue = moment(dateInc).format('MM');
      let yearValue = moment(dateInc).format('YYYY');
      let setVal= dateValue;
      if(setVal < 10){
        setdateVal = '0'+setVal.toString()+'/'+monthValue+'/'+yearValue;
      }
      else{
        setdateVal = setVal.toString()+'/'+monthValue+'/'+yearValue;
      }
      dateInc.setDate(dateInc.getDate() + 1);
      dateValueList.push(setdateVal);
    }
    let seventhDate = new Date(date);
    seventhDate.setDate(seventhDate.getDate()+6);
    this.searchParams = '?from_datetime=' +moment(this.selectedDate).format('YYYY-MM-DD') + '&to_datetime='+ moment(seventhDate).format('YYYY-MM-DD');
    this.doctorService.getDoctorAppointmentSlot(this.doctor['uuid'],this.searchParams).subscribe(
      data => {
        this.calendarData = data;
        const nullObj = {
          'status':'No Slot'
        };
        const slot_data = Object.values(data);
        const dates = slot_data[0];
        for(let i=1; i<slot_data.length; i++){
          this.slots.push(slot_data[i]);
          this.slotsAvailable = true;
        }
        for(let i=0; i<this.slots.length; i++){
          for (let j=0; j<dateValueList.length; j++){
            if(!dates.includes(dateValueList[j])){
              this.slots[i].splice(j+1,0,{'status':'No Slot'});
            }
          }
          for(let k=0; k<this.slots[i].length; k++){
            if(this.slots[i][k] === null){
              this.slots[i][k] = {'status':'No Slot'};
            }
          }
        }
      this.isLoading= false;
      },
      error => {
        this.isLoading= false;
        console.log(error);
        const status = error['status'];
        if(status == 400){
          const message =error['error']['error']['message'];
          if(message){
            this.notificationService.error(`${message}`, 'Med.Bot');
          }else{
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }

          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        this.slotsAvailable = false;
      }
    );
  }

  returnToAppoinments(){
    this.activity.emit('No Activity');
  }

  addAppointment(data,id){

    if(this.appointments.find(
      (obj) => obj.uuid === data['uuid']
    )){
      this.appointments = this.appointments.filter(
        (obj) => obj.uuid !== data['uuid']
      );
      this.selectedIdList = this.selectedIdList.filter(
        (searchId) => searchId != id
      );
    }
    else{
      this.appointments.push(data);
      this.bookedAppointments = this.appointments.filter(
        (obj) => obj.status === 'Booked'
      );
      this.blockedAppointments = this.appointments.filter(
        (obj) => obj.status === 'Blocked'
      );
      this.selectedIdList.push(id);
    }
  }

  markSlotsUnavailable(arrData){
    for(let i=0; i<arrData.length; i++){
      const uuid = arrData[i]['uuid'];
      const datetime = arrData[i]['from_datetime'];
      const formated = new Date(Date.parse(datetime));
      const currentDateTime = new Date();

      if(formated > currentDateTime){
        this.doctorService.blockAppointmentSlot(uuid).subscribe(
          data => {

            $("#confirmMaModal").modal("hide");
            this.returnToAppoinments();
            if(i===0){
              this.notificationService.success('Slots marked Unavailable', "Med.Bot");
            }

          },error=>{
            console.log(error);
            const status = error['status'];
            if(status == 400){
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
              }
            else if(status == 409){
              this.notificationService.error(`${error['error']['error_message']}`, 'Med.Bot');
            }else{
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          }
        );
      }
      else{
        $("#confirmMaModal").modal("hide");
        this.notificationService.error("Past Slots can't marked Unavailable");
      }
    }

  }

  clearSelection(){
    this.selectedIdList=[];
    this.appointments = [];
  }

  modifySelect(data,event){
    // this.appointments = [];
    // this.selectedIdList = [];
    let id = data.split(" ");
    id = parseInt(id[1]) + 1;
    if(event.target.checked){
      for(let i=0; i<this.slots.length; i++){
          const slot = this.slots[i][id];
          if(slot['status'] =='Available' || slot['status']=='Blocked'){
            let slot_id = "slot"+i+'-'+id;
            this.selectedIdList.push(slot_id);
            this.appointments.push(slot);
          }
      }
      this.bookedAppointments = this.appointments.filter(
        (obj) => obj.status === 'Booked'
      );
      this.blockedAppointments = this.appointments.filter(
        (obj) => obj.status === 'Blocked'
      );
    }
    else{
      for(let i=0; i<this.slots.length; i++){
        const slot = this.slots[i][id];
        if(slot['status'] =='Available' || slot['status']=='Blocked'){
          let slot_id = "slot"+i+'-'+id;
          this.selectedIdList = this.selectedIdList.filter(
            (id)=> id!=slot_id
          );
          this.appointments = this.appointments.filter(
            (slot_dat)=>slot_dat != slot
          );
          this.bookedAppointments = this.appointments.filter(
            (obj) => obj.status === 'Booked'
          );
        }
    }
    }
  }

  markSlotsAvailable(arrData){
    for(let i=0; i<arrData.length; i++){
      const uuid = arrData[i]['uuid'];
      const datetime = arrData[i]['from_datetime'];
      const formated = new Date(Date.parse(datetime));
      const currentDateTime = new Date();
      if(formated > currentDateTime){
        this.doctorService.availableAppointmentSlot(uuid).subscribe(
          data => {

            $("#confirmMaModal").modal("hide");
            this.returnToAppoinments();
            if(i===0){
              this.notificationService.success('Slots marked available', "Med.Bot");
            }

          },error=>{
            console.log(error);
            const status = error['status'];
            if(status == 400){
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
              }
            else if(status == 409){
              this.notificationService.error(`${error['error']['error']}`, 'Med.Bot');
            }else{
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          }
        );
      }
      else{
        $("#confirmMaModal").modal("hide");
        this.notificationService.error("Past Slots can't marked Unavailable");
      }
    }

  }
}
