<div class="content">
    <div class="container-fluid">
        <div class="col-lg-12">
            <!-- <div class="col-md-5 col-lg-4 col-xl-3 theiaStickySidebar">
            </div> -->
            <h3 class="mb-4 ms text-info"><i class="fas fa-chevron-circle-left" (click)="openDoctorDashboard()"></i> Consultation</h3>
            <div class="text-center ">
                <!-- <h3 class="text-info">Consultation</h3> -->
            </div>
        </div>

        <form [formGroup]="consultForm" (ngSubmit)="onFormSubmit()">
            <div class="row mt-4">
                <div class="column  card bg-secondary resize" style="width: 800px; height: 700px; left: 1%; right: 1%;">
                    <app-doctor-messages></app-doctor-messages>
                </div>
                <div class="column col-md-6">
                    <div class="row">
                        <div class="card-body d-flex flex-column">
                            <div>
                                <div class="row">
                                    <div class="col-sm-12">
                                        <!-- <div class="card"> -->
                                        <div class="card-body">
                                            <div class="profile-box">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="card schedule-widget mb-0">

                                                            <div class="schedule-header">
                                                                <div class="schedule-nav">
                                                                    <ul class="nav nav-tabs nav-justified">
                                                                        <li class="nav-item">
                                                                            <a class="nav-link  pt-4 pb-2" data-toggle="tab" href="#slot_medical">
                                                                                <h6><i class="fas fa-book-medical"></i> Medical History</h6>
                                                                            </a>
                                                                        </li>
                                                                        <li class="nav-item">
                                                                            <a class="nav-link active pt-4 pb-4" data-toggle="tab" href="#slot_vitals">
                                                                                <h6><i class="fab fa-digital-ocean"></i> Vitals</h6>
                                                                            </a>
                                                                        </li>
                                                                        <li class="nav-item">
                                                                            <a class="nav-link pt-4 pb-2" data-toggle="tab" href="#slot_physicalexam">
                                                                                <h6><i class="fas fa-user-md"></i> Physical Exam</h6>
                                                                            </a>
                                                                        </li>
                                                                        <li class="nav-item">
                                                                            <a class="nav-link pt-4 pb-2" data-toggle="tab" href="#slot_systemicexam">
                                                                                <h6><i class="fas fa-assistive-listening-systems"></i>Systemic Exam</h6>
                                                                            </a>
                                                                        </li>
                                                                        <li class="nav-item">
                                                                            <a class="nav-link pt-3 pb-3" data-toggle="tab" href="#slot_diagnosis">
                                                                                <h6><i class="fas fa-diagnoses"></i> Diagnosis</h6>
                                                                            </a>
                                                                        </li>
                                                                        <li class="nav-item">
                                                                            <a class="nav-link pt-3 pb-3" data-toggle="tab" href="#slot_investigation">
                                                                                <h6><i class="fas fa-comment-medical"></i> Investigation</h6>
                                                                            </a>
                                                                        </li>
                                                                        <li class="nav-item">
                                                                            <a class="nav-link pt-3 pb-3" data-toggle="tab" href="#slot_prescription">
                                                                                <h6><i class="fas fa-pills"></i> Prescription</h6>
                                                                            </a>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                            <div class="tab-content schedule-cont">

                                                                <div id="slot_medical" class="tab-pane fade">
                                                                    <div class="card border-info pb-2" style="height: 410px; overflow: scroll;">
                                                                        <h5 class="text-danger text-center mt-4 pl-2">Medical History</h5>


                                                                        <table class="mt-3">
                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Immunization History
                                                                                    <textarea (focusout)="onKey('medical_history','immunization_history', $event)" formControlName="immunizationHistory" name="immunizationHistory" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Personal History
                                                                                    <textarea (focusout)="onKey('medical_history','past_medical_history', $event)" formControlName="personalHistory" name="personalHistory" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr class="pt-2">
                                                                                <td class="pl-2 mt-2">
                                                                                    Appetite
                                                                                    <textarea (focusout)="onKey('medical_history','appetite', $event)" formControlName="appetite" name="appetite" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Diet
                                                                                    <textarea (focusout)="onKey('medical_history','diet', $event)" formControlName="dietHistory" name="dietHistory" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>

                                                                            <tr class="ml-2 pt-2">
                                                                                <td class="pl-2">
                                                                                    Thirst / Water Intake
                                                                                    <textarea (focusout)="onKey('medical_history','thirst', $event)" formControlName="thirst" name="thirst" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Sleep
                                                                                    <textarea formControlName="sleep" name="sleep" (focusout)="onKey('medical_history','sleep', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="pl-2">
                                                                                    Social History/ Habits/ Addictions
                                                                                    <textarea formControlName="habits" name="habits" (focusout)="onKey('medical_history','bladder', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Smoking
                                                                                    <textarea formControlName="smoking" name="smoking" (focusout)="onKey('medical_history','smoking', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Alcohol
                                                                                    <textarea formControlName="alcohol" name="alcohol" (focusout)="onKey('medical_history','alcohol', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Drugs
                                                                                    <textarea formControlName="drugs" name="drugs" (focusout)="onKey('medical_history','drugs', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>                                                                                    </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="pl-2">
                                                                                    Sexual History
                                                                                    <textarea formControlName="sexualHistory" name="sexualHistory" (focusout)="onKey('medical_history','sexual_history', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Other Observation/ Notes
                                                                                    <textarea formControlName="otherObservation" name="otherObservation" (focusout)="onKey('medical_history','other_observations', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <!-- <div class="col-md-12 text-center mt-4 pt-4 ml-4 pl-4 mb-4 text-success"> -->
                                                                            <div class="d-flex justify-content-center text-success">
                                                                                <tr class="mt-2">

                                                                                    <h5>Female patient </h5>
                                                                                    <input type="radio" class="ml-3" (click)="onGenderFemale()" formControlName="gender" name="gender" value="yes">
                                                                                    <label class="ml-1" for="gender">Yes</label>
                                                                                    <input type="radio" class="ml-3" (click)="onGenderNoFemale()" formControlName="gender" name="gender" value="no">
                                                                                    <label class="ml-1" for="gender">No</label>
                                                                                    <!-- </div> -->
                                                                                </tr>
                                                                            </div>
                                                                            <!-- <div *ngIf="female"> -->
                                                                            <tr *ngIf="female">
                                                                                <td class="pl-2">
                                                                                    Gynaecological History
                                                                                    <textarea formControlName="gynaecologicalHistory" name="gynaecologicalHistory" (focusout)="onKey('medical_history','past_medical_history', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 80%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Age of Menarche
                                                                                    <textarea formControlName="ageOfMenarch" name="ageOfMenarch" (focusout)="onKey('medical_history','age_of_menarch', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 90%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr *ngIf="female">
                                                                                <td class="pl-2">
                                                                                    Menstrual History
                                                                                    <textarea formControlName="menstrualHistory" name="menstrualHistory" (focusout)="onKey('medical_history','menstrual_history', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 80%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Last Menstrual Period
                                                                                    <textarea formControlName="lastMenstrualPeriod" name="lastMenstrualPeriod" (focusout)="onKey('medical_history','last_menstrual_period', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 90%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr *ngIf="female">
                                                                                <td class="pl-2">
                                                                                    Number Of Pregnancy
                                                                                    <textarea formControlName="numberOfPregnancy" name="numberOfPregnancy" (focusout)="onKey('medical_history','number_of_pregnancy', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 80%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Gravida
                                                                                    <textarea formControlName="gravida" name="gravida" (focusout)="onKey('medical_history','gravida', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 90%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr *ngIf="female">
                                                                                <td class="pl-2">
                                                                                    Para
                                                                                    <textarea formControlName="para" name="para" (focusout)="onKey('medical_history','para', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 80%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Abortions
                                                                                    <textarea formControlName="abortions" name="abortions" (focusout)="onKey('medical_history','abortions', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 90%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <!-- </div> -->

                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <!-- </div> -->

                                                                <div id="slot_vitals" class="tab-pane fade show active">
                                                                    <div class="card border-info pb-2" style="height: 410px;">
                                                                        <!-- <div class="pl-7 mt-2"> -->
                                                                        <!-- <h5 class="text-danger text-center mt-4 mb-4">Vitals</h5> -->
                                                                        <!-- </div> -->
                                                                        <table class="mt-4">
                                                                            <tr class="ml-2 mt-3">

                                                                                <td class="pl-2 mt-2">
                                                                                    <h6 class="ml-3"><i class="fas fa-file-medical-alt"></i> Blood Pressure Systolic</h6>
                                                                                    <input (focusout)="onKey('vital_signs','blood_pressure_systolic', $event)" class="ml-3" pattern="^[1-9]" formControlName="bloodPressureSystolic" type="textarea" style="width: 70px;">
                                                                                    <!-- <textarea formControlName="bloodPressure" name="bloodPressure" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>         -->
                                                                                </td>

                                                                                <td class="pl-2 mt-2">
                                                                                    <h6 class="ml-3"><i class="fas fa-file-medical-alt"></i> Blood Pressure Diastolic</h6>
                                                                                    <input (focusout)="onKey('vital_signs','blood_pressure_diastolic', $event)" class="ml-3" pattern="^[1-9]" formControlName="bloodPressureDiastolic" type="textarea" style="width: 70px;">
                                                                                    <!-- <textarea formControlName="bloodPressure" name="bloodPressure" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>         -->
                                                                                </td>
                                                                                <td class="pl-2 mt-2">
                                                                                    <h6 class="ml-3"><i class="fa fa-thermometer-empty" aria-hidden="true"></i> Temperature (Farenheit) </h6>
                                                                                    <input (focusout)="onKey('vital_signs','temperature', $event)" class="ml-3" formControlName="temperature" type="number">
                                                                                </td>
                                                                                <td>
                                                                                    <h6 class="ml-3"><i class="fa fa-heartbeat" aria-hidden="true"></i> Pulse Rate </h6>
                                                                                    <input (focusout)="onKey('vital_signs','pulse_rate', $event)" class="ml-3" formControlName="pulseRate" type="number">
                                                                                    <!-- <textarea formControlName="pulseRate" name="pulseRate" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>     -->
                                                                                </td>

                                                                            </tr>
                                                                            <tr>
                                                                                <div class="mt-4 mb-4"></div>
                                                                            </tr>

                                                                            <tr>
                                                                                <td class="ml-2 justify-content-center">
                                                                                    <div class="mt-4 pl-2" style="margin-left: 10px;margin-right: 20px;">
                                                                                        <h6 class="ml-3"><i class="fas fa-stethoscope"></i> Auscultation </h6>
                                                                                        <textarea formControlName="auscultation" name="auscultation" (focusout)="onKey('vital_signs','auscultation', $event)" class="form-control" id="notes" cols="5" rows="1" style="width: 100%;"></textarea>
                                                                                    </div>
                                                                                </td>
                                                                                <td>
                                                                                    <div class="mt-4 pl-4" style="margin-left: 20px;margin-right: 20px;">
                                                                                        <h6 class="ml-3"><i class="fas fa-wave-square"></i> ECG </h6>
                                                                                        <textarea formControlName="ecg" name="ecg" (focusout)="onKey('vital_signs','ecg', $event)" class="form-control" id="notes" cols="5" rows="1" style="width: 100%;"></textarea>
                                                                                    </div>
                                                                                </td>
                                                                                <td class="pl-2 mt-2">
                                                                                    <h6 class="ml-3"><i class="fas fa-file-medical-alt"></i> SPO2</h6>
                                                                                    <input (focusout)="onKey('vital_signs','spo2', $event)" class="ml-3" pattern="^[1-9]" formControlName="spo2" type="textarea" style="width: 70px; height: 40px;">
                                                                                    <!-- <textarea formControlName="bloodPressure" name="bloodPressure" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>         -->
                                                                                </td>
                                                                                <td class="ml-4 justify-content-center">
                                                                                    <div class="mt-4 pl-4 mr-2">
                                                                                        <h6 class="ml-3"><i class="far fa-sticky-note"></i> Additional Notes </h6>
                                                                                        <textarea formControlName="notes" name="notes" (focusout)="onKey('vital_signs','auscultation', $event)" class="form-control mr-3" id="notes" cols="20" rows="1"></textarea>
                                                                                    </div>
                                                                                </td>
                                                                        </table>
                                                                    </div>
                                                                </div>

                                                                <div id="slot_physicalexam" class="tab-pane fade">
                                                                    <div class="card border-info pb-2" style="height: 410px; overflow: scroll;">
                                                                        <!-- <h5 class="text-danger text-center mt-4 pl-2 mb-4">Physical Examination</h5> -->
                                                                        <table class="pt-2 pb-2 mt-4">
                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Weight (kg)
                                                                                    <input formControlName="weight" type="number" (focusout)="onKey('physical_examination','weight', $event)" style="width:22%">
                                                                                </td>
                                                                                <td>
                                                                                    Height (cm)
                                                                                    <input formControlName="height" type="number" (focusout)="onKey('physical_examination','height', $event)" style="width:22%">
                                                                                </td>
                                                                                <td>
                                                                                    BMI
                                                                                    <input formControlName="height" type="number" (focusout)="onKey('physical_examination','bmi', $event)" style="width:22%">
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                        <table class="mt-3">
                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Nutrition
                                                                                    <textarea formControlName="nutrition" name="nutrition" (focusout)="onKey('physical_examination','nutrition', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td class="mt-2">
                                                                                    Nail Changes
                                                                                    <textarea formControlName="nailChanges" name="nailChanges" (focusout)="onKey('physical_examination','nail_changes', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>

                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Clubbing Of Fingers
                                                                                    <textarea formControlName="clubbingOfFingers" name="clubbingOfFingers" (focusout)="onKey('physical_examination','clubbing_of_fingers', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Cyanosis
                                                                                    <textarea formControlName="cyanosis" name="cyanosis" (focusout)="onKey('physical_examination','cyanosis', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Icterus/Jaundice
                                                                                    <textarea formControlName="icterusJaundice" name="icterusJaundice" (focusout)="onKey('physical_examination','icterus_jaundice', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Pallor
                                                                                    <textarea formControlName="pallor" name="pallor" (focusout)="onKey('physical_examination','pallor', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Lymph Nodes
                                                                                    <textarea formControlName="lymphNodes" name="lymphNodes" (focusout)="onKey('physical_examination','lymph_nodes', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Oedema
                                                                                    <textarea formControlName="oedema" name="oedema" (focusout)="onKey('physical_examination','oedema', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Sclera
                                                                                    <textarea formControlName="sclera" name="sclera" (focusout)="onKey('physical_examination','sclera', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Other Observations
                                                                                    <textarea formControlName="otherObservations" name="otherObservations" (focusout)="onKey('physical_examination','other_observations', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>                                                                                    </td>
                                                                            </tr>
                                                                            <!-- <div class="col-md-12 text-center mt-4 pt-4 ml-4 pl-4 mb-4 text-success"> -->
                                                                        </table>
                                                                    </div>
                                                                </div>

                                                                <div id="slot_systemicexam" class="tab-pane fade">

                                                                    <div class="card border-info pb-2" style="height: 410px; overflow: scroll;">
                                                                        <!-- <h5 class="text-danger text-center mt-4 pl-2 pb-2">Systemic Examination</h5> -->


                                                                        <table class="mt-4">
                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Respiratory System
                                                                                    <textarea formControlName="respiratorySystem" name="respiratorySystem" (focusout)="onKey('systemic_examination','respiratory_system', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Gastro Intestinal/Abdomen
                                                                                    <textarea formControlName="gastroIntestinal" name="gastroIntestinal" (focusout)="onKey('systemic_examination','gastro_intestinal_system', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr class="pt-2">
                                                                                <td class="pl-2 mt-2">
                                                                                    Cardio Vascular System
                                                                                    <textarea formControlName="cardioVascular" name="cardioVascular" (focusout)="onKey('systemic_examination','cardio_vascular_system', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Genito Urinary System
                                                                                    <textarea formControlName="genitoUrinary" name="genitoUrinary" (focusout)="onKey('systemic_examination','genitourinary_system', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>

                                                                            <tr class="ml-2 pt-2">
                                                                                <td class="pl-2">
                                                                                    Musculoskeletal System
                                                                                    <textarea formControlName="musculoSkeletal" name="musculoSkeletal" (focusout)="onKey('systemic_examination','musculoskeletal_system', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Central Nervous System
                                                                                    <textarea formControlName="centralNervous" name="centralNervous" (focusout)="onKey('systemic_examination','central_nervous_system', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="pl-2">
                                                                                    Eye
                                                                                    <textarea formControlName="eye" name="eye" (focusout)="onKey('systemic_examination','eye', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Ear
                                                                                    <textarea formControlName="ear" name="ear" (focusout)="onKey('systemic_examination','ear', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Nose
                                                                                    <textarea formControlName="nose" name="nose" (focusout)="onKey('systemic_examination','nose', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Mouth
                                                                                    <textarea formControlName="mouth" name="mouth" (focusout)="onKey('systemic_examination','mouth', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>                                                                                    </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="pl-2">
                                                                                    Throat
                                                                                    <textarea formControlName="throat" name="throat" (focusout)="onKey('systemic_examination','throat', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Neck
                                                                                    <textarea formControlName="neck" name="neck" (focusout)="onKey('systemic_examination','neck', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="pl-2">
                                                                                    Skin
                                                                                    <textarea formControlName="skin" name="skin" (focusout)="onKey('systemic_examination','skin', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Psychiatric History
                                                                                    <textarea formControlName="psychiatricHistory" name="psychiatricHistory" (focusout)="onKey('systemic_examination','respiratory_system', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div id="slot_diagnosis" class="tab-pane fade">
                                                                    <div class="card border-info pb-2" style="height: 410px; overflow: scroll;">
                                                                        <!-- <h5 class="text-danger text-center mt-4 pl-2 pb-2">Diagnosis</h5> -->


                                                                        <table class="mt-4">
                                                                            <tr class="ml-2">
                                                                                <td class="pl-2">
                                                                                    Primary
                                                                                    <textarea formControlName="primary" name="primary" (focusout)="onKey('diagnosis','primary_diagnosis', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 80%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Secondary
                                                                                    <textarea formControlName="secondary" name="secondary" (focusout)="onKey('diagnosis','secondary_diagnosis', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 90%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                            <tr class="pt-2">
                                                                                <td class="pl-2 mt-2">
                                                                                    Differential Diagnosis
                                                                                    <textarea formControlName="differentialDiagnosis" name="differentialDiagnosis" (focusout)="onKey('diagnosis','differential_diagnosis', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 80%;"></textarea>
                                                                                </td>
                                                                                <td>
                                                                                    Final Diagnosis
                                                                                    <textarea formControlName="finalDiagnosis" name="finalDiagnosis" (focusout)="onKey('diagnosis','final_diagnosis', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 90%;"></textarea>
                                                                                </td>
                                                                            </tr>

                                                                            <tr class="ml-2 pt-2">
                                                                                <td class="pl-2">
                                                                                    ICD 10 Codes
                                                                                    <textarea formControlName="ICD10Codes" name="ICD10Codes" name="finalDiagnosis" (focusout)="onKey('diagnosis','icd_10_codes', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 80%;"></textarea>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div id="slot_investigation" class="tab-pane fade">
                                                                    <div class="col-xs-12 text-success">
                                                                        <div class="card border-info pb-2" style="height: 410px; overflow: scroll;">
                                                                            <!-- <h5 class="text-danger text-center mt-4 mb-4">Investigation</h5> -->

                                                                            <table class="mt-4">
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        HAEMATOLOGY
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="haematologyItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        BIOCHEMISTRY AND IMMUNOASSAYS
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="biochemistryItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        MICROBIOLOGY
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="microbiologyItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        CLINICAL PATHOLOGY
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="clinicalPathologyItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        PATHOLOGY
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="pathologyItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        SEROLOGY
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="serologyItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        MALARIA
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="malariaItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        FILARIASIS
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="filariasisItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        DENGUE
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="dengueItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        JAPANESE ENCEPHALITIS
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="japaneseEncephalitisItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        CHIKUNGUNYA
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="chikungunyaItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        SCRUB TYPHUS
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="scrubTyphusItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        LEPTOSPIROSIS
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="leptospirosisItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        BRUCELLOSIS
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="brucellosisItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        TUBERCULOSIS
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="tuberculosisItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        HIV
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="hivItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        HEPATITIS B
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="hepatitisBItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        HEPATITIS C
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="hepatitisCItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        HEPATITIS A
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="hepatitisAItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        HEPATITIS E
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="hepatitisEItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        HBC (CORE ANTIBODIES)
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="hbcItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        OTHER DIAGNOSTIC TESTS
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="otherDiagnosticTestsItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr class="ml-2 mt-4">
                                                                                    <td class="pl-2">
                                                                                        RADIOLOGY & OTHER DIAGNOSTIC TESTS
                                                                                        <div class="text-center mb-2" style="width: 75%;">
                                                                                            <ng-select [items]="radiologyAndOtherDiagnosticTestsItems" bindLabel="name" placeholder="Select item" appendTo="body" multiple="true" [addTag]="addTagFn" (focusout)="onKey('investigation','tests_prescribed', $event)">
                                                                                            </ng-select>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        <div class="text-center mb-2" style="width: 75%;">

                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>

                                                                            <!-- <div>
                                                                                    HAEMATOLOGY
                                                                                    <div class="text-center mb-2 ml-5 pl-4" style="width: 75%;">
                                                                                        <ng-select [items]="items"
                                                                                        bindLabel="name"
                                                                                        placeholder="Select item"
                                                                                        appendTo="body"
                                                                                        multiple="true"
                                                                                        >
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div> -->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div id="slot_prescription" class="tab-pane fade">
                                                                    <div class="col-md-12 text-danger">
                                                                        <div class="card border-info pb-2" style="height: 410px; overflow: scroll;">
                                                                            <!-- <h5 class="text-danger text-center mt-2">Prescription</h5> -->
                                                                            <div class="mt-3">
                                                                                <table class="table mt-4" id="prescriptionTable" #prescriptionTable>
                                                                                    <thead>
                                                                                        <tr>
                                                                                            <!-- <th scope="col">#</th> -->
                                                                                            <th scope="col">Medicine Name</th>
                                                                                            <th scope="col">Morning</th>
                                                                                            <th scope="col">Afternoon</th>
                                                                                            <th scope="col">Night</th>
                                                                                            <th scope="col">Before Food</th>
                                                                                            <th scope="col">No of Days</th>
                                                                                            <th scope="col">Remarks</th>
                                                                                        </tr>
                                                                                    </thead>

                                                                                    <tbody>
                                                                                        <tr>
                                                                                            <!-- <th scope="row">1</th> -->
                                                                                            <td> <input type="text" name="medicine"> </td>
                                                                                            <td><input type="checkbox"></td>
                                                                                            <td><input type="checkbox"></td>
                                                                                            <td><input type="checkbox"></td>
                                                                                            <td><input name='intake1' type="checkbox"> </td>
                                                                                            <td><input type="number" name="days" style="width: 50%;"></td>
                                                                                            <td> <input type="text" name="remarks"> </td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <!-- <th scope="row">2</th> -->
                                                                                            <td> <input type="text" name="medicine"> </td>
                                                                                            <td><input type="checkbox"></td>
                                                                                            <td><input type="checkbox"></td>
                                                                                            <td><input type="checkbox"></td>
                                                                                            <td><input name='intake2' type="checkbox"></td>
                                                                                            <td><input type="number" name="days" style="width: 50%;"></td>
                                                                                            <td> <input type="text" name="remarks"> </td>
                                                                                        </tr>
                                                                                    </tbody>
                                                                                </table>
                                                                                <span>
                                                                                <button (click)="onAddRow('prescriptionTable')">
                                                                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                                                                  </button>
                                                                                </span>
                                                                            </div>

                                                                            <div class="mt-4">
                                                                                <textarea formControlName="diet" name="diet" class="form-control" id="diet" cols="20" rows="2" placeholder="Diet / Recommendations"></textarea>
                                                                            </div>
                                                                            <div class="mt-4">
                                                                                <input class="ml-2" name="nextVisitDate" type="date" placeholder="Next Visit Date"> Next Visit Date
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="container">
                                                                    <div class="col-md-12 mb-2 text-center text-danger">
                                                                        <h5 class="text-info">
                                                                            <div>
                                                                                <!-- <button (click) = "onReport()" class="btn btn-secondary btn-lg mb-2 pl-10 pl-10 mr-3">Reports</button> -->
                                                                                <!-- <input class="mr-2" type="checkbox" id="tems-check" (change)="onCaseHistory($event)" /> Show Patient History -->
                                                                                <button class="btn btn-info btn-lg" *ngIf="!patientHistory" (click)="onShowPatientHistory()"><i class="fas fa-file-medical"></i> Show Patient History </button>
                                                                                <button class="btn btn-info btn-lg" *ngIf="patientHistory" (click)="onHidePatientHistory()"><i class="fas fa-file-medical"></i> Hide Patient History </button>
                                                                            </div>
                                                                        </h5>
                                                                        <!-- <button type="submit" (click)="onCaseHistory()" class="btn btn-secondary btn-lg mb-2 pl-10 ml-3">Case History</button> -->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <hr style="color: #333; height: 5px;">

    <div class="col-md-8 text-center mt-3" *ngIf="showCaseHistory" style="margin-left: 15%;">
        <h4 class="mt-3 text-info text-center">Patient History</h4>
        <!-- <div *ngIf="!caseHistoryDetailed"> -->
        <app-case-history-visit-dates (visitDate)="onVisitDate()"></app-case-history-visit-dates>
        <!-- </div> -->

        <div *ngIf="caseHistoryDetailed">
            <!-- <h3 class="pointer text-info d-flex justify-content-between"><i class="fas fa-chevron-circle-left" (click)="onBackCaseHistoryDetailed()"></i></h3> -->
            <app-case-history-detailed (close)="onBackCaseHistoryDetailed()"></app-case-history-detailed>
        </div>
        <!-- <app-case-history-detailed></app-case-history-detailed> -->
    </div>
</div>
