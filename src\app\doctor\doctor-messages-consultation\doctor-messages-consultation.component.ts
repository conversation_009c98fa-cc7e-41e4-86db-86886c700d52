import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { ActivatedRoute, Router } from '@angular/router';
import { DocumentModalComponent } from 'src/app/shared/document-modal/document-modal.component';
import { TeleConsultService } from '../../tele-consult/tele-consult.service'

@Component({
  selector: 'app-doctor-messages-consultation',
  templateUrl: './doctor-messages-consultation.component.html',
  styleUrls: ['./doctor-messages-consultation.component.css']
})
export class DoctorMessagesConsultationComponent implements OnInit {
    consultForm = new FormGroup({
      //Medical History
      immunizationHistory: new FormControl(),
      personalHistory: new FormControl(),
      appetite: new FormControl(),
      dietHistory: new FormControl(),
      thirst: new FormControl(),
      sleep: new FormControl(),
      habits: new FormControl(),
      smoking: new FormControl(),
      alcohol: new FormControl(),
      drugs: new FormControl(),
      sexualHistory: new FormControl(),
      notes: new FormControl(),
      gender: new FormControl(),
      gynaecologicalHistory: new FormControl(),
      ageOfMenarch: new FormControl(),
      menstrualHistory: new FormControl(),
      lastMenstrualPeriod: new FormControl(),
      numberOfPregnancy: new FormControl(),
      gravida: new FormControl(),
      para: new FormControl(),
      abortions: new FormControl(),
      bloodPressureSystolic: new FormControl(),
      bloodPressureDiastolic: new FormControl(),
      pulseRate: new FormControl(),
      spo2: new FormControl(),
      auscultation: new FormControl(),
      temperature: new FormControl(),
      ecg: new FormControl(),
      otherObservation: new FormControl(),
      medicine: new FormControl(),
      diet: new FormControl(),
      recommendations: new FormControl(),
      //Physical Examination
      weight: new FormControl(),
      height: new FormControl(),
      bmi: new FormControl(),
      built: new FormControl(),
      nutrition: new FormControl(),
      clubbingOfFingers: new FormControl(),
      nailChanges: new FormControl(),
      cyanosis: new FormControl(),
      icterusJaundice: new FormControl(),
      pallor: new FormControl(),
      lymphNodes: new FormControl(),
      oedema: new FormControl(),
      sclera: new FormControl(),
      otherObservations: new FormControl(),
      //Systemic Examination
      respiratorySystem: new FormControl(),
      gastroIntestinal: new FormControl(),
      cardioVascular: new FormControl(),
      genitoUrinary: new FormControl(),
      musculoSkeletal: new FormControl(),
      centralNervous: new FormControl(),
      eye: new FormControl(),
      ear: new FormControl(),
      nose: new FormControl(),
      mouth: new FormControl(),
      throat: new FormControl(),
      neck: new FormControl(),
      skin: new FormControl(),
      psychiatricHistory: new FormControl(),
      //Diagnosis
      primary: new FormControl(),
      secondary: new FormControl(),
      differentialDiagnosis: new FormControl(),
      finalDiagnosis: new FormControl(),
      ICD10Codes: new FormControl(),
      //Investigation
      keyAdvice: new FormControl(),
      others: new FormControl(),
      haematology: new FormControl(),
      biochemistryAndImmunoassay: new FormControl(),
      clinicalPathology: new FormControl(),
      pathology: new FormControl(),
      serology: new FormControl(),
      malaria: new FormControl(),
      filaria: new FormControl(),
      dengu: new FormControl(),
      japaneseEncephalitis: new FormControl(),
      chikungunya: new FormControl(),
      scrubTyphus: new FormControl(),
      leptrospirosis: new FormControl(),
      brucellosis: new FormControl(),
      tuberculosis: new FormControl(),
      hiv: new FormControl(),
      hepatitisB: new FormControl(),
      hepatitisC: new FormControl(),
      hepatitisA: new FormControl(),
      hepatitisE: new FormControl(),
      hbc: new FormControl(),
      otherDiagnosticTest: new FormControl(),
      radioloyAndOtherDiagnostics: new FormControl()
    });
    patientHistory = false;
    female = false;
    haematologyItems = [
      {id: 1, name: 'HAEMOGLOBIN ESTIMATION'},
      {id: 2, name: 'PLATELET COUNT'},
      {id: 1, name: 'TOTAL RBC COUNT'},
      {id: 2, name: 'RETICULOCYTE COUNT'},
      {id: 1, name: 'ABSOLUTE EOSINOPHIL COUNT'},
      {id: 2, name: 'TOTAL LEUCOCYTE COUNT'},
      {id: 1, name: 'DIFFERENTIAL LEUCOCYTE COUNT'},
      {id: 2, name: 'CBC'},
      {id: 1, name: 'PERIPHERAL BLOOD SMEAR'},
      {id: 2, name: 'ESR'},
      {id: 1, name: 'PROTHROMBIN TIME AND INR'},
      {id: 2, name: 'BLEEDING TIME'},
      {id: 1, name: 'CLOTTING TIME'},
      {id: 2, name: 'BLOOD GROUPING AND RH TYPING'},
      {id: 1, name: 'BLOOD CROSS MATCHING'},
      {id: 2, name: 'PACKED CELL VOLUME'},
      {id: 1, name: 'APTT'},
      {id: 2, name: 'FIBRINOGEN DEGRADATION PRODUCTS (D-DIMER)'},
      {id: 1, name: 'COOMB\'S TEST-DIRECT WITH TITRE'},
      {id: 2, name: 'ANA/ANF'},
      {id: 1, name: 'BONE MARROW ASPIRATION'},
      {id: 2, name: 'SICKLE CELL ANAEMIA*'},
      {id: 1, name: 'REDUCTION TEST FOR SCREENING G6PD DEFICIENCY'},
      {id: 2, name: 'THALASSEMIA'},
      {id: 1, name: 'HAEMOGLOBINOPATHIES SCREENING/TESTING (IN HIGH PREVALENCE AREAS)'},
      {id: 2, name: 'SERUM FERRITIN'},
    ];


    biochemistryItems = [
      {id: 1, name: 'BLOOD SUGAR'},
      {id: 2, name: 'SGPT'},
      {id: 1, name: 'GLUCOSE TOLERANCE TEST'},
      {id: 2, name: 'LFT: TOTAL, DIRECT & INDIRECT BILIRUBIN'},
      {id: 1, name: 'SGOT'},
      {id: 2, name: 'ALKALINE PHOSPHATASE'},
      {id: 1, name: 'TOTAL PROTEIN'},
      {id: 2, name: 'ALBUMIN'},
      {id: 1, name: 'ALBUMIN: GLOBULIN RATIO'},
      {id: 2, name: 'KFT: UREA'},
      {id: 1, name: 'CREATININE'},
      {id: 2, name: 'BUN'},
      {id: 1, name: 'URIC ACID'},
      {id: 2, name: 'LIPID PROFILE: TOTAL CHOLESTEROL'},
      {id: 1, name: 'TRIGLYCERIDE'},
      {id: 2, name: 'VLDL'},
      {id: 1, name: 'HDL'},
      {id: 2, name: 'LDL'},
      {id: 1, name: 'AMYLASE'},
      {id: 2, name: 'LIPASE'},
      {id: 1, name: 'SODIUM'},
      {id: 2, name: 'POTASSIUM'},
      {id: 1, name: 'CALCIUM'},
      {id: 2, name: 'PHOSPHOROUS'},

      {id: 2, name: 'MAGNESIUM'},
      {id: 1, name: 'CHLORIDES'},
      {id: 2, name: 'BLOOD GASES ANALYSIS'},
      {id: 1, name: 'CREATINE PHOSPHOKINASE (CPK)'},
      {id: 2, name: 'LDH'},
      {id: 1, name: 'GLYCOSYLATED HAEMOGLOBIN (HBA1C)'},
      {id: 2, name: 'T3'},
      {id: 1, name: 'T4'},
      {id: 2, name: 'TSH'},
      {id: 1, name: 'CK MB'},
      {id: 2, name: 'TROPONIN I/T'},
    ];

  microbiologyItems = [
    {id:1, name:'SPUTUM, PUS FOR AFB'},
    {id:1, name:'SMEAR FOR RTI/STDS'},
    {id:1, name:'SMEAR EXAMINATION FOR LEPROSY'},
    {id:1, name:'THROAT SWAB FOR DIPHTHERIA'},
    {id:1, name:'HANGING DROP TEST FOR V. CHOLERA'},
    {id:1, name:'GRAM STAIN  FOR MENINGOCOCCI'},
    {id:1, name:'KOH STUDY FOR FUNGUS'},
    {id:1, name:'BLOOD CULTURE AND ANTIMICROBIAL SENSITIVITY'},
    {id:1, name:'URINE, STOOL, PUS, FLUID ETC. CULTURE AND ANTIMICROBIAL SENSITIVITY'},
    {id:1, name:'STOOL CULTURE FOR VIBRIO '},
    {id:1, name:'CHOLERA AND OTHER BACTERIAL ENTEROPATHOGENS'},
  ];

  clinicalPathologyItems = [
    {id:1, name:'URINE ALBUMIN AND SUGAR'},
    {id:1, name:'HAEMOGLOBIN, BILE SALTS, BILE PIGMENTS, KETONE BODIES, SPECIFIC GRAVITY, REACTION (PH) AND LEUCOCYTE ESTERASE'},
    {id:1, name:'URINE MICROSCOPY'},
    {id:1, name:'24-HOURS URINARY PROTEIN'},
    {id:1, name:'STOOL ROUTINE EXAMINATION'},
    {id:1, name:'STOOL FOR OCCULT BLOOD'},
    {id:1, name:'VAGINAL SMEAR FOR PRESENCE OF SPERMS (MEDICO LEGAL CASE)'},
  ];

  pathologyItems = [
    {id:1, name:'PAP SMEAR'},
    {id:1, name:'SPUTUM CYTOLOGY'},
    {id:1, name:'HISTOPATHOLOGY'},
    {id:1, name:'CYTOLOGY - FNAC'},
    {id:1, name:'BONE MARROW ASPIRATION'},
    {id:1, name:'SEMEN ANALYSIS'},
    {id:1, name:'CSF ANALYSIS (SUGAR, PROTEIN, CELL COUNT)'},
    {id:1, name:'FLUID ANALYSIS (CELL COUNT, BIOCHEMISTRY AND CYTOLOGY)'}
  ];

  serologyItems = [
    {id:1, name:'PREGNANCY TEST'},
    {id:1, name:'RPR FOR SYPHILIS'},
    {id:1, name:'RK39 TEST FOR KALA-AZAR*'},
    {id:1, name:'WIDAL FOR DIAGNOSIS OF TYPHOID (TUBE METHOD)'},
    {id:1, name:'RHEUMATOID FACTOR QUANTITATIVE'},
    {id:1, name:'ANTI-STREPTOLYSIN O QUANTITATIVE'},
    {id:1, name:'C-REACTIVE PROTEIN QUANTITATIVE'},
    {id:1, name:'IGM FOR MEASLES'},
    {id:1, name:'RAPID ANTIGEN DETECTION TEST FOR BACTERIAL MENINGITIS'}
  ];

  malariaItems = [
    {id:1, name:'PERIPHERAL SMEAR FOR MALARIA PARASITE DETECTION'},
    {id:1, name:'ANTIGEN BASED BIVALENT RDT FOR DETECTION OF MALARIA'},
  ];

  filariasisItems = [
    {id:1, name:'PERIPHERAL SMEAR FOR FILARIAL PARASITE DETECTION'},
    {id:1, name:'FILARIA TEST STRIP (FTS)'},
  ];

  dengueItems = [
    {id:1, name:'NS1 ANTIGEN AND IGM ANTIBODY BASED TEST'}
  ];

  japaneseEncephalitisItems = [
    {id:1, name:'IGM, ANTIBODY TEST  (JAPANESE ENCEPHALITIS)'}
  ];

  chikungunyaItems = [
    {id:1, name:'IGM, ANTIBODY TEST (CHIKUNGUNYA)'}
  ];

  scrubTyphusItems = [
    {id:1, name:'IGM DETECTION TEST (SCRUB TYPHUS)'}
  ];

  leptospirosisItems = [
    {id:1, name:'RAPID TEST'},
    {id:1, name:'IGM, ANTIBODY TEST'},
  ];

  brucellosisItems = [
    {id:1, name:'ANTIGEN BASED TEST'}
  ];

  BRUCELLOSIS

  tuberculosisItems = [
    {id:1, name:'SMEAR FOR AFB'},
    {id:1, name:'CAPILLARY-  BASED NUCLEIC ACID  AMPLIFICATION TEST'},
    {id:1, name:'MANTOUX'},
    {id:1, name:'TB LAMP TEST'},
    {id:1, name:'TB CULTURE (LIQUID)'},
    {id:1, name:'TB DST (LIQUID)'},
  ];

  hivItems = [
    {id:1, name:'RAPID TEST FOR HIV, ELISA FOR HIV'}
  ];

  hepatitisBItems = [
    {id:1, name:'HBS AG (RAPID TEST AND ELISA)'}
  ];

  hepatitisCItems = [
    {id:1, name:'HCV (ELISA)'}
  ];

  hepatitisAItems = [
    {id:1, name:'IGM DETECTION TEST (HEP A)'}
  ];

  hepatitisEItems = [
    {id:1, name:'IGM DETECTION TEST (HEP E)'}
  ];

  hbcItems = [
    {id:1, name:'IGM DETECTION TEST (HBC)'}
  ];

  otherDiagnosticTestsItems = [
    {id:1, name:'VISUAL INSPECTION ACETIC ACID'},
    {id:1, name:'WATER QUALITY TESTING'},
    {id:1, name:'ESTIMATION OF RESIDUAL CHLORINE IN DRINKING WATER'},
    {id:1, name:'URINE FOR IODINE'},
    {id:1, name:'IODOMETRY TITRATION'},
    {id:1, name:'BLOOD BANK'},
  ];

  radiologyAndOtherDiagnosticTestsItems = [
    {id:1, name:'X-RAY'},
    {id:1, name:'C-ARM'},
    {id:1, name:'INTRA ORAL PERIAPICAL IOPA X-RAY'},
    {id:1, name:'ORTHOPANTOMOGRAM (OPG)'},
    {id:1, name:'USG(WITHOUT COLOUR DOPPLER)'},
    {id:1, name:'USG (WITH COLOUR DOPPLER)'},
    {id:1, name:'ECHOCARDIOGRAPHY'},
    {id:1, name:'CT SCAN'},
    {id:1, name:'ECG'},
    {id:1, name:'MAMMOGRAPHY'},
    {id:1, name:'MRI (WITH SERVICE LINKAGES)'},
    {id:1, name:'EEG'},
    {id:1, name:'NCV (NERVE CONDUCTIONVELOCITY )'},
    {id:1, name:'EMG'},
    {id:1, name:'TMT'},
    {id:1, name:'PFT'},
    {id:1, name:'COMPREHENSIVE OPHTHALMIC DIAGNOSTIC SERVICES (OCULAR COHERENCE TOMOGRAPHY,PERIMETRY,PACHYMETRY, EYE ANGIOGRAPHY,REFRACTION  '},
    {id:1, name:'ANGIOGRAPHY'},
    {id:1, name:'ENDOSCOPY'},
  ];

    selected = [
      {id: 2, name: 'Clinical Pathology'},
      {id: 8, name: 'Leptrospirosis'}
    ];
    save = false;
    showCaseHistory = false;
    caseHistoryDetailed = false;
    screenOptions: NgbModalOptions = {
      keyboard: false,
      centered: true,
      size: 'lg',
      windowClass: 'modal-xl'
    };

    appointmentId='';
    consultationId='';
    drugId;
    investigationId='';
    sub: any;
    joinedVideo=false;
    constructor(private route: ActivatedRoute, private modalService: NgbModal, private router: Router, private teleConsultService: TeleConsultService) { }

    ngOnInit(): void {
      this.sub = this.route
      .queryParams
      .subscribe(params => {
        this.consultationId = params['consultationId'];
        console.log('CONSULTATION NGONINIT CONSULTATION ID' + this.consultationId);
      },
      err =>{
        console.log('ERROR:' + err);
      });
    }

    onCaseHistory(event){
      // this.modalService.dismissAll();
      // this.modalService.open(CaseHistoryComponent, this.screenOptions).result.then((result) => {
      // });
      if(event.target.checked){
        this.showCaseHistory=true;
      } else {
        this.showCaseHistory=false;
        this.caseHistoryDetailed = false;
      }
    }

    onShowPatientHistory(){
      this.showCaseHistory=true;
      this.patientHistory = true;
    }

    onHidePatientHistory(){
      this.showCaseHistory=false;
      this.caseHistoryDetailed = false;
      this.patientHistory = false;
    }

    onReports(){

    }

    onSave(){
      this.save = true;
    }

    onFormSubmit(){

    }

    onTabClick(event: any){
      console.log(event);
      console.log(event.tab.textLabel);
      this.save=false;

    }
    activeTabIndex = 0;
    public isShow = false;
    public toggleDiv() {
      this.isShow = !this.isShow;
    }

    openDoctorDashboard(){
      this.router.navigateByUrl('/doctor/dashboard')
    }


    onAddRow(prescriptionTable){
      let tableRef = <HTMLTableElement >document.getElementById(prescriptionTable);
      // let newRow = tableR.insertBefore(-1)
          // TSection.insertBefore();
      let newRow = tableRef.insertRow(-1);

      // Insert a cell in the row at index 0
      let medicine = newRow.insertCell(0);
      medicine.innerHTML = "<td> <input type='text'> </td>";

      let morningDosage = newRow.insertCell(1);
      morningDosage.innerHTML = "<td> <input type='checkbox'> </td>";

      let afternoonDosage = newRow.insertCell(2);
      afternoonDosage.innerHTML = "<td> <input type='checkbox'> </td>";

      let nightDosage = newRow.insertCell(3);
      nightDosage.innerHTML = "<td> <input type='checkbox'> </td>";

      let beforeFood = newRow.insertCell(4);
      beforeFood.innerHTML = "<td> <input type='checkbox'> </td>";

      let days = newRow.insertCell(5);
      days.innerHTML = "<td> <input type='number' name='days' style='width: 50%;'> </td>";

      let remarks = newRow.insertCell(6);
      remarks.innerHTML = "<td> <input type='text' name='remarks'> </td>";


      // Append a text node to the cell
      // let newText = document.createTextNode('New bottom row');
      // newCell.appendChild(newText);
    }

    onGenderFemale(){
      this.female=true;
    }

    onGenderNoFemale(){
      this.female=false;
    }


    onVisitDate(){
      this.caseHistoryDetailed = true;
    }

    onReport(){
      this.modalService.open(DocumentModalComponent,  { windowClass : "modalSize"});
    }

    addTagFn(name) {
      return { name: name, tag: true };
    }

    onBackCaseHistoryDetailed(){
      this.caseHistoryDetailed = false;
    }

    onKey(sectionName, fieldName, event){
      this.teleConsultService.updateConsultation(this.consultationId, sectionName, fieldName, event.target.value).subscribe( data => {
        // this.consultationId = data['uuid'];
      },
      err =>{
        console.log('ERROR:' + err.message);
      });
    }

    joinVideo(){
      this.joinedVideo=true;
    }
  }
