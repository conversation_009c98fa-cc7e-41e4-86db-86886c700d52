

.edit-btn {
    float: right;
}



#text-area {
    height: 100px;
    resize: none;
    align-content: left;
    border-radius: 2px;
}

#text-area:focus {
    outline: none !important;
    border-color: #719ECE;
    box-shadow: 0 0 10px #719ECE;
}

.add-more {
    margin-top: 15px;
    margin-bottom: -5px;
}

.week-val label {
    padding: 10px;
}

.save-modal-btn {
    margin-top: 18px;
    margin-bottom: 5px;
    text-align: center;
}

.week-val label input {
    margin-right: 3px;
}

label {
    color: #757575;
}



.footer-time {
    float: left;
}

.m-btn {
    margin-top: 30px;
}

.rend-form {
    background-color: #ffff;
    /*#DCDCC1*/
}

p.edit-link {
    cursor: pointer;
}

.add-more {
    color: #20c0f3;
    cursor: pointer;
}

#no-ch-text {
    color: #495057;
}

.hl {
    margin-top: 0px;
}

.fa-trash-alt {
    color: #d11a2a;
    margin-top: 2px;
    cursor: pointer;
}

.date-label {
    margin-left: 18px;
    margin-right: 10px;
}

.uad {
    background-color: #DCDCC1;
    outline: none;
    width: 100%;
    border: 0px;
    margin-right: -110px;
}

#del-unavail-icon {
    margin-top: 12px;
}

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .example {background: red;}
  .vl {
    margin-top: 0px;
    /* width: 10px; */
    margin-left: 0px;
    border-left: 1px solid rgba(0, 0, 0, .125);
    height:0px;
    padding: 0px;
  }
  .modal-content {
    width: 100%;
    /* height: 320px; */
    height: 640px;
    overflow: auto;
}
.form-vl {
  margin-top: 0px;
  /* width: 10px; */
  margin-left: 0px;
  border-left: 1px solid rgba(0, 0, 0, .125);
  height:0px;
  padding: 0px;
}
.time-marg {
  margin-left: 0px;
  /* margin-bottom: -14px; */
}
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .example {background: green;}
  .vl {
    margin-top: 0px;
    /* width: 10px; */
    margin-left: 0px;
    border-left: 1px solid rgba(0, 0, 0, .125);
    height:0px;
    padding: 0px;
  }
  .form-vl {
    margin-top: 0px;
    /* width: 10px; */
    margin-left: 0px;
    border-left: 1px solid rgba(0, 0, 0, .125);
    height:0px;
    padding: 0px;
  }
  .time-marg {
    margin-left: 0px;
    /* margin-bottom: -14px; */
}
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .example {background: blue;}
  .modal-content {
    width: 100%;
    /* height: 320px; */
    height: 400px;
    overflow: auto;
}
.vl {
  margin-top: 0px;
  /* width: 10px; */
  margin-left: 0px;
  border-left: 1px solid rgba(0, 0, 0, .125);
  height:0px;
  padding: 0px;
}
.form-vl {
  margin-top: 0px;
  /* width: 10px; */
  margin-left: 0px;
  border-left: 1px solid rgba(0, 0, 0, .125);
  height:0px;
  padding: 0px;
}
.time-marg {
  margin-left: -30px;
  /* margin-bottom: -14px; */
}
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .example {background: orange;}
  .modal-content {
    width: 600px;
    /* height: 340px; */
    height: 400px;
    margin-left: -20%;
}
.vl {
  margin-top: 28px;
  /* width: 10px; */
  margin-left: -40px;
  border-left: 1px solid rgba(0, 0, 0, .125);
  height: 130px;
  padding: 0px;
}
.form-vl {
  margin-top: 28px;
  /* width: 10px; */
  margin-left: -140px;
  border-right: 1px solid rgba(0, 0, 0, .125);
  height: 130px;
  margin-right: 15px;
  /* margin-left: 20px; */
}
.time-marg {
  margin-left: -48px;
  /* margin-bottom: -14px; */
}
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .example {background: pink;}
  .modal-content {
    width: 780px;
    /* height: 340px; */
    height: 400px;
  }
.vl {
  margin-top: 28px;
  /* width: 10px; */
  margin-left: -40px;
  border-left: 1px solid rgba(0, 0, 0, .125);
  height: 130px;
  padding: 0px;
}
.form-vl {
  margin-top: 28px;
  /* width: 10px; */
  margin-left: -200px;
  border-right: 1px solid rgba(0, 0, 0, .125);
  height: 130px;
  margin-right: 15px;
  /* margin-left: 20px; */
}
.time-marg {
  margin-left: -48px;
  /* margin-bottom: -14px; */
}
}
