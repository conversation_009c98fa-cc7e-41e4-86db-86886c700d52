<div *ngIf="!dataAvailable" class="row">
    <div class="col-md-12">
        <!-- <div class="card schedule-widget mb-0"> -->
        <p *ngIf="!noCreate" id="no-ch-text" translate>"Yet to set up the consulting hours for this location, you can now set it up using by clicking Add Consulting Hours"</p>
        <!-- <p *ngIf="!noCreate && !feeDetailAvailable" id="no-ch-text" translate>Note: Please create consultation fee to create consulting hour</p> -->
        <p *ngIf="noCreate" id="no-ch-text2" translate>"No consulting hours for this location, you can now set it in practice locations"</p>
        <!-- </div> -->
    </div>
</div>
<!-- Normal Form -->
<form *ngIf="dataAvailable" [formGroup]="consultationForm">
    <fieldset [disabled]="formDisable">
        <div formArrayName="chArray">
            <ng-container *ngFor="let data of this.consultationForm.controls.chArray.value; let i=index;" [formGroupName]=" i ">
                <div *ngIf="dataAvailable" id="form{{i}}" class=" card tab-content schedule-cont rend-form">
                    <div class="row form-row">
                        <div class="col-lg-8">
                            <div class="row">
                                <div class="col-lg-11">
                                    <label translate>Days of the Week</label>
                                    <div class="row">
                                        <div class="col-lg-12 week-val">
                                            <label class="checkbox-inline">
                                                <input id="Sunday{{i}}" formControlName="sunday" type="checkbox" >{{'Sun'|translate}}
                                              </label>
                                            <label class="checkbox-inline">
                                                <input id="Monday{{i}}" formControlName="monday" type="checkbox" >{{'Mon'|translate}}
                                              </label>
                                            <label class="checkbox-inline">
                                                <input id="Tuesday{{i}}" formControlName="tuesday" type="checkbox" >{{'Tue'|translate}}
                                              </label>
                                            <label class="checkbox-inline">
                                                <input id="Wednesday{{i}}" formControlName="wednesday" type="checkbox" >{{'Wed'| translate}}
                                              </label>
                                            <label class="checkbox-inline">
                                                <input id="Thursday{{i}}" formControlName="thursday" type="checkbox" >{{'Thu'|translate}}
                                              </label>
                                            <label class="checkbox-inline">
                                                <input id="Friday{{i}}"  formControlName="friday" type="checkbox">{{'Fri'|translate}}
                                              </label>
                                            <label class="checkbox-inline">
                                                <input  id="Saturday{{i}}" formControlName="saturday" type="checkbox">{{'Sat'|translate}}
                                              </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-4 col-md-6">
                                    <div class="form-group">
                                        <div class="form-group">
                                            <label translate>From Date<span class="text-danger">*</span></label>
                                            <input id="fromDate{{i}}" formControlName="effective_from" class="form-control" type="date" [value]="todayDate" name="fromDate">
                                        </div>

                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6">
                                    <div class="form-group">
                                        <label translate>To Date</label>
                                        <input id="toDate{{i}}" formControlName="effective_upto" class="form-control" type="date" name="toDate">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-1 form-vl">
                        </div>
                        <div class="col-lg-4">
                            <div class="row">
                                <div class="col-lg-6 col-md-6">
                                    <label translate>Start Time<span class="text-danger">*</span></label>
                                    <input id="frm-start-time{{i}}" class="form-control" formControlName="time_from" name="start-time" placeholder="Start Time">
                                </div>
                                <div class="col-lg-6 col-md-6">
                                    <div class="form-group">
                                        <label translate>End Time<span class="text-danger">*</span></label>
                                        <input id="frm-end-time{{i}}" class="form-control" formControlName="time_to" name="end-time" placeholder="End Time">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-8">
                                    <label translate>Consulting Time(min)</label>
                                    <input id="frm-cnslt-time{{i}}" class="form-control" formControlName="consultation_duration" name="consultation-duration" placeholder="Time">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=" edit-btn tab-pane fade show active">
                        <h4 class="card-title d-flex justify-content-between">
                            <p id="mark-unavail-icon"  *ngIf="!data['ua_data_available']" class="edit-link" (click)="markUnavailability(i)" translate>Mark Unavailability</p>
                            <p id="edit-ch-icon" class="edit-link" (click)="editConsultingHours(i, true)" translate><i class="fa fa-edit mr-1"></i>Edit</p>
                        </h4>
                    </div>
                    <div *ngIf="data['ua_data_available']">
                        <hr class='hl'>
                        <label>Unavailable Date(s) From and To</label>
                        <div class="row">
                            <!-- <div class="col-lg-3"></div> -->
                            <div class="col-md-6 col-lg-3">
                                <input class="form-control uad" id="unavail-from-date{{i}}" formControlName="ua_start_datetime">
                                <!-- <label id="unavail-from-date" formControlName="ua_start_datetime" class="date-label"></label> -->
                            </div>
                            <div class="col-md-6 col-lg-3">
                                <input class="form-control uad" id="unavail-end-date{{i}}" formControlName="ua_end_datetime">
                            </div>
                            <i id="del-unavail-icon" (click)="deleteUnavailability(i)" class="fa fa-trash-alt" aria-hidden="true"></i>
                        </div>
                    </div>
                </div>
            </ng-container>
        </div>
    </fieldset>
</form>
<p class="add-more" *ngIf="!noCreate" data-toggle="modal" (click)="newConsultingModal()" translate><i id="new-consult-icon" class="fa fa-plus-circle"></i> Add Consulting Hours</p>
<!-- Normal Form -->
<!--Modal-->
<div class="modal" id="consultModal" tabindex="-1" role="dialog" style="z-index:980px;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" translate>Add Consulting Hours</h5>
                <button id="modal-top-close" type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form [formGroup]="consultingForm" (ngSubmit)="submitConsultingForm(consultationData.uuid)">
                <div class="modal-body">
                    <fieldset [disabled]="editAvailability">
                        <div class="row form-row">
                            <div class="col-lg-8 col-md-12 col-sm-12">
                                <div class="row">
                                    <form [formGroup]="daysForm" (change)="updateDays()">
                                        <fieldset [disabled]="editConsultingHour">
                                            <div class="col-lg-12 col-md-12 col-sm-12">
                                                <label translate>Days of the Week</label>
                                                <div class="row">
                                                    <div class="col-lg-12 col-md-12 col-sm-12 week-val">
                                                        <label class="checkbox-inline">
                                                          <input id="mod-sun" formControlName="Sunday" [(ngModel)]="daysModel['Sunday']" type="checkbox" value="Sunday">{{'Sun'|translate}}
                                                        </label>
                                                        <label class="checkbox-inline">
                                                          <input id="mod-mon" formControlName="Monday" [(ngModel)]="daysModel['Monday']" type="checkbox" value="Monday" >{{'Mon'|translate}}
                                                        </label>
                                                        <label class="checkbox-inline">
                                                          <input id="mod-tue" formControlName="Tuesday" [(ngModel)]="daysModel['Tuesday']" type="checkbox" value="Tuesday" >{{'Tue'|translate}}
                                                        </label>
                                                        <label class="checkbox-inline">
                                                          <input id="mod-wed" formControlName="Wednesday" [(ngModel)]="daysModel['Wednesday']" type="checkbox" value="Wednesday" >{{'Wed'|translate}}
                                                        </label>
                                                        <label class="checkbox-inline">
                                                          <input id="mod-thur" formControlName="Thursday" [(ngModel)]="daysModel['Thursday']" type="checkbox" value="Thursday" >{{'Thu'|translate}}
                                                        </label>
                                                        <label class="checkbox-inline">
                                                          <input id="mod-fri" formControlName="Friday" [(ngModel)]="daysModel['Friday']" type="checkbox" value="Friday" >{{'Fri'|translate}}
                                                        </label>
                                                        <label class="checkbox-inline">
                                                          <input id="mod-sat" formControlName="Saturday" [(ngModel)]="daysModel['Saturday']" type="checkbox" value="Saturday" >{{'Sat'|translate}}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                </div>
                                <input formControlName="uuid" id="uuid2" [(ngModel)]="consultationData.uuid" class="form-control" type="text" name="uuid" hidden>
                                <div class="row">
                                    <div class="col-lg-5 col-md-6 col-sm-6">
                                        <div class=" modal-text form-group">
                                            <label translate>From Date<span class="text-danger">*</span></label>
                                            <input *ngIf="editConsultingHour" id="mod-from-date" value="{{consultationData.effective_from|date:'dd/MM/yyyy'}}" class="form-control" type="text" name="fromDate" [readonly]="editConsultingHour">
                                            <input *ngIf="!editConsultingHour" type="text" style="caret-color: transparent;" class="form-control" formControlName="effective_from" name="fromDate" [minDate]="todayDate" id="mod-from-date" [(ngModel)]="consultationData.effective_from" #dp="bsDatepicker"
                                                placeholder="Select Date" onkeydown="return false" autocomplete="off" bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true}">

                                        </div>
                                    </div>
                                    <div class="col-lg-5 col-md-6 col-sm-6 ">
                                        <div class="form-group">
                                            <label translate>To Date</label>
                                            <!-- <input id="mod-end-date" formControlName="effective_upto" [min]="consultationData.effective_from" [(ngModel)]="consultationData.effective_upto" class="form-control" type="date" name="toDate"> -->
                                            <!-- <input *ngIf="editConsultingHour" id="mod-from-date" value="{{consultationData.effective_upto|date:'dd/MM/yyyy'}}" class="form-control" type="text" name="toDate" [readonly]="editConsultingHour"> -->
                                            <input id="mod-end-date" formControlName="effective_upto" type="text" style="caret-color: transparent;" class="form-control" name="toDate" [minDate]="consultationData.effective_from" [(ngModel)]="consultationData.effective_upto" #dp="bsDatepicker" placeholder="Select Date"
                                                onkeydown="return false" autocomplete="off" bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true }">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 vl hidden-md">
                                <div class="form-group">
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6 col-sm-6 time-marg">
                                <div class="row">
                                    <div class="col-lg-6    col-md-6 col-sm-6 form-group">
                                        <label translate>Start Time<span class="text-danger">*</span></label>
                                        <ng-select *ngIf="!editConsultingHour" id="mod-time-from-new" formControlName="time_from" [(ngModel)]="consultationData.time_from" placeholder="&nbsp;&nbsp;--:-- " name="start_time" required [readonly]="editConsultingHour" [items]="time_slots" [searchable]="false"
                                            [clearable]="false" bindLabel="slot_12" bindValue="slot_24">
                                        </ng-select>
                                        <input *ngIf="editConsultingHour" id="mod-time-from-edit" formControlName="time_from" [(ngModel)]="consultationData.time_from" class="form-control" name="start_time" required [readonly]="editConsultingHour">
                                        <!-- <input *ngIf="editConsultingHour" id="mod-time-from-edit" formControlName="time_from" [(ngModel)]="consultationData.time_from" class="form-control" name="start_time" required [readonly]="editConsultingHour">
                                        <input *ngIf="!editConsultingHour" id="mod-time-from-new" formControlName="time_from" [(ngModel)]="consultationData.time_from" class="form-control" type="time" name="start_time" required [readonly]="editConsultingHour"> -->
                                        <!-- <input formControlName="time_from" class="form-control" [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1" [placeholder]="consultationData.time_from">
                                                  <owl-date-time #dt1 pickerType="timer"></owl-date-time> -->
                                    </div>
                                    <div class="col-lg-6 col-md-6  col-sm-6">
                                        <div class="form-group">
                                            <label translate>End Time<span class="text-danger">*</span></label>
                                            <ng-select id="mod-time-to-new" *ngIf="!editConsultingHour" formControlName="time_to" [(ngModel)]="consultationData.time_to" placeholder="&nbsp;&nbsp;--:-- " name="end_time" required [readonly]="editConsultingHour" [items]="time_slots" [searchable]="false"
                                                [clearable]="false" bindLabel="slot_12" bindValue="slot_24">
                                            </ng-select>
                                            <input *ngIf="editConsultingHour" id="mod-time-to-edit" formControlName="time_to" [(ngModel)]="consultationData.time_to" class="form-control" name="end_time" required [readonly]="editConsultingHour">
                                            <!-- <input *ngIf="editConsultingHour" id="mod-time-to-edit" formControlName="time_to" [(ngModel)]="consultationData.time_to" class="form-control" name="end_time" required [readonly]="editConsultingHour">
                                            <input *ngIf="!editConsultingHour" id="mod-time-to-new" formControlName="time_to" [(ngModel)]="consultationData.time_to" class="form-control" type="time" name="end_time" required [readonly]="editConsultingHour"> -->
                                            <!-- <input formControlName="time_to" class="form-control" [owlDateTime]="dt2" [owlDateTimeTrigger]="dt2" [placeholder]="consultationData.time_to">
                                                      <owl-date-time #dt2 pickerType="timer"></owl-date-time> -->
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-10">
                                        <label translate>Consulting Time(min)</label>
                                        <ng-select id="mod-consdur0" *ngIf="!editConsultingHour && !editAvailability" formControlName="consultation_duration" [readonly]="editConsultingHour" [items]="time" [searchable]="false" [clearable]="false" bindLabel="name" [(ngModel)]="consultationData.consultation_duration">
                                        </ng-select>
                                        <input id="mod-consdur" *ngIf="editConsultingHour || editAvailability" [readonly]="editConsultingHour" class="form-control" formControlName="consultation_duration" name="consultation-duration" [(ngModel)]="consultationData.consultation_duration">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <div *ngIf="!editAvailability" class="row">
                        <div class="col-lg-4 col-sm-2"></div>
                        <div class="col-lg-4 col-sm-2"></div>
                        <div class="col-lg-4 col-sm-8 save-modal-btn">
                            <button *ngIf="!editConsultingHour" id="mod-chn-save" type="submit" class="btn btn-primary"  translate [disabled]="this.days.length == 0 || consultationData.consultation_duration == null || consultationData.time_to == null || consultationData.time_from == null || consultationData.effective_from == null || consultationData.effective_upto == null || submitted == true">Save</button>
                            <button *ngIf="editConsultingHour" id="mod-ch-save" type="submit" class="btn btn-primary" translate>Save</button>
                        </div>
                        <!-- <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button> -->
                    </div>
                    <!-- From Date -->
                </div>
            </form>
            <p *ngIf="editConsultingHour&&!editAvailability" class="ml-5">If you modify the consulting hours group the unavailability dates will be reseted.</p>
            <div *ngIf="editAvailability" class="modal-footer">
                <form [formGroup]="unavailabilityForm" (ngSubmit)="saveUnavailability()">
                    <input id="uuid1" formControlName="uuid" [value]="unavailabilityDates.uuid" type="text" hidden>
                    <input id="ch-uuid" formControlName="consulting_hours_group" [(ngModel)]="consultationData.uuid" class="form-control" type="text" required hidden>
                    <input id="doctor-uuid" formControlName="doctor" [(ngModel)]="doctor['uuid']" class="form-control" type="text" required hidden>
                    <input id="loc-uuid" formControlName="practice_location" [(ngModel)]="selectedLocation['uuid']" class="form-control" required type="text" hidden>
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="slot-monday doc-times modal-text form-group">
                                <label translate>From Date<span class="text-danger">*</span></label>
                                <input id="fromDate" formControlName="start_datetime" type="text" style="caret-color: transparent;" class="form-control" name="fromDate" [minDate]="consultationData.effective_from" [value]="unavailabilityDates.fromDate" [maxDate]="consultationData.effective_upto"
                                    #dp="bsDatepicker" placeholder="Select Date" onkeydown="return false" autocomplete="off" bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true }" required>
                                <!-- <input id="fromDate" formControlName="start_datetime" class="form-control" type="date" [min]="consultationData.effective_from" [max]="consultationData.effective_upto" [value]="unavailabilityDates.fromDate" name="fromDate" required> -->
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="form-group">
                                <label translate>To Date</label>
                                <input id="toDate" formControlName="end_datetime" type="text" style="caret-color: transparent;" class="form-control" name="toDate" [minDate]="unavailabilityForm.controls['start_datetime'].value" #dp="bsDatepicker" [value]="unavailabilityDates.endDate"
                                    [maxDate]="consultationData.effective_upto" placeholder="Select Date" onkeydown="return false" autocomplete="off" bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true }" required>
                                <!-- <input id="toDate" class="form-control" formControlName="end_datetime" type="date" [min]="unavailabilityForm.controls['start_datetime'].value" [max]="consultationData.effective_upto" [value]="unavailabilityDates.endDate" name="toDate" required> -->
                            </div>
                        </div>
                        <div class="col-lg-1"></div>
                        <div class="col-lg-3">
                            <button id="mod-unavail-save" type="submit" class="m-btn  btn btn-primary" [disabled]="unavailabilityForm.controls['start_datetime'].value == null || unavailabilityForm.controls['end_datetime'].value == null" translate>Save</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!--Modal-->
<div class="add-more">
    <!-- <p data-toggle="modal" data-target="#consultModal"><i class="fa fa-plus-circle"></i> Add More</p> -->
</div>
