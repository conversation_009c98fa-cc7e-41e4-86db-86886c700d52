.cancel-btn {
    float: right;
    font-size: 15px;
    padding: 4px;
    margin: 5px;
    margin-right: 11px;
}
.btn-cancel{
  float: right;
  font-size: 15px;
  padding: 4px;
  margin-right: 11px;
  margin: 0px 0px 0px 14px;
}
.btn_color {
    color: #fff;
}

.fa-edit {
    color: #20c0f3;
    cursor: pointer;
}

.basic-data-btn {
    float: right;
}

::ng-deep .ng-select .ng-select-container {
    color: #333;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ccc;
    min-height: 46px;
    align-items: center;
}

::ng-deep .ng-select.ng-select-disabled>.ng-select-container {
    background-color: #e9ecef;
}

.fa-plus-circle {
    color: #20c0f3;
}

.fa-trash-alt {
    margin-left: 5px;
    color: #d11a2a;
    cursor: pointer;
}

.can-btn {
    padding: 3px;
    margin-left: 11px;
    margin-top: -3px;
}

.save-loc-btn {
    margin-left: -15px;
    margin-top: 35px;
}

#no-loc-msg {
    color: #757575;
}

.fa {
    cursor: pointer;
}
.btn-primary[disabled] {
  background-color: darkgray !important;
  border-color: darkgray !important;

}
.btn-primary{
  border: none;
  color: white;
  padding: 4px 14px 4px 10px;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
}
.btn-possition{
  margin-top: 39px;
}