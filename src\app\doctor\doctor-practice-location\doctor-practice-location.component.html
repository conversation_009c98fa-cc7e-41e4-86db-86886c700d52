<h4 translate>
    Practice Location <i id="new-loc-icon" class="fa fa-plus-circle" (click)="createNewLocationForm()" aria-hidden="true"></i></h4>
<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 col-lg-3">
                <div *ngIf="!showLocationForm" [formGroup]="locationForm">
                    <label translate>Location Details <i id="edit-loc-icon"  *ngIf="location_data" class="fa fa-edit" (click)="editLocation()" aria-hidden="true"></i></label>
                    <ng-select id="practice-location" [searchable]="false" formControlName="practiceLocation" [clearable]="false" [items]="practiceLocationList" bindLabel="name" placeholder="{{'Select Location'|translate}}" bindValue="uuid" (change)="setParacticeLocationId($event)">
                    </ng-select>
                </div>
                <br>
            </div>
        </div>
        <form *ngIf="showLocationForm" [formGroup]="newlocationForm" (ngSubmit)="submitNewLocationForm($event)">
            <fieldset [disabled]="saved">
                <input class="form-control" formControlName="uuid" type="text" hidden>
                <div class="row">
                    <div class="col-lg-4">
                        <label translate>Name</label>
                        <input class="form-control" formControlName="name" pattern="[a-zA-Z ]*" type="text" id="location_name" placeholder="{{'Clinic Name'|translate}}" required maxlength="50" pattern="[a-zA-Z ]*">
                    </div>
                    <div class="col-lg-4">
                        <label translate>Practice Type</label>
                        <!-- <input *ngIf="!create" class="form-control" [(ngModel)]="selectedLocation.practice_type" type="text" id="location_name" placeholder="Practice Type"> -->
                        <ng-select formControlName="practice_type" [items]="practiceType" [searchable]="false" [clearable]="false" bindLabel="name" bindValue="value" placeholder="{{'Select Practice Type'| translate}}" required>
                        </ng-select>
                    </div>
                    <div class="col-lg-4 btn-possition">
                        <button id="create-save-loc-btn" *ngIf="create" class="btn btn-primary" [disabled]="!newlocationForm.valid" (click)="submitNewLocationForm('create')" translate>Save</button>
                        <button id="edit-save-loc-btn" *ngIf="edit" class="btn btn-primary" [disabled]="!newlocationForm.valid" (click)="submitNewLocationForm('edit')" [disabled]="!newlocationForm.valid" translate>Save</button>
                        <button id="cancel-edit-btn" *ngIf="edit" class="btn btn-secondary can-btn" (click)="cancelLocation()" translate>Cancel</button>
                        <i *ngIf="create" id="del-new-loc-icon" class="fa fa-trash-alt ml-4"  (click)="cancelLocation()"></i>
                    </div>
                    <!-- <div class="col-lg-2">
                    </div> -->
                </div>
            </fieldset>
        </form>
        <div class="col-md-12 text-center" *ngIf="!showAllForms && !showLocationForm">
            <p id="no-loc-msg">Yet to create a location to setup consulting hours.Click + icon to add a new practice location </p>
        </div>
    </div>
</div>
<div *ngIf="showAllForms">
    <!-- clinic address start-->
    <div>
        <form id="clinicAddressForm" [formGroup]="clinicAddressForm">
            <h4 class="card-title" translate>Clinic Address <i class="fa fa-edit" (click)="editClinicAddress()" *ngIf="!!clinicAddressReadOnly"></i> </h4>
            <div class="card">
                <div class="card-body">
                    <div class="col-md-12 text-right">
                        <a></a>
                    </div>
                    <div class="row form-row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label translate>Line 1<span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="Cline1" formControlName="line_1"  [readonly]="clinicAddressReadOnly" maxlength="50" required>
                                <div *ngIf="clinicAddressForm.controls.line_1.invalid && (clinicAddressForm.controls.line_1.dirty || clinicAddressForm.controls.line_1.touched)"
                                      class="alert alert-danger">{{specialCharacterError}}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label translate>Line 2<span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="Cline2" formControlName="line_2"  [readonly]="clinicAddressReadOnly" maxlength="50" >
                                <div *ngIf="clinicAddressForm.controls.line_2.invalid && (clinicAddressForm.controls.line_2.dirty || clinicAddressForm.controls.line_2.touched)"
                                class="alert alert-danger">{{specialCharacterError}}</div>
                              </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label translate>City/Town/Village<span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="Ccity" formControlName="city_town_village" [readonly]="clinicAddressReadOnly" maxlength="50" required pattern="[a-zA-Z ]*" >
                                <div *ngIf="clinicAddressForm.controls.city_town_village.invalid && (clinicAddressForm.controls.city_town_village.dirty || clinicAddressForm.controls.city_town_village.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                              </div>
                        </div>
                        <div class="col-md-4">
                          <div class="form-group">
                              <label translate>Taluk<span class="text-danger">*</span></label>
                              <input type="text" id="Ctaluk" class="form-control" [readonly]="clinicAddressReadOnly" formControlName="taluk" maxlength="50" pattern="[a-zA-Z ]*" >
                              <div *ngIf="clinicAddressForm.controls.taluk.invalid && (clinicAddressForm.controls.taluk.dirty || clinicAddressForm.controls.taluk.touched)"
                              class="alert alert-danger">{{alphabetsError}}</div>
                            </div>
                      </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label translate>District<span class="text-danger">*</span> </label>
                                <input type="text" id="Cdistric" class="form-control" [readonly]="clinicAddressReadOnly" formControlName="district" maxlength="50" pattern="[a-zA-Z ]*" >
                                <div *ngIf="clinicAddressForm.controls.district.invalid && (clinicAddressForm.controls.district.dirty || clinicAddressForm.controls.district.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                              </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label translate>State<span class="text-danger">*</span></label>
                                <input type="text" id="Cstate" class="form-control" formControlName="state" [readonly]="clinicAddressReadOnly" required maxlength="50" pattern="[a-zA-Z ]*" >
                                <div *ngIf="clinicAddressForm.controls.state.invalid && (clinicAddressForm.controls.state.dirty || clinicAddressForm.controls.state.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                              </div>
                        </div>
                        <div class="col-md-4 ng-select-container">
                            <div class="form-group">
                                <label translate>Country <span class="text-danger">*</span></label>

                                <ng-select id="country" class="custom" formControlName="country" bindValue="Name" [items]="countryList" [readonly]="clinicAddressReadOnly" [searchable]="true" bindLabel="Name" [clearable]="false" placeholder="{{'Select Country' | translate}}" multiple
                                    required>
                                </ng-select>

                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label translate>Postal Code<span class="text-danger">*</span></label>
                                <input type="text" id="Cpostal_code" class="form-control" formControlName="postal_code" [readonly]="clinicAddressReadOnly" maxlength="10"  required>
                                <div *ngIf="clinicAddressForm.controls.postal_code.invalid && (clinicAddressForm.controls.postal_code.dirty || clinicAddressForm.controls.postal_code.touched)"
                                class="alert alert-danger">{{alphanumericError}}</div>
                              </div>

                              </div>

                    </div>
                    <div class="col-md-12 col-sm-12 col-xs-12 text-right">
                        <button *ngIf="!clinicAddressReadOnly" [disabled]="!clinicAddressForm.valid" id="save-clinic-btn" class="btn btn-primary" (click)="saveClinicAddress()" translate>Save</button>

                        <button class="btn btn-secondary btn-cancel" id="cancel-clinic-btn" (click)="cancelClinicAddress()" *ngIf="!clinicAddressReadOnly" translate>Cancel</button>
                    </div>

                </div>
            </div>

        </form>

    </div>
    <!-- clinic address end-->
    <h4 id="cH" class="card-title" translate>Consulting Hours</h4>
    <div class="card">
        <div class="card-body">
            <div class="profile-box">
              <p *ngIf="!showConsultingGroup">Please add the clinic address, to proceed with the creation of consulting hours.</p>
                <app-doctor-consulting-hours *ngIf="showConsultingGroup" [selectedLocation]="selectedLocationCh" [feeDetailAvailable]="feeDetailAvailable"></app-doctor-consulting-hours>
            </div>
        </div>
    </div>
</div>
