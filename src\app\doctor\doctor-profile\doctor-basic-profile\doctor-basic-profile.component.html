<div class="card">
    <div class="card-body">
        <h4 class="card-title card-title-bottom" id="h-basic-profile">Basic Profile Data <i
                *ngIf="formDisabled" (click)="formEnable()" class="fa fa-edit"></i></h4>
        <span style="font-size: 12px;" class="text-danger mb-2" *ngIf="!formDisabled&&isPublicDoctor">Please fill the
            basic profile data before proceeding education and registration</span>
        <div class="experience-info">
            <div class="row form-row experience-cont mt-3">
                <form [formGroup]="profileDataForm" (ngSubmit)="onSubmit()">
                    <div class="col-12 col-md-10 col-lg-11">
                        <div class="row form-row">
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label translate>Years of Experience<span class="text-danger">*</span></label>
                                    <ng-select id="select-yoe" formControlName="years_of_experience" [items]="Year"
                                        [searchable]="false" bindLabel="Year" placeholder="Select Year"
                                        [readonly]="formDisabled">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label translate>Language<span class="text-danger">*</span></label>
                                    <ng-select id="select-lang" formControlName="languages" [items]="languages"
                                        [searchable]="true" bindLabel="value" [maxSelectedItems]="7"
                                        placeholder="Select Languages" multiple="true" [readonly]="formDisabled">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="system-of-medicine" translate>System of Medicine<span
                                            class="text-danger">*</span></label>
                                    <ng-select formControlName="system_of_medicine" [items]="systemOfMedicine"
                                        [searchable]="false" bindLabel="systemOfMedicine"
                                        placeholder="Select System of Medicine" [readonly]="formDisabled"
                                        (change)="onChangeSystemOfMedicine($event,'changes')">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4" *ngIf="!isPublicDoctor">
                                <div class="form-group">
                                    <label for="department">Department<span class="text-danger">*</span></label>
                                    <ng-select formControlName="department" [items]="specificDepartment"
                                        bindLabel="value" [readonly]="formDisabled" multiple="false"
                                        placeholder="Select Department" [maxSelectedItems]="1">
                                    </ng-select>
                                </div>
                            </div>

                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label translate>Speciality<span class="text-danger">*</span></label>
                                    <ng-select id="select-spe" formControlName="speciality" [items]="specificSpeciality"
                                        [maxSelectedItems]="3" [searchable]="true" bindLabel="value"
                                        placeholder="Select Speciality" multiple="true" [readonly]="formDisabled">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="practice_types" translate>Practice Type<span
                                            class="text-danger">*</span></label>
                                    <ng-select id="select-pra" formControlName="practice_types" [items]="practiceType"
                                        [searchable]="false" bindLabel="name" bindValue="value"
                                        placeholder="Select Practice Type" multiple="true" [readonly]="formDisabled">
                                    </ng-select>
                                </div>
                            </div>
                            <!-- <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label translate>Consultation Duration</label>
                                    <ng-select id="select-consult" formControlName="consultation_duration" [searchable]="false" bindLabel="time" placeholder="{{'Select Time' | translate}}" [readonly]="formDisabled">
                                        <ng-option [value]="time" *ngFor="let time of this.time"> {{time}}
                                        </ng-option>
                                    </ng-select>
                                </div>
                            </div> -->
                        </div>
                        <div>
                            <button *ngIf="!formDisabled" id="bp-frm-disable" (click)="formDisable()" type="button"
                                [disabled]="profileDataForm.dirty === true ? false : profileDataForm.valid=== true ? false : true"
                                class="btn btn-secondary cancel-btn" translate>Cancel</button>
                            <button *ngIf="!formDisabled " id="bp-save" class="btn btn-primary float-right"
                                type="button" [disabled]="!profileDataForm.valid" (click)="onSubmit()">{{ this.saving ?
                                'Saving....' : 'Save'|translate }}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>