import { delay } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { DoctorService } from '../../doctor.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, EventEmitter, Output, Input, ChangeDetectorRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import * as Settings from '../../../config/settings';

@Component({
  selector: 'app-doctor-basic-profile',
  templateUrl: './doctor-basic-profile.component.html',
  styleUrls: ['./doctor-basic-profile.component.css']
})

export class DoctorBasicProfileComponent implements OnInit {
  @Output() profileCompletion: EventEmitter<string> = new EventEmitter<string>();
  @Output() system_of_medicine: EventEmitter<string> = new EventEmitter<string>();
  @Input() doctorProfileData: any;
  public profileDataForm: FormGroup;
  public practicingSince: any;
  public Year = [];
  public languages = [];
  public specialityMaster = {};
  public department = {};
  public specificSpeciality = [];
  public systemOfMedicine = [];
  public practiceType = [{ name: 'Own Practice', value: 'own_practice' }, { name: 'Hospital Based', value: 'hospital_based' }];
  public time = [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60];
  public saving = false;
  public formDisabled = false;
  dataAvailable = false;
  isPublicDoctor: boolean;
  hospital_id: string;
  specificDepartment = [];
  specificDept = {};
  doctorType: string = '';

  constructor(
    private formBuilder: FormBuilder,
    private doctorService: DoctorService,
    private notificationService: ToastrService,
    private translate: TranslateService,
    private httpClient: HttpClient,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    const lang = localStorage.getItem('pageLagnuage');
    this.translate.use(lang);
    this.checkDoctorIsPublic();
    if (this.isPublicDoctor) {
      this.profileDataForm = this.formBuilder.group({
        years_of_experience: [null, Validators.required],
        languages: [[], Validators.required],
        speciality: [[], Validators.required],
        system_of_medicine: [null, Validators.required],
        practice_types: [[], Validators.required],
        // consultation_duration: [null, Validators.required]
      });
    } else {
      this.profileDataForm = this.formBuilder.group({
        years_of_experience: [null, Validators.required],
        languages: [[], Validators.required],
        speciality: [[], Validators.required],
        department: [[], Validators.required],
        system_of_medicine: [null, Validators.required],
        practice_types: [[], Validators.required],
        // consultation_duration: [null, Validators.required]
      });
      this.getDepartmentData();
    }

    this.generateYear();
    this.getSystemOfMedicine();
    this.getSpecialityData();

    this.doctorService.getDoctorLanguages().subscribe(
      data => {
        this.languages = Object.values(data);
      },
      error => {
        console.log(error);
      }
    );
    this.formDisable();
  }

  formDisable() {
    this.profileDataForm.reset();
    this.renderProfileData();
  }

  renderProfileData() {
    const data = this.doctorProfileData;
    if (data['years_of_experience'] == null) {
      this.formDisabled = false;
    }
    else {
      console.log(data);
      this.system_of_medicine.emit(data['system_of_medicine']);
      this.dataAvailable = true;
      if (this.isPublicDoctor) {
        this.profileDataForm.setValue(
          {
            years_of_experience: data['years_of_experience'],
            // consultation_duration: data['consultation_duration'],
            system_of_medicine: data['system_of_medicine'],
            languages: Object.values(data['languages']),
            practice_types: Object.values(data['practice_types']),
            speciality: Object.values(data['speciality']),
          });
      }
      else {
        this.profileDataForm.setValue(
          {
            years_of_experience: data['years_of_experience'],
            // consultation_duration: data['consultation_duration'],
            system_of_medicine: data['system_of_medicine'],
            languages: Object.values(data['languages']),
            practice_types: Object.values(data['practice_types']),
            department: Object.values(data['department']),
            speciality: Object.values(data['speciality']),
          });
      }

      this.formDisabled = true;
    }
  }

  checkDoctorIsPublic() {
    this.hospital_id = localStorage.getItem('hospital_id');
    if (this.hospital_id != null) {
      this.isPublicDoctor = false;
    }
    else {
      this.isPublicDoctor = true;
      this.hospital_id = null;
    }
    return this.isPublicDoctor;
  }

  patchDoctorProfile(data) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/profile/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  generateYear() {
    const startingYear = 0;
    const currentYear = new Date().getFullYear();
    for (let i = startingYear; i <= 100; i++) {
      this.Year.push(i);
    }
  }

  trackFn(index) {
    return index;
  }

  formEnable() {
    this.formDisabled = false;
    const system_of_medicine = this.profileDataForm.controls['system_of_medicine'].value;
    this.onChangeSystemOfMedicine(system_of_medicine, 'edit');
  }

  getSystemOfMedicine() {
    this.doctorService.getSystemOfMedicine().subscribe(
      (data) => {
        this.systemOfMedicine = [];
        this.systemOfMedicine = Object.keys(data);
        this.specificDept = data;
      },
      (error) => {
        this.notificationService.error('Internal server error', 'Med.Bot');
        console.log(error);
      }
    );
  }

  getDepartmentData() {
    this.doctorService.getParticularHospitalDepartment(this.hospital_id).subscribe(
      (data) => {
        this.department = data;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  async getSpecialityData() {
    if (this.isPublicDoctor) {
      try {
        const data = await this.doctorService.getSpecialitywithoutHospital1();
        this.specialityMaster = data;
      } catch (error) {
        console.error('Error fetching specialties', error);
      }
      //  this.doctorService.getSpecialitywithoutHospital().subscribe(
      //   (data) => {
      //     this.specialityMaster = data;
      //     this.cdr.detectChanges();
      //   },
      //   (error) => {
      //     this.notificationService.error('Internal server error', 'Med.Bot');
      //     console.log(error);
      //   }
      // );
    }
    else {
      try {
        const data = await this.doctorService.getParticularHospitalSpeciality1(this.hospital_id);
        this.specialityMaster = data;
        // Further processing...
      } catch (error) {
        console.error('Error fetching specialties', error);
      }
      // this.doctorService.getParticularHospitalSpeciality(this.hospital_id).subscribe(
      //   (data) => {
      //     this.specialityMaster = data;
      //   },
      //   (error) => {
      //     this.notificationService.error('Internal server error', 'Med.Bot');
      //     console.log(error);
      //   }
      // );
    }
  }

  async onChangeSystemOfMedicine(data, value) {
    if (value === 'changes') {
      if (!this.isPublicDoctor) {
        this.profileDataForm.get('department')?.setValue({ dept_code: '', value: '', department_uuid: '', medical_system: '', approved: false });
        this.profileDataForm.get('department')?.markAsTouched();
        this.profileDataForm.get('department')?.setErrors({ required: true });
      }

      this.profileDataForm.get('speciality')?.setValue({ value: '', label: '', approved: false });
      this.profileDataForm.get('speciality')?.markAsTouched();
      this.profileDataForm.get('speciality')?.setErrors({ required: true });
    }
    this.specificDepartment = [];
    this.specificSpeciality = [];

    if (data) {
      if (!this.isPublicDoctor) {
        const departmentArray = [];
        this.specificDept[data].departments.map(item => {
          return {
            dept_code: item.dept_code,
            value: item.value,
            department_uuid: item.department_uuid,
            medical_system: item.medical_system,
            approved: false,
          };
        }).forEach(item => departmentArray.push(item));;
        this.specificDepartment = departmentArray;
      }
      await this.getSpecialityData();
      // this.cdr.detectChanges();

      Object.keys(this.specialityMaster).forEach(category => {
        if (category == data) {
          const specialityArray = [];
          this.specialityMaster[category].map(item => {
            if (this.isPublicDoctor) {
              return {
                value: item.code,
                label: item.value,
                approved: false,
              };
            } else {
              return {
                value: item.spec_code,
                label: item.spec_name,
                approved: false,
              };
            }
          })
            .forEach(item => specialityArray.push(item));
          this.specificSpeciality = specialityArray;
        }
      });
    }
    this.system_of_medicine.emit(data);
  }

  onSubmit() {
    this.saving = true;
    let profileData;

    if (this.isPublicDoctor) {
      profileData = {
        'years_of_experience': this.profileDataForm.value.years_of_experience,
        'languages': this.profileDataForm.value.languages,
        'system_of_medicine': this.profileDataForm.value.system_of_medicine,
        'practice_types': this.profileDataForm.value.practice_types,
        'speciality': this.profileDataForm.value.speciality
      }
    } else {
      profileData = {
        'years_of_experience': this.profileDataForm.value.years_of_experience,
        'languages': this.profileDataForm.value.languages,
        'system_of_medicine': this.profileDataForm.value.system_of_medicine,
        'department': this.profileDataForm.value.department,
        'practice_types': this.profileDataForm.value.practice_types,
        'speciality': this.profileDataForm.value.speciality
      }
    }

    this.patchDoctorProfile(profileData).subscribe(
      data => {
        this.doctorProfileData = data;
        this.formDisable();
        this.notificationService.success('Profile Data Updated', 'Med.Bot');
        this.saving = false;
        this.profileCompletion.emit();
      }
    );
  }
}
