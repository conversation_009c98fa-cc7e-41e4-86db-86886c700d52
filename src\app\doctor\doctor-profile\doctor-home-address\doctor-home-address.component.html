<div class="card">
    <form id="homeAddressForm" [formGroup]="homeAddressForm">
        <div >
            <div class="card-body">

             <h4 class="card-title" translate>Home Address
                        <a *ngIf="!!readHomeAddress" (click)="editHomeAddress()"><i class="fa fa-edit"></i></a>
                    </h4>
                    <div>
                        <div class="row form-row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label translate>Line 1<span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="line1" formControlName="line_1" autocomplete="off"  maxlength="50" [readonly]="readHomeAddress">
                                    <div *ngIf="homeAddressForm.controls.line_1.invalid && (homeAddressForm.controls.line_1.dirty || homeAddressForm.controls.line_1.touched)"
    class="alert alert-danger">{{specialCharacterError}}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label translate>Line 2<span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="line2" formControlName="line_2" autocomplete="off"  maxlength="50" [readonly]="readHomeAddress">
                                    <div *ngIf="homeAddressForm.controls.line_2.invalid && (homeAddressForm.controls.line_2.dirty || homeAddressForm.controls.line_2.touched)"
    class="alert alert-danger">{{specialCharacterError}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>City/Town/Village<span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="city" formControlName="city_town_village" autocomplete="off" [readonly]="readHomeAddress"  maxlength="50"  pattern="[a-zA-Z ]*" >
                                    <div *ngIf="homeAddressForm.controls.city_town_village.invalid && (homeAddressForm.controls.city_town_village.dirty || homeAddressForm.controls.city_town_village.touched)"
                                      class="alert alert-danger">{{alphabetsError}}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                              <div class="form-group">
                                  <label translate>Taluk<span class="text-danger">*</span></label>
                                  <input type="text" id="taluk" class="form-control" formControlName="taluk" autocomplete="off" [readonly]="readHomeAddress"  maxlength="50"  pattern="[a-zA-Z ]*">
                                  <div *ngIf="homeAddressForm.controls.taluk.invalid && (homeAddressForm.controls.taluk.dirty || homeAddressForm.controls.taluk.touched)"
                                  class="alert alert-danger">{{alphabetsError}}</div>
                              </div>
                          </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>District<span class="text-danger">*</span></label>
                                    <input type="text" id="distric" class="form-control" formControlName="district" autocomplete="off" [readonly]="readHomeAddress"  maxlength="50"  pattern="[a-zA-Z ]*">
                                    <div *ngIf="homeAddressForm.controls.district.invalid && (homeAddressForm.controls.district.dirty || homeAddressForm.controls.district.touched)"
                                    class="alert alert-danger">{{alphabetsError}}</div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>State<span class="text-danger">*</span></label>
                                    <input type="text" id="state" class="form-control" formControlName="state" autocomplete="off" [readonly]="readHomeAddress"  maxlength="50"  pattern="[a-zA-Z ]*">
                                    <div *ngIf="homeAddressForm.controls.state.invalid && (homeAddressForm.controls.state.dirty || homeAddressForm.controls.state.touched)"
                                    class="alert alert-danger">{{alphabetsError}}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>Country <span class="text-danger">*</span></label>
                                    <ng-select id="country" formControlName="country" [items]="countryList" [clearable]="false" [searchable]="true" bindLabel="Name" bindValue="Name" placeholder="{{'Select Country' | translate}}" multiple [readonly]="readHomeAddress">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>Postal Code<span class="text-danger">*</span>  </label>
                                    <input type="text" id="postal_code" class="form-control" formControlName="postal_code" autocomplete="off"  maxlength="10" [readonly]="readHomeAddress">
                                    <div *ngIf="homeAddressForm.controls.postal_code.invalid && (homeAddressForm.controls.postal_code.dirty || homeAddressForm.controls.postal_code.touched)"
                                    class="alert alert-danger">{{alphanumericError}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12 text-right" *ngIf="!readHomeAddress">
                          <button id="canc-hme-addr" class="btn btn-secondary cancel-btn"  (click)="cancelHomeAddress()" type="button" [disabled]="homeAddressForm.dirty=== true?false  :homeAddressForm.valid=== true? false:true" translate>Cancel</button>
                            <button id="save-hme-addr" class="btn btn-primary"  [disabled]="!homeAddressForm.valid" (click)="saveHomeAddress()" translate>Save</button>

                        </div>
                        <br>
                    </div>


            </div>
        </div>
    </form>
</div>
