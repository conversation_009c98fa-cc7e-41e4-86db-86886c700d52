.cancel-btn {
  float: right;
  font-size: 15px;
  padding: 3px;
  margin: 5px;
  margin-right: 11px;
}

.btn_color {
  color: #fff;
}

.fa-edit {
  color: #20c0f3;
  cursor: pointer;
}

.basic-data-btn {
  float: right;
  padding: 3px 13px 4px 10px;
}

#profile-picture {
  width: 100%;
}

.tandc {
  display: inline;
  margin-right: 15px;
}

.text-center {
  margin-bottom: 25px;
}

.modalShowClass {
  display: block !important;
}




/* .modal-body {
    height: 250px;
    overflow-y: auto;
} */

.btn-primary[disabled] {
  background-color: darkgray !important;
  border-color: darkgray !important;
}

.btn-primary {
  background-color: #09e5ab !important;
  border-color: #09e5ab !important;
  color: #fff;
  font-size: 15px;
  padding: 2px 10px 6px 13px;
  margin: 5px;
}

::ng-deep .ng-select .ng-select-container {
  color: #333;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ccc;
  min-height: 46px;
  align-items: center;
}

::ng-deep .ng-select.ng-select-disabled>.ng-select-container {
  background-color: #e9ecef;
}


.change-photo-btn.doc-sign {
  margin-left: 0px !important;
}

.bs-datepicker {
  display: none !important;
}

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .example {
    background: red;
  }

  .modal-size {
    width: 90%;
    height: 500px;
    overflow: auto;
    margin: 4%;
  }

  .modal-dialog.t-c {
    overflow-y: initial !important;
  }

  .modal-body.t-c {
    height: 300px;
    overflow-y: auto;
  }

  .doc-sign {
    width: 60%;
    margin-left: 0px !important;
  }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .example {
    background: green;
  }

  .modal-size {
    width: 90%;
    height: 300px;
    overflow: auto;
  }

  .modal-body.t-c {
    height: 400px;
    overflow-y: auto;
  }

  .modal-dialog.t-c {
    overflow-y: initial !important;
  }

  .doc-sign {
    width: 60%;
    margin-left: 0px !important;
  }

}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .example {
    background: blue;
  }

  .modal-size {
    width: 100%;
    height: 500px;
    overflow: auto;
    margin: 0%;
  }

  .modal-dialog.t-c {
    overflow-y: initial !important;
    min-width: 700px;
  }

  .modal-body.t-c {
    height: 400px;
    overflow-y: auto;
  }

  .doc-sign {
    width: 60%;
    margin-left: 0px !important;
  }
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .example {
    background: orange;
  }

  .modal-size {
    width: 100%;
    height: 600px;
    overflow: auto;

  }

  .modal-dialog.t-c {
    overflow-y: initial !important;
    min-width: 1000px;
  }

  .modal-body.t-c {
    height: 400px;
    overflow-y: auto;
  }

  .doc-sign {
    width: 60%;
    margin-left: 0px !important;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .example {
    background: pink;
  }

  .modal-size {
    width: 130%;
    height: 600px;
    overflow: auto;
    margin-left: -100px;
  }

  .modal-dialog.t-c {
    overflow-y: initial !important;
    min-width: 1000px;
  }

  .modal-body.t-c {
    height: 500px;
    overflow-y: auto;
  }

  .doc-sign {
    width: 60%;
    margin-left: 0px !important;
  }
}

.mat-card {
  position: relative
}

.buttons {
  position: absolute;
  font-family: serif;
  padding: 10px 20px;
  display: inline-block;
  top: 20%;
  right: 0%;
  width: 140px;
  transform: translateY(-50%);
  color: #fff;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  outline: none;
  background-color: #04C5FF;
  border: none;
  border-radius: 15px;

}

.buttons:hover {
  background-color: #17233E
}

/*.buttons:active {
  background-color: #3e8e41;
  box-shadow: 0 5px #666;
  transform: translateY(4px);
}
*/
.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  transition: opacity 500ms;
  visibility: visible;
  opacity: 1;
}


.socialSharePopup {
  margin: 70px auto;
  padding: 20px;
  background: #fff;
  border-radius: 5px;
  width: 30%;
  position: relative;
  transition: all 5s ease-in-out;
}

.socialSharePopup h2 {
  margin-top: 0;
  color: #333;
  font-family: Tahoma, Arial, sans-serif;
}

.socialSharePopup .close {
  position: absolute;
  top: 20px;
  right: 30px;
  transition: all 200ms;
  font-size: 30px;
  font-weight: bold;
  text-decoration: none;
  color: #333;
}

.socialSharePopup .close:hover {
  color: #d80606;
  cursor: pointer;
}

.socialSharePopup .content {
  max-height: 30%;
  overflow: auto;
}

@media screen and (max-width: 700px) {

  .popup {
    width: 70%;
  }
}

/* .slugname-btn {
  float: left;
  font-size: 15px;
  color: #fff;
  padding: 5px 10px;
  background-color: #04C5FF;
  border-radius: 5px;

} */


.publicprofile-container {
  display: table;
  width: 100%
}

.publicprofile-container-label {
  display: table-cell;
  width: 1px;
  white-space: nowrap;
}

.publicprofile-container input {
  display: table-cell;
  width: 60%;
}

.publicprofile-container-button {
  display: table-cell;
  font-size: 15px;
  color: #fff;
  padding: 5px 10px;
  background-color: #04C5FF;
  border-radius: 5px;

}

.publicprofile-container-social {
  position: absolute;
  font-family: serif;
  padding: 10px 20px;
  display: inline-block;
  top: 20%;
  right: 0%;
  width: 140px;
  transform: translateY(-50%);
  color: #fff;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  outline: none;
  background-color: #04C5FF;
  border: none;
  border-radius: 15px;
}

/* .publicprofile-container-social:hover {background-color: #17233E} */
.publicprofile-container-button[disabled] {
  border: 1px solid #999999;
  background-color: #cccccc;
  color: #666666;
}

.publicprofile-container-social[disabled] {
  border: 1px solid #999999;
  background-color: #cccccc;
  color: #666666;
}