<!--profile form-->
<div *ngIf="isLoading">
    <app-loading-spinner></app-loading-spinner>
</div>
<div *ngIf="!isLoading">
    <div class="">
        <p *ngIf="disableApprovalBtn && profileCompletion < 100 && isPublicDoctor" class="text-danger">* Please fill the
            Basic Profile Data, Education, Registration, Fee details, Home Address and accept the terms and condition.
        </p>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="row form-row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="change-avatar">
                            <div class="profile-img">
                                <img [src]="doctorProfilePictureUrl" alt="User Image">
                            </div>
                            <div class="upload-img">
                                <div class="change-photo-btn">
                                    <span><i class="fa fa-upload"></i> {{ profileUpload ? ('Upload Photo'|translate):
                                        'Uploading'|translate}}</span>
                                    <input type="file" class="upload" id="profile-picture"
                                        [disabled]="disabledUploadPhotoBtn"
                                        (change)="doctorProfilePictureChange($event)" accept=".jpg, .png,">
                                </div>
                                <small class="form-text text-muted" translate>Photograph you upload here will be seen by
                                    the patient when they search.</small>
                                <small class="form-text text-muted" translate>(Allowed JPG, JPEG or PNG. Max size of
                                    2MB)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <h4 class="card-title" translate>Personal Profile <i *ngIf="disabled" (click)="editProfile()"
                    class="fa fa-edit"></i></h4>
            <div class="form-group " *ngIf="formError">
                <div class="">
                    <ng-container *ngFor="let err of errorValue">
                        <p class="text-danger">&nbsp;{{err.value}}</p>
                    </ng-container>
                </div>
            </div>
            <form [formGroup]="personalProfileForm">
                <div class="row form-row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Display Name<span class="text-danger">*</span></label>
                            <input id="fullname" class="form-control" type="text" name="username"
                                formControlName="username" maxlength="25" pattern="[a-zA-Z0-9 ]*" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Email <span class="text-danger">*</span></label>
                            <input id="email" type="email" name="email" class="form-control" formControlName="email"
                                maxlength="50" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>First Name</label>
                            <input id="firstname" type="text" class="form-control" name="first_name"
                                formControlName="first_name" maxlength="25" pattern="[a-zA-Z ]*" autocomplete="off"
                                [readonly]="disabled">
                            <div *ngIf="personalProfileForm.controls.first_name.invalid && (personalProfileForm.controls.first_name.dirty || personalProfileForm.controls.first_name.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Middle Name</label>
                            <input id="middlename" type="text" class="form-control" name="middle_name"
                                formControlName="middle_name" maxlength="25" pattern="[a-zA-Z ]*" autocomplete="off"
                                [readonly]="disabled">
                            <div *ngIf="personalProfileForm.controls.middle_name.invalid && (personalProfileForm.controls.middle_name.dirty || personalProfileForm.controls.middle_name.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Last Name</label>
                            <input id="lastname" type="text" class="form-control" name="last_name"
                                formControlName="last_name" maxlength="25" pattern="[a-zA-Z ]*" autocomplete="off"
                                [readonly]="disabled">
                            <div *ngIf="personalProfileForm.controls.last_name.invalid && (personalProfileForm.controls.last_name.dirty || personalProfileForm.controls.last_name.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Phone Number<span class="text-danger">*</span></label>
                            <input id="phone" type="text" class="form-control" formControlName="phone" pattern="[0-9]*"
                                name="phone" maxlength="15" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Gender<span class="text-danger">*</span></label>
                            <select class="form-control" name="gender" id="gender" formControlName="gender">
                                <option *ngFor="let data of gender" [value]="data.value ">{{data.name}}</option>

                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-0 ">
                            <label translate>Date of Birth<span class="text-danger">*</span></label>
                            <input [maxDate]="maxDate" [minDate]="minDate" placeholder="DOB" onkeydown="return false"
                                class="form-control" formControlName="date_of_birth" [readonly]="disabled" bsDatepicker
                                [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }"
                                [ngClass]="{'bs-datepicker':disabled==true}" required="">
                            <input class="form-control" formControlName="date_of_birth" *ngIf="disabled==true" readonly required>
                        </div>
                    </div>
                </div>
                <div class="form-group float-right">
                    <button *ngIf="!disabled" type="button" id="save-btn" id="per-prof-btn" class="btn btn-primary"
                        translate (click)="onSubmit()" [disabled]="!personalProfileForm.valid">Save</button>
                    <button *ngIf="!disabled" type="button" id="cancel-btn" (click)="cancelUpdate()"
                        class="btn btn-secondary cancel-btn" translate>Cancel</button>

                </div>
                <!-- /Basic Information -->
            </form>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <h5>Public Profile URL</h5>
            <button class="publicprofile-container-social" (click)="socialShare = true" translate
                [disabled]="PersonalDoctorUrlForm.controls.slug.value != this.urlname?true:false">
                <i class="fa fa-share-alt"><b> Share</b></i>
            </button>
            <div class="publicprofile-container">
                <label class="publicprofile-container-label">{{ domainName }}</label>
                <form [formGroup]="PersonalDoctorUrlForm">
                    <div class="form-group">
                        <input id="slug" class="form-control" type="text" name="slug" formControlName="slug"
                            maxlength="20" pattern="[a-zA-Z0-9\-_ ]*" size="20">
                        <!-- <button *ngIf="slugDisabled" class="publicprofile-container-button" (click)="slugSubmit()" translate [disabled]="PersonalDoctorUrlForm.dirty=== true?false : PersonalDoctorUrlForm.valid=== true?false:true"><b>Update</b> -->
                        <button class="publicprofile-container-button" (click)="slugSubmit()" translate
                            [disabled]="!PersonalDoctorUrlForm.controls.slug.dirty  || PersonalDoctorUrlForm.controls.slug.invalid || PersonalDoctorUrlForm.controls.slug.value == this.urlname?true:false"><b>Update</b>
                        </button>
                        <button class="publicprofile-container-button" (click)="cancelSubmit()" translate
                            [disabled]="!PersonalDoctorUrlForm.controls.slug.dirty"><b>Cancel</b>
                        </button>
                        <button class="publicprofile-container-button" (click)="copyText(doctorPersonalUrl)"
                            translate><b>Copy Link</b>
                        </button>
                        <!-- <label>{{ PersonalDoctorUrlForm.controls.slug.data }}</label> -->
                    </div>
                    <div *ngIf="PersonalDoctorUrlForm.controls.slug.invalid && (PersonalDoctorUrlForm.controls.slug.dirty || PersonalDoctorUrlForm.controls.slug.touched)"
                        class="alert alert-danger">only accept [letters, numbers, underscores, hyphens]</div>
                    <label id="urlstatus" style="color: red;"></label>
                </form>
            </div>
        </div>
    </div>
    <!--Basic Profile Data-->
    <app-doctor-basic-profile (profileCompletion)="checkProfileCompletionStatus()"
        (system_of_medicine)="getSystemOfMedicine($event)"
        [doctorProfileData]="doctorProfileData"></app-doctor-basic-profile>
    <!-- doctor-qualifications -->
    <app-qualifications (profileCompletion)="checkProfileCompletionStatus()"
        [system_of_medicine]="system_of_medicine"></app-qualifications>
    <!-- doctor-registration -->
    <app-doctor-registration (profileCompletion)="checkProfileCompletionStatus()"
        [system_of_medicine]="system_of_medicine"></app-doctor-registration>
    <!-- doctor-fee-->
    <div *ngIf="checkDoctorIsPublic()">
        <app-fees (profileCompletion)="checkProfileCompletionStatus()"></app-fees>
    </div>

    <!-- doctor signature -->
    <div class="card">
        <div class="card-body">
            <h4 id="doctor-sign-title">Signature<span class="text-danger">*</span></h4>
            <div class="row">
                <div class="  col-xl-2 col-lg-3 col-md-4 col-sm-6" *ngIf="doctorSignatureUrl !='null'">
                    <div class="change-avatar">
                        <div class="profile-img">
                            <img [src]="doctorSignatureUrl" alt="User Image" style="width: 200px;">
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mt-4">
                    <div class="change-photo-btn doc-sign" id="doc-sign-upload" style="padding: 6px;">
                        <span><i class="fa fa-upload"></i> {{ signUpload ? ('Upload Photo'|translate):
                            'Uploading'|translate}}</span>
                        <input type="file" class="upload" id="doctor-sign" [disabled]="disabledUploadPhotoBtn"
                            (change)="doctorSignatureUpload($event)" accept=".jpg, .png,">
                    </div>
                    <small class="form-text text-muted" translate>Allowed JPG,JPEG or PNG. Max size of 2MB</small>
                </div>
            </div>
        </div>
    </div>

    <!-- doctor-home-address -->
    <app-doctor-home-address (profileCompletion)="checkProfileCompletionStatus()"></app-doctor-home-address>

    <div *ngIf="approvalBtn&&isPublicDoctor">
        <input type="checkbox" id="t-c-checkbox" class="tandc" [value]="checkTCValue" (change)="checkTandC($event)">
        <p class="tandc">I agreed to the <a href="javascript:void(0);" data-toggle="modal" data-target="#myModal"
                (click)="showTermsAndCondition()">Terms and Conditions</a> of MEDBOT.
        </p>
    </div>
    <div class="text-center">
        <button *ngIf="approvalBtn && isPublicDoctor && (approvalStatus == 'null' || approvalStatus == null)"
            type="submit" class="btn btn-primary" data-toggle="modal" data-target="#approvalConfirmModal"
            [disabled]="disableApprovalBtn">Submit For Approval</button>
        <button *ngIf="approvalStatus == 'Approved'" type="submit" class="btn btn-primary" disabled>Approved</button>
        <button *ngIf="approvalStatus == 'Pending'" type="submit" class="btn btn-primary" disabled>Approval
            Pending</button>
        <button *ngIf="approvalStatus == 'Rejected'" type="submit" class="btn btn-primary" disabled>Rejected</button>
        <p *ngIf="disableApprovalBtn && profileCompletion < 100 && isPublicDoctor" class="text-danger">
            {{pending_sections_message}}
        </p>
        <p *ngIf="disableApprovalBtn && !this.checkTCValue && isPublicDoctor &&!termsandC && (approvalStatus == 'null' || approvalStatus == null)"
            class="text-danger">Please accept the terms and condition.</p>
        <p *ngIf="disableApprovalBtn && this.checkTCValue && profileCompletion == 100" class="text-danger">Processing..
        </p>
    </div>
</div>
<div id="myModal" class="modal" tabindex="-1" role="dialog">
    <div class="modal-dialog t-c" role="document">
        <div class="modal-content modal-size">
            <div class="modal-header">
                <div class="tex-center">
                    <h5 class="modal-title tex-center" translate>Terms and Conditions</h5>
                    <p translate>
                        Here are the terms & conditions governing the usage of Med.Bot by doctors. Please read and
                        accept before you can proceed further.
                    </p>
                </div>
                <button type="button" class="close" data-dismiss="modal" id="close-model-btn" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="terms-model-body">
                <div [innerHTML]="contentHtml">
                    <p>{{ contentText }}</p>
                </div>
            </div>
            <div class="modal-footer">
                <div class="text-center">
                    <button type="button" id="cancel" class="btn btn-danger mr-5" data-dismiss="modal"
                        translate>Close</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="approvalConfirmModal" class="modal fade" tabindex="-1" role="dialog"
    aria-labelledby="approvalConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalConfirmModalLabel">Are you sure?</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <h5>Once request sent, you are not allowed to modify or change the content education degree
                    qualification of registrations.</h5>
                <h5 class="text-center">If you need any support please mail to <a
                        href="javascript:void(0)"><EMAIL></a></h5>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" #closeButton data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" (click)="submitForApproval()"
                    [disabled]="disCnfBtn">Confirm</button>
            </div>
        </div>
    </div>
</div>

<div class="overlay" *ngIf="socialSharePopup">
    <div class="socialSharePopup">
        <h3>Share Profile Link</h3>
        <a class="close" (click)="socialSharePopup = false">&times;</a>
        <div class="content">
            <table>
                <tr>
                    <td><app-share-button type='facebook' [shareUrl]='doctorPersonalUrl'>
                        </app-share-button>
                    </td>
                    <td><app-share-button type='twitter' [shareUrl]='doctorPersonalUrl'>
                        </app-share-button>
                    </td>
                </tr>
                <tr>
                    <td><app-share-button type='gmail' [shareUrl]='doctorPersonalUrl'>
                        </app-share-button>
                    </td>
                    <td><app-share-button type='linkedin' [shareUrl]='doctorPersonalUrl'>
                        </app-share-button>
                    </td>
                </tr>
                <tr>
                    <td><app-share-button type='telegram' [shareUrl]='doctorPersonalUrl'>
                        </app-share-button>
                    </td>
                    <td><app-share-button type='whatsapp' [shareUrl]='doctorPersonalUrl'>
                        </app-share-button>
                    </td>
                </tr>
                <!-- <tr>
                    <td>
                        <button class="fa fa-link" (click)="copyText(doctorPersonalUrl)" >Copy Link</button>
                    </td>                    
                </tr> -->
            </table>
        </div>
    </div>
</div>