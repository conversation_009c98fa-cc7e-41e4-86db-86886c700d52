import { SharedService } from './../../shared/shared.service';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from '../../auth/auth.service';
import { DoctorService } from '../doctor.service';
import * as Settings from '../../config/settings';
import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
  NgForm,
  FormGroup,
  FormArray,
  FormControlName,
  FormControl,
  Validators,
} from '@angular/forms';
import * as moment from 'moment';
declare var $: any;
@Component({
  selector: 'app-profile',
  templateUrl: './doctor-profile.component.html',
  styleUrls: ['./doctor-profile.component.css'],
})
export class DoctorProfileComponent implements OnInit {
  @ViewChild('closebutton') closebutton;
  @ViewChild('profileFieldset') profileFieldset: ElementRef;
  @Output() messageEvent: EventEmitter<string> = new EventEmitter<string>();
  @ViewChild('qualSubmitButton') qualSubmitButton: ElementRef;
  public changed = false;
  public profileUpload = true;
  public signUpload = true;
  public disabledUploadPhotoBtn = false;
  public personalProfileForm: FormGroup;
  public user_data = {};
  public disabled = true;
  public doctorProfilePictureUrl: string;
  public PersonalDoctorUrlForm: FormGroup;
  public cancelbtn = false;
  profileFileSizeLarge = false;
  public feesEditBtnShow = false;
  public cancleBtnShow = false;
  public feeForm: FormGroup;
  public msg = '';
  public dob: any;
  userID: any;
  // qualification
  qualificationFrom: FormGroup;
  public qualificationArray: FormArray;
  public qualificationForm: FormGroup;
  public qualificationFormSubmit = true;
  public qualificationFile: File;
  public create = false;
  public termsandC = false;
  public contentHtml = '';
  public contentText = '';
  public disableApprovalBtn = true;
  public checkTCValue: boolean;
  public approvalStatus = '';
  approvalBtn = true;
  profileCompletion: any;
  public isLoading = true;
  disCnfBtn = false;
  maxDate: Date;
  minDate: Date;
  disabelFormBtn: boolean;
  socialSharePopup: boolean;
  doctorPersonalUrl: string;
  domainName: string;
  public urlname: string;
  gender = [
    { value: '', name: 'Select' },
    { value: 'Male', name: 'Male' },
    { value: 'Female', name: 'Female' },
    { value: 'Prefer not to answer', name: 'Prefer not to answer' },
  ];
  formError = false;
  errorValue = [];
  public userDetails: any = {
    username: null,
    email: null,
    phone: null,
    gender: null,
    first_name: null,
    middle_name: null,
    last_name: null,
    date_of_birth: null
  };

  public slugDetails: any = {
    slug: null,
    uuid: null
  }
  doctorSignatureUrl = 'null';
  system_of_medicine = null;
  doctorProfileData: Object;
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  isPublicDoctor: boolean;
  pending_sections_message: string = '';

  constructor(
    private userService: AuthService,
    private notificationService: ToastrService,
    private doctorService: DoctorService,
    public translate: TranslateService,
    private sharedService: SharedService
  ) { }

  ngOnInit(): void {
    this.maxDate = new Date();
    this.minDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate() - 7672);
    this.minDate.setDate(this.minDate.getDate() - 36500);
    console.log('mindate', this.maxDate);
    this.doctorProfilePictureUrl = '../../../../assets/img/doctors/doctor-thumb-02.png';
    const lang = localStorage.getItem('pageLanguage');
    console.log('profile settings', lang);
    this.translate.use(lang);
    this.checkTandC(null);
    this.addProfileFromControl(null);
    this.addSlugFormControl(null);
    this.getuserData();
    this.approvalStatus = localStorage.getItem('profile_approved_status');
    this.profileCompletion = parseInt(
      localStorage.getItem('profile_completion')
    );
    if (this.profileCompletion < 100) {
      this.notificationService.error('Please fill all mandatory fields marked (*) and click "submit for approval"', '', { timeOut: 20000 });
    }
    if (
      this.approvalStatus == 'Approved' ||
      this.approvalStatus == 'Pending' ||
      this.approvalStatus == 'Rejected'
    ) {
      this.approvalBtn = false;
    }
    if (this.approvalStatus == 'Pending') {
      this.notificationService.warning('Your approval is pending');
    } else if (this.approvalStatus == 'Rejected') {
      this.notificationService.error('Your approval is not accepted');
    }

    this.userService.getDoctorSignature().subscribe(
      data => {
        if (data['results'].length > 0) {
          const result = data['results'].pop();
          this.doctorSignatureUrl = result?.['file'];
        }
      }, error => {
        this.notificationService.error('internal server error', 'Med.Bot')
      }
    );
  }

  checkDoctorIsPublic() {
    const hospital_id = localStorage.getItem('hospital_id');
    if (hospital_id != null) {
      this.isPublicDoctor = false;
    }
    else {
      this.isPublicDoctor = true;
    }
    return this.isPublicDoctor;
  }

  getuserData() {
    this.doctorService.getDoctorProfile().subscribe(
      (data) => {
        this.urlname = data['slug'];
        this.addSlugFormControl(data['slug']);
        console.log(`${Settings.API_URL_PREFIX}` + "/" + data['slug']);
        if (`${Settings.API_URL_PREFIX}` == "http://127.0.0.1") {
          this.doctorPersonalUrl = "http://localhost:8004/#/" + data['slug'];
          this.domainName = "http://localhost:8004/#/";
        } else {
          this.doctorPersonalUrl = `${Settings.API_URL_PREFIX}` + "/#/" + data['slug'];
          this.domainName = `${Settings.API_URL_PREFIX}` + "/#/";
        }
        this.doctorProfileData = data;
        this.user_data = data['user'];
        this.addProfileFromControl(data['user']);
        if (data['user_type'] === 'Doctor') {
          this.getPracticeLocation();
        }
        if (this.user_data['profile_picture'] !== null) {
          this.doctorProfilePictureUrl = this.user_data['profile_picture'];
        }
        this.approvalStatus = localStorage.getItem('profile_approved_status');
        this.profileCompletion = parseInt(
          localStorage.getItem('profile_completion')
        );
        console.log(this.approvalStatus);
        if (
          this.approvalStatus == 'Approved' ||
          this.approvalStatus == 'Pending'
        ) {
          this.approvalBtn = false;
        }
        setTimeout(() => {
          this.isLoading = false;
        }, 2000);
        this.pending_sections_message = data['profile_completion_status']?.pending_sections_message;
      },
      (error) => {
        console.log(error);
      }
    );
  }
  copyText(val: string) {
    let selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
  }

  addSlugFormControl(data) {
    if (data === null) {
      this.PersonalDoctorUrlForm = new FormGroup({
        slug: new FormControl(null, [Validators.required, Validators.maxLength(20), Validators.pattern('[a-zA-Z0-9\-_ ]*')])
      });

    } else {
      this.PersonalDoctorUrlForm = new FormGroup({
        slug: new FormControl(data, [Validators.required, Validators.maxLength(20), Validators.pattern('[a-zA-Z0-9\-_ ]*')])
      });
    }
  }

  cancelSubmit() {
    this.slugDetails.slug = this.urlname;
    this.addSlugFormControl(this.slugDetails.slug);
  }

  slugSubmit() {
    this.slugDetails.slug = this.PersonalDoctorUrlForm.controls[`slug`].value;
    this.slugDetails.uuid = localStorage.getItem('Doctor');
    this.doctorService.updateSlugName(this.slugDetails)
      .subscribe(
        (data) => {
          this.addSlugFormControl(data['slug']);
          this.notificationService.success('Doctor URL Name Updated', 'Med.Bot');
          if (`${Settings.API_URL_PREFIX}` == "http://127.0.0.1") {
            this.doctorPersonalUrl = "http://localhost:4200/" + data['slug'];
            this.urlname = data['slug'];
            document.getElementById('slug').style.borderColor = "#C0C0C0";
            document.getElementById('urlstatus').innerHTML = '';
          } else {
            this.doctorPersonalUrl = `${Settings.API_URL_PREFIX}` + "/" + data['slug'];
            this.urlname = data['slug'];
            document.getElementById('slug').style.borderColor = "white";
            document.getElementById('urlstatus').innerHTML = '';
          }
        },
        (error) => {
          const status = error['status'];
          if (status == 409) {
            this.slugDetails.slug = this.urlname;
            this.addSlugFormControl(this.slugDetails.slug);
            this.formError = true
            const error_message = error['error']['error_message']
            document.getElementById('slug').style.borderColor = "red";
            document.getElementById('urlstatus').innerHTML = `${error_message}`;
          } else {
            document.getElementById('slug').style.borderColor = "red";
            document.getElementById('urlstatus').innerHTML = `${error['statusText']}`;
          }
        }
      );
  }

  onSubmit() {
    this.formError = false;
    this.errorValue = [];
    const dob = this.personalProfileForm.controls[`date_of_birth`].value;
    console.log(dob)
    this.userDetails.username = this.personalProfileForm.controls[`username`].value;
    this.userDetails.email = this.personalProfileForm.controls[`email`].value;
    this.userDetails.phone = this.personalProfileForm.controls[`phone`].value;
    this.userDetails.gender = this.personalProfileForm.controls[`gender`].value;
    this.userDetails.first_name = this.personalProfileForm.controls[`first_name`].value;
    this.userDetails.last_name = this.personalProfileForm.controls[`last_name`].value;
    this.userDetails.middle_name = this.personalProfileForm.controls[`middle_name`].value;
    this.userDetails.date_of_birth = moment(dob, 'DD-MM-YYYY').format('YYYY-MM-DD');
    this.userService
      .updatePersonalProfile(this.userDetails)
      .subscribe(
        (data) => {
          this.personalProfileForm.get('gender').disable();
          this.user_data = data;
          this.addProfileFromControl(data);
          this.notificationService.success('Profile Updated', 'Med.Bot');
          this.disabled = true;
          this.checkProfileCompletionStatus();
        },
        (error) => {
          console.log(error);
          const err = error['error']['error_details']['validation_errors'];
          if (err) {
            this.formError = true;
            const gender = err['gender'];
            const dob = err['date_of_birth'];
            if (gender && dob) {
              const genderError = 'Gender : ' + gender[0];
              const dobError = 'DOB : ' + dob[0];
              this.notificationService.error(
                `${genderError} ${dobError}`,
                'Med.Bot'
              );
              this.errorValue.push({ value: genderError }, { value: dobError });
            } else if (gender) {
              this.formError = true;
              const genderError = 'Gender : ' + gender[0];
              this.notificationService.error(`${genderError}`, 'Med.Bot');
              this.errorValue.push({ value: genderError });

            } else if (dob) {

              const dobError = 'DOB : ' + dob[0];
              this.notificationService.error(` ${dobError}`, 'Med.Bot');
              this.errorValue.push({ value: dobError })
            } else {
              this.formError = false;
              this.notificationService.error('Updation Error', 'Med.Bot');
            }
          } else {
            this.notificationService.error(
              'Internal server error',
              'Med.Bot'
            );
          }
        }
      );
  }

  onChange() {
    this.changed = true;
  }

  doctorProfilePictureChange(event) {
    const file = event.target.files;

    if (file.length > 0) {
      this.profileFileSizeLarge = false;
      const selectedProfilePicture = file[0];
      console.log(selectedProfilePicture);
      if (
        selectedProfilePicture.size < 2000000 &&
        (selectedProfilePicture.type === 'image/jpg' ||
          selectedProfilePicture.type === 'image/png' ||
          selectedProfilePicture.type === 'image/jpeg')
      ) {
        this.disabledUploadPhotoBtn = true;
        this.profileUpload = false;
        this.userService
          .updateDoctorProfilePicture(selectedProfilePicture)
          .subscribe(
            (data) => {
              this.user_data = data;
              this.doctorProfilePictureUrl = this.user_data['profile_picture'];
              this.sendProfileToChildComponent();
              this.checkProfileCompletionStatus();
              this.profileUpload = true;
              this.sharedService.setPicture(this.doctorProfilePictureUrl);
              this.disabledUploadPhotoBtn = false;
              this.notificationService.success(
                'Profile Picture Updated',
                'Med.Bot'
              );
            },
            (error) => {
              this.profileUpload = true;
              this.disabledUploadPhotoBtn = false;
              console.log(error);
              this.notificationService.error(
                'Error In Updating profile picture',
                'Med.Bot'
              );
            }
          );
      } else {
        this.profileFileSizeLarge = true;
        this.notificationService.error(
          '  Profile picture size large',
          'Med.Bot'
        );
      }
    } else {
      this.profileUpload = true;
      this.disabledUploadPhotoBtn = false;
      this.notificationService.error(
        'Please select  profile picture',
        'Med.Bot'
      );
    }
  }

  sendProfileToChildComponent() {
    this.messageEvent.emit(this.doctorProfilePictureUrl);
  }
  editProfile() {
    this.disabled = false;
    this.personalProfileForm.get('gender').enable();
  }
  cancelUpdate() {
    this.disabled = true;
    this.addProfileFromControl(this.user_data);
  }

  getPracticeLocation() {
    this.doctorService.getDoctorPracticeLocations().subscribe(
      (data) => {
        const practiceLocationList = Object.values(data);
        if (practiceLocationList.length > 0) {
          sessionStorage.setItem(
            'practice_location',
            practiceLocationList[0]['uuid']
          );
        }
      },
      (err) => {
        console.log(err);
      }
    );
  }

  checkTandC(event) {
    const status = localStorage.getItem('t&c');
    if (status == 'pending') {
      this.termsandC = false;
    } else {
      this.termsandC = true;
    }
    this.checkTCValue = event?.target.checked;
    if (this.checkTCValue) {
      this.doctorService.getDoctorProfile().subscribe((data) => {
        let profile_completion = data['profile_completion_percentage'];
        this.approvalStatus = data['approval_request_status'];

        this.profileCompletion = profile_completion;

        if (!this.termsandC && event != null) {
          console.log(this.checkTCValue);
          this.checkTCValue = event.target.checked;

          if (parseInt(profile_completion) == 100 && this.checkTCValue) {
            this.disableApprovalBtn = false;
          } else {
            this.disableApprovalBtn = true;
          }
        }
      });
    } else {
      this.disableApprovalBtn = true;
    }
  }

  submitForApproval() {
    this.disCnfBtn = true;
    if (this.checkTCValue) {
      const id = localStorage.getItem('t&c_uuid');
      const data = { uuid: id };
      this.doctorService.updateTermsAndCondtion(data).subscribe(
        (data) => {
          this.notificationService.success(
            'Your profile is submitted for approval'
          );
          localStorage.setItem('t&c', 'verified');
          this.notificationService.warning(
            'your approval is pending'
          );
        },
        (error) => {
          this.notificationService.error(
            'Profile Approval Request Sent Failed'
          );
        }
      );
    }
    this.doctorService.submitForApproval().subscribe(
      (data) => {
        $('#approvalConfirmModal').modal('hide');
        this.approvalStatus = 'Pending';
        this.approvalBtn = false;
        this.checkProfileCompletionStatus();

      },
      (error) => {
        $('#approvalConfirmModal').modal('hide');
        this.checkProfileCompletionStatus();
        this.disCnfBtn = false;
      }
    );
  }

  checkProfileCompletionStatus() {
    console.log(this.approvalStatus);
    if (this.approvalStatus == 'null' || this.approvalStatus == null) {
      this.doctorService.getDoctorProfile().subscribe((data) => {
        let profile_completion = data['profile_completion_percentage'];
        localStorage.setItem('profile_completion', profile_completion);
        const completed_sections =
          data['profile_completion_status']?.completed_sections;
        this.profileCompletion = parseInt(profile_completion);
        this.pending_sections_message = data['profile_completion_status']?.pending_sections_message;
        if (!this.termsandC) {
          if (parseInt(profile_completion) == 100 && this.checkTCValue) {
            this.disableApprovalBtn = false;
          } else {
            this.disableApprovalBtn = true;
          }
        }
      });
    } else if (this.approvalStatus == 'Approved') {
      this.doctorService.getDoctorProfile().subscribe((data) => {
        let profile_completion = data['profile_completion_percentage'];
        localStorage.setItem('profile_completion', profile_completion);
        this.profileCompletion = parseInt(profile_completion);
        this.pending_sections_message = '';
        this.approvalBtn = false;
      });
    }
  }

  checkCompletedSections(data) { }
  selectGender(gender) {
    if (gender === 'Select') {
    }
  }

  addProfileFromControl(data) {
    let today = moment(new Date()).format('YYYY-MM-DD');
    if (data === null) {
      this.disabled = false;
      this.personalProfileForm = new FormGroup({
        username: new FormControl('', [
          Validators.required,
          Validators.maxLength(50),
        ]),
        email: new FormControl('', [Validators.required, Validators.email]),
        first_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z ]*')]),
        middle_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z ]*')]),
        last_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z ]*')]),
        phone: new FormControl('', [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl('', [
          Validators.required,
          Validators.maxLength(10),
        ]),
        date_of_birth: new FormControl(today, [
          Validators.required,
          Validators.maxLength(20),
        ]),
      });
    } else {
      if ((data.first_name || data.middle_name || data.last_name) && data.gender && data.date_of_birth) {
        this.disabled = true;
        this.personalProfileForm.get('gender').disable();
      } else {
        this.disabled = false;
        this.personalProfileForm.get('gender').enable();
      }

      this.personalProfileForm = new FormGroup({
        username: new FormControl(data.username, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        email: new FormControl(data.email, [
          Validators.required,
          Validators.email,
        ]),
        first_name: new FormControl(data.first_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z ]*')
        ]),
        middle_name: new FormControl(data.middle_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z ]*')
        ]),
        last_name: new FormControl(data.last_name, [Validators.maxLength(25), Validators.pattern('[a-zA-Z ]*')]),
        phone: new FormControl(data.phone, [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl(data.gender, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        date_of_birth: new FormControl(
          (data.date_of_birth?moment(data.date_of_birth).format('DD-MM-YYYY'):today),
          [Validators.required, Validators.maxLength(25)]
        ),
      });

    }
  }
  showTermsAndCondition() {
    this.contentHtml = localStorage.getItem('content_html');
    this.contentText = localStorage.getItem('content_text');

  }

  doctorSignatureUpload(event) {
    const file = event.target.files;
    if (file.length > 0) {
      this.profileFileSizeLarge = false;
      const selectedProfilePicture = file[0];
      console.log(selectedProfilePicture);
      if (
        selectedProfilePicture.size < 2000000 &&
        (selectedProfilePicture.type === 'image/jpg' ||
          selectedProfilePicture.type === 'image/png' ||
          selectedProfilePicture.type === 'image/jpeg')
      ) {
        this.disabledUploadPhotoBtn = true;
        this.signUpload = false;
        this.userService
          .uploadDoctorSignature(selectedProfilePicture)
          .subscribe(
            (data) => {

              this.doctorSignatureUrl = data['file'];
              this.signUpload = true;
              this.disabledUploadPhotoBtn = false;
              this.notificationService.success(
                'Signature Updated',
                'Med.Bot'
              );
            },
            (error) => {
              this.signUpload = true;
              this.disabledUploadPhotoBtn = false;
              console.log(error);
              this.notificationService.error(
                'Error In Updating profile picture',
                'Med.Bot'
              );
            }
          );
      } else {
        this.profileFileSizeLarge = true;
        this.notificationService.error(
          '  Signature picture size large',
          'Med.Bot'
        );
      }
    } else {
      this.signUpload = true;
      this.disabledUploadPhotoBtn = false;
      this.notificationService.error(
        'Please select valid picture',
        'Med.Bot'
      );
    }
  }
  getSystemOfMedicine(data) {

    this.system_of_medicine = data;
  }
}
