.file-upload-icon {
    font-size: 27px;
    color: #20C0F4;
    margin-bottom: -12px;
    /* marr */
}

.fa-edit {
    color: #20c0f3;
    cursor: pointer;
}

.basic-data-btn {
    float: right;
}

.valid-upto-reg {
    width: 75%;
}

.col-lg-2.reg-file-up {
  padding-right: 5px;
  padding-left: 13px;
  margin-top: 5px
}

.btn-primary[disabled] {
    background-color: darkgray !important;
    border-color: darkgray !important;
}

.btn-primary {
    font-size: 15px;
    padding: 2px 10px 6px 13px;
    margin: 5px;
}



.filename {
    text-align: center;
    font-size: 1rem;
}
@media only screen and (max-width: 600px) {
  .example {background: red;}
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .example {background: green;}
  .file-btn-alinment {
    margin-top: 2rem!important;
    width: 100%;
}
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .example {background: blue;}
  .file-btn-alinment {
    margin-top: 2rem!important;
    width: 100%;
}
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .example {background: orange;}
  .file-btn-alinment {
    margin-top: 2rem!important;
    width: 100%;
}
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .example {background: pink;}
  .file-btn-alinment {
    margin-top: 2rem!important;
    width: 100%;
}
}
