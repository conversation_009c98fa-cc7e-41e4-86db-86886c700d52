<div class="card">
    <div class="card-body">
        <h4 class="card-title">Registrations <span *ngIf="system_of_medicine==null && isPublicDoctor"
                style="font-size: 12px;" class="text-danger">*Please fill the Basic Profile Data before you proceed to
                Registration.</span> </h4>
        <div class="form-group " *ngIf="formError">
            <div class="card">
                <ng-container *ngFor="let err of errorValue">
                    <p class="text-danger">&nbsp;{{err.value}}</p>
                </ng-container>
            </div>
        </div>
        <form [formGroup]="registerForm">
            <div formArrayName="registrationArray">
                <ng-container
                    *ngFor="let item of this.registerForm.controls.registrationArray.value; let i= index; trackBy:trackFn"
                    [formGroupName]="i">
                    <div class="registrations-info">
                        <div class="row form-row reg-cont">
                            <div class="col-xl-4 col-md-6  col-lg-4">
                                <div class="form-group">
                                    <label>Council<span class="text-danger">*</span></label>
                                    <ng-select id="council" class="custom" formControlName="council" bindValue="code"
                                        [items]="councilList" [readonly]="saved_registrstion" [searchable]="true"
                                        bindLabel="code" [clearable]="false"
                                        placeholder="{{'Select Council' | translate}}" multiple required>
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-xl-2 col-md-6 col-lg-2">
                                <div class="form-group">
                                    <label>Number<span class="text-danger">*</span></label>
                                    <input type="text" formControlName="number" [readonly]="saved_registrstion"
                                        pattern="[a-zA-Z0-9 ]*" placeholder="{{'Number'| translate}}"
                                        class="form-control" maxlength="50">
                                    <div *ngIf="number(i)===true" class="alert alert-danger">{{alphanumericError}}</div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-4 col-lg-3">
                                <div class="form-group ">
                                    <label>Registered On<span class="text-danger">*</span></label>
                                    <input type="text" [maxDate]="maxDate" onkeydown="return false" class="form-control"
                                        formControlName="valid_upto" *ngIf="!saved_registrstion"
                                        placeholder="{{ 'Registered On'| translate }}" bsDatepicker [bsConfig]="{
                                    showWeekNumbers: false,
                                    isAnimated: true,
                                    dateInputFormat: 'DD-MM-YYYY'
                                  }" />
                                    <input type="text" class="form-control" readonly formControlName="valid_upto"
                                        *ngIf="saved_registrstion">
                                </div>
                            </div>
                            <div class="col-xl-1 col-md-2 col-lg-2 reg-file-up">
                                <div *ngIf="!!registrationFileUpload">
                                    <div class="file-upload-btn file-btn-alinment">
                                        <span><i class="fa fa-upload"></i> {{ registrationFileUpload ? ('Upload
                                            File'|translate): 'View File|translate'}}</span>
                                        <input type="file" class="upload" [disabled]="saved_registrstion"
                                            formControlName="regiterFileName" (change)="chooseRegistrationFile($event)"
                                            accept=".jpg,.pdf,.png">
                                    </div>
                                    <small class="form-text text-muted" *ngIf="selectedFileName === ' '"
                                        translate>Allowed JPG or Pdf. Max size of 2MB</small>
                                    <small class="form-text text-muted filename"
                                        *ngIf="selectedFileName !== ' '">{{selectedFileName}}</small>
                                </div>

                                <div class="file-upload-btn file-btn-alinment" *ngIf="!registrationFileUpload">
                                    <span translate><i class="fa fa-view"></i> View File</span>
                                    <input type="t" class="upload" (click)="viewPdf()">
                                </div>
                            </div>
                            <div class="col-xl-1 col-md-4 col-lg-1 mt-1" style="float: left;">
                                <label></label>
                                <button id="save-prf-reg" class="btn btn-primary mt-4"
                                    style="margin-top:30px !important" *ngIf="!saved_registrstion"
                                    [disabled]="!registerForm.valid" (click)="saveRegisterForm(i)">{{!updating ?
                                    ('Save'|translate):'Uploading'| translate}}</button>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
        </form>
    </div>
</div>