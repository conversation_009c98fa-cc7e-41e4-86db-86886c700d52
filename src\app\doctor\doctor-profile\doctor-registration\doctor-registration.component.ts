import { ToastrService } from 'ngx-toastr';
import { delay } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { AuthService } from '../../../auth/auth.service';
import * as Settings from '../../../config/settings';
import { Component, OnInit, EventEmitter, Output, Input } from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { DoctorService } from '../../doctor.service'
@Component({
  selector: 'app-doctor-registration',
  templateUrl: './doctor-registration.component.html',
  styleUrls: ['./doctor-registration.component.css'],
})
export class DoctorRegistrationComponent implements OnInit {
  @Output() profileCompletion: EventEmitter<string> = new EventEmitter<string>();
  public registrationData = {
    council: null,
    number: null,
    valid_upto: null,
    file: null,
  };
  public registrationFileUpload = true;
  registrationFile: File;
  doctorRegisteredData = {};
  fileUrl: any;
  userID: any;
  content_text: string[];
  content_html: any;
  termsAndconditionID: string;
  saved_registrstion = false;
  acceptedTerms = false;
  registerFileSizeLarge = false;
  profileFileSizeLarge = false;
  public disabledUploadPhotoBtn = false;
  public registrationArray: FormArray;
  public registerForm: FormGroup;
  public user_data = {};
  public register_form_data = {};
  public selectedFileName: string = ' ';
  public savedFileName: string;
  public updating: boolean = false;
  public maxDate: Date;
  public formError: boolean;
  public errorValue = [];
  public councilList = [];
  public isPublicDoctor: boolean;
  @Input() system_of_medicine: any;
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  constructor(
    private userService: AuthService,
    private translate: TranslateService,
    private _FormBuilder: FormBuilder,
    private httpClient: HttpClient,
    private notificationService: ToastrService,
    private doctorService: DoctorService
  ) { }

  ngOnInit(): void {
    this.maxDate = new Date();
    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this.doctorService.getDoctorProfile().subscribe(
      data => {
        this.system_of_medicine = data['system_of_medicine'];
        if (this.system_of_medicine) {
          this.getCouncils();
        }

      }, error => {
        console.log(error);
      }
    )

    this.getRegistedData();
    this.checkDoctorIsPublic();
    this.addFormControl(null);
  }

  checkDoctorIsPublic() {
    const hospital_id = localStorage.getItem('hospital_id');
    if (hospital_id != null) {
      this.isPublicDoctor = false;
    }
    else {
      this.isPublicDoctor = true;
    }
    return this.isPublicDoctor;
  }

  ngOnChanges() {
    if (this.system_of_medicine && (this.system_of_medicine.length > 0 || this.system_of_medicine != '')) {
      this.getCouncils();
    }
    console.log(this.system_of_medicine);
  }

  addFormControl(data) {
    if (data === null) {
      this.registerForm = this._FormBuilder.group({
        registrationArray: this._FormBuilder.array([]),
      });
      this.addResgistration();
    } else {
      const validUpto = moment(data['valid_upto']).format('DD-MM-YYYY');
      this.registerForm = this._FormBuilder.group({
        registrationArray: this._FormBuilder.array(['data']),
      });

      this.registrationArray = this.registerForm.get(
        'registrationArray'
      ) as FormArray;
      this.registrationArray.removeAt(0);
      this.registrationArray.push(
        this._FormBuilder.group({
          council: new FormControl(
            { value: data['council'], disabled: true },
            [Validators.required, Validators.pattern('[a-zA-Z ]*')]
          ),
          number: new FormControl(
            { value: data['number'], disabled: true },
            Validators.required
          ),
          valid_upto: new FormControl(
            { value: validUpto, disabled: true },
            Validators.required
          ),
          regiterFileName: '',
        })
      );
    }
  }

  addResgistration() {
    this.registrationArray = this.registerForm.get(
      'registrationArray'
    ) as FormArray;
    this.registrationArray.push(
      this._FormBuilder.group({
        council: new FormControl('', [Validators.required, Validators.pattern('.*\\S.*[a-zA-Z0-9]*')]),
        number: new FormControl('', [Validators.required, Validators.pattern('.*\\S.*[a-zA-Z0-9]*')]),
        valid_upto: new FormControl('', Validators.required),
        regiterFileName: new FormControl('', Validators.required),
      })
    );
  }

  trackFn(index) {
    return index;
  }

  chooseRegistrationFile(event: any) {
    const file = event.target.files;
    this.registrationFile = file[0];
    const fileNameLength = this.registrationFile.name
    if (fileNameLength.length < 51) {
      if (
        this.registrationFile.size < 2000000 &&
        (this.registrationFile.type === 'image/jpeg' ||
          this.registrationFile.type === 'image/jpg' || this.registrationFile.type === 'image/png' ||
          this.registrationFile.type === 'application/pdf')
      ) {
        this.selectedFileName = this.registrationFile.name;
        this.registerFileSizeLarge = false;
      } else {
        const control = this.registrationArray.at(0);
        control.get('regiterFileName').setValue(null);
        this.registerFileSizeLarge = true;
        this.notificationService.warning('File Size Large && Check File Format');
      }
    } else {
      const control = this.registrationArray.at(0);
      control.get('regiterFileName').setValue(null);
      this.notificationService.warning("File name: Ensure this field has no more than 50 characters.");
    }
  }

  saveRegisterForm(i): void {
    this.updating = true;
    const registervalus = this.registerForm.controls['registrationArray'].value;
    const sendRegisterValue = registervalus[i];
    this.registrationData.council = sendRegisterValue.council;
    this.registrationData.number = sendRegisterValue.number;
    this.registrationData.valid_upto = moment(sendRegisterValue.valid_upto).format('YYYY-MM-DD');
    this.doctorService.updateRegistrationForm(
      this.registrationData,
      this.registrationFile
    ).subscribe(
      (data) => {
        this.updating = false;
        this.notificationService.success(
          'Registration Form Updated',
          'Med.Bot'
        );
        this.addFormControl(data);
        this.saved_registrstion = true;
        this.register_form_data = data;
        const registerDocument = this.register_form_data['r_documents'];
        this.fileUrl = registerDocument[0].file;
        this.registrationFileUpload = false;
        this.profileCompletion.emit();
      },
      (error) => {
        console.log(error);
        this.updating = false;
        const status = error['status'];
        if (status === 400) {
          this.formError = true;
          const file = error['error']['error_details']['validation_errors']['file'];
          const fileName = error['error']['error_details']['validation_errors']['file_name']
          const fileType = error['error']['error_details']['validation_errors']['file_type']
          const year = error['error']['error_details']['validation_errors']['year'];
          const numberError = error['error']['error_details']['validation_errors']['number'];
          if (!!numberError) {
            const rgisterNumberError = 'Number :' + numberError[0];
            this.errorValue.push({ value: rgisterNumberError })
            this.notificationService.error(
              `${rgisterNumberError}`,
              'Med.Bot'
            );
          } else if (file) {
            const fileErr = 'File :' + file[0];
            this.errorValue.push({ value: fileErr })
            this.notificationService.error(
              `${fileErr}`,
              'Med.Bot'
            );

          } else if (fileType) {
            const fileErr = 'File :' + fileType[0];
            this.errorValue.push({ value: fileErr })
            this.notificationService.error(
              `${fileErr}`,
              'Med.Bot'
            );

          } else if (fileName) {
            const fileErr = 'File Name' + fileName[0];
            this.errorValue.push({ value: fileErr })
            this.notificationService.error(
              `${fileErr}`,
              'Med.Bot'
            );

          } else if (year) {
            const yearError = 'Year :' + year[0];
            this.errorValue.push({ value: yearError })
            this.notificationService.error(
              `${yearError}`,
              'Med.Bot'
            );

          } else {
            this.notificationService.error(
              'Registration Form Updation Error',
              'Med.Bot'
            );
          }
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  viewPdf() {
    window.open(this.fileUrl);
  }

  getRegisterFormDetails() {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/registrations/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getRegistedData(): void {
    this.getRegisterFormDetails().subscribe(
      (data) => {
        data = data['results'];
        const length = Object.values(data).length;
        if (length > 0) {
          this.registrationFileUpload = false;
          this.saved_registrstion = true;
          this.doctorRegisteredData = data[0];
          this.addFormControl(this.doctorRegisteredData);
          const list = Object.values(data);
          const fileData = list[0].r_documents;
          console.log('file', fileData);
          this.fileUrl = fileData[0]?.file;
          this.selectedFileName = fileData[0]?.file_name;
        } else {
          this.addFormControl(null);
          this.saved_registrstion = false;
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }

  getCouncils() {
    this.doctorService.getCouncils(this.system_of_medicine).subscribe(
      (data) => {
        this.councilList = Object.values(data);
      }, error => {
      });
  }

  number(index) {
    const control = this.registrationArray.at(index);
    const value = control.get('number').invalid;
    const toutched = control.get('number').touched;
    const dirty = control.get('number').dirty;
    if (toutched && dirty) {
      return value
    } else {
      return false
    }

  }
}
