import { ToastrService } from 'ngx-toastr';
import { DoctorService } from './../../doctor.service';
import {
  FormGroup,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  ViewChild,
} from '@angular/core';
import * as moment from 'moment';
import {
  NgbCalendar,
  NgbDateStruct,
  NgbInputDatepickerConfig,
} from '@ng-bootstrap/ng-bootstrap';
import { setTheme } from 'ngx-bootstrap/utils';
import { copyFileSync } from 'fs';
declare var $: any;

@Component({
  selector: 'app-fees',
  templateUrl: './fees.component.html',
  styleUrls: ['./fees.component.css'],
})
export class FeesComponent implements OnInit {
  model: NgbDateStruct;
  @Output()
  profileCompletion: EventEmitter<string> = new EventEmitter<string>();
  public feeForm = {
    uuid: '',
    amount: null,
    effective_from: {},
    effective_upto: {},
    doctor: localStorage.getItem('Doctor'),
  };
  public currentFeeData: any;
  public formDisable = false;
  public newFee = true;
  public editFee = true;
  public feeList = [];
  public oldFeeList = [];
  public feeValidDate = '';
  public feeDataAvailable = false;
  public tomDate: any;
  public today: any;
  public formError = false;
  public errorValue = [];
  constructor(
    config: NgbInputDatepickerConfig,
    calendar: NgbCalendar,
    private formBuilder: FormBuilder,
    private doctorService: DoctorService,
    private notificationService: ToastrService
  ) {
    setTheme('bs4');
  }

  ngOnInit(): void {
    const todayDate = new Date();
    this.today = new Date();
    const tomorrowDate = new Date(todayDate);
    tomorrowDate.setDate(tomorrowDate.getDate() + 1);
    this.tomDate = tomorrowDate;

    this.doctorService.getDoctorFees().subscribe(
      (data) => {
        const results = Object.values(data);
        this.showValidFee(results);
        console.log(this.feeList);
        if (this.feeList.length == 0) {
          this.feeDataAvailable = false;
          this.feeForm.amount = null;
          this.feeForm.effective_from = todayDate; //moment(todayDate).format('DD/MM/YYYY');
          this.feeForm.effective_upto = todayDate;
          this.newFee = true;
          this.editFee = false;
          this.feeValidDate = moment(todayDate).format('YYYY-MM-DD');
        } else {
          this.feeDataAvailable = true;
          this.newFee = false;
          this.editFee = false;
          this.formDisable = true;
          this.feeForm.uuid = data[0]['uuid'];
          this.feeForm.amount = data[0]['amount'];
          this.feeForm.effective_from = this.tomDate; //data[0]['effective_from'];
          this.feeForm.effective_upto = data[0]['effective_upto'];
          this.feeValidDate = data[0]['effective_from'];
        }
        if (this.feeList.length > 1) {
          this.oldFeeList = this.feeList.filter(
            (obj) => obj['effective_upto'] != null
          );
          // this.currentFeeData = this.feeList.filter(
          //   (obj) => obj['effective_upto'] == null
          // );
          this.oldFeeList = this.oldFeeList.reverse();
        } else {
          this.oldFeeList = this.feeList;
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }

  onFeeSubmit() {
    this.formError = false;
    this.errorValue = [];
    console.log(this.feeDataAvailable);
    const data = this.feeForm;
    this.feeForm.effective_from = moment(this.feeForm.effective_from).format(
      'YYYY-MM-DD'
    );
    this.feeForm.effective_upto = moment(this.feeForm.effective_upto).format(
      'YYYY-MM-DD'
    );
    this.feeValidDate = moment(this.feeValidDate).format('YYYY-MM-DD');
   
    if (this.feeDataAvailable) {
      // if (this.feeForm.effective_from > this.feeValidDate) {
        if (this.feeForm.effective_from > this.feeValidDate && this.feeForm.effective_upto > this.feeValidDate &&  this.feeForm.effective_from <= this.feeForm.effective_upto) {
        this.doctorService.postDoctorFee(data).subscribe(
          (data) => {
            $('#feeNotificationModal').modal('hide');
            this.feeForm.effective_from = this.tomDate;
            this.feeValidDate = data[0]['effective_from'];
            console.log(this.feeValidDate);
            this.notificationService.success('Fee Saved');
            this.feeList = [];
            this.profileCompletion.emit();
            const results = Object.values(data);
            this.showValidFee(results);
            this.editFee = false;
            this.feeDataAvailable = true;
          },
          (error) => {
            const status = error['status'];
            if (status == 400) {
              const err = error['error']['error']['error'];
              if (err) {
                this.notificationService.error(`${err}`);
              } else {
                this.notificationService.error(
                  `${error['statusText']}`,
                  'Med.Bot'
                );
              }
            } else {
              this.notificationService.error(
                `${error['statusText']}`,
                'Med.Bot'
              );
            }

            this.editFee = true;
            this.formDisable = false;
          }
        );
      } else {
        $('#feeNotificationModal').modal('hide');
        this.formError = true;
        this.errorValue.push(
          { value: 'Date : Please select future date.' },
          { value: 'Date : You already have a valid fee on that date' }
        );
        this.notificationService.error('Please select future date.');
        this.notificationService.error(
          'You already have a valid fee on that date'
        );
      }
    } else {
      this.doctorService.postDoctorFee(data).subscribe(
        (data) => {
          $('#feeNotificationModal').modal('hide');
          this.feeValidDate = data[0]['effective_from'];
          console.log(this.feeValidDate);
          this.profileCompletion.emit();
          this.notificationService.success('Fee Saved');
          this.feeList = [];
          const results = Object.values(data);
          this.showValidFee(results);
          this.editFee = false;
          this.feeDataAvailable = true;
        },
        (error) => {
          const err = error['error']['error']['error'];
          this.notificationService.error(`${err}`);
          this.editFee = true;
          this.formDisable = false;
        }
      );
    }
    this.newFee = false;
  }

  onEditFee() {
    this.editFee = true;
    this.formDisable = false;
    this.feeForm.amount = null;
    // this.feeForm.effective_upto = null;
  }

  cancelFeeSubmit() {
    this.editFee = false;
    this.formDisable = true;
    // this.feeForm.amount = this.currentFeeData?.amount;
    // this.feeForm.effective_from = this.currentFeeData?.effective_from;
    // this.feeForm.effective_upto = this.currentFeeData?.effective_upto;
  }
  showWarningFeeNotification() {
    $('#feeNotificationModal').modal('show');
  }
  showValidFee(results) {
    const today_date_formated = moment(this.today).format('YYYY-MM-DD');
    for (let fee of results) {
      if (fee.effective_upto == null) {
        this.feeList.push({
          amount: `${fee.amount}`,
          currency: `${fee.currency}`,
          doctor: `${fee.doctor}`,
          effective_from: `${fee.effective_from}`,
          effective_upto: `${fee.effective_upto}`,
          hospital: `${fee.hospital}`,
          uuid: `${fee.uuid}`,
        });
      } else {
        const effective_upto = moment(fee.effective_upto).format('YYYY-MM-DD');
        if (effective_upto >= today_date_formated) {
          this.feeList.push({
            amount: `${fee.amount}`,
            currency: `${fee.currency}`,
            doctor: `${fee.doctor}`,
            effective_from: `${fee.effective_from}`,
            effective_upto: `${fee.effective_upto}`,
            hospital: `${fee.hospital}`,
            uuid: `${fee.uuid}`,
          });
        }
      }
    }
  }
}
