import { Component, OnInit, Input} from '@angular/core';

@Component({
  selector: 'app-share-button',
  templateUrl: './share-button.component.html',
  styleUrls: ['./share-button.component.css']
})
export class ShareButtonComponent implements OnInit {

  
  @Input() type: 'facebook' | 'twitter' | 'gmail' | 'linkedin' | 'telegram' | 'whatsapp';
  @Input() shareUrl: string;
  navUrl: string;
  content: string;
  subject: string;
  constructor() { }

  ngOnInit(): void {
        this.createNavigationUrl();
  }

  private createNavigationUrl() {
    let searchParams = new URLSearchParams();
    this.content = 'Hi, I am available for online consultation at med.bot Please use the link to book your appointments ';
    this.subject = 'My link for online consultation ';
    // TODO: zrobić map z tego manualnego dziugania

    switch(this.type) {
      case 'facebook':
        searchParams.set('u', this.shareUrl);
        this.navUrl = 'https://www.facebook.com/sharer/sharer.php?' + searchParams + this.content;
        break;
      case 'twitter':
        searchParams.set('url', this.shareUrl);
        this.navUrl =  'https://twitter.com/share?' + searchParams + '&text=' + this.content;
        break;
      case 'gmail':
        searchParams.set('', this.shareUrl);
        this.navUrl =  'https://mail.google.com/mail/?view=cm&su=' + this.subject + '&body=' + this.content + searchParams;
        break;
      case 'linkedin':
        searchParams.set('url', this.shareUrl);
        this.navUrl =  'https://www.linkedin.com/shareArticle?'+ searchParams + this.content;
        break;
//      case 'skype':
//        searchParams.set('url', this.shareUrl);
//        this.navUrl =  'https://web.skype.com/share?'+ searchParams + '&text=Doctor personal profile';
//        break;
//      case 'pinterest':
//        searchParams.set('url', this.shareUrl);
//        this.navUrl =  'http://pinterest.com/pin/create/link/?'+ searchParams + '&text=Doctor personal profile';
//        break;
      case 'telegram':
        searchParams.set('url', this.shareUrl);
        this.navUrl =  'https://telegram.me/share/url?'+ searchParams + '&text=' + this.content;
        break;
      case 'whatsapp':
        this.navUrl =  'https://web.whatsapp.com/send?text='+ this.content + this.shareUrl;
//        console.log(this.navUrl);
        break;
    }
  }

  public share() {
    return window.open(this.navUrl, "_blank");
  }


}
