
  <h5 class="mb-4 ms ml-2"><i class="fas fa-chevron-circle-left" (click)="back()"></i>&nbsp;Back</h5>
<div class="row pub-prof" *ngIf="!isLoading" >
    <div class="col-lg-3">
        <div class="card left-pane" >
            <div class="doc-profile-data" >
              <div class="spinner-border" role="status" *ngIf="!showProfilePic" style="margin: 10px 0px 0px 140px;">
                <span class="sr-only">Loading...</span>
              </div>
                <img [src]="profilePicture" class="img-fluid" alt="User Image" *ngIf="showProfilePic">
                <p class="doc-name first-name">Dr. {{personalInfo['username']}}</p>
                <p class="doc-speciality">{{degreeString}}</p>
                <p class="doc-location" *ngFor="let location of practiceLocations"><i class="fas fa-map-marker-alt"></i>&nbsp;&nbsp;{{location.addresses?location.addresses.line_1+' , '+location.addresses.line_2+' , '+location.addresses.city_town_village+' , '+location.addresses.postal_code :"Online Consultation"}}</p>
             </div>
        </div>

    </div>
    <div class="col-lg-9" >
        <div class="card right-pane">
            <div class="top-para">
                <div class="row">
                    <div class="col-lg-6 details">
                        <h3>Dr. {{personalInfo['username']}}</h3>
                        <p class="doc-speciality right-pane">{{degreeList}}</p>
                        <div class="btns">
                            <button *ngFor="let spec of specialization" class="btn btn-info sm">{{spec}}</button>
                        </div>
                    </div>
                    <div *ngIf="today_available == false" class="col-lg-2 offset-3">
                        <button class="btn btn-info m"><i class="fa fa-check-circle" aria-hidden="true"></i>
                          Available Today</button>
                    </div>
                    <div *ngIf="slots_availability == null" class="col-lg-2 offset-3">
                      <button class="btn btn-danger m"><i class="fa fa-times-circle" aria-hidden="true"></i>
                        Unavailable Today</button>
                  </div>
                </div>
                <div class="row">
                    <div *ngIf="userType === 'Doctor'" class="col-lg-12">
                        <ckeditor [config]="ckConfig" [(ngModel)]="ckText" (blur)="saveProfSummary(ckText)">
                            <ckbutton [name]="'saveButton'" [command]="'saveCmd'" [label]="'Save Document'">
                            </ckbutton>
                        </ckeditor>
                    </div>
                    <div *ngIf="userType === 'Patient' && ckText!=null" class="col-lg-12">
                        <div class="card card-body" [innerHTML]="ckText">
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <div class="cont-para">
                    <p class="sub-head">Consulting Hours</p>
                    <div *ngIf="slots_availability !==null">
                    <div *ngFor="let add of practiceLocations;let i=index" class="row ch-details">
                        <div class="col-lg-4">
                          <ng-container *ngIf="add['addresses']">
                            <p id="clinic-name" class="clinic-head" *ngIf="practiceLocations.length==1">{{add['name']}}</p>
                            <p id="clinic-name" class="clinic-head" *ngIf="practiceLocations.length>=2">{{i+1}}.{{add['name']}}</p>
                            <p class="cont-text">{{add['addresses']?add['addresses']['line_1'] :'No address'}}</p>
                            <p class="cont-text">{{add['addresses']? add['addresses']['line_2']: ''}}</p>
                            <p class="cont-text">{{add['addresses']['district']}}-{{add['addresses']['postal_code']}}</p>
                            <p class="cont-text"></p>
                          </ng-container>
                          <ng-container *ngIf="!add['addresses']">
                            <p class="clinic-head" *ngIf="practiceLocations.length==1">Address </p>
                            <p class="clinic-head" *ngIf="practiceLocations.length>=2"> {{i+1}}.Address </p>
                            <p class="cont-text">Online Consultation</p>
                          </ng-container>
                        </div>
                        <div class="col-lg-5">
                            <p id="timing-head" class="clinic-head">Timings [24 hours]</p>
                            <ng-container  *ngIf="add['consultinghoursgroups'].length>0">
                              <div *ngFor="let days of add['consultinghoursgroups']">
                                <div *ngFor="let day of days['days_of_the_week']">
                                  <ng-container *ngIf="days['time_from'] && days['time_to']">
                                    <div class="row"><p  class="day cont-text three_chars">&nbsp;&nbsp;{{day}} </p><p>&nbsp;&nbsp;:&nbsp;&nbsp;{{days['time_from']}} - {{days['time_to']}}</p></div>
                                  </ng-container>
                                </div>

                             </div>
                            </ng-container>

                            <ng-container  *ngIf="add['consultinghoursgroups'].length===0">
                              <p class="timing mt-2">No Slots </p>
                              </ng-container>

                        </div>
                        <div class=" col-lg-3 clinic-booking" *ngIf="amount !=='None'" >
                            <p id="fee-head" class="clinic-head">Fee</p>
                            <p class="cont-text fee">&#x20B9;&nbsp; {{amount}}/Visit</p>
                            <a class="view-pro-btn" id="book-appointment"  (click)="bookAppointment()" style="cursor:pointer" >Book Appointment</a>
                            <a class="view-pro-btn" id="consult-appointment" *ngIf="instant_appointment_slot_available"  (click)="consultNow()" style="cursor:pointer" >Consult Now</a>

                        </div>
                    </div>
                  </div>
                  <div *ngIf="slots_availability ==null">
                    <p>No slots</p>
                  </div>
                  <div>
                    <!-- <div class="row ch-details">
                        <div class="col-lg-4">
                            <p id="clinic-name" class="clinic-head">2.Aspire Clinic</p>
                            <p class="cont-text">No 4, RK Nagar</p>
                            <p class="cont-text">Chennai - 600001</p>
                            <p class="cont-text">Phone - +91 9876543210</p>
                        </div>
                        <div class="col-lg-5">
                            <p id="timing-head" class="clinic-head">Timings</p>
                            <p class="timing day cont-text">Mon</p>
                            <p class="timing">:8 AM - 12 PM, 8PM - 10:30 PM</p>
                        </div>
                        <div class=" col-lg-3 ">
                            <p id="fee-head" class="clinic-head">Fee</p>
                            <p class="cont-text fee">INR {{amount}}/Visit</p>
                            <button id="book-appointment-dr" type="button" class="btn btn-info appoint">Book Appoinment Now</button>
                        </div>
                    </div> -->
                </div>
                <hr>
                <div class="cont-para">
                    <p class="sub-head">Info</p>
                    <div class="row ch-details">
                        <div class="col-lg-4">
                            <p id="dr-edu" class="clinic-head">Education</p>
                            <div  *ngIf="degreeList.length==1">
                              <p *ngFor="let deg of degreeList; let i=index" class="cont-text"> {{deg}}</p>
                            </div>
                            <div *ngIf="degreeList.length>=2">
                              <p *ngFor="let deg of degreeList; let i=index" class="cont-text">{{i+1}}. {{deg}}</p>
                            </div>

                        </div>
                        <div class="col-lg-4">
                            <p id="dr-spec" class="clinic-head">Specialization</p>
                            <p *ngFor="let spec of specialization" class="cont-text">{{spec}}</p>
                        </div>
                        <div class="col-lg-4">
                          <p id="dr-reg" class="clinic-head">Registration</p>
                          <p class="cont-text">{{registration?registration['number']:''}}</p>
                      </div>
                    </div>
                    <div class="row ch-details">
                        <div class="col-lg-4">
                          <p id="dr-reg" class="clinic-head">Years of Experience</p>
                          <p class="cont-text">{{yearsOfExperience}}</p>
                      </div>
                      <div class="col-lg-4">
                        <p id="dr-reg" class="clinic-head">Language</p>
                        <p *ngFor="let lang of languages" class="cont-text">{{lang.value}}</p>
                    </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<div *ngIf="isLoading">
  <app-loading-spinner></app-loading-spinner>
</div>
