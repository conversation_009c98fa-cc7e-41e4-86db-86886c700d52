.disabled-pagination{
  color: darkgray !important;
  pointer-events: none !important;
}
@media only screen and (max-width: 600px) {

}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {

}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {

  .pager_position{

  }
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {

  .pager_position{
    margin-top: -72px;
  }
  .tab_pager_position{
    float:  right !important;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {

  .pager_position{
    margin-top: -72px;
  }
  .tab_pager_position{
    float:  right !important;
  }

}
