<div class="tab-content">
  <div class="appointment-tab">
    <!-- Heading Tab -->
    <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
      <li class="nav-item">
        <a class="nav-link nm-size active" href="#earnings-rep" data-toggle="tab">
          <h5>Earning Report</h5>
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link nm-size" a href="#consult-rep" data-toggle="tab">
          <h5>Consultation Report</h5>
        </a>
      </li>
    </ul>
    <!-- /headint Tab -->

    <div class="tab-content">
      <!-- Earning report Tab -->
      <div class="tab-pane show active" id="earnings-rep">
        <div class="card">
          <div class="mx-5 my-2">
            <div class="search">
            </div>
            <div class="row">
              <div class="col-md-12">
                <div class="col-md-12">
                  <!-- <h4 class="mb-4 dashboard-font-size">Earning Report</h4> -->
                  <div class="appointment-tab">

                    <div class="mb-5">
                      <div class="row">
                        <div class="col-md-3">
                          <label>From Date</label>
                          <input type="text" id="from_date" placeholder="From Date" onkeydown="return false"
                            class="form-control input-field-border mb-2" [minDate]="" [maxDate]="" bsDatepicker
                            [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }">
                        </div>
                        <div class="col-md-3">
                          <label>To Date</label>
                          <input type="text" id="to_date" placeholder="To Date" onkeydown="return false"
                            class="form-control input-field-border mb-2" [minDate]="" [maxDate]="" bsDatepicker
                            [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }">
                        </div>
                      </div>
                      <button class="btn btn-primary btn-sm float-right" (click)="ngOnInit()">Submit</button>
                    </div>

                    <div>
                      <!-- Appointment Tab -->
                      <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
                        <li class="nav-item">
                          <a class="nav-link active" href="#admin-user" data-toggle="tab">Current Month</a>
                        </li>
                        <!-- <li class="nav-item">
                        <a class="nav-link" href="#doctor-assistant" data-toggle="tab">Last Week</a>
                      </li> -->
                      </ul>
                      <!-- /Appointment Tab -->
                      <div class="tab-content">

                        <!-- Upcoming Appointment Tab -->
                        <div class="tab-pane show active" id="admin-user">
                          <div class="col-md-12 tab_pager_position">
                            <div class="tab_pager_position">
                              <nav aria-label="Page navigation example" *ngIf="this.eraningReportTotalPage > 1">
                                <ul class="pagination pager_position">
                                  <li class="page-item" (click)="eraningReportFirstPageList()" [ngClass]="{
                                    'disabled-pagination':
                                      eraningReportCurrentPage === 1
                                  }">
                                    <a class="page-link">&lt;&lt;</a>
                                  </li>
                                  <li class="page-item" (click)="eraningReportPreviousPageList()" [ngClass]="{
                                    'disabled-pagination':
                                      eraningReportCurrentPage === 1
                                  }">
                                    <a class="page-link">&lt;</a>
                                  </li>
                                  <li class="page-item">
                                    <a class="page-link">page &nbsp;{{
                                      eraningReportCurrentPage
                                      }}&nbsp;of&nbsp; {{ eraningReportTotalPage }}</a>
                                  </li>
                                  <li class="page-item" (click)="eraningReportNextPageList()" [ngClass]="{
                                    'disabled-pagination':
                                      eraningReportCurrentPage ===
                                      eraningReportTotalPage
                                  }">
                                    <a class="page-link">&gt;</a>
                                  </li>
                                  <li class="page-item" (click)="eraningReportLastPageList()" [ngClass]="{
                                    'disabled-pagination':
                                      eraningReportCurrentPage ===
                                      eraningReportTotalPage
                                  }">
                                    <a class="page-link">&gt;&gt;</a>
                                  </li>
                                </ul>
                              </nav>
                            </div>
                          </div>
                          <div *ngIf="eraningReportLoading">
                            <app-loading-spinner></app-loading-spinner>
                          </div>
                          <div class="card card-table mb-0" *ngIf="!eraningReportLoading">
                            <div class="card-body">
                              <div class="table-responsive">
                                <table class="table table-hover table-center mb-0">
                                  <thead>
                                    <tr>
                                      <th>Sl.No</th>
                                      <th>Patient Name</th>
                                      <th>Consultation Date</th>
                                      <th>Consultation Time</th>
                                      <th>Doctor's Earning</th>
                                      <!-- <th>Platform Share</th> -->
                                      <!-- <th>Total</th> -->
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr *ngFor="let data of currentMonthEarning;let i=index">
                                      <td>{{eraningReportSerialNumber+i+1}}</td>
                                      <td>{{data.customer_name}} </td>
                                      <td>&nbsp;&nbsp;{{data.start_datetime | date:'dd-MM-yyyy'}}</td>
                                      <td>&nbsp;{{data.start_datetime | date:'hh:mm'}}-&nbsp;{{data.end_datetime |
                                        date:'hh:mm
                                        a'}}
                                      </td>

                                      <td>{{data.net_amount }}</td>
                                      <!-- <td>{{data.platform_service_fee}}</td> -->
                                      <!-- <td>{{data.gross_amount}}</td> -->
                                    </tr>
                                    <tr *ngIf="currentMonthEarning.length >0">
                                      <td></td>
                                      <td></td>
                                      <td></td>
                                      <th>Total</th>

                                      <th>{{doctorTotalAmount}}</th>
                                      <!-- <th>{{plateformTotalAmount}}.00</th> -->
                                      <!-- <th>{{totalAmount}}.00</th> -->
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                              <div class="text-center mb-2 p-2 mt-2">
                                <span class="appointmentList-no-data" *ngIf="currentMonthEarning.length ===0">No Earning
                                  Data</span>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-12 float-right mt-2">
                            <div class="float-right">
                              <nav aria-label="Page navigation example" *ngIf="this.eraningReportTotalPage > 1">
                                <ul class="pagination">
                                  <li class="page-item" (click)="eraningReportFirstPageList()" [ngClass]="{
                                    'disabled-pagination':
                                      eraningReportCurrentPage === 1
                                  }">
                                    <a class="page-link">&lt;&lt;</a>
                                  </li>
                                  <li class="page-item" (click)="eraningReportPreviousPageList()" [ngClass]="{
                                    'disabled-pagination':
                                      eraningReportCurrentPage === 1
                                  }">
                                    <a class="page-link">&lt;</a>
                                  </li>
                                  <li class="page-item">
                                    <a class="page-link">page &nbsp;{{
                                      eraningReportCurrentPage
                                      }}&nbsp;of&nbsp; {{ eraningReportTotalPage }}</a>
                                  </li>
                                  <li class="page-item" (click)="eraningReportNextPageList()" [ngClass]="{
                                    'disabled-pagination':
                                      eraningReportCurrentPage ===
                                      eraningReportTotalPage
                                  }">
                                    <a class="page-link">&gt;</a>
                                  </li>
                                  <li class="page-item" (click)="eraningReportLastPageList()" [ngClass]="{
                                    'disabled-pagination':
                                      eraningReportCurrentPage ===
                                      eraningReportTotalPage
                                  }">
                                    <a class="page-link">&gt;&gt;</a>
                                  </li>
                                </ul>
                              </nav>
                            </div>
                          </div>
                        </div>
                        <!-- /Upcoming Appointment Tab -->

                        <!-- Today Appointment Tab -->
                        <div class="tab-pane" id="doctor-assistant">
                          <div class="card card-table mb-0">
                            <div class="card-body">
                              <div class="table-responsive">
                                <table class="table table-hover table-center mb-0">
                                  <thead>
                                    <tr>
                                      <th> Name</th>
                                      <th>Email</th>
                                      <th>Phone</th>
                                      <th>Action</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <!-- <tr *ngFor="let data of assistantList;let i=index">
                                    <td>{{data.username}}    </td>
                                    <td>{{data.email}}</td>
                                    <td>{{data.phone}}</td>
                                    <td><button class="btn btn-primary" disabled>view</button></td>
                                  </tr> -->
                                  </tbody>
                                </table>
                              </div>
                              <div class="text-center mb-2 p-2 mt-2">
                                <span class="appointmentList-no-data" style="color: orangered;">No Assistant Data</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- /Today Appointment Tab -->
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- / Earning report Tab -->

      <!--  Consultation report Tab -->
      <div class="tab-pane show " id="consult-rep">
        <div class="card">
          <div class="mx-5 my-2">
            <app-consultation-report></app-consultation-report>

            <div class="row">
              <div class="col-md-12">
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- /Consultation report Tab -->
    </div>
  </div>
</div>




<!-- <div class="card">
  <div class="card-body">
    <div class="row">
      <div class="col-md-12">
        <h4 class="mb-4 dashboard-font-size">Earning Report</h4>
        <div class="appointment-tab">

          <div class="mb-5">
            <div class="row">
              <div class="col-md-3">
                <label>From Date</label>
                <input type="text" id="from_date" placeholder="From Date" onkeydown="return false"
                  class="form-control input-field-border mb-2" [minDate]="" [maxDate]="" bsDatepicker
                  [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }">
              </div>
              <div class="col-md-3">
                <label>To Date</label>
                <input type="text" id="to_date" placeholder="To Date" onkeydown="return false"
                  class="form-control input-field-border mb-2" [minDate]="" [maxDate]="" bsDatepicker
                  [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }">
              </div>
            </div>
            <button class="btn btn-primary btn-sm float-right" (click)="ngOnInit()">Submit</button>
          </div> -->

<!-- <div> -->
<!-- Appointment Tab -->
<!-- <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
              <li class="nav-item">
                <a class="nav-link active" href="#admin-user" data-toggle="tab">Current Month</a>
              </li> -->
<!-- <li class="nav-item">
              <a class="nav-link" href="#doctor-assistant" data-toggle="tab">Last Week</a>
            </li> -->
<!-- </ul> -->
<!-- /Appointment Tab -->
<!-- <div class="tab-content"> -->

<!-- Upcoming Appointment Tab -->
<!-- <div class="tab-pane show active" id="admin-user">
                <div class="col-md-12 tab_pager_position">
                  <div class="tab_pager_position">
                    <nav aria-label="Page navigation example" *ngIf="this.eraningReportTotalPage > 1">
                      <ul class="pagination pager_position">
                        <li class="page-item" (click)="eraningReportFirstPageList()" [ngClass]="{
                          'disabled-pagination':
                            eraningReportCurrentPage === 1
                        }">
                          <a class="page-link">&lt;&lt;</a>
                        </li>
                        <li class="page-item" (click)="eraningReportPreviousPageList()" [ngClass]="{
                          'disabled-pagination':
                            eraningReportCurrentPage === 1
                        }">
                          <a class="page-link">&lt;</a>
                        </li>
                        <li class="page-item">
                          <a class="page-link">page &nbsp;{{
                            eraningReportCurrentPage
                            }}&nbsp;of&nbsp; {{ eraningReportTotalPage }}</a>
                        </li>
                        <li class="page-item" (click)="eraningReportNextPageList()" [ngClass]="{
                          'disabled-pagination':
                            eraningReportCurrentPage ===
                            eraningReportTotalPage
                        }">
                          <a class="page-link">&gt;</a>
                        </li>
                        <li class="page-item" (click)="eraningReportLastPageList()" [ngClass]="{
                          'disabled-pagination':
                            eraningReportCurrentPage ===
                            eraningReportTotalPage
                        }">
                          <a class="page-link">&gt;&gt;</a>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
                <div *ngIf="eraningReportLoading">
                  <app-loading-spinner></app-loading-spinner>
                </div>
                <div class="card card-table mb-0" *ngIf="!eraningReportLoading">
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-hover table-center mb-0">
                        <thead>
                          <tr>
                            <th>Sl.No</th>
                            <th>Patient Name</th>
                            <th>Consultation Date</th>
                            <th>Consultation Time</th>
                            <th>Doctor's Earning</th> -->
<!-- <th>Platform Share</th> -->
<!-- <th>Total</th> -->
<!-- </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let data of currentMonthEarning;let i=index">
                            <td>{{eraningReportSerialNumber+i+1}}</td>
                            <td>{{data.customer_name}} </td>
                            <td>&nbsp;&nbsp;{{data.start_datetime | date:'dd-MM-yyyy'}}</td>
                            <td>&nbsp;{{data.start_datetime | date:'hh:mm'}}-&nbsp;{{data.end_datetime | date:'hh:mm
                              a'}}
                            </td>

                            <td>{{data.net_amount }}</td> -->
<!-- <td>{{data.platform_service_fee}}</td> -->
<!-- <td>{{data.gross_amount}}</td> -->
<!-- </tr>
                          <tr *ngIf="currentMonthEarning.length >0">
                            <td></td>
                            <td></td>
                            <td></td>
                            <th>Total</th>

                            <th>{{doctorTotalAmount}}</th> -->
<!-- <th>{{plateformTotalAmount}}.00</th> -->
<!-- <th>{{totalAmount}}.00</th> -->
<!-- </tr> -->
<!-- </tbody>
                      </table>
                    </div>
                    <div class="text-center mb-2 p-2 mt-2">
                      <span class="appointmentList-no-data" *ngIf="currentMonthEarning.length ===0">No Earning
                        Data</span>
                    </div>
                  </div>
                </div>
                <div class="col-md-12 float-right mt-2">
                  <div class="float-right">
                    <nav aria-label="Page navigation example" *ngIf="this.eraningReportTotalPage > 1">
                      <ul class="pagination">
                        <li class="page-item" (click)="eraningReportFirstPageList()" [ngClass]="{
                          'disabled-pagination':
                            eraningReportCurrentPage === 1
                        }">
                          <a class="page-link">&lt;&lt;</a>
                        </li>
                        <li class="page-item" (click)="eraningReportPreviousPageList()" [ngClass]="{
                          'disabled-pagination':
                            eraningReportCurrentPage === 1
                        }">
                          <a class="page-link">&lt;</a>
                        </li>
                        <li class="page-item">
                          <a class="page-link">page &nbsp;{{
                            eraningReportCurrentPage
                            }}&nbsp;of&nbsp; {{ eraningReportTotalPage }}</a>
                        </li>
                        <li class="page-item" (click)="eraningReportNextPageList()" [ngClass]="{
                          'disabled-pagination':
                            eraningReportCurrentPage ===
                            eraningReportTotalPage
                        }">
                          <a class="page-link">&gt;</a>
                        </li>
                        <li class="page-item" (click)="eraningReportLastPageList()" [ngClass]="{
                          'disabled-pagination':
                            eraningReportCurrentPage ===
                            eraningReportTotalPage
                        }">
                          <a class="page-link">&gt;&gt;</a>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
              </div> -->
<!-- /Upcoming Appointment Tab -->

<!-- Today Appointment Tab -->
<!-- <div class="tab-pane" id="doctor-assistant">
                <div class="card card-table mb-0">
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-hover table-center mb-0">
                        <thead>
                          <tr>
                            <th> Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Action</th>
                          </tr>
                        </thead>
                        <tbody> -->
<!-- <tr *ngFor="let data of assistantList;let i=index">
                          <td>{{data.username}}    </td>
                          <td>{{data.email}}</td>
                          <td>{{data.phone}}</td>
                          <td><button class="btn btn-primary" disabled>view</button></td>
                        </tr> -->
<!-- </tbody>
                      </table>
                    </div>
                    <div class="text-center mb-2 p-2 mt-2">
                      <span class="appointmentList-no-data" style="color: orangered;">No Assistant Data</span>
                    </div>
                  </div>
                </div>
              </div> -->
<!-- /Today Appointment Tab -->
<!-- </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
 -->