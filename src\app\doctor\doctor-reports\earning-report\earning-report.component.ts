import { Component, OnInit } from '@angular/core';
import { DoctorService } from '../../doctor.service';
import { AuthService } from '../../../auth/auth.service';
import { ToastrService } from 'ngx-toastr';
import { FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-earning-report',
  templateUrl: './earning-report.component.html',
  styleUrls: ['./earning-report.component.css']
})
export class EarningReportComponent implements OnInit {
  public doctorId: string;
  public currentMonthEarning: any = [];
  eraningReportCurrentPage: any;
  public eraningReportSerialNumber: number = 0;
  eraningReportTotalPage: any;
  eraningReportLoading: boolean;
  public totalAmount: number;
  public doctorTotalAmount: number;
  public plateformTotalAmount: number;
  // from_date="2021-07-01";
  // to_date="2021-07-30";
  public filter: string;
  public fromDate: string;
  public toDate: string;
  constructor(
    private doctorService: DoctorService,
    private userService: AuthService,
    private notificationService: ToastrService
  ) { }

  ngOnInit(): void {

    this.eraningReportLoading = true;
    this.eraningReportSerialNumber = 0;
    //   let form = this.FormGroup.controls;
    this.fromDate = (document.getElementById("from_date") as HTMLInputElement).value
    this.toDate = (document.getElementById("to_date") as HTMLInputElement).value
    console.log(this.fromDate);
    console.log(this.toDate);
    console.log('from date empty');
    // this.filter = '?page=1';
    this.getUser();
    console.log('init successful');
  }
  getUser() {
    this.userService.getUserDetail().subscribe(
      (data) => {
        this.doctorId = data['uuid'];
        this.getEarningReportList(1);
      },
      (error) => {
        console.log(error);
      }
    );
  }
  eraningReportNextPageList() {
    this.eraningReportCurrentPage = this.eraningReportCurrentPage + 1;
    if (this.eraningReportTotalPage >= this.eraningReportCurrentPage) {
      this.getEarningReportList(this.eraningReportCurrentPage);
      this.eraningReportSerialNumber = (this.eraningReportCurrentPage - 1) * 10;
    } else {
      this.eraningReportCurrentPage = this.eraningReportCurrentPage - 1;
    }
  }

  eraningReportLastPageList() {
    this.eraningReportSerialNumber = (this.eraningReportTotalPage - 1) * 10;
    this.getEarningReportList(this.eraningReportTotalPage);

  }
  eraningReportFirstPageList() {
    this.eraningReportCurrentPage = 1;
    this.eraningReportSerialNumber = 0;
    this.getEarningReportList(this.eraningReportCurrentPage);
  }
  eraningReportPreviousPageList() {
    this.eraningReportCurrentPage = this.eraningReportCurrentPage - 1;
    if (this.eraningReportTotalPage >= this.eraningReportCurrentPage && this.eraningReportCurrentPage > 0) {
      this.getEarningReportList(this.eraningReportCurrentPage);
      this.eraningReportSerialNumber = (this.eraningReportCurrentPage - 1) * 10;
    } else {
      this.eraningReportCurrentPage = this.eraningReportCurrentPage + 1;
    }
  }
  getEarningReportList(page) {
    this.totalAmount = 0;
    this.doctorTotalAmount = 0;
    this.plateformTotalAmount = 0;
    this.eraningReportLoading = true;
    this.eraningReportTotalPage = 0;
    //    const query='?from_date='+this.from_date+'&to_date='+this.to_date+'&page='+page;
    //    const query='?page='+page;
    if (this.fromDate == '' && this.toDate == '') {
      const query = '?page=' + page;
      this.doctorService.getEarningData(query, this.doctorId).subscribe((data) => {
        this.currentMonthEarning = data['results'];
        this.eraningReportTotalPage = data['total_pages'];
        this.eraningReportCurrentPage = data['page_number'];
        this.eraningReportLoading = false;
        for (let data of this.currentMonthEarning) {
          this.totalAmount = this.totalAmount + parseInt(data.gross_amount);
          this.doctorTotalAmount = this.doctorTotalAmount + parseInt(data.net_amount);
          this.plateformTotalAmount = this.plateformTotalAmount + parseInt(data.platform_service_fee);
        }

      }, error => {
        this.eraningReportLoading = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });

    }
    else {
      const query = '?from_date=' + this.fromDate + '&to_date=' + this.toDate + '&page=' + page;
      this.doctorService.getEarningData(query, this.doctorId).subscribe((data) => {
        this.currentMonthEarning = data['results'];
        this.eraningReportTotalPage = data['total_pages'];
        this.eraningReportCurrentPage = data['page_number'];
        this.eraningReportLoading = false;
        for (let data of this.currentMonthEarning) {
          this.totalAmount = this.totalAmount + parseInt(data.gross_amount);
          this.doctorTotalAmount = this.doctorTotalAmount + parseInt(data.net_amount);
          this.plateformTotalAmount = this.plateformTotalAmount + parseInt(data.platform_service_fee);
        }

      }, error => {
        this.eraningReportLoading = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
    }

  }
  getCsvReport() {
    this.doctorService.getEarningCsvData(this.doctorId).subscribe((data) => {

    }, error => { })
  }
}
