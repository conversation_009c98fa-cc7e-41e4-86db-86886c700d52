.modalShowClass {
    display: block !important;
}

.modal-dialog.t-c {
    overflow-y: initial !important;
    min-width: 1000px;
}

.modal-body.t-c {
    height: 250px;
    overflow-y: auto;
}

.breadcrumb-bar {
    background-color: #15558d;
}


/* .breadcrumb-bar.down {
    background-color: #17a2b8;
    padding: 15px 0;
} */

.bc {
    display: inline;
    margin-right: 35px;
}

#breadcrumb-img {
    width: 4%;
    margin-left: 5px;
}
.fas{
  font-size: 16px !important;
}

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .text-aline{
    text-align: left;
  }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .text-aline{
    text-align: left;
  }
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .text-aline{
    text-align: right;
  }

}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .text-aline{
    text-align: right;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .text-aline{
    text-align: right;
  }
}

button.quick-help {
  display: inline-block;
  width: 100px;
  height: 30px;
  color: #fff;
  cursor: pointer;
  text-align: center;
  background-color: #25d366;
  border: black;
  border-radius: 5px;

}
button.quick-help:hover {
  background-color:#1877f2;
}

.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  transition: opacity 500ms;
visibility: visible;
  opacity: 1;
}


.helpPopup {
  margin: 70px auto;
  padding: 20px;
  background: #fff;
  border-radius: 5px;
  width: 54%;
  position: relative;
  transition: all 5s ease-in-out;
}

.helpPopup .close {
  position: absolute;
  top: 20px;
  right: 5px;
  transition: all 200ms;
  font-size: 30px;
  font-weight: bold;
  text-decoration: none;
  color: #333;
}
.helpPopup .close:hover {
  color: #d80606;
  cursor: pointer;
}
.helpPopup .content {
  height: 30%;
  width: 600px;  
  overflow: auto;
}


