<!--BreadCrumbs-->
<div class="breadcrumb-bar">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="row">
                    <div class="col-xl-8 col-lg-8 col-md-8 col-sm-4">
                        <h2 #header class="breadcrumb-title">{{breadcrumbHeader |translate}}</h2>
                    </div>
                    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 text-aline"
                        *ngIf="breadcrumbHeader=='Consultation' &&showProfilePic" style="display: inline;">
                        <div class="avatar avatar-teleconsult avatar-sm mr-5 ">
                            <img [src]="profilePicture" class="avatar-img rounded-circle" alt="User Image">
                        </div>
                        <h5 class="doc-name first-name" style="color: white; display: inline;">
                            {{personalInfo['username']}}&nbsp;&nbsp;</h5><br>
                            <h6 class="doc-name first-name" style="color: white; display: inline;">
                                ID:{{patientId}}&nbsp;&nbsp;</h6><br>
                        <p style="color: white;display: inline"><span class="fas fa-phone-alt"></span>
                            Support-{{supportNumber}}</p>
                    </div>
                    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 mt-2 text-aline"
                        *ngIf="breadcrumbHeader !=='Consultation'">
                        <p style="color: white;"><span class="fas fa-phone-alt"></span> Support-{{supportNumber}}</p>
                        <div>
                            <button class="quick-help" *ngIf="QuickHelpVideo != null" (click)="quickHelpPopup()">
                                <p>Quick Help</p>
                            </button>
                        </div>
                    </div>
                </div>
                <nav aria-label="breadcrumb" class="page-breadcrumb">
                    <ol class="breadcrumb">
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 col-lg-12 col-xl-12">
                <div>
                    <router-outlet (messageEvent)="getPictureUrl($event)" (consult)="onConsult($event)"
                        (activate)="onActivate($event)"></router-outlet>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Help Popup-->
<div class="overlay" *ngIf="helpPopup">
    <div class="helpPopup">
        <h4>Quick Help video </h4>
        <a class="close" (click)="helpPopup = false">&times;</a>
        <div class="content">
            <iframe width="600" height="315" [src]='doctorHelpVideo'>
            </iframe>
        </div>
    </div>
</div>

<!--INstant consult Modal-->
<!-- Modal -->
<div class="modal i-c fade" id="instantConsultModal" tabindex="-1" role="dialog"
    aria-labelledby="instantConsultModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5 class="modal-title" id="instantConsultModalLabel">An instant appointment request has arrived </h5>

                <P class="mt-1"><span id="timer" style="color: tomato;">03:00</span></P>
            </div>
            <div class="modal-body" id="inst-appt-modal-body" *ngIf="!instantAppointmentAccepted">
                <div class="row ">
                    <div class="col-md-12 text-center">
                        <h5> Patient Details</h5>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-6  text-center">
                        <p>Patient Name</p>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <p>:</p>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <p><strong>{{patientDetails['username']}}</strong></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-6 text-center">
                        <p>Age</p>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <p>:</p>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <p>
                            <strong>{{patientDetails['age']}}</strong>
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-6 text-center">
                        <p>Gender</p>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <p>:</p>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <p><strong> {{patientDetails['gender']}}</strong></p>
                    </div>
                </div>
            </div>
            <div class="modal-body text-center" id="inst-appt-modal-body" *ngIf="instantAppointmentAccepted">
                Processing Consultation. Please Wait....
            </div>
            <div class="modal-footer text-center">
                <button *ngIf="btnInstReq &&!instantAppointmentAccepted" type="button" (click)="acceptAppointment()"
                    class="btn btn-primary">Accept</button>
                <button *ngIf="btnInstReq &&!instantAppointmentAccepted" type="button" (click)="cancelAppointment()"
                    class="btn btn-secondary" data-dismiss="modal">Decline</button>
            </div>
        </div>
    </div>
</div>