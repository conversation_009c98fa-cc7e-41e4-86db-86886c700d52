import { SharedService } from './../shared/shared.service';
import { DoctorService } from './doctor.service';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { AuthService } from './../auth/auth.service';
import { Component, OnInit, Input, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import * as Settings from '../config/settings';
import { DomSanitizer } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import { TeleConsultService } from '../tele-consult/tele-consult.service';
// import * as moment from 'moment';
declare var $: any;
@Component({
  selector: 'app-doctor',
  templateUrl: './doctor.component.html',
  styleUrls: ['./doctor.component.css']
})
export class DoctorComponent implements OnInit {
  clickLocation = false;
  helpPopup = false;
  helpStatus: string;
  breadcrumbHeader: string;
  public route = "";
  stockQuote: any;
  private sub: Subscription | null = null;
  private msgSub: Subscription | null = null;
  @Input() doctorProfilePictureUrl = 'assets/img/doctors/doctor-thumb-02.png';
  @Output() doctorInstantAvailability: boolean;
  public userProfile = {
    username: null,
    profile_picture: null,
  };
  contentText: string[];
  contentHtml: any;
  termsAndconditionID: string;
  acceptedTerms = false;
  consultation = false;
  public selectedId = "ps";
  public doctorProfile = {};
  public qualification = "";
  public speciality = "";
  public registrations = "";
  public instantAppointment = {};
  public instantReqInterval: any;
  public sec_interval: any;
  public btnInstReq = true;
  inst_msg$: any;
  message_id: any;
  instantAppointmentAccepted: boolean = false;
  showWarningNotification: boolean;
  patientDetails: any = [{ username: '', age: '', gender: '' }];
  downloadTimer: any;
  timeleft: number = Settings.popupDuration;
  showInstantAppointmentRequestAvailableNotification: Boolean;
  showInstantPaymentFailedNotification: Boolean;
  ShowPatientJoinedConsultationNotification: boolean;
  ShowPatientJoinedVideoNotification: boolean;
  ShowPatientLeftConsultationNotification: boolean;
  profilePicture = 'assets/img/doctors/doctor-thumb-02.png';
  personalInfo: { username: null };
  showProfilePic: boolean;
  msgId: any;
  supportNumber = Settings.supportNumber;
  doctorHelpVideo: any;
  public QuickHelpVideo: string;
  public test: any;
  book_user_type = 'Patient';
  current_user_uuid: string;
  patientId: any;
  private processedUUIDs = new Set<string>();

  constructor(
    private translate: TranslateService,
    private userService: AuthService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private teleConsultService: TeleConsultService,
    private notificationService: ToastrService,
    private doctorService: DoctorService,
    private sharedService: SharedService,
    private _sanitizer: DomSanitizer,

  ) { }

  ngOnInit(): void {
    this.current_user_uuid = localStorage.getItem('current_user_uuid');
    this.showInstantAppointmentRequestAvailableNotification = true;
    this.showInstantPaymentFailedNotification = true;
    this.ShowPatientJoinedConsultationNotification = true;
    this.ShowPatientJoinedVideoNotification = true;
    this.ShowPatientLeftConsultationNotification = true;
    if (this.userService.loggedIn()) {
      const lang = localStorage.getItem('pageLanguage');
      this.translate.use(lang);
      this.getTermsAndCondition();
      this.pageLoadActiveTab();
      this.userService.getUserDetail().subscribe(
        (data) => {
          this.userProfile.username = data['username'];
          this.sharedService.setUserName(data['username']);
          if (data['profile_picture'] !== null) {
            this.doctorProfilePictureUrl = data['profile_picture'];
            this.sharedService.setPicture(this.doctorProfilePictureUrl);
          }
          if (data['user_type'] != "Doctor") {
            this.router.navigate(['/login']);
          }
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );

      this.route = this.router.url;

      this.activatedRoute.queryParams.subscribe(params => {
        const consultation = params['consultation'];
        this.consultation = consultation;
      });
      // this.instantReqInterval = setInterval(() => this.instantConsultAvailable(),5000);
    } else {
      this.userService.logout();
      // clearInterval();
      this.sharedService.setPicture(this.doctorProfilePictureUrl);
      this.sharedService.setUserName(null);
      this.userService.setLogin(true);
      this.router.navigate(['/login']);
    }

    this.msgSub = this.doctorService.getWebSocketAvailable().subscribe(
      (data: any) => {
        if (data) {
          // console.log(data['message_type'], data['e_id']);
          // console.log('data:', data['data']);
          let inst_appt;
          if (data['e_id'] !== this.msgId && data['e_id'] !== undefined) {
            if (data['data'] !== undefined) {
              inst_appt = JSON.parse(data['data']);
              if (inst_appt.booked_user_json != null) {
                this.book_user_type = inst_appt.booked_user_json.user_type
              }
            }

            this.msgId = localStorage.getItem('msg_id');
            if (data['message_type'] == "Instant Appointment Request Available") {
              if (this.processedUUIDs.has(inst_appt.uuid)) {
                console.log(`Duplicate message with UUID ${inst_appt.uuid} ignored.`);
                return; // Ignore duplicate
              }
              if (this.showInstantAppointmentRequestAvailableNotification) {
                this.patientDetails = [];
                this.processWSInstantAppointment(data);
              }
              this.processedUUIDs.add(inst_appt.uuid);
            } else if (data['message_type'] == "Instant Payment Failed") {
              clearInterval(this.downloadTimer);
              $('#instantConsultModal').modal('hide');
              this.showInstantAppointmentRequestAvailableNotification = true;
              this.instantAppointmentAccepted = false;
              this.btnInstReq = true;
              this.notificationService.error(' Instant appointment processing failed. ');
              this.notificationService.error(' Payment cancelled by the patient ');
              this.doctorService.setWebScoketMsg();
            } else if (data['message_type'] == "Payment Success") {
              clearInterval(this.downloadTimer);
              $('#instantConsultModal').modal('hide');
              this.instantAppointmentAccepted = false;
              const resp_data = JSON.parse(data['data'])
              console.log(resp_data);
              this.instantAppointment['consultation_uuid'] = resp_data['consultation_uuid']
              this.notificationService.warning('Please wait while we are redirecting  you to consultaion page');
              this.doctorService.joinConsultation(this.instantAppointment['consultation_uuid']).subscribe(
                data => {
                  $('#inst-appt-modal-body').html("An instant appointment request has arrived.");
                  this.router.navigate(['/doctor/consultation'], { queryParams: { consultationId: this.instantAppointment['consultation_uuid'] } });
                  this.btnInstReq = true;
                }, error => {
                  console.log(error);
                  const status = error['status'];
                  if (status == 400) {
                    this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
                  }
                  else {
                    this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
                  }
                }
              );
            } else if (data['message_type'] == "Patient Joined Consultation") {
              if (!!this.ShowPatientJoinedConsultationNotification) {
                this.notificationService.success('Patient Joined Consultation');
                this.ShowPatientJoinedConsultationNotification = false;
              }
              this.doctorService.setWebScoketMsg();
              // localStorage.setItem('patient_joined', JSON.stringify(true));
              // localStorage.setItem('patient_joined_msg', data['e_id'] );
              this.doctorService.setWebSocketAvailable(null);
            } else if (data['message_type'] == "Patient Joined Video") {
              if (!!this.ShowPatientJoinedVideoNotification) {
                this.notificationService.success('Patient Joined Video');
                this.ShowPatientJoinedVideoNotification = false;
              }
              this.doctorService.setWebScoketMsg();
            } else if (data['message_type'] == "Patient Left Consultation") {
              if (!!this.ShowPatientLeftConsultationNotification) {
                this.notificationService.warning('Patient Left Consultation');
                this.ShowPatientLeftConsultationNotification = false;
              }
              this.doctorService.setWebScoketMsg();
            }
            // else if (data['message_type'] == "Book user Joined Consultation") {
            //   this.notificationService.success('Book user Joined Consultation');
            //   this.doctorService.setWebScoketMsg();
            // }
          }
        }
      }
    );
    this.sub = this.doctorService.getMessages().subscribe();

    setInterval(() => {
      this.processedUUIDs.clear();
      console.log("Cleared processed UUIDs from the cache.");
    }, 600000);
  }

  ngOnDestroy() {
    clearInterval(this.downloadTimer);
    this.processedUUIDs.clear();
    if (this.sub) {
      this.sub.unsubscribe();
    }
    if (this.msgSub) {
      this.msgSub.unsubscribe();
    }
  }

  logout() {
    clearInterval(this.downloadTimer);
    localStorage.clear();
    this.userService.setPicture('../../../../assets/img/doctors/doctor-thumb-02.png');
    this.router.navigate(['/login']);
  }

  activeTab(id) {
    const url = this.router.url;
    this.route = url;
    this.clickLocation = false;
    $("#" + id).addClass("active");
    $("#" + this.selectedId).removeClass('active');
    this.selectedId = id;
    this.breadcrumbHeader = this.activatedRoute.snapshot.children[0].data['title'];
  }

  pageLoadActiveTab() {
    const url = this.router.url;
    const ar = url.split('/');
    const id = ar[ar.length - 1];
    // $("#"+id).addClass("active");
    this.selectedId = id;
    this.breadcrumbHeader = this.activatedRoute.snapshot.children[0].data['title'];
  }
  onLocation(event: string) {
    const url = this.router.url;
    this.route = url;
    this.clickLocation = true;
    this.breadcrumbHeader = event;
    this.QuickHelpVideo = event;
  }

  quickHelpPopup() {
    this.doctorHelpVideo = this._sanitizer.bypassSecurityTrustResourceUrl(this.QuickHelpVideo);
    this.helpPopup = true;
  }
  getPictureUrl(data) {
    this.doctorProfilePictureUrl = data;
  }

  onActivate(event) {
    const url = this.router.url;
    const ar = url.split('/');
    const id = ar[ar.length - 1];
    this.sharedService.setActiveLink(id);
    this.breadcrumbHeader = this.activatedRoute.snapshot.children[0].data[
      'title'
    ];
    this.QuickHelpVideo = this.activatedRoute.snapshot.children[0].data['video'];


    if (url.includes('consultation?consultationId')) {
      const routerurl = this.router.url;
      const ar = routerurl.split('=');
      console.log(ar);
      const id = ar[ar.length - 1];
      console.log(id)
      this.teleConsultService
        .getConsultationData(id)
        .subscribe((data) => {
          const patientId = data['patient_uuid'];
          localStorage.setItem('consult_patient_id', patientId);
          this.getPatientProfile(patientId)
        }, error => { })
    }
  }
  getPatientProfile(id) {
    this.doctorService.getPatientProfile(id).subscribe(
      (data) => {
        this.personalInfo = data['user'];
        this.patientId = data['med_patient_id'];
        console.log(this.personalInfo);
        if (this.personalInfo['profile_picture']) {
          this.profilePicture = this.personalInfo['profile_picture'];
        }
        setTimeout(() => {
          this.showProfilePic = true;
        }, 1000);
      },
      (error) => {
        this.showProfilePic = true;
        console.log(error);
      }
    );
  }
  //T & C

  getTermsAndCondition(): void {
    this.doctorService.getDoctorProfile().subscribe(
      (data) => {
        console.log(data);
        localStorage.setItem('Doctor', data['uuid']);
        localStorage.setItem('available_now', data['instant_appointment_slot_available']);
        localStorage.setItem('profile_approved_status', data['approval_request_status']);
        localStorage.setItem('profile_completion', data['profile_completion_percentage']);
        const result = data['terms_conditions_item_pending'];
        console.log(result)
        const checkAcceptance = data['terms_conditions_accepted'];
        // const len = Object.values(checkAcceptance).length;
        if (checkAcceptance.length == 0 && result !== null) {
          localStorage.setItem('t&c', 'pending');
          localStorage.setItem('content_html', result['content_html']);
          localStorage.setItem('content_text', result['content_text']);
          localStorage.setItem('t&c_uuid', result['uuid']);
          this.doctorInstantAvailability = data['instant_appointment_slot_available'];
          this.termsAndconditionID = result['uuid'];
          this.contentHtml = result['content_html'];
          this.contentText = result['content_text'];
          // $('.modal').addClass('modalShowClass');
          // $('#myModal').modal('show');
        }
        else {
          localStorage.setItem('t&c', 'accepted');
        }
        this.doctorProfile = data;
        if (data['qualifications'].length != 0) {
          this.qualification = data['qualifications'][0]['name'];
        }
        if (data['speciality'] !== null) {
          this.speciality = data['speciality'][0]['Speciality_name'];
        }
        if (data['registrations'].length != 0) {
          this.registrations = data['registrations'][0]['number'];
        }
        if (data['profile_completion_percentage'] < 80) {
          // this.router.navigate(['/doctor/profile']);
        }

      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  // updateTermsAndCondtion(id) {
  //   return this.httpClient
  //     .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/terms/acceptance/`, id)
  //     .pipe(delay(Settings.REQUEST_DELAY_TIME));
  // }

  // saveTermsAndcondition() {
  //   const id: any = { uuid: '' };
  //   id.uuid = this.termsAndconditionID;
  //   this.updateTermsAndCondtion(id).subscribe(
  //     (data) => {
  //       // console.log(data);
  //       $('.modal').removeClass('modalShowClass');
  //       $('#myModal').modal('hide');
  //       this.notificationService.success(
  //         ' Terms and Condition  Accepted',
  //         'Med.Bot'
  //       );
  //     },
  //     (err) => {
  //       console.log(err);
  //       this.notificationService.error(
  //         ' Terms and Condition  Updation Error',
  //         'Med.Bot'
  //       );
  //     }
  //   );
  // }
  // acceptTermsAndCondtion(event) {
  //   this.acceptedTerms = event.target.checked;
  // }
  // closeModelPopup() {
  //   $('.modal').removeClass('modalShowClass');
  //   $('#myModal').modal('hide');
  //   localStorage.clear();
  //   this.router.navigate(['/login']);
  // }

  onConsult(data) {
    console.log('Dr component - onConsult clicked');
    this.consultation = true;
    this.router.navigateByUrl('/consultation');
  }

  instantConsultAvailable() {
    const available_now = localStorage.getItem('available_now');
    if (available_now === 'true') {
      this.doctorService.checkInstantAppointmentAvailability().subscribe(
        data => {
          if (Object.values(data).length != 0) {
            const dat = Object.values(data);
            const inst_appt = dat[0];
            this.instantAppointment = inst_appt;
            if (inst_appt['status'] === 'Pending') {
              $('#instantConsultModal').modal({ backdrop: 'static', keyboard: false });
              $('#instantConsultModal').modal('show');
            }
          }
        }, error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );

    }
  }

  acceptAppointment() {
    console.log(this.message_id);
    const status = { 'status': 'Accepted', 'message_id': this.message_id };
    if (this.book_user_type != "Patient") {
      this.doctorService.updateInstantAppointmentStatusForPatient(this.current_user_uuid, this.instantAppointment['uuid'], status).subscribe(
        data => {
          console.log(data);
          this.notificationService.success("Appointment Accepted");
          this.instantAppointmentAccepted = true;
          this.btnInstReq = false;
          this.doctorService.setWebScoketMsg();
        }, error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
    else {
      this.doctorService.updateInstantAppointmentStatus(this.instantAppointment['uuid'], status).subscribe(
        data => {
          console.log(data);
          this.notificationService.success("Appointment Accepted");
          this.instantAppointmentAccepted = true;
          this.btnInstReq = false;
          this.doctorService.setWebScoketMsg();
        }, error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }

  }

  cancelAppointment() {
    const status = { 'status': 'Declined', 'message_id': this.message_id };
    if (this.book_user_type != "Patient") {
      this.doctorService.updateInstantAppointmentStatusForPatient(this.current_user_uuid, this.instantAppointment['uuid'], status).subscribe(
        data => {
          this.notificationService.success("Appointment Declined");
          this.showInstantAppointmentRequestAvailableNotification = true;
          this.instantAppointmentAccepted = false;
          this.btnInstReq = true;
          $('#instantConsultModal').modal('hide');
          this.doctorService.setWebScoketMsg();
          this.timeleft = 180;
        }, error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
    else {
      this.doctorService.updateInstantAppointmentStatus(this.instantAppointment['uuid'], status).subscribe(
        data => {
          this.notificationService.success("Appointment Declined");
          this.showInstantAppointmentRequestAvailableNotification = true;
          this.instantAppointmentAccepted = false;
          this.btnInstReq = true;
          $('#instantConsultModal').modal('hide');
          this.doctorService.setWebScoketMsg();
          this.timeleft = 180;
        }, error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
  }

  processWSInstantAppointment(data) {
    const available_now = localStorage.getItem('available_now');
    if (available_now === 'true') {
      this.showInstantAppointmentRequestAvailableNotification = false;
      this.instantAppointmentAccepted = false;
      this.btnInstReq = true;
      this.message_id = data['e_id'];
      const inst_appt = JSON.parse(data['data']);
      // console.log(inst_appt['patient_user_json']);
      this.patientDetails = inst_appt['patient_user_json']
      this.instantAppointment = inst_appt;
      if (this.instantAppointment['status'] == 'Pending') {
        $('#instantConsultModal').modal({ backdrop: 'static', keyboard: false });
        $('#instantConsultModal').modal('show');
        const display = document.querySelector('#timer');
        this.timeRamainder(this.timeleft, display);
      }
    };
  }

  timeRamainder(duration, display) {
    let timer = duration, minutes, seconds;
    this.downloadTimer = setInterval(() => {
      minutes = Math.floor(timer / 60);
      seconds = Math.floor(timer % 60);

      minutes = minutes < 10 ? "0" + minutes : minutes;
      seconds = seconds < 10 ? "0" + seconds : seconds;
      if (minutes >= 0 && seconds >= 0) {
        display.textContent = minutes + ":" + seconds;
      }
      if (--timer == 0) {
        this.cancelAppointment();
        clearInterval(this.downloadTimer);
      }

    }, 1000);
  }
  // ws.onmessage = function (e) {
  //     let messages = document.getElementById("messages");
  //     let message = document.createElement("li");
  //     let data = JSON.parse(e.data);
  //     console.log("data received is ",data)
  //     let message_content = null;

  //     if (data["msg"] !== null) {
  //       message_content = document.createElement("span");
  //       message_content.appendChild(
  //         document.createTextNode(data["uname"] + ": " + data["msg"])
  //       );
  //       message_content.setAttribute("class", "comment");
  //     }
  //             message.appendChild(message_content);
  //     messages.appendChild(message);
  //     messages.scrollTop = messages.scrollHeight;
  //   };
  // }

}

