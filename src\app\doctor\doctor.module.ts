import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { ModalModule } from 'ngb-modal';
import { CKEditorModule } from 'ng2-ckeditor';
import { SharedModule } from '../shared/shared.module';

// doctor components
import { DoctorComponent } from './doctor.component';
import { DoctorReportsComponent } from './doctor-reports/doctor-reports.component';
import { Doctor<PERSON>rofileComponent } from './doctor-profile/doctor-profile.component';
import { DoctorTeleConsultComponent } from './doctor-tele-consult/doctor-tele-consult.component';
import { DoctorBasicProfileComponent } from './doctor-profile/doctor-basic-profile/doctor-basic-profile.component';
import { DoctorBankAccountsComponent } from './doctor-bank-accounts/doctor-bank-accounts.component';
import { DoctorPracticeLocationComponent } from './doctor-practice-location/doctor-practice-location.component';
import { DoctorRegistrationComponent } from './doctor-profile/doctor-registration/doctor-registration.component';
import { DoctorHomeAddressComponent } from './doctor-profile/doctor-home-address/doctor-home-address.component';
import { DoctorConsultingHoursComponent } from './doctor-practice-location/doctor-consulting-hours/doctor-consulting-hours.component';
import { DoctorPublicProfileComponent } from './doctor-public-profile/doctor-public-profile.component';
import { DoctorDashboardComponent } from './doctor-dashboard/doctor-dashboard.component';
import { ManageAppointmentsComponent } from './doctor-dashboard/manage-appointments/manage-appointments.component';
import { FeesComponent } from './doctor-profile/fees/fees.component';
import { DoctorMessagesComponent } from './doctor-messages/doctor-messages.component';
import { DoctorMessagesConsultationComponent } from './doctor-messages-consultation/doctor-messages-consultation.component';
import { QualificationsComponent } from './doctor-profile/qualifications/qualifications.component';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { DatepickerModule, BsDatepickerModule, BsDatepickerConfig } from 'ngx-bootstrap/datepicker';
import { FeeCollectedComponent } from './doctor-dashboard/fee-collected/fee-collected.component';
import { EarningReportComponent } from './doctor-reports/earning-report/earning-report.component';
import { ShareButtonComponent } from './doctor-profile/share-button/share-button.component';



@NgModule({
  declarations: [
    DoctorComponent,
    DoctorProfileComponent,
    DoctorTeleConsultComponent,
    DoctorReportsComponent,
    DoctorBasicProfileComponent,
    DoctorBankAccountsComponent,
    DoctorPracticeLocationComponent,
    DoctorRegistrationComponent,
    DoctorHomeAddressComponent,
    DoctorConsultingHoursComponent,
    DoctorPublicProfileComponent,
    DoctorDashboardComponent,
    ManageAppointmentsComponent,
    FeesComponent,
    DoctorMessagesComponent,
    DoctorMessagesConsultationComponent,
    QualificationsComponent,
    FeeCollectedComponent,
    EarningReportComponent,
    ShareButtonComponent,

  ],
  imports: [
    RouterModule,
    FormsModule,
    CommonModule,
    ReactiveFormsModule,
    NgSelectModule,
    NgMultiSelectDropDownModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    TooltipModule.forRoot(),
    BsDatepickerModule.forRoot(),
    DatepickerModule.forRoot(),
    ModalModule,
    CKEditorModule,
    NgbModule,
    SharedModule,

  ],
  exports: [DoctorMessagesComponent, SharedModule],
  providers: [{ provide: BsDatepickerConfig, useFactory: getDatepickerConfig }],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DoctorModule { }

export function HttpLoaderFactory(httpClient: HttpClient) {
  return new TranslateHttpLoader(httpClient);
}
export function getDatepickerConfig(): BsDatepickerConfig {
  return Object.assign(new BsDatepickerConfig(), {
    dateInputFormat: 'DD-MM-YYYY'
  });
}
