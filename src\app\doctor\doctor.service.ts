import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as Settings from './../config/settings';
import { Observable, Observer, BehaviorSubject } from 'rxjs';
import { webSocket, WebSocketSubject } from "rxjs/webSocket";
import { catchError, tap, shareReplay, delay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class DoctorService {
  private wsSubject: WebSocketSubject<any> | null = null;
  private webSocketAvailable = new BehaviorSubject<object | null>(null);
  private sharedMessages$: Observable<object> | null = null;
  msg: any;

  constructor(private httpClient: HttpClient,
    private router: Router,
    private notificationService: ToastrService) { }


  getMessages(): Observable<object> {
    if (!this.sharedMessages$) {
      const tokenObj = localStorage.getItem('currentUser');
      const accessToken = JSON.parse(tokenObj)['access'];

      this.wsSubject = webSocket(`${Settings.WEBSOCKET_BASE_URL}/ws?username=${accessToken}`);

      this.sharedMessages$ = this.wsSubject.pipe(
        tap(msg => {
          console.log(msg);
          localStorage.setItem('msg_id', msg['e_id']);
          if (msg['e_id']) {
            this.webSocketAvailable.next(msg);
          }
        }),
        catchError(error => {
          console.error('WebSocket error:', error);
          return [];
        }),
        shareReplay(1)
      );
    }

    return this.sharedMessages$;
  }

  getWebSocketAvailable(): Observable<object | null> {
    return this.webSocketAvailable.asObservable();
  }

  setWebSocketAvailable(value: object | null) {
    this.webSocketAvailable.next(value);
  }

  closeConnection() {
    if (this.wsSubject) {
      this.wsSubject.complete();
      this.wsSubject = null;
      this.sharedMessages$ = null; // Reset shared observable
    }
    this.webSocketAvailable.next(null);
  }

  getCountryDetail() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/countries/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorQualifications() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorLanguages() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/languages/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorProfile() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/profile/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }



  getDoctorPracticeLocations() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/practice_locations/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsultingHours() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/consulting_hours_group/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorFees() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorHaFees(doc_uuid) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doc_uuid}/fees/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctersByConsultNow(page, hospital_uuid) {
    if (hospital_uuid != null) {
      return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/search/?page=${page}&consult_now=true&is_busy=False&hospital_uuid=${hospital_uuid}`
      ).pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
    else {
      return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/search/?page=${page}&consult_now=true&is_busy=False`
      ).pipe(delay(Settings.REQUEST_DELAY_TIME));
    }

  }
  getDoctersBypage(page, searchStringValue, hospital_uuid) {
    if (searchStringValue) {
      if (hospital_uuid != null && hospital_uuid != undefined && hospital_uuid != '') {
        return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/search/${searchStringValue}&page=${page}&hospital_uuid=${hospital_uuid}`
        ).pipe(delay(Settings.REQUEST_DELAY_TIME));
      }
      else {
        return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/search/${searchStringValue}&page=${page}`
        ).pipe(delay(Settings.REQUEST_DELAY_TIME));
      }

    } else {
      if (hospital_uuid != null && hospital_uuid != undefined && hospital_uuid != '') {
        return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/search/?page=${page}&hospital_uuid=${hospital_uuid}`
        ).pipe(delay(Settings.REQUEST_DELAY_TIME));
      }
      else {
        return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/search/?page=${page}`
        ).pipe(delay(Settings.REQUEST_DELAY_TIME));
      }

    }

  }
  searchDoctors(searchParams, hospital_uuid) {
    if (hospital_uuid != null && hospital_uuid != undefined && hospital_uuid != '') {
      return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/search/${searchParams}&hospital_uuid=${hospital_uuid}`
      ).pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
    else {
      return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/search/${searchParams}`
      ).pipe(delay(Settings.REQUEST_DELAY_TIME));
    }

  }
  getAllDocters() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/serarch/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorUnavailability() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/unavailability/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorAppointments(searchParams) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/appointments/${searchParams}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorAppointmentsHa(current_user_uuid, searchParams) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital/appointments/${current_user_uuid}/${searchParams}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorAllAppointments() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/appointments/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorRegistrations() {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/registrations/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorAppointmentSlot(id, searchParams) {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${id}/appointment_slots/calendar/${searchParams}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  bookAppointment(id) {
    return this.httpClient
      .post(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/appointment_slots/${id}/booking/`, null
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorListByPageNumber(num) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/search/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));

  }

  cancelPatientAppointment(uuid) {
    const data = { status: 'Cancelled' };
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/appointments/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getAppointmentSlots(searchParams) {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/appointment_slots/?${searchParams}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getSpeciality(hospital_uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/specialities/${hospital_uuid}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getSpecialitywithoutHospital() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/specialities/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  async getSpecialitywithoutHospital1() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/specialities/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME)).toPromise();
  }
  // getDepartment(hospital_uuid) {departments_list
  //   return this
  //     .httpClient
  //     .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/departments/${hospital_uuid}/`)
  //     .pipe(delay(Settings.REQUEST_DELAY_TIME));
  // }
  getDepartment(hospital_uuid?) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/departments_list_som`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getParticularHospitalSpeciality(hospital_uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/l/specialities/${hospital_uuid}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  async getParticularHospitalSpeciality1(hospital_uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/l/specialities/${hospital_uuid}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME)).toPromise();
  }
  getParticularHospitalDepartment(hospital_uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/l/departments/${hospital_uuid}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  blockAppointmentSlot(uuid) {
    const data = { status: 'Blocked' };
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/appointment_slots/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  blockAppointmentSlotByOthers(doctor_uuid, uuid) {
    const data = { status: 'Blocked' };
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/appointment_slots/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  availableAppointmentSlot(uuid) {
    const data = { status: 'Available' };
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/appointment_slots/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  availableAppointmentSlotByOthers(doctor_uuid, uuid) {
    const data = { status: 'Available' };
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/appointment_slots/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postDoctorFee(data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchDoctorFee(uuid, data) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  postDoctorFeeWith_uuid(doc_uuid, data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doc_uuid}/fees/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  patchDoctorFeeWith_uuid(doc_uuid, data) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/${doc_uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  doctorAvailableNow() {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_appointment_slot/open/`, {}
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  doctorUnAvailableNow() {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_appointment_slot/close/`, {}
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postConsultingHours(data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/consulting_hours_group/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchConsultingHours(data, uuid) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/consulting_hours_group/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postUnavailability(data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/unavailability/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  patchUnavailability(uuid, data) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/unavailability/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  deleteUnavailabilityData(uuid) {
    return this.httpClient.delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/unavailability/${uuid}/`,
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchDoctorProfile(data) {
    const formData = { 'professional_summary': data };
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/profile/`, formData
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  checkInstantAppointmentAvailability() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_requests/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateInstantAppointmentStatus(uuid, status) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_requests/${uuid}/`, status
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  updateInstantAppointmentStatusForPatient(doctor_uuid, uuid, status) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital_instant_requests_detail/${doctor_uuid}/${uuid}/`, status
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  initiateConsultation(appointment_uuid) {
    const data = {};
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}api/doctor/me/appointments/${appointment_uuid}/consultations/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getInstantAppointment(uuid) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_requests/${uuid}/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getPastInstantAppointment(param) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/instant_requests/booked/?${param}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  joinConsultation(uuid) {
    const data = {};
    localStorage.setItem('sess', uuid);
    return this.httpClient.post(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${uuid}/join/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getConsultationMessage(uuid) {
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${uuid}/messages/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getConsultationMessages(page) {
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/me/messages/?seen=False&recipient_type=Doctor&page=${page}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  checkProfileCompletion() {
    const profile_approval_status = localStorage.getItem('profile_approved_status');
    if (profile_approval_status == 'Pending') {
      this.notificationService.warning('Please wait for admin approval ');
      this.router.navigate(['/doctor/profile']);
    } else if (profile_approval_status == 'Rejected') {
      this.notificationService.error('Sorry medbot rejected your profile');
      this.router.navigate(['/doctor/profile']);
    } else if (profile_approval_status == null || profile_approval_status == 'null') {
      this.notificationService.warning('Please complete the profile to access other pages');
      this.router.navigate(['/doctor/profile']);

    }

  }

  submitForApproval() {
    const data = '';
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/profile/approval_request/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateTermsAndCondtion(id) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/terms/acceptance/`, id)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postDoctorQualification(data) {
    return this
      .httpClient
      .post(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/`, data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }
  updateDoctorQualification(qual_uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/${qual_uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchDoctorQualification(qual_uuid, data) {
    return this
      .httpClient
      .patch(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/${qual_uuid}/`, data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }

  deleteDoctorQualification(qual_uuid) {
    return this
      .httpClient
      .delete(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/${qual_uuid}/`
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }
  getHospitalAssociationPendingRequest() {
    return this
      .httpClient
      .get(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/h/me/associations/pending/`
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }

  getHospitalAssociationRequest() {
    return this
      .httpClient
      .get(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/h/me/h-associations/`
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }

  approveAssociateReq(uuid, status) {
    const data = { 'approved': status };
    return this
      .httpClient
      .patch(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/h/me/associations/pending/${uuid}/`, data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }
  deleteBankAccount(uuid) {
    return this.httpClient.delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/bank_accounts/${uuid}/`
    )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  updateAccountDetail(data, uuid) {
    if (uuid) {
      return this
        .httpClient
        .patch(
          `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/bank_accounts/${uuid}/`, data
        )
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
    else {
      return this
        .httpClient
        .post(
          `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/bank_accounts/`, data
        )
        .pipe(delay(Settings.REQUEST_DELAY_TIME))
        ;
    }
  }
  getBankAccountDetails() {
    return this
      .httpClient
      .get(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/bank_accounts/`
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }
  getAllInstantAppointment() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_requests/?status=Booked&fulfilment_status=Completed%2BSuspended`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getTodayInstantAppointment(date) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_requests/?status=Booked&date=${date}&fulfilment_status=Completed%2BSuspended`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getOngoingInstantAppointment(date) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_requests/?status=Booked&date=${date}&fulfilment_status=Started`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  changeMessageStatus(uuid, msgid) {
    const data = { 'seen': 'true' };
    return this.httpClient.patch(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${uuid}/messages/${msgid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getPatientNotes(uuid) {
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${uuid}/messages/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateRegistrationForm(data, registerfile) {
    const formData = new FormData();
    formData.append('file', registerfile);
    formData.append('data', JSON.stringify(data));
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/registrations/`, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDegree(data) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/degrees/?system_of_medicine=${data}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getCouncils(data) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/councils/?system_of_medicine=${data}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  addSpeciality(data) {
    const newSpeciality = { 'value': data };
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/specialities/`, newSpeciality)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  addSystemOfMedicine(data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/system_of_medicine/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  addDegree(data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/degrees/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getSystemOfMedicine(id?: string) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/departments_list_som/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getdoctorProfileById(id) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/profile/${id}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }


  getdoctorProfileByIdHa(id) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/profile/${id}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getEarningData(query, id) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${id}/earnings/${query}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getEarningDataHa(hospital_uuid, query) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospitals/${hospital_uuid}/earnings-report/${query}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getEarningCsvData(id) {
    let headers = new HttpHeaders().set('Accept', 'text/csv');
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${id}/earnings/`, { headers: headers })
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getEarningCsvDataHa(id) {
    let headers = new HttpHeaders().set('Accept', 'text/csv');
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${id}/earnings/`, { headers: headers })
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  setWebScoketMsg() {
    const id = localStorage.getItem('msg_id')
    const data = { message_type: null, e_id: id };
    this.webSocketAvailable.next(data);
  }
  getPatientProfile(id) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/profile/${id}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }
  getMedicalReportsById(id) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/m_reports/?consultation=${id}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  postPracticeLocation(data) {
    return this.httpClient
      .post(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/practice_locations/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  patchPracticalLocation(data, uuid) {
    return this.httpClient
      .patch(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/practice_locations/${uuid}/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  updateSlugName(data) {
    return this.httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${data.uuid}/profile/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getSlugName() {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctor_slug/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }





  saveHaAddress(data, doctor_uuid, uuid) {
    // return this.httpClient
    //     .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital/${doctor_uuid}/addresses/${uuid}`, data)
    //     .pipe(delay(Settings.REQUEST_DELAY_TIME));

    if (uuid === null) {
      return this.httpClient
        .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital/${doctor_uuid}/addresses/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    } else {
      return this.httpClient
        .patch(
          `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/addresses/${uuid}/`,
          data
        )
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
  }

  getHaAddressDetail(doc_uuid) {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital/${doc_uuid}/addresses/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }



  saveAssistAddress(data, doctorasst_uuid, uuid) {

    if (data.uuid === null) {
      return this.httpClient
        .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/doctorasst/${doctorasst_uuid}/addresses/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    } else {
      return this.httpClient
        .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/doctorasst/${doctorasst_uuid}/addresses/${uuid}/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
  }


  getAssistAddressDetail(doctorasst_uuid) {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/doctorasst/${doctorasst_uuid}/addresses/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }


  partnersaveAddress(data, partner_uuid, uuid) {
    if (uuid === null) {
      return this.httpClient
        .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/partner/${partner_uuid}/addresses/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
    else {
      return this.httpClient
        .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/partner/${partner_uuid}/addresses/${uuid}/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }


    // if (data.uuid === null) {
    //   return this.httpClient
    //     .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hosptial/${partner_uuid}/addresses/${uuid}`, data)
    //     .pipe(delay(Settings.REQUEST_DELAY_TIME));
    // } else {
    //   return this.httpClient
    //     .patch(
    //       `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hosptial/${partner_uuid}/addresses/${uuid}`,
    //       data
    //     )
    //     .pipe(delay(Settings.REQUEST_DELAY_TIME));
    // }
  }


  getpartnerAddressDetail(partner_uuid) {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/partner/${partner_uuid}/addresses/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  checkProfileHaCompletion() {
    const profile_approval_status = localStorage.getItem('profile_approved_status');
    if (profile_approval_status == 'Pending') {
      this.notificationService.warning('Please wait for admin approval ');
      this.router.navigate(['/hospital/doctor-profile']);
    } else if (profile_approval_status == 'Rejected') {
      this.notificationService.error('Sorry medbot rejected your profile');
      this.router.navigate(['/hospital/doctor-profile']);
    } else if (profile_approval_status == null || profile_approval_status == 'null') {
      // this.notificationService.warning('Please complete the profile to access other pages');
      this.router.navigate(['/hospital/doctor-profile']);

    }
  }
  getHaDoctorPracticeLocations(doctor_uuid) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/practice_locations/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  postHaPracticeLocation(data, doctor_uuid) {
    return this.httpClient
      .post(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/practice_locations/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchHaPracticalLocation(data, uuid, doctor_uuid) {
    return this.httpClient
      .patch(
        `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/practice_locations/${uuid}/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getHaDoctorUnavailability(doctor_uuid) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/unavailability/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  postHaConsultingHours(data, doctor_uuid) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/consulting_hours_group/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  patchHaConsultingHours(data, uuid, doctor_uuid) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/consulting_hours_group/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getHaConsultingHours(doctor_uuid) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/consulting_hours_group/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  postHaUnavailability(data, doctor_uuid) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/unavailability/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  patchHaUnavailability(uuid, data, doctor_uuid) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/unavailability/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  deleteHaUnavailabilityData(uuid, doctor_uuid) {
    return this.httpClient.delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doctor_uuid}/unavailability/${uuid}/`,
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getPendingInstantAppointment(param) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/instant_requests/?${param}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorAppointmentCount(param) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/appointment_count?${param}`)
  }

  getOngoingConsultation(param) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/ongoing_consultations/?${param}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

}