import { Component, OnInit } from '@angular/core';
import { AuthService } from './../auth/auth.service';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.css']
})
export class FooterComponent implements OnInit {

  constructor(private userService: AuthService,
    private router: Router,
    private notificationService: ToastrService) { }
  loginValue() {
    return this.userService.loggedIn();
  }
  getUserType() {
    return localStorage.getItem('user_type');
  }
  ngOnInit(): void {
  }

  routerNavigation(id) {
    if (this.loginValue()) {
      if (this.getUserType() == 'Patient') {
        if (id === 'Search') {
          const query = 'consult_now=false'
          const page = 1;
          this.router.navigate(['/patient/search/', page, query]);
        } else if (id === 'Login') {
          this.userService.logout();
          this.router.navigate(['/login']);
        } else if (id === 'Register') {
          const url = `/signup?user_type=Patient`;
          this.router.navigateByUrl(`${url}`);
        } else if (id === 'Dashboard') {
          this.router.navigate(['/patient/dashboard']);
        }
      } else {
        this.notificationService.warning('Please login as patient');
      }
    } else {
      this.router.navigate(['/login'])
    }
  }

  doctorNavigation(id) {
    if (this.loginValue()) {
      if (this.getUserType() == 'Doctor') {
        if (id === 'Login') {
          this.userService.logout();
          this.router.navigate(['/login']);
        } else if (id === 'Register') {
          const url = `/signup?user_type=Doctor`;
          this.router.navigateByUrl(`${url}`);

        } else if (id === 'Dashboard') {
          this.router.navigate(['/doctor/dashboard']);
        }
      } else {
        this.notificationService.warning('Please login as doctor');
      }
    } else {
      this.router.navigate(['/login'])
    }
  }

}
