.inactive-tab {
    color: #333333;
    font-size: 18px;
}

.empty-link {
    cursor: not-allowed;
}

/* .bgImg {
    background-color: #77C1F9;
} */

.image-possition {
    margin-left: 15% !important;
}

.has-submenu.auth-static a {
    font-size: 20px;
    font-family: 'Rubik', sans-serif;
    font-weight: lighter;
}

nav {
    border-bottom-color: #77C1F9 !important;
    padding-top: 20px;
}

.connect {
    font-size: 20px;
    color: #fff;
    margin-bottom: 0px;
    padding-top: 180px;
    line-height: 35px;
    padding-left: 30px;
}

a.navbar-brand.logo {
    margin-right: 0px !important;
}

#top-logo {
    height: 65px;
    margin-left: 25%;
}

.header-top-pad {
    padding: 20px 0 0;
}

.signin-login {
    margin-top: 4%;
}

.forgt-pass {
    color: #fff !important;
}


/*
@media only screen and (max-width: 575.98px) {
    .header-navbar-rht {
        display: none !important;
    }
}

@media only screen and (max-width: 978) {
    .header-navbar-rht {
        display: none !important;
    }
}

@media only screen and (min-width: 1336px) {
    .hideName {
        display: block !important;
    }
    .show-dropdown {
        display: none !important;
    }
}

@media only screen and (max-width: 1336px) {
    .hideName {
        display: none !important;
    }
} */

li a span h5 {
    font-weight: 600 !important;
}


/* Extra small devices (phones, 600px and down) */

@media only screen and (max-width: 600px) {
    .mob-btn {
        margin-left: 15px;
    }

    .bgImg {
        height: 350px;
    }

    .example {
        background: red;
    }

    .menu-content-top {
        top: 0;
    }

    .hide-logout {
        display: block !important;
    }

    .log-icon {
        color: #ffffff;
    }

    .log-icon:hover {
        color: #EF548B;
    }

    .inactive-tab {
        color: #ffffff !important;
        font-size: 10px;
    }

    .img_position {
        margin-top: -100%;
    }

    ul li a span .active-tab {
        color: #EF548B !important;
        font-size: smaller;
    }

    .header-right {
        margin-right: 0px;
    }

    .btn-sign {
        border-radius: 18px !important;
        background: #005cb7;
        border: none;
        color: #fff;
        font-size: 16px;
        padding: 7px 15px;
        width: 100%;
        /* margin-left: -14px; */
    }

    .header-mobile-top {
        margin-top: 20%;
        margin-left: -9%;
    }

    .email-id,
    input.passwrd-id {
        padding: 4px;
        padding-left: 15px;
        width: 100%;
        height: calc(1.5em + .75rem + 2px);
        box-sizing: border-box;
        outline: none;
        margin-left: -14px;
    }

    .email-id,
    input.passwrd-id {
        padding: 5px 4px;
        border-radius: 10px;
        border: none;
    }

    .forgt-pass {
        /* margin-left: 51%; */
        margin-bottom: 5px;
    }

    #top-logo {
        margin-left: 75%;
    }

    .signin-login {
        margin-right: 20px;
    }

    .hide-logo {
        display: none;
    }

    .hideName {
        display: none !important;
    }

    .mob-forgt-link {
        text-align: center;
        margin-left: 45px;
    }
}



@media only screen and (min-width: 600px) {
    .example {
        background: green;
    }

    .img-fluid {
        max-width: 80% !important;
        /* height: auto; */
    }

    .menu-content-top {
        top: 0;
    }

    .hide-logout {
        display: block !important;
    }

    .log-icon {
        color: #ffffff;
    }

    .log-icon:hover {
        color: #EF548B;
    }

    .inactive-tab {
        color: #ffffff !important;
        font-size: 12px;
    }

    .hideName {
        display: none !important;
    }

    .img_position {
        margin-top: 0%;
    }

    ul.main-nav {
        margin-left: 0px;
    }

    ul li a span .active-tab {
        color: #EF548B !important;
        font-size: smaller;
    }

    .hide-logo {
        display: none;
    }

    .header-right {
        margin-right: 0px;
    }
}


/* Medium devices (landscape tablets, 768px and up) */

@media only screen and (min-width: 768px) {
    .example {
        background: blue;
    }

    .img-fluid {
        max-width: 50 !important;
        /* height: auto; */
    }

    .menu-content-top {
        top: 0;
    }

    .hide-logout {
        display: block !important;
    }

    .log-icon {
        color: #ffffff;
    }

    .log-icon:hover {
        color: #EF548B;
    }

    .inactive-tab {
        color: #ffffff !important;
        font-size: 12px;
    }

    .header-navbar-rht li a.header-login {
        font-size: x-small;
    }

    .img_position {
        margin-top: 0%;
    }

    ul.main-nav {
        margin-left: 0px;
    }

    ul li a span .active-tab {
        color: #EF548B !important;
        font-size: smaller;
    }

    .hide-logo {
        display: none;
    }

    .header-right {
        margin-right: 0px;
    }
}


/* Large devices (laptops/desktops, 992px and up) */

@media only screen and (min-width: 992px) {
    .example {
        background: orange;
    }

    .hide-logout {
        display: none !important;
    }

    .main-nav>li {
        margin-right: 15px !important;
        ;
    }

    .main-nav li .submenu::before {
        right: 1px !important;
        left: auto !important;
    }

    .main-nav li>ul {
        margin: 0px 2px 5px -200px;
    }

    .image-possition {
        margin-left: 0% !important;
    }

    .inactive-tab {
        color: #000000 !important;
        font-size: 14px;
    }

    .hideName {
        display: block !important;
    }

    .show-dropdown {
        display: none !important;
    }

    ul li a span .active-tab {
        color: #EF548B !important;
        font-size: 14px;
    }

    .header-navbar-rht li a.header-login {
        font-size: small;
    }

    .header_center {
        margin-left: 2%;
    }

    .img_position {
        margin-top: 0%;
    }

    h5 {
        font-size: 1rem;
    }

    ul.main-nav {
        margin-left: 40px;
    }

    .hide-logo {
        display: block;
    }

    .header-right {
        margin-right: 0px;
    }
}


/* Extra large devices (large laptops and desktops, 1200px and up) */

@media only screen and (min-width: 1200px) and (max-width:1299px) {
    .example {
        background: pink;
    }

    .hideName {
        display: block !important;
    }

    .hide-logo {
        display: block;
    }

    .show-dropdown {
        display: none !important;
    }

    .inactive-tab {
        color: #000000 !important;
        font-size: 18px;
    }

    ul li a span .active-tab {
        color: #EF548B !important;
        font-size: 18px;
    }

    .header-navbar-rht li a.header-login {
        font-size: large;
    }

    .header_center {
        margin-left: 15%;
    }

    .img_position {
        margin-top: 0%;
    }

    ul.main-nav {
        margin-left: 0px;
    }

    li#about a {
        margin-right: 15px;
    }

    .header-right {
        margin-right: 37px;
    }

    .forgt-pass {
        margin-left: -25px;
        color: #fff;
    }
}

.btn-sign {
    border-radius: 18px !important;
    background: #005cb7;
    border: none;
    color: #fff;
    font-size: 16px;
    padding: 7px 15px;
}

.email-id,
input.passwrd-id {
    padding: 4px;
    padding-left: 15px;
    width: 110%;
    height: calc(1.5em + .75rem + 2px);
    box-sizing: border-box;
    outline: none;
}

.email-id,
input.passwrd-id {
    padding: 5px 4px;
    border-radius: 10px;
    border: none;
}

input[type=email]:focus {
    outline: none;
}

input[type=password]:focus {
    outline: none;
}

.header_bg_color {
    background-color: #77C1F9;
}

@media only screen and (min-width: 1400px) {
    .example {
        background: pink;
    }

    .hideName {
        display: block !important;
    }

    .hide-logo {
        display: block;
    }

    .show-dropdown {
        display: none !important;
    }

    .inactive-tab {
        color: #000000 !important;
        font-size: 18px;
    }

    ul li a span .active-tab {
        color: #EF548B !important;
        font-size: 18px;
    }

    .header-navbar-rht li a.header-login {
        font-size: large;
    }

    .header_center {
        margin-left: 15%;
    }

    .img_position {
        margin-top: 0%;
    }

    ul.main-nav {
        margin-left: 0px;
    }

    li#about a {
        margin-right: 15px;
    }

    .header-right {
        /* margin-right: -70px; */
    }

    #top-logo {
        padding-left: 15px !important;
    }
}

@media only screen and (min-width: 1536px) {
    .example {
        background: pink;
    }

    .hideName {
        display: block !important;
    }

    .hide-logo {
        display: block;
    }

    .show-dropdown {
        display: none !important;
    }

    .inactive-tab {
        color: #000000 !important;
        font-size: 18px;
    }

    ul li a span .active-tab {
        color: #EF548B !important;
        font-size: 18px;
    }

    .header-navbar-rht li a.header-login {
        font-size: large;
    }

    .header_center {
        margin-left: 15%;
    }

    .img_position {
        margin-top: 0%;
    }

    ul.main-nav {
        margin-left: 0px;
    }

    li#about a {
        margin-right: 15px;
    }

    .header-right {
        margin-right: 40px;
    }

    #top-logo {
        padding-left: 15px !important;
    }
}

@media only screen and (min-width: 1920px) {
    .header-right {
        margin-right: 220px;
    }

}

@media only screen and (min-width: 1600px) and (max-width:1680px) {
    .header-right {
        margin-right: 104px;
    }
}

.changeHeight {
    height: 95px !important;
    background-color: #fff !important;
}

.whiteBorder {
    border-bottom-color: #fff !important;
}

button.quick-help {
    display: inline-block;
    width: 100px;
    height: 30px;
    color: #fff;
    cursor: pointer;
    text-align: center;
    background-color: #25d366;
    border: black;
    border-radius: 5px;

}

button.quick-help:hover {
    background-color: #1877f2;
}