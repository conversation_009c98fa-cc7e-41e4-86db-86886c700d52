<!-- Main Wrapper -->
<div class="main-wrapper" [ngClass]="{'bgImg':getUserType()==null}">

    <!-- Header -->
    <header class="header" [ngClass]="{'header-top-pad':!loginValue()}">
        <nav class="navbar navbar-expand-lg header-nav" [ngClass]="{'header_bg_color':getUserType()==null}">
            <div class="navbar-header">
                <a id="mobile_btn" href="javascript:void(0);">
                    <span class="bar-icon">
                        <span [ngStyle]="getUserType() == null ? { 'background-color' : '#ffff'} : ''"></span>
                        <span [ngStyle]="getUserType() == null ? { 'background-color' : '#ffff'} : ''"></span>
                        <span [ngStyle]="getUserType() == null ? { 'background-color' : '#ffff'} : ''"></span>
                    </span>
                </a>
                <a class="navbar-brand logo hide-logo pp" (click)="navigateion()">
                    <img id="top-logo" [src]="hospital.hospitalLogo" class="img-fluid" alt="Logo">
                </a>
            </div>
            <div class="main-menu-wrapper menu-content-top">
                <div class="menu-header">
                    <a class="menu-logo auth">
                        <img src="assets/img/logo.png" class="img-fluid" alt="Logo">
                    </a>
                    <a id="menu_close" class="menu-close" href="javascript:void(0);">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
                <ul class="main-nav" *ngIf="!loginValue()">
                    <li id="about" class="has-submenu auth-static">
                        <a routerLink="/about" style="color: #fff;" translate>
                            About
                        </a>
                    </li>
                    <li id="contact" class="has-submenu auth-static">
                        <a routerLink="/contact" style="color: #fff;">
                            Contact
                        </a>
                    </li>
                    <li id="help" class="has-submenu auth-static">
                        <a routerLink="/help" (click)="quickHelpPopup()" style="color: #fff;">
                            Help
                        </a>
                    </li>
                </ul>
            </div>
            <div *ngIf="getUserType() ==='Patient'" class="">
                <div class="main-menu-wrapper menu-content-top">
                    <div class="menu-header">
                        <a class="menu-logo">
                            <img src="assets/img/logo.png" class="img-fluid" alt="Logo">
                        </a>
                        <a id="menu_close" class="menu-close" href="javascript:void(0);">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                    <ul class="main-nav" *ngIf="loginValue()">
                        <li id="dashboard" class="has-submenu">
                            <a routerLink="/patient/dashboard" routerLinkActive="active">
                                <span translate>
                                    <h5
                                        [ngClass]="{'active-tab':getActiveLink()==='dashboard','inactive-tab':getActiveLink()!=='dashboard'}">
                                        Dashboard</h5>
                                </span>
                            </a>
                        </li>
                        <li id="search" class="has-submenu">
                            <a [routerLink]="['/patient/search',1,'consult_now=false']" routerLinkActive="active">
                                <span translate>
                                    <h5
                                        [ngClass]="{'active-tab':getActiveLink()==='search','inactive-tab':getActiveLink()!=='search'}">
                                        Book Appointment</h5>
                                </span>
                            </a>
                        </li>
                        <li id="profile" class="has-submenu">
                            <a [routerLink]="['/patient/profile']">
                                <span translate>
                                    <h5
                                        [ngClass]="{'active-tab':getActiveLink()==='profile','inactive-tab':getActiveLink()!=='profile'}">
                                        Profile Settings</h5>
                                </span>
                            </a>
                        </li>
                        <li id="doc-logout" class="has-submenu hide-logout">
                            <a (click)="logout()">
                                <span translate>
                                    <h5 class="inactive-tab">Logout</h5>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>

            </div>
            <div *ngIf="getUserType() ==='HospitalAdmin'" style="margin-left: 15%;">
                <div class="main-menu-wrapper menu-content-top">
                    <div class="menu-header">
                        <a class="menu-logo">
                            <img [src]="hospitalLogo" class="img-fluid" alt="Logo">
                        </a>
                        <a id="menu_close" class="menu-close" href="javascript:void(0);">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                    <ul class="main-nav" *ngIf="loginValue()">
                        <li id="hadashboard" class="has-submenu">
                            <a [routerLink]="['/hadashboard']">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hadashboard','inactive-tab':getActiveLink()!=='hadashboard'}">
                                        Dashboard</h5>
                                </span>
                            </a>
                        </li>
                        <li id="users" class="has-submenu">
                            <a [routerLink]="['/users']">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='users','inactive-tab':getActiveLink()!=='users'}">
                                        Users</h5>
                                </span>
                            </a>
                        </li>
                        <li id="assistant-profile" class="has-submenu">
                            <a [routerLink]="['/assistant-profile']">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='assistant-profile','inactive-tab':getActiveLink()!=='assistant-profile'}">
                                        Profile Setting</h5>
                                </span>
                            </a>
                        </li>
                        <li id="hadoctor-bookappointment" class="has-submenu">
                            <a (click)="hadoctorBookAppointment()">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hadoctor-bookappointment','inactive-tab':getActiveLink()!=='hadoctor-bookappointment'}">
                                        Book Appointment</h5>
                                </span>
                            </a>
                        </li>
                        <li class="has-submenu">
                            <a (click)="haReports()">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hadoctor-reports','inactive-tab':getActiveLink()!=='hadoctor-reports'}">
                                        Reports</h5>
                                </span>
                            </a>
                        </li>
                        <li id="detail" class="has-submenu">
                            <a (click)="hospitalDetails()" style="cursor: pointer">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hospital details','inactive-tab':getActiveLink()!=='hospital details'}">
                                        Hospital Details</h5>
                                </span>
                            </a>
                        </li>
                        <li id="detail" class="has-submenu">
                            <a (click)="hospitalSettings()" style="cursor: pointer">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hospital-settings','inactive-tab':getActiveLink()!=='hospital-settings'}">
                                        Settings</h5>
                                </span>
                            </a>
                        </li>
                        <li id="referal" class="has-submenu" *ngIf="userType=='DoctorAssistant' || userType=='HospitalAdmin' || userType=='Doctor'">
                            <a (click)="referral()" style="cursor: pointer">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='referral','inactive-tab':getActiveLink()!=='referral'}">
                                        Referral</h5>
                                </span>
                            </a>
                        </li>
                        <li id="doc-logout" class="has-submenu hide-logout">
                            <label>HospitalAdmin</label>
                            <a (click)="logout()">
                                <span translate>
                                    <h5 class="inactive-tab">Logout</h5>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- partner dashboard  -->
            <div *ngIf="getUserType() ==='Partner'" style="margin-left: 15%;">
                <div class="main-menu-wrapper menu-content-top">
                    <div class="menu-header">
                        <a class="menu-logo">
                            <img src="assets/img/logo.png" class="img-fluid" alt="Logo">
                        </a>
                        <a id="menu_close" class="menu-close" href="javascript:void(0);">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                    <ul class="main-nav" *ngIf="loginValue()">
                        <li id="dashboard" class="has-submenu">
                            <a [routerLink]="['/add-asst-pat']">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='dashboard','inactive-tab':getActiveLink()!=='dashboard'}">
                                        Center Dashboard</h5>
                                </span>
                            </a>
                        </li>
                        <li id="hadashboard" class="has-submenu">
                            <a [routerLink]="['/hadashboard']">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hadashboard','inactive-tab':getActiveLink()!=='hadashboard'}">
                                        Consultation Dashboard</h5>
                                </span>
                            </a>
                        </li>
                        <li id="hadoctor-bookappointment" class="has-submenu">
                            <a (click)="hadoctorBookAppointment()">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hadoctor-bookappointment','inactive-tab':getActiveLink()!=='hadoctor-bookappointment'}">
                                        Book Appointment</h5>
                                </span>
                            </a>
                        </li>
                        <li class="has-submenu">
                            <a (click)="haReports()">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hadoctor-reports','inactive-tab':getActiveLink()!=='hadoctor-reports'}">
                                        Reports</h5>
                                </span>
                            </a>
                        </li>
                        <li id="referal" class="has-submenu">
                            <a (click)="referral()" style="cursor: pointer">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='referral','inactive-tab':getActiveLink()!=='referral'}">
                                        Referral</h5>
                                </span>
                            </a>
                        </li>

                        <li id="doc-logout" class="has-submenu hide-logout">
                            <label>Partner</label>
                            <a (click)="logout()">
                                <span translate>
                                    <h5 class="inactive-tab">Logout</h5>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>

            </div>

            <div *ngIf="getUserType() ==='DoctorAssistant'" style="margin-left: 15%;">
                <div class="main-menu-wrapper menu-content-top">
                    <div class="menu-header">
                        <a class="menu-logo">
                            <img src="assets/img/logo.png" class="img-fluid" alt="Logo">
                        </a>
                        <a id="menu_close" class="menu-close" href="javascript:void(0);">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>

                    <ul class="main-nav" *ngIf="loginValue()">
                        <li id="dashboard" class="has-submenu">
                            <a [routerLink]="['/addpatient']">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='dashboard','inactive-tab':getActiveLink()!=='dashboard'}">
                                        DoctorAssistant Dashboard</h5>
                                </span>
                            </a>
                        </li>
                        <li id="hadashboard" class="has-submenu">
                            <a [routerLink]="['/hadashboard']">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hadashboard','inactive-tab':getActiveLink()!=='hadashboard'}">
                                        Consultation Dashboard</h5>
                                </span>
                            </a>
                        </li>
                        <li id="hadoctor-bookappointment" class="has-submenu">
                            <a (click)="hadoctorBookAppointment()">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hadoctor-bookappointment','inactive-tab':getActiveLink()!=='hadoctor-bookappointment'}">
                                        Book Appointment</h5>
                                </span>
                            </a>
                        </li>
                        <!-- <li class="has-submenu">
                            <a (click)="haReports()">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='hadoctor-reports','inactive-tab':getActiveLink()!=='hadoctor-reports'}">
                                        Reports</h5>
                                </span>
                            </a>
                        </li> -->
                        <li id="referal" class="has-submenu">
                            <a (click)="referral()" style="cursor: pointer">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='referral','inactive-tab':getActiveLink()!=='referral'}">
                                        Referral</h5>
                                </span>
                            </a>
                        </li>
                        <li id="doc-logout" class="has-submenu hide-logout">
                            <label>DoctorAssistant</label>
                            <a (click)="logout()">
                                <span translate>
                                    <h5 class="inactive-tab">Logout</h5>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <div *ngIf="getUserType() ==='Doctor'">
                <div class="main-menu-wrapper menu-content-top">
                    <div class="menu-header">
                        <a class="menu-logo">
                            <img src="assets/img/logo.png" class="img-fluid" alt="Logo">
                        </a>
                        <a id="menu_close" class="menu-close" href="javascript:void(0);">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                    <ul class="main-nav" *ngIf="loginValue()">
                        <li id="doc-dashboard" class="has-submenu" *ngIf="checkDoctorIsPublic()">
                            <!-- <a *ngIf="getApprovalstatus()" [routerLink]="['/doctor/dashboard']" routerLinkActive="active">
                                <span translate><h5 [ngClass]="{'active-tab':getActiveLink()==='dashboard','inactive-tab':getActiveLink()!=='dashboard'}">Dashboard</h5></span>
                            </a>
                            <h5 *ngIf="!getApprovalstatus()" class="empty-link" (click)="approveCheck()"> Dashboard</h5> -->
                            <a *ngIf="getPracticeStatus()&&getBankStatus()&&getApprovalstatus()"
                                [routerLink]="['/doctor/dashboard']" routerLinkActive="active">
                                <span translate>
                                    <h5
                                        [ngClass]="{'active-tab':getActiveLink()==='dashboard','inactive-tab':getActiveLink()!=='dashboard'}">
                                        Dashboard</h5>
                                </span>
                            </a>
                            <h5 *ngIf="!getPracticeStatus()&&!getBankStatus()&&!getApprovalstatus()" class="empty-link">
                                Dashboard</h5>
                        </li>
                        <li id="doc-dashboard" class="has-submenu" *ngIf="!checkDoctorIsPublic()">
                            <!-- <a *ngIf="getApprovalstatus()" [routerLink]="['/doctor/dashboard']" routerLinkActive="active">
                                <span translate><h5 [ngClass]="{'active-tab':getActiveLink()==='dashboard','inactive-tab':getActiveLink()!=='dashboard'}">Dashboard</h5></span>
                            </a>
                            <h5 *ngIf="!getApprovalstatus()" class="empty-link" (click)="approveCheck()"> Dashboard</h5> -->
                            <a *ngIf="getApprovalstatus()" [routerLink]="['/doctor/dashboard']"
                                routerLinkActive="active">
                                <span translate>
                                    <h5
                                        [ngClass]="{'active-tab':getActiveLink()==='dashboard','inactive-tab':getActiveLink()!=='dashboard'}">
                                        Dashboard</h5>
                                </span>
                            </a>
                            <h5 *ngIf="!getPracticeStatus()&&!getBankStatus()&&!getApprovalstatus()" class="empty-link">
                                Dashboard</h5>
                        </li>
                        <li id="practice-locations" class="has-submenu" *ngIf="checkDoctorIsPublic()">
                            <a *ngIf="getApprovalstatus()" [routerLink]="['/doctor/practice-locations']"
                                routerLinkActive="active">
                                <span translate>
                                    <h5
                                        [ngClass]="{'active-tab':getActiveLink()==='practice-locations','inactive-tab':getActiveLink()!=='practice-locations'}">
                                        Practice Locations</h5>
                                </span>
                            </a>
                            <h5 *ngIf="!getApprovalstatus()" class="empty-link" (click)="approveCheck()">Practice
                                Locations</h5>
                        </li>

                        <li id="earning-report" class="has-submenu" *ngIf="checkDoctorIsPublic()">
                            <!-- <a *ngIf="getApprovalstatus()" [routerLink]="['/doctor/earning-report']">
                                <span translate><h5 [ngClass]="{'active-tab':getActiveLink()==='earning-report','inactive-tab':getActiveLink()!=='earning-report'}">Reports</h5></span>
                            </a>
                            <h5 *ngIf="!getApprovalstatus()" class="empty-link" (click)="approveCheck()">Reports</h5> -->
                            <a *ngIf="getPracticeStatus()&&getBankStatus()&&getApprovalstatus()"
                                [routerLink]="['/doctor/earning-report']">
                                <span translate>
                                    <h5
                                        [ngClass]="{'active-tab':getActiveLink()==='earning-report','inactive-tab':getActiveLink()!=='earning-report'}">
                                        Reports</h5>
                                </span>
                            </a>
                            <h5 *ngIf="!getPracticeStatus()&&!getBankStatus()&&!getApprovalstatus()" class="empty-link">
                                Reports</h5>
                        </li>
                        <li id="bank-accounts" class="has-submenu" *ngIf="checkDoctorIsPublic()">
                            <!-- <a *ngIf="getApprovalstatus()" [routerLink]="['/doctor/bank-accounts']">
                                <span translate><h5 [ngClass]="{'active-tab':getActiveLink()==='bank-accounts','inactive-tab':getActiveLink()!=='bank-accounts'}">Bank Accounts</h5></span>
                            </a>
                            <h5 *ngIf="!getApprovalstatus" class="empty-link" (click)="approveCheck()">Bank Accounts</h5> -->
                            <a *ngIf="getPracticeStatus()&&getApprovalstatus()"
                                [routerLink]="['/doctor/bank-accounts']">
                                <span translate>
                                    <h5
                                        [ngClass]="{'active-tab':getActiveLink()==='bank-accounts','inactive-tab':getActiveLink()!=='bank-accounts'}">
                                        Bank Accounts</h5>
                                </span></a>
                            <p *ngIf="getActiveLink()!=='bank-accounts'&&getPracticeStatus()&&!getBankStatus()&&getApprovalstatus()"
                                style="color: white;">Bank A<img width="30px" height="30px" alt="not take"
                                    src="../../assets/img/hand_gif.gif"></p>
                            <h5 *ngIf="!getApprovalstatus()" class="empty-link">Bank Accounts</h5>
                        </li>
                        <li id="doc-profile" class="has-submenu">
                            <a [routerLink]="['/doctor/profile']">
                                <span translate>
                                    <h5
                                        [ngClass]="{'active-tab':getActiveLink()==='profile','inactive-tab':getActiveLink()!=='profile'}">
                                        Profile</h5>
                                </span>
                            </a>
                        </li>
                        <li id="referal" class="has-submenu">
                            <a (click)="referral()" style="cursor: pointer">
                                <span>
                                    <h5 translate
                                        [ngClass]="{'active-tab':getActiveLink()==='referral','inactive-tab':getActiveLink()!=='referral'}">
                                        Referral</h5>
                                </span>
                            </a>
                        </li>
                        <li id="doc-logout" class="has-submenu hide-logout">
                            <a (click)="logout()">
                                <span translate>
                                    <h5 class="inactive-tab">Logout</h5>
                                </span>
                            </a>
                        </li>

                    </ul>
                </div>
            </div>

            <ul class="nav header-navbar-rht" [ngClass]="{'header-right':!loginValue()}">
                <li class="nav-item img_position" *ngIf="loginValue()&&getUserType() !='HospitalAdmin'">
                    <a *ngIf="loginValue()" class="avatar avatar-sm mr-2">
                        <div class="spinner-border" role="status" *ngIf="!showProfilePic">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div *ngIf="showProfilePic&&getUserType()!==null" class="avatar avatar-sm mr-2">
                            <img class="avatar-img rounded-circle" [src]="getDrPicture()">
                        </div>
                    </a>
                    <h5 class="hideName" *ngIf="getUserType() === 'Patient' ">{{getUserName()}}&nbsp; &nbsp;</h5>
                    <h5 class="hideName" *ngIf="getUserType() === 'Doctor'">Dr.{{getUserName() !==' '?
                        getUser():getUserName()}}&nbsp; &nbsp;</h5>
                    <h5 class="hideName" *ngIf="getUserType() === 'PlatformAdmin'">{{getUserName()}}&nbsp; &nbsp;</h5>
                    <h5 class="hideName" *ngIf="getUserType() === 'HospitalAdmin'">{{getUserName()}}&nbsp; &nbsp;</h5>
                    <h5 class="hideName" *ngIf="getUserType() === 'DoctorAssistant'">{{getName()}}&nbsp; &nbsp;</h5>
                    <h5 class="hideName" *ngIf="getUserType() === 'Partner'">{{getName()}}&nbsp; &nbsp;</h5>
                </li>
                <!-- <li>
              <form>
                <input type="text" name="" class="email-id " placeholder="email">

                <input type="password" name=""  class="passwrd-id ml-2"  placeholder="password">

                <button type="button" name=""  value="Sign In" class="btn btn-sign btn-sm ml-2">Sign In</button>
              </form>
            </li> -->

                <li class="nav-item hideName" *ngIf="getUserType()!==null">
                    <a id="logout-link" class="nav-link header-login" (click)="logout()" translate>Logout </a>
                </li>

                <div class="signin-login" *ngIf="!loginValue() || getUserType()==null">
                    <form [formGroup]="loginForm">
                        <div class="col-md-12 header-mobile-top">
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <input type="email" name="" formControlName="email" class="email-id ml-3 mt-1"
                                        placeholder="Email">
                                </div>
                                <div class="col-md-4 mb-2">
                                    <input id="password1" type="password" name="" formControlName="password"
                                        class="passwrd-id ml-3 mt-1" placeholder="Password">
                                </div>
                                <div class="col-md-1 " style="max-width: 0;">
                                    <i class="far fa-eye" id="togglePassword"
                                        style="margin-top: 14px; margin-left: -30px; cursor: pointer;"
                                        (click)="passwordhideshow1()"></i>
                                </div>
                                <div class="col-md-2 mob-btn">
                                    <button type="submit" name="" (click)="onSubmit()"
                                        class="btn btn-sm btn-sign ml-3 mr-2 mt-1" [disabled]="loginForm.invalid">Sign
                                        In</button>
                                </div>
                            </div>
                            <div class="row">
                                <!-- <div class="col-md-4"></div> -->
                                <div class="col-md-4 offset-md-5 mob-forgt-link">
                                    <a class="forgt-pass" [routerLink]="['/forgot-password']"
                                        routerLinkActive="router-link-active" style="color:#fff">Forgot Password ?</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </ul>
        </nav>
    </header>
    <!-- /Header -->
</div>
<!-- /Main Wrapper -->