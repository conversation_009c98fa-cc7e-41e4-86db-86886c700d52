import { DoctorService } from '../doctor/doctor.service';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { AuthService } from './../auth/auth.service';
import { Component, OnInit, } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SharedService } from '../shared/shared.service';
import { ToastrService } from 'ngx-toastr';
import { PatientService } from '../patient/patient.service';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { HospitalService } from '../hospital-admin/hospital-admin.service';
import { HospitalModel } from '../hospital-admin/models/hospital.model';
import { Subscription } from "rxjs";

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css'],
})
export class HeaderComponent implements OnInit {
  showProfilePic: boolean;
  userName: any;
  loading: boolean;
  email: any;
  phoneNumber: any;
  verifyEmailOtpFormData: any;
  emailVerified: any;
  phoneVerified: any;
  helpPopup = false;
  doctorHelpVideo: any;
  public QuickHelpVideo: any;
  isPublicDoctor = true;
  doctorAprovalStatusPending: boolean;
  password: any;
  public practice: boolean;
  public bank: boolean;
  public schedule: string;
  public account: string;
  doctorProfilePictureUrl = 'assets/img/doctors/doctor-thumb-02.png';
  //consult lalith var
  isLoading = false;
  public doctorApproved = false;
  public userProfile = {
    username: null,
    profile_picture: null,
  };
  breadcrumbHeader: string;
  showSignout: boolean;
  adminName = '';
  changeHeight: boolean;
  public hederTagsId = [
    { id: 'dashboard', name: 'Dashboard' },
    { id: 'search', name: 'Book Appointment' },
    { id: 'profile', name: 'Profile settings' },
  ];
  loginForm: FormGroup;
  private subscriptions: Subscription[] = [];
  hospital: HospitalModel;
  urlType: any = 0;

  constructor(
    private translate: TranslateService,
    private userService: AuthService,
    private doctorService: DoctorService,
    private router: Router,
    private sharedService: SharedService,
    private notificationService: ToastrService,
    private patientService: PatientService,
    private _sanitizer: DomSanitizer,
    private activatedRoute: ActivatedRoute,
    private hospitalService: HospitalService,
  ) {
    this.hospital = new HospitalModel();
    const sb1 = this.hospitalService.currentHospitalDetails.pipe()
      .subscribe(value => {
        if (value && value.hospitalId != '') {
          this.hospital = Object.assign({}, value);
        } else {
          this.hospital = this.hospitalService.getHospitalDetails();
        }
      });
    this.subscriptions.push(sb1);

    const sb2 = this.sharedService.individualLogin$.pipe()
      .subscribe(data => {
        this.urlType = data;
      });
    this.subscriptions.push(sb2);
  }

  loginValue() {
    return this.userService.loggedIn();
  }
  getDrPicture() {
    return this.sharedService.getDrPicture();
  }
  getActiveLink() {
    return this.sharedService.getActiveLink();
  }
  getUserType() {
    return localStorage.getItem('user_type');
  }
  getUserName() {
    return this.sharedService.getUserName();
  }
  getName() {
    return localStorage.getItem('user_name');
  }

  getApprovalstatus() {
    const status = localStorage.getItem('profile_approved_status');
    if (status == 'Approved') {
      this.doctorApproved = true;
    }
    else {
      this.doctorApproved = false;
    }
    return this.doctorApproved;
  }

  checkDoctorIsPublic() {
    const hospital_id = localStorage.getItem('hospital_id');
    if (hospital_id != null) {
      this.isPublicDoctor = false;
    }
    else {
      this.isPublicDoctor = true;
    }
    return this.isPublicDoctor;
  }

  setPracticeStatus() {
    this.doctorService.getConsultingHours().subscribe(
      data => {
        // console.log(data);
        if (data == '') {
          this.practice = false;
          this.schedule = 'false'
          // console.log('data null');
          localStorage.setItem('practice', "false");
        }
        else {
          // console.log("data not null");
          this.practice = true;
          this.schedule = 'true'
          localStorage.setItem('practice', "true");
        }
      }
    );
    return this.practice;
  }

  setBankStatus() {
    this.doctorService.getBankAccountDetails().subscribe(
      data => {
        if (data['results'] == '') {
          // console.log("bank not found");
          this.bank = false;
          localStorage.setItem('bank', 'false');
        } else {
          // console.log("bank found");
          this.bank = true;
          localStorage.setItem('bank', 'true');
        }
      }
    );
    return this.bank;
  }

  getPracticeStatus() {
    var status = localStorage.getItem('practice');
    if (status === 'true') {
      var getpracticevalue = true;
    } else {
      var getpracticevalue = false;
    }
    return getpracticevalue
  }

  getBankStatus() {
    var status = localStorage.getItem('bank');
    if (status === 'true') {
      var getbankvalue = true;
    } else {
      var getbankvalue = false;
    }
    return getbankvalue
  }

  ngOnInit(): void {
    this.userService.setLogin(true);
    this.refreshTime();
    this.router.url;
    this.loginFormControl();
    this.adminName = localStorage.getItem('AdminName');
    this.QuickHelpVideo = this.activatedRoute.snapshot.data;
    this.getBankStatus();
    this.getPracticeStatus();
  }

  haReports() {
    const user_type = localStorage.getItem('user_type');
    localStorage.removeItem("report");

    if (user_type == 'HospitalAdmin') {
      localStorage.setItem("report", "hospital");
      // var uuid = localStorage.getItem("hstId")
      // this.router.navigate([`doctor-consultation-summary/${uuid}/view`]);
    } else if (user_type == 'Partner') {
      localStorage.setItem("report", "book_user");
      // var uuid = localStorage.getItem("current_user_uuid")
      // this.router.navigate([`doctor-consultation-summary/${uuid}/view`]);
    } else if (user_type == 'DoctorAssistant') {
      localStorage.setItem("report", "book_user");
      // var uuid = localStorage.getItem("current_user_uuid")
      // this.router.navigate([`doctor-consultation-summary/${uuid}/view`]);
    }
    this.router.navigate([`reports`]);
  }

  loginFormControl() {
    this.loginForm = new FormGroup({
      email: new FormControl(null, [Validators.required, Validators.email, Validators.minLength(8)]),
      password: new FormControl(null, [Validators.required, Validators.minLength(8)])
    })
  }

  logout() {
    const user_type = localStorage.getItem('user_type');
    const available_now = localStorage.getItem('available_now');
    if (available_now == 'true' && user_type == 'Doctor') {
      console.log('available_now', available_now);
      this.doctorService.doctorUnAvailableNow().subscribe((data) => {
      }, error => {
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
    }
    if (user_type == 'Doctor') {
      this.doctorService.setWebScoketMsg();
    } else if (user_type == 'Patient') {
      this.patientService.setWebScoketMsg();
    }
    this.userService.logout();
    this.sharedService.setPicture(this.doctorProfilePictureUrl);
    this.sharedService.setUserName(null);
    this.userService.setLogin(true);
    if (this.urlType == 2) {
      this.router.navigate(['/login1']);
    } else {
      this.router.navigate(['/login']);
    }
  }

  hospitalDetails() {
    const id = localStorage.getItem('hstId');
    this.router.navigate([`hospital-detail/${id}/view`]);
  }

  hospitalSettings() {
    this.router.navigate([`hospital-settings`]);
  }

  referral() {
    this.router.navigate([`referedAppointment`]);
  }

  users() {
    this.router.navigate([`users`]);
  }

  approveCheck() {
    if (!this.doctorApproved) {
      this.notificationService.warning('Available after approval');
    }
    this.doctorService.getDoctorProfile().subscribe(
      data => {
        this.doctorApproved = data['is_approved'];
      }
    );
  }

  refreshTime() {
    setTimeout(() => {
      this.showProfilePic = true;
    }, 2000)
  }

  searchDoctor() {
    const query = 'consult_now=false'
    const page = 1;
    this.router.navigate(['/patient/search/', page, query]);
  }

  getUser() {
    return this.sharedService.getUserName();
  }

  navigateion() {
    const loggedIn = this.userService.loggedIn();
    if (loggedIn) {
      const user_type = localStorage.getItem('user_type');
      if (user_type == 'Doctor') {
        this.router.navigate(['/doctor/dashboard']);
      } else if (user_type == 'Patient') {
        this.router.navigate(['/patient/dashboard']);
      } else if (user_type == 'HospitalAdmin') {
        this.router.navigate(['/hadashboard']);
      }
    }
  }

  onSubmit() {
    this.userService
      .login(this.loginForm.controls[`email`].value, this.loginForm.controls[`password`].value)
      .subscribe(
        (data) => {
          this.loading = true;
          this.email = data['email'];
          this.phoneNumber = data['phone'];
          this.password = this.loginForm.controls[`password`].value;
          this.emailVerified = data['email_verified'];
          this.phoneVerified = data['phone_verified'];
          if (this.email == null) {
            this.userService.getUserDetail().subscribe(
              (data) => {
                // console.log(data);
                const user_type = data['user_type'];
                localStorage.setItem('user_type', user_type);
                localStorage.setItem('user_name', data['username']);
                localStorage.setItem('current_user_uuid', data['uuid']);
                if (user_type != 'Doctor') {
                  this.sharedService.createWebsocketStream();
                  this.sharedService.updateIndividualLogin(0);
                }
                if (user_type == 'Patient') {
                  this.redirectappointment()
                  this.router.events.subscribe((val) => {
                    const nvigationEnd = val instanceof NavigationEnd;
                    if (!!nvigationEnd) {
                      location.reload();
                    }
                  });
                } else if (user_type == 'Doctor') {
                  this.redirectToDashboard();
                } else if (user_type == 'PlatformAdmin') {
                  this.router.navigate(['/platform-admin/dashboard']);
                } else if (user_type == 'HospitalAdmin') {
                  this.hospitalService.setHospitalDetails();
                  this.router.navigate(['/hadashboard']);
                } else if (user_type == 'Partner') {
                  this.hospitalService.setHospitalDetails();
                  this.router.navigate(['/add-asst-pat']);
                } else if (user_type == 'DoctorAssistant') {
                  this.hospitalService.setHospitalDetails();
                  this.router.navigate(['/addpatient']);
                }

                this.notificationService.success(
                  'LoggedIn Successfully',
                  'Med.Bot'
                );
              },
              (error) => {
                console.log(error);                
                this.notificationService.error(
                  `Internal server error`,
                  'Med.Bot'
                );
              }              
            );
          } else {
            this.userService.setLogin(false);
            this.loading = false;
            this.router.navigate(['/verifcation-pending', this.email, this.phoneNumber, this.password]);
          }
        },
        (error) => {
          console.log(error);
          this.notificationService.error(
            `${error['error']['non_field_errors']}`,
            'Med.Bot'
          );
        }
      );
  }

  redirectToDashboard() {
    this.setBankStatus();
    this.setPracticeStatus();
    const userType = localStorage.getItem('user_type');
    if (userType == 'Doctor') {
      this.doctorService.getDoctorProfile().subscribe(
        (data) => {
          if (data['user'].hospital != null) {
            this.isPublicDoctor = false;
            localStorage.setItem("hospital_id", data['user'].hospital);
          }
          const doctorApproved = data['is_approved'];
          const status = data['approval_request_status'];
          localStorage.setItem(
            'profile_approved_status',
            data['approval_request_status']
          );
          localStorage.setItem('profile_approval_status', doctorApproved);
          if (doctorApproved && status === 'Approved') {
            if (data['user'].hospital != null) {
              localStorage.setItem('available_now', 'true');
              this.doctorService.doctorAvailableNow().subscribe((data) => {
                this.router.navigate(['/doctor/dashboard']);
                this.notificationService.success(
                  'You are available for Instant Consultation'
                );
              });
            }
            // console.log(this.schedule);
            // console.log(this.account);
            if (this.practice == true && this.bank == true) {
              this.router.navigate(['/doctor/dashboard']);
              this.router.events.subscribe((val) => {
                const nvigationEnd = val instanceof NavigationEnd;
                if (!!nvigationEnd) {
                  location.reload();
                }
              });
            } else {
              this.router.navigate(['/doctor/practice-locations'])
              if (this.schedule == 'false') {
                this.notificationService.warning('Please create your schedule', 'Med.Bot');
              }
            }
          } else {
            this.router.navigate(['/doctor/profile']);
          }
          this.loading = false;
        },
        (error) => {
          this.loading = false;
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(
              `${error['statusText']}`,
              'Med.Bot'
            );
          } else {
            this.notificationService.error(
              `${error['statusText']}`,
              'Med.Bot'
            );
          }
          this.router.navigate(['/doctor/profile']);
        }
      );
    } else
      if (userType == 'Patient') {
        this.redirectappointment()
      } else
        if (userType == 'PlatformAdmin') {
          this.router.navigate(['/platform-admin/dashboard']);
        } else {
          this.loading = false;
        }
  }

  hadoctorBookAppointment() {
    const query = 'consult_now=false'
    const page = 1;
    const patientId = '';
    this.router.navigate(['/hadoctor-bookappointment/', page, query, patientId]);
  }

  redirectappointment() {
    const appointment = localStorage.getItem('appointment');
    const apturls = localStorage.getItem('appointmenturl');
    const conurls = localStorage.getItem('consulturl');
    const personalurl = localStorage.getItem('personalurl');
    // console.log(appointment);
    // console.log(apturls);
    if (appointment === 'true') {
      this.router.navigate([apturls]);
    } else
      if (appointment === 'false') {
        this.router.navigate([conurls]);
      } else {
        this.router.navigate(['/patient/dashboard/']);
      }
  }

  passwordhideshow1() {
    var x = (<HTMLInputElement>document.getElementById("password1")).type;
    if (x === "password") {
      (<HTMLInputElement>document.getElementById("password1")).type = "text";
      // console.log(x);
    } else {
      (<HTMLInputElement>document.getElementById("password1")).type = "password";
      // console.log(x);
    }
  }

  ngOnDestroy() {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

}
