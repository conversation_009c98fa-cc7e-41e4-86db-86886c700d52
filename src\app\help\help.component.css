.overlay {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    transition: opacity 500ms;
    visibility: visible;
    opacity: 1;
  }
  
  
  .helpPopup {
    margin: 70px auto;
    padding: 20px;
    background: #fff;
    border-radius: 5px;
    width: 54%;
    position: relative;
    transition: all 5s ease-in-out;
  }
  
  .helpPopup .close {
    position: absolute;
    top: 20px;
    right: 5px;
    transition: all 200ms;
    font-size: 30px;
    font-weight: bold;
    text-decoration: none;
    color: #333;
  }
  .helpPopup .close:hover {
    color: #d80606;
    cursor: pointer;
  }
  .helpPopup .content {
    height: 30%;
    width: 600px;  
    overflow: auto;
  }
