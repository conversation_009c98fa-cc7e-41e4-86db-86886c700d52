import { Component, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-help',
  templateUrl: './help.component.html',
  styleUrls: ['./help.component.css']
})
export class HelpComponent implements OnInit {
  helpPopup = true;
  doctorHelpVideo: any;
  QuickHelpVideo: string;
  constructor(
    private _sanitizer:DomSanitizer,
    private route:ActivatedRoute,
  ) { }

  ngOnInit(): void {

    this.QuickHelpVideo = this.route.snapshot.data['video'];
    this.doctorHelpVideo = this._sanitizer.bypassSecurityTrustResourceUrl(this.QuickHelpVideo);  
    // this.helpPopup = true;

  }

}
