<!--BreadCrumbs-->
<div class="breadcrumb-bar">
  <div class="container-fluid">
    <div class="row align-items-center">
      <div class="col-md-12 col-12">
        <nav aria-label="breadcrumb" class="page-breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="javascript:void(0);">{{ "Admin" | translate }}</a>
            </li>
            <li #listHeader class="breadcrumb-item active" aria-current="page">
              {{ "Profile Settings" | translate }}
            </li>
          </ol>
        </nav>
        <h2 #header class="breadcrumb-title">{{ "Profile Settings " | translate }}</h2>
      </div>
    </div>
  </div>
</div>
<!--BreadCrumbs Ends-->

<h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" (click)="back()"></i>Back</h5>
<div class="card">
  <div class="card-body">
      <!-- <h4 class="card-title" translate>Profile Picture</h4> -->
      <div class="row form-row">
          <div class="col-md-12">
              <div class="form-group">
                  <div class="change-avatar">
                      <div class="profile-img">
                          <img [src]="doctorProfilePictureUrl" alt="User Image">
                      </div>
                      <div class="upload-img">
                          <div class="change-photo-btn">
                              <span><i class="fa fa-upload"></i> {{ profileUpload ? ('Upload Photo'|translate): 'Uploading'|translate}}</span>
                              <input type="file" class="upload" id="profile-picture" [disabled]="disabledUploadPhotoBtn" (change)="doctorProfilePictureChange($event)" accept=".jpg, .png,">
                          </div>
                          <small class="form-text text-muted" translate>Allowed JPG, GIF or PNG. Max size of 2MB</small>
                      </div>
                  </div>
              </div>
          </div>
      </div>
      <form #_personalProfileForm="ngForm" (ngSubmit)="onSubmit()">
          <fieldset #profileFieldset [disabled]="disabled">
              <h4 class="card-title" translate>Personal Profile <i *ngIf="disabled" (click)="editProfile()" class="fa fa-edit"></i></h4>
              <div class="row form-row">
                  <div class="col-md-6">
                      <div class="form-group">
                          <label translate>Full Name<span class="text-danger">*</span></label>
                          <input id="fullname" class="form-control" type="text" name="username" [(ngModel)]="userData['username']" #_username="ngModel" readonly>
                      </div>
                  </div>
                  <div class="col-md-6">
                      <div class="form-group">
                          <label translate>Email <span class="text-danger">*</span></label>
                          <input id="email" type="email" name="email" [(ngModel)]="userData['email']" #_email="ngModel" class="form-control" readonly>
                      </div>
                  </div>
                  <div class="col-md-6">
                      <div class="form-group">
                          <label translate>First Name</label>
                          <input id="firstname" type="text" class="form-control" name="first_name" [(ngModel)]="userData['first_name']" #_first_name="ngModel">
                      </div>
                  </div>
                  <div class="col-md-6">
                      <div class="form-group">
                          <label translate>Middle Name</label>
                          <input id="middlename" type="text" class="form-control" name="middle_name" [(ngModel)]="userData['middle_name']" #_middle_name="ngModel">
                      </div>
                  </div>
                  <div class="col-md-6">
                      <div class="form-group">
                          <label translate>Last Name</label>
                          <input id="lastname" type="text" class="form-control" name="last_name" [(ngModel)]="userData['last_name']" #_last_name="ngModel">
                      </div>
                  </div>
                  <div class="col-md-6">
                      <div class="form-group">
                          <label translate>Phone Number<span class="text-danger">*</span></label>
                          <input id="phone" type="text" class="form-control" name="phone" [(ngModel)]="userData['phone']" #_phone="ngModel" readonly>
                      </div>
                  </div>
                  <div class="col-md-6">
                      <div class="form-group">
                          <label translate>Gender</label>
                          <select class="form-control select" name="gender" id="gender" [(ngModel)]="userData['gender']" #_gender="ngModel">
                    <option>Select</option>
                    <option value="Male" translate>Male</option>
                    <option value="Female" translate>Female</option>
                    <option value="Prefer not to answer" translate>Prefer not to answer</option>
                </select>
                      </div>
                  </div>
                  <div class="col-md-6">
                      <div class="form-group mb-0">
                          <label translate>Date of Birth<span class="text-danger">*</span></label>
                          <input type="date" id="dob" class="form-control" name="date_of_birth" [max]="maxDate" [(ngModel)]="userData['date_of_birth']" #_date_of_birth="ngModel" max>
                      </div>
                  </div>
              </div>
              <div class="form-group float-right">
                  <button *ngIf="!disabled" type="submit" id="save-btn" id="per-prof-btn" class="save-btn basic-data-btn" translate>Save</button>
                  <button *ngIf="!disabled" type="button" id="cancel-btn" (click)="cancelUpdate()" class="btn btn-secondary cancel-btn" translate>Cancel</button>

              </div>
              <!-- /Basic Information -->
          </fieldset>
      </form>
  </div>
  </div>
