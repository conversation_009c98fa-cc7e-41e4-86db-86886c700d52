
import { AuthService } from '../../auth/auth.service';
import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  ViewChild,
} from '@angular/core';
import * as Settings from '../../config/settings';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  NgForm,
  FormGroup,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
declare var $: any;
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import {SharedService  } from '../../shared/shared.service';
import * as moment from 'moment';
@Component({
  selector: 'app-admin-profile',
  templateUrl: './admin-profile.component.html',
  styleUrls: ['./admin-profile.component.css']
})
export class AdminProfileComponent implements OnInit {
  public changed = false;
  public profileUpload = true;
  public disabledUploadPhotoBtn = false;
  public personalProfileForm: NgForm;
  public userData = {};
  public disabled = true;
  doctorProfilePictureUrl = 'assets/img/doctors/doctor-thumb-02.png';
  public cancelbtn = false;
  profileFileSizeLarge = false;
  @Output() messageEvent: EventEmitter<string> = new EventEmitter<string>();
  userID: any;
  public isLoading = false;
  public maxDate = moment().format('YYYY-MM-DD');

  constructor( private userService: AuthService,
    private formBuilder: FormBuilder,
    private router: Router,
    public translate: TranslateService,
    private sharedService: SharedService,
    private notificationService: ToastrService,
    private location: Location) { }


  ngOnInit(): void {
    this.sharedService.setActiveLink('profile');
    this.getuserData();
  }

  getuserData() {
    this.userService.getUserDetail().subscribe(
      (data) => {
        this.userData = data;
        console.log(this.userData);
        if (this.userData['profile_picture'] !== null) {
          this.doctorProfilePictureUrl = this.userData['profile_picture'];
        }
        this.isLoading= false;
      },
      (error) => {
        console.log(error);
        this.isLoading= true;
      }
    );
  }

  onSubmit() {
    this.userService.updatePersonalProfile(this.userData).subscribe(
      (data) => {
        this.notificationService.success('Profile Update', 'Med.Bot');
        this.disabled = true;
      },
      (error) => {
        console.log(error);
        this.notificationService.error('Updation Error', 'Med.Bot');
      }
    );
  }

  onChange() {
    this.changed = true;
  }
  editProfile() {
    this.disabled = false;
  }
  cancelUpdate() {
    this.getuserData();
    this.disabled = true;
  }

  doctorProfilePictureChange(event) {
    const file = event.target.files;

    if (file.length > 0) {
      this.profileFileSizeLarge = false;
      const selectedProfilePicture = file[0];
      console.log(selectedProfilePicture);
      if (
        selectedProfilePicture.size < 2000000 &&
        (selectedProfilePicture.type === 'image/jpeg' ||
          selectedProfilePicture.type === 'image/jpg' ||
          selectedProfilePicture.type === 'image/png')
      ) {
        this.disabledUploadPhotoBtn = true;
        this.profileUpload = false;
        this.userService
          .updateDoctorProfilePicture(selectedProfilePicture)
          .subscribe(
            (data) => {
              this.userData = data;
              this.doctorProfilePictureUrl = this.userData['profile_picture'];
              this.sharedService.setPicture(this.doctorProfilePictureUrl);
              this.profileUpload = true;
              this.notificationService.success(
                'Profile Picture Update',
                'Med.Bot'
              );
            },
            (error) => {
              this.profileUpload = true;
              this.disabledUploadPhotoBtn = false;
              console.log(error);
              this.notificationService.error(
                'Error In Updating profile picture',
                'Med.Bot'
              );
            }
          );
      } else {
        this.profileFileSizeLarge = true;
      }
    } else {
      this.profileUpload = true;
      this.disabledUploadPhotoBtn = false;
      this.notificationService.error(
        'Please select  profile picture',
        'Med.Bot'
      );
    }
  }
  back() {
    this.location.back();
  }
}
