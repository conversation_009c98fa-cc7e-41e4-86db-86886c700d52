<h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left back" (click)="back()"></i>Back</h5>

<div class="content success-page-cont" *ngIf="!isLoading">
    <div class="container-fluid">

        <div class="row justify-content-center">
            <div class="col-lg-6">

                <!-- Success Card -->
                <div class="card success-card">
                    <div class="card-body">
                        <div class="success-cont">
                            <i class="fas fa-check"></i>
                            <h3>Appointment Booking Status</h3>
                            <p>Appointment booked with <strong>{{docotorName}}</strong><br> on <strong>{{appointmentStartDate|date:'mediumDate'}} &nbsp;{{appointmentStartDate|date:'hh:mm a'}} &nbsp;to {{appointmentEndDate |date:'h:mm a'}}</strong></p>
                            <div class="row">
                              <div class="col-md-2">

                              </div>
                              <div class="col-md-8">
                                <textarea type="text" name="notes"  class="form-control" placeholder="Enter any note to Dr before Consulting" [(ngModel)]="notes" maxlength="500"(input)="countNotesLetters($event)"></textarea>

                              </div>  
                              <div class="col-md-2">
                                <!-- <p>{{notesCount }}/500</p> -->
                              </div>

                            </div>
                            <a  class="btn btn-secondary view-inv-btn mt-1 mr-2" [routerLink]="['/hadashboard']" routerLinkActive="router-link-active" >Back</a>
                            <button  id="saveNotes"  type="button" class="btn btn-primary view-inv-btn mt-1" (click)="saveNotes()" [disabled]="notesCount >4 ? false:true">Save</button>

                        </div>
                    </div>
                </div>
                <!-- /Success Card -->

            </div>
        </div>

    </div>
</div>
<div *ngIf="isLoading" class="centered">
  <app-haloading-spinner></app-haloading-spinner>
</div>
<!-- /Page Content -->

