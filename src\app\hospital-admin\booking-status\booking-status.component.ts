import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PatientService } from '../../patient/patient.service';
import * as moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';

@Component({
  selector: 'app-booking-status',
  templateUrl: './booking-status.component.html',
  styleUrls: ['./booking-status.component.css'],
})
export class BookingStatusComponent implements OnInit {
  public appointmentId: string;
  public docotorName: string;
  public appointmentStartDate: any;
  public appointmentEndDate: any;
  public isLoading = false;
  public notes : string;
  notesCount: number;
  public consultationUuid: string;
  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private patientService: PatientService,
    private notificationService: ToastrService,
    private location: Location
  ) {}

  ngOnInit(): void {
    this.notesCount = 0;
    this.activatedRoute.params.subscribe((params) => {
      this.appointmentId = params.id;
      this.getAppointment();
    });
  }
  getAppointment() {
    this.isLoading = true;
    this.patientService.getAppointmentDataHospital(this.appointmentId).subscribe(
      (data) => {
        this.docotorName = data['doctor_user_json']['username'];
        this.appointmentStartDate = moment(data['start_datetime']);
        this.appointmentEndDate = moment(data['end_datetime']);
        this.consultationUuid = data['consultation_uuid'];
        this.isLoading = false;

      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if(status == 400){
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  back() {
    this.location.back();
  }

  saveNotes() {
    $('#saveNotes').prop('disabled', true);
    const data = {text:this.notes};
    let userType=localStorage.getItem('user_type');
    this.patientService.saveNotes(data, this.consultationUuid).subscribe(
      (data) => {
        this.notificationService.success('Notes added Successfully');
        this.redirectDashboard();
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if(status == 400){
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  countNotesLetters(event){
    const count = this.notes.length;
    this.notesCount = count;
  }
  redirectDashboard(){
    let userType= localStorage.getItem('user_type');
    if(userType=='Patient'){
      this.router.navigate(['/patient/dashboard']);
    }
    else if(userType == 'HospitalAdmin'){
      this.router.navigate(['/hadashboard']);
    }
    else if(userType=='DoctorAssistant'){
      this.router.navigate(['/addpatient']);
    }
    else if(userType=='Partner'){
      this.router.navigate(['/add-asst-pat']);
    }
  }


}
