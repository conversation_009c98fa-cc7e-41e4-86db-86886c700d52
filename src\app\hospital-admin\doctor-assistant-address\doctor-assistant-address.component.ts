import { HttpClient } from '@angular/common/http';
import { Component, OnInit, EventEmitter, Output } from '@angular/core';
import { FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { DoctorService } from '../../doctor/doctor.service';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import * as Settings from '../../config/settings';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-doctor-assistant-address',
  templateUrl: './doctor-assistant-address.component.html',
  styleUrls: ['./doctor-assistant-address.component.css'],
})
export class DoctorAssistantAddressComponent implements OnInit {
  @Output() profileCompletion: EventEmitter<string> = new EventEmitter<string>();
  @Output() profileCompletionStatus: EventEmitter<boolean> = new EventEmitter<boolean>();
  public homeAddressForm: FormGroup;
  public homeAddressFormArray: FormArray;
  homeAddressReadOnly = false;
  homeAddressEdit = true;
  public countryList = [];
  public homeAddressData = {};
  public readHomeAddress = false;
  selecthomeAddressEdit = 'true';
  public selectedHomeAddressIndex: number;
  doctorHomeAddressList: any[];
  showCancelBtn = false;
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  numberError = Settings.numberError;
  public doctorasst_uuid: any;
  public uuid = null;
  constructor(
    private httpClient: HttpClient,
    private doctorService: DoctorService,
    private translate: TranslateService,
    private notificationService: ToastrService,
    private route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(
      url => {
        console.log("url", url);
        this.doctorasst_uuid = url['uuid'];
        console.log(this.doctorasst_uuid);
      }
    );
    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this.addHomeFormControl(null);
    this.getDoctorAddress();
    this.doctorService.getCountryDetail().subscribe(
      (data) => {
        this.countryList = Object.values(data);
      },
      (error) => {
        console.log(error);
      }
    );
  }
  addHomeFormControl(data) {
    if (data === null) {
      this.homeAddressForm = new FormGroup({
        uuid: new FormControl(this.uuid),
        // practice_location:new FormControl (''),
        doctorassistant: new FormControl(this.doctorasst_uuid),
        address_type: new FormControl('Home', [Validators.required, Validators.maxLength(50)]),
        line_1: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        line_2: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        city_town_village: new FormControl(
          null,
          [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')],
        ),
        district: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        taluk: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        state: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        country: new FormControl('India', [Validators.required]),
        postal_code: new FormControl(null, [Validators.required, Validators.maxLength(10), Validators.pattern('[0-9 ]*')]),
      });
    } else {
      console.log('addrress', data);
      this.uuid = data.uuid;
      this.readHomeAddress = true;
      this.homeAddressForm = new FormGroup({
        uuid: new FormControl(this.uuid),
        doctorassistant: new FormControl(this.doctorasst_uuid),
        address_type: new FormControl('Home', [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        line_1: new FormControl(data.line_1, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        line_2: new FormControl(data.line_2, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        city_town_village: new FormControl(
          data.city_town_village,
          [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')],
        ),
        district: new FormControl(data.district, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        taluk: new FormControl(data.taluk, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        state: new FormControl(data.state, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        country: new FormControl(data.country, [Validators.required]),
        postal_code: new FormControl(data.postal_code, [Validators.required, Validators.maxLength(10), Validators.pattern('[0-9]*')]),
      });
    }
  }


  saveHomeAddress() {
    this.doctorService.saveAssistAddress(this.homeAddressForm.value, this.doctorasst_uuid, this.uuid).subscribe(
      (data) => {

        if (this.homeAddressForm.controls['uuid'].value) {
          this.notificationService.success('Home address updated');
        } else {
          this.notificationService.success('Home address added');
        }

        this.readHomeAddress = true;
        data = [data];
        const doctorAddress = Object.values(data);
        console.log('doctorAddress0,', doctorAddress);
        this.doctorHomeAddressList = doctorAddress.filter(
          (obj) => obj.address_type === 'Home'
        );
        this.profileCompletion.emit();
        this.profileCompletionStatus.emit(true);
      },
      (err) => {
        this.notificationService.error(
          'Home Address Updation Failed',
          'Med.Bot'
        );
      }
    );
  }

  trackFn(index) {
    return index;
  }
  editHomeAddress(i): void {
    this.readHomeAddress = false;
  }
  cancelHomeAddress(): void {
    if (this.doctorHomeAddressList.length > 0) {
      this.addHomeFormControl(this.doctorHomeAddressList[0]);
    } else {
      this.addHomeFormControl(null)
    }


  }


  getDoctorAddress() {
    this.doctorService.getAssistAddressDetail(this.doctorasst_uuid).subscribe(
      (data) => {
        data = data['results'];
        const doctorAddress = Object.values(data);
        // this.doctorHomeAddressList = doctorAddress.filter(
        //   (obj) => obj.address_type === 'Home'
        // );
        this.doctorHomeAddressList = doctorAddress;
        console.log(doctorAddress);
        if (this.doctorHomeAddressList.length > 0) {
          this.addHomeFormControl(this.doctorHomeAddressList[0]);
        }
      },
      (err) => {
        console.log('err', err);
      }
    );
  }
}
