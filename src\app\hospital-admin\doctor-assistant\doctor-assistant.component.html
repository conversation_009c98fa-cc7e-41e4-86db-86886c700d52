<!-- Page Content -->
<div class="content">
	<h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" (click)="back()"></i>Back</h5>
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-8 offset-md-2">

				<!-- Register Content -->
				<div class="account-content">
					<div class="row align-items-center justify-content-center">

						<div class="col-md-12 col-lg-6 login-right">
							<div class="login-header">
								<h3>Add Doctor Assistant </h3>
							</div>

							<!-- Register Form -->
							<form action="doctor-dashboard.html" [formGroup]="doctorAssitantForm">
								<div class="form-group form-focus">
									<input type="text" class="form-control floating" id="username"
										formControlName="username">
									<label class="focus-label">Contact Name</label>
									<div class="text-danger"
										*ngIf="doctorAssitantForm.get('username').errors?.required && (doctorAssitantForm.get('username').dirty || doctorAssitantForm.get('username').touched)">
										Contact Name is required.
									</div>
								</div>
								<div class="form-group form-focus">
									<input type="text" class="form-control floating" id="email" formControlName="email">
									<label class="focus-label">Contact Email</label>
									<div class="text-danger"
										*ngIf="doctorAssitantForm.get('email').errors?.required && (doctorAssitantForm.get('email').dirty || doctorAssitantForm.get('email').touched)">
										email is required.
									</div>
									<div class="text-danger" *ngIf="doctorAssitantForm.get('email').errors?.email">
										Please enter a valid email address.
									</div>
								</div>
								<div class="form-group form-focus">
									<input type="password" class="form-control floating" id="password"
										formControlName="password1">
									<label class="focus-label">Password</label>
									<div class="text-danger"
										*ngIf="doctorAssitantForm.get('password1').errors?.required && (doctorAssitantForm.get('password1').dirty || doctorAssitantForm.get('password1').touched)">
										Password is required.
									</div>
								</div>
								<div class="form-group form-focus">
									<input type="text" class="form-control floating" id="number" formControlName="phone"
										[max]="10" [min]="10">
									<label class="focus-label">Contact Number</label>
									<div class="text-danger"
                                        *ngIf="doctorAssitantForm.get('phone').errors?.required && (doctorAssitantForm.get('phone').dirty || doctorAssitantForm.get('phone').touched)">
                                        Contact Number is required.
                                    </div>
								</div>
								<!-- <div class="form-group form-focus">
												<input type="password" class="form-control floating">
												<label class="focus-label">User Id</label>
											</div> -->

								<button id="save-assistant" class="btn btn-primary btn-block btn-lg login-btn"
									type="submit" [disabled]="!doctorAssitantForm.valid"
									(click)="saveHospitalAdmin()">{{uploadingData ?'Uploading':'Save'}}</button>


							</form>
							<!-- /Register Form -->

						</div>
					</div>
				</div>
				<!-- /Register Content -->

			</div>
		</div>

	</div>

</div>
<!-- /Page Content -->