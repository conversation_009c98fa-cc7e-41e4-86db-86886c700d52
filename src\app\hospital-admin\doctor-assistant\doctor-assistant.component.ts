import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { HospitalService } from '../hospital-admin.service';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
@Component({
  selector: 'app-doctor-assistant',
  templateUrl: './doctor-assistant.component.html',
  styleUrls: ['./doctor-assistant.component.css'],
})
export class DoctorAssistantComponent implements OnInit {
  public doctorAssitantForm: FormGroup;
  hospitalId: string;
  uploadingData = false;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private hospitalService: HospitalService,
    private notificationService: ToastrService,
    private location: Location
  ) { }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe((parms) => {
      this.hospitalId = parms['id'];
    });
    this.addHospitalAdminFormControl();
  }

  addHospitalAdminFormControl() {
    this.doctorAssitantForm = new FormGroup({
      email: new FormControl('', [Validators.required, Validators.email]),
      username: new FormControl('', Validators.required),
      phone: new FormControl('', Validators.required),
      password1: new FormControl('', Validators.required),
      // user_type: new FormControl('Doctor', Validators.required),
      user_type: new FormControl('DoctorAssistant', Validators.required),
    });
  }

  saveHospitalAdmin() {
    const user_type = localStorage.getItem('user_type');
    this.uploadingData = true;
    this.hospitalService
      .createHospitalAdmin(this.hospitalId, this.doctorAssitantForm.value)
      .subscribe(
        (data) => {
          this.notificationService.success('Doctor Assistant Added Successfully ', 'Med.Bot');

          if (user_type == 'HospitalAdmin') {
            this.router.navigate(['/users']);
          } else if (user_type == 'Partner') {
            this.router.navigate(['/add-asst-pat']);
          }
        },
        (error) => {
          this.uploadingData = false;
          const status = error['status'];
          if (status == 400) {
            if (error.error.error_details.validation_errors) {
              let messages = '';
              for (let i = 0; i < Object.keys(error.error.error_details.validation_errors).length; i++) {
                const key = Object.keys(error.error.error_details.validation_errors)[i];
                messages = messages + ' ' + key + ': ' + error.error.error_details.validation_errors[key];
              }

              this.notificationService.error(
                `${messages}`,
                'Med.Bot'
              );
            } else {
              this.notificationService.error(
                `${error.error['error_message']}`,
                'Med.Bot'
              );
            }
          } else {
            this.notificationService.error('Internal server error', 'Med.Bot');
            console.log(error);
          }
        }
      );
  }

  back() {
    this.location.back();
  }
}
