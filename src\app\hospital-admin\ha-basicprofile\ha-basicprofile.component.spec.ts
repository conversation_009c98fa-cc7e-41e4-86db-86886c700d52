import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { HaBasicprofileComponent } from './ha-basicprofile.component';

describe('HaBasicprofileComponent', () => {
  let component: HaBasicprofileComponent;
  let fixture: ComponentFixture<HaBasicprofileComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ HaBasicprofileComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HaBasicprofileComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
