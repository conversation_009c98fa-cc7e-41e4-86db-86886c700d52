import { ActivatedRoute } from '@angular/router';
import { PlatformService } from './../../platform/platform.service';
import { DoctorService } from 'src/app/doctor/doctor.service';
import { ToastrService } from 'ngx-toastr';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-ha-basicprofile',
  templateUrl: './ha-basicprofile.component.html',
  styleUrls: ['./ha-basicprofile.component.css']
})
export class HaBasicprofileComponent implements OnInit {
  @Output()  profileCompletion: EventEmitter<string> = new EventEmitter<string>();

  public profileDataForm: FormGroup;
  public practicingSince: any;
  public Year = [];
  public languages = [];
  public specialityMaster = {};
  @Output() system_of_medicine: EventEmitter<string> = new EventEmitter<string>();
  public systemOfMedicine = [];
  public practiceType = [{ name: 'Own Practice', value: 'own_practice' }, { name: 'Hospital Based', value: 'hospital_based' }];
  public time = [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60];
  public saving = false;
  public formDisabled = false;
  public doc_uuid = '';
  specificSpeciality: any = [];
  hospital_uuid: any;
  department = {};
  specificDepartment: any = [];
  specificDept: any = {};

  constructor(
    private formBuilder: FormBuilder,
    private doctorService: DoctorService,
    private platformService: PlatformService,
    private notificationService: ToastrService,
    private translate: TranslateService,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    const lang = localStorage.getItem('pageLagnuage');
    this.hospital_uuid = localStorage.getItem('hstId');
    this.translate.use(lang);

    this.profileDataForm = this.formBuilder.group({
      years_of_experience: [null, Validators.required],
      languages: [[], Validators.required],
      speciality: [[], Validators.required],
      department: [[], Validators.required],
      system_of_medicine: [null, Validators.required],
      practice_types: [[], Validators.required],
      consultation_duration: [null]
    });

    this.generateYear();
    this.getSystemOfMedicine();
    this.getDepartmentData();
    this.getSpecialityData();

    this.doctorService.getDoctorLanguages().subscribe(
      data => {
        this.languages = Object.values(data);
      },
      error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );

    this.route.params.subscribe(
      url => {
        this.doc_uuid = url['uuid'];
        this.formDisable();
      }
    );
  }

  formDisable() {
    this.profileDataForm.reset();
    this.renderProfileData();
    this.formDisabled = true;
  }

  renderProfileData() {
    this.platformService.getDoctorDetails(this.doc_uuid).subscribe(
      data => {
        this.system_of_medicine.emit(data['system_of_medicine']);
        if (data['years_of_experience'] == null) {
          this.formDisabled = false;
        }
        else {
          this.onChangeSystemOfMedicine(data['system_of_medicine']);
          this.profileDataForm.setValue(
            {
              years_of_experience: data['years_of_experience'],
              consultation_duration: data['consultation_duration'],
              system_of_medicine: data['system_of_medicine'],
              languages: Object.values(data['languages']),
              practice_types: Object.values(data['practice_types']),
              speciality: Object.values(data['speciality']),
              department: Object.values(data['department']),
            });
          this.formDisabled = true;
        }
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }

  generateYear() {
    const startingYear = 0;
    const currentYear = new Date().getFullYear();
    for (let i = startingYear; i <= 100; i++) {
      this.Year.push(i);
    }
  }

  trackFn(index) {
    return index;
  }

  formEnable() {
    this.formDisabled = false;
  }

  getSystemOfMedicine() {
    this.doctorService.getSystemOfMedicine().subscribe(
      (data) => {
        this.systemOfMedicine = [];
        this.systemOfMedicine = Object.keys(data);
        this.specificDept = data;
      },
      (error) => {
        this.notificationService.error('Internal server error', 'Med.Bot');
        console.log(error);
      }
    );
  }

  getDepartmentData() {
    this.doctorService.getParticularHospitalDepartment(this.hospital_uuid).subscribe(
      (data) => {
        this.department = data;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  async getSpecialityData() {
    try {
      const data = await this.doctorService.getParticularHospitalSpeciality1(this.hospital_uuid);
      this.specialityMaster = data;
    } catch (error) {
      console.error('Error fetching specialties', error);
    }
    // this.doctorService.getParticularHospitalSpeciality(this.hospital_uuid).subscribe(
    //   (data) => {
    //     this.specialityMaster = data;
    //   },
    //   (error) => {
    //     console.log(error);
    //     const status = error['status'];
    //     if (status == 400) {
    //       this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
    //     }
    //     else {
    //       this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
    //     }
    //   }
    // );
  }

  onChangeSystemOfMedicine(data: any, value?: string) {
    if (value === 'changes') {
      this.profileDataForm.get('speciality').setValue({ value: '', label: '', approved: false });
      this.profileDataForm.get('department').setValue({ dept_code: '', value: '', department_uuid: '', medical_system: '', approved: false });

      this.profileDataForm.get('speciality')?.markAsTouched();
      this.profileDataForm.get('department')?.markAsTouched();

      this.profileDataForm.get('speciality')?.setErrors({ required: true });
      this.profileDataForm.get('department')?.setErrors({ required: true });
    }
    this.specificDepartment = [];
    this.specificSpeciality = [];

    if (data) {
      const departmentArray = [];
      this.specificDept[data].departments.map(item => {
        return {
          dept_code: item.dept_code,
          value: item.value,
          department_uuid: item.department_uuid,
          medical_system: item.medical_system,
          approved: false,
        };
      }).forEach(item => departmentArray.push(item));;
      this.specificDepartment = departmentArray;
      this.getSpecialityData();
      this.cdr.detectChanges();
      Object.keys(this.specialityMaster).forEach(category => {
        if (category == data) {
          const specialityArray = [];
          this.specialityMaster[category].map(item => {
            return {
              value: item.spec_code,
              label: item.spec_name,
              approved: false,
            };
          })
            .forEach(item => specialityArray.push(item));
          this.specificSpeciality = specialityArray;
        }
      });
    }
    this.system_of_medicine.emit(data);
  }

  onSubmit() {
    this.saving = true;
    const profileData = {
      'years_of_experience': this.profileDataForm.value.years_of_experience,
      'languages': this.profileDataForm.value.languages,
      'system_of_medicine': this.profileDataForm.value.system_of_medicine,
      'department': this.profileDataForm.value.department,
      'practice_types': this.profileDataForm.value.practice_types,
      'speciality': this.profileDataForm.value.speciality
    }
    this.platformService.updateDoctorProfile(this.doc_uuid, profileData).subscribe(
      data => {
        this.formDisable();
        this.profileCompletion.emit();
        this.notificationService.success('Basic Profile Data Updated', 'Med.Bot');
        this.saving = false;
        this.formDisabled = true;
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
}
