input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}
.fas{
  color: #20c0f3;
  cursor: pointer;

}
.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  transition: opacity 500ms;
  visibility: visible;
  opacity: 1;
}


.addNewPopup {
  margin: 70px auto;
  padding: 20px;
  background: #fff;
  border-radius: 5px;
  min-width: 25%;
  max-width: fit-content;
  position: relative;
  transition: all 5s ease-in-out;
}

.addNewPopup h2 {
  margin-top: 0;
  color: #333;
  font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
}
.addNewPopup .close {
  position: absolute;
  top: 20px;
  right: 30px;
  transition: all 200ms;
  font-size: 30px;
  font-weight: bold;
  text-decoration: none;
  color: #333;
}
.addNewPopup .close:hover {
  color: #d80606;
  cursor: pointer;
}
.addNewPopup .content {
  max-height: 30%;
  overflow: auto;
}
