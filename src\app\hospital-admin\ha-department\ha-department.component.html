<!-- Page Content -->
<div class="content">
    <h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" (click)="back()"></i>Back</h5>
    <div class="container-fluid">

        <div class="row">
            <div class="col-md-8 offset-md-2">

                <!-- Register Content -->
                <div class="account-content">
                    <div class="row align-items-center justify-content-center">

                        <div class="col-md-12 col-lg-6 login-right">
                            <div class="login-header">
                                <h3>Department </h3>
                            </div>

                            <!-- Register Form -->
                            <form [formGroup]="hospitalAdminForm">

                                <div class="form-group">
                                    <label for="system-of-medicine"  translate>System of Medicine</label>
                                    <button id="save-hospital-admin" class="btn btn-primary login-btn ml-2 mb-2" type="submit"
                                        (click)="showHidePopup(1,1)">{{'+'}}</button>
                                    <ng-select formControlName="medicine_type" [items]="systemOfMedicine"
                                        (change)="getSpecificDepartmentData($event) " (clear)="onClearClick()">
                                    </ng-select>
                                </div>
                                <div class="form-group">
                                    <label for="department" translate>Department</label>
                                    <button id="save-hospital-admin" class="btn btn-primary login-btn ml-2 mb-2" type="submit"
                                        (click)="showHidePopup(2,1)">{{'+'}}</button>
                                    <ng-select formControlName="dept_name" [items]="specificDepartment"
                                        bindLabel="value" [addTag]="true" addTagText="add new department"
                                        (clear)="onClearClick()">
                                    </ng-select>
                                </div>

                                <!-- <div class="form-group form-focus">
                                    <input type="text" class="form-control floating" id="departmentname"
                                        formControlName="dept_name">
                                    <label class="focus-label"> Department Name</label>
                                </div>

                                <div class="form-group form-focus">
                                    <input type="text" class="form-control floating" id="departmentcode"
                                        formControlName="dept_code" [max]="10" [min]="10">
                                    <label class="focus-label">Department Code</label>
                                </div> -->

                                <button id="save-hospital-admin" class="btn btn-primary btn-block btn-lg login-btn"
                                    type="submit" [disabled]="!hospitalAdminForm.valid"
                                    (click)="createNewDept()">{{uploadingData ?'Uploading':'Save'}}</button>


                            </form>
                            <!-- /Register Form -->

                        </div>
                    </div>
                </div>
                <!-- /Register Content -->






            </div>
        </div>

    </div>

</div>
<!-- /Page Content -->
<!-- add new department popup starts -->
<div class="overlay" *ngIf="addNewPopup">
    <div class="addNewPopup">
        <h3>Add </h3>
        <a class="close" (click)="addNewPopup = false">&times;</a>
        <div class="content justify-content ">
            <form [formGroup]="createHospitalAdminForm">
                <div class="form-group " *ngIf="onlySOM">
                    <label class="">System of Medicine</label>
                    <input type="text" name="newSOM" class="form-control form-control-height" required formControlName="newSOM" />
                </div>
                <div *ngIf="!onlySOM">
                    <div class="form-group " >
                        <label class="focus-label floating   ">System of Medicine</label>
                        <ng-select formControlName="dept_name" class=" mr-2" [items]="systemOfMedicine" bindLabel="value"
                            [addTag]="true" addTagText="add new department" (change)="selectSOM($event)">
                        </ng-select>
                    </div>
                    <div class="form-group" >
                        <label class="focus-label floating ">Department</label>
                        <input type="text"   name="new" class=" form-control form-control-height" required formControlName="newDept" />
                    </div>
                    <div class="form-group">
                        <label class="focus-label floating "> Acronym</label>
                        <input type="text"   name="new" class="form-control form-control-height" required formControlName="newDepCode" />
                    </div>
                </div>
                
                <div class="form-group">
                    <button id="save-hospital-admin" class="btn btn-primary btn-block btn-lg login-btn" type="submit"
                        (click)="createNewDept()"
                        [disabled]="createHospitalAdminForm.get('newSOM').hasError('required')"
                        *ngIf="onlySOM">Save</button>
                    <button id="save-hospital-admin" class="btn btn-primary btn-block btn-lg login-btn" type="submit"
                        (click)="createNewDept()" [disabled]="!createHospitalAdminForm.valid"
                        *ngIf="!onlySOM">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- add new department popup ends-->