import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { HospitalService } from '../hospital-admin.service';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import { DoctorService } from 'src/app/doctor/doctor.service';
import { log } from 'console';
import { event } from 'jquery';
@Component({
  selector: 'app-ha-department',
  templateUrl: './ha-department.component.html',
  styleUrls: ['./ha-department.component.css'],
})
export class HaDepartmentComponent implements OnInit {
  public hospitalAdminForm: FormGroup;
  // doctorList: any = [];
  //  departmentList: any = [];
  public doctorSerialNumber = 0;
  departmentisLoading: boolean;
  hospitalId: string;
  uploadingData = false;
  public systemOfMedicine = [];
  specificDepartment: any = [];
  department = {};
  addNewPopup: boolean = false;
  public createHospitalAdminForm: FormGroup;
  onlySOM: boolean=true;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private hospitalService: HospitalService,
    private notificationService: ToastrService,
    private doctorService: DoctorService,
    private location: Location
  ) { }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe((parms) => {
      this.hospitalId = parms['id'];
    });
    this.addHospitalAdminFormControl();
    this.getDepartmnetData();
  }
  getSpecificDepartmentData(data) {
    console.log(data);
    this.hospitalAdminForm.controls[`dept_name`].setValue([]);
    if (data != '') {
      this.specificDepartment = this.department[data].departments;
    }
    else {
      this.specificDepartment = [];
    }
    // if (data == 'Allopathy') {
    //   this.specificDepartment = this.department['Allopathy'].departments;
    //   console.log(this.specificDepartment);
    // } else if (data == 'Ayurveda') {
    //   this.specificDepartment = this.department['Ayurveda'].departments;
    // } else if (data == 'Dental') {
    //   this.specificDepartment = this.department['Dental'].departments;
    // } else if (data == 'Homoeopathy') {
    //   this.specificDepartment = this.department['Homoeopathy'].departments;
    // }
    // else if (data == 'Siddha') {
    //   this.specificDepartment = this.department['Siddha'].departments;
    // }
    // else if (data == 'Unani') {
    //   this.specificDepartment = this.department['Unani'];
    // } else {
    //   this.specificDepartment = [];
    // }
  }
  getDepartmnetData() {
    this.doctorService.getDepartment(this.hospitalId).subscribe(
      (data) => {
        console.log("data : ", data);
        this.department = data;
        const keysArray = Object.keys(data);
        this.systemOfMedicine = keysArray;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  addHospitalAdminFormControl() {
    this.hospitalAdminForm = new FormGroup({
      medicine_type: new FormControl('', Validators.required),
      dept_name: new FormControl('', Validators.required),
      dept_code: new FormControl(''),
      code: new FormControl(''),
      value: new FormControl(''),
      hospital: new FormControl(this.hospitalId),
      // user_type: new FormControl('Department', Validators.required),
    });
    this.createHospitalAdminForm = new FormGroup({
      newSOM: new FormControl('', Validators.required),
      newDept: new FormControl('', Validators.required),
      newDepCode: new FormControl('', Validators.required),
      code: new FormControl(''),
      value: new FormControl(''),
      hospital: new FormControl(this.hospitalId),
      // user_type: new FormControl('Department', Validators.required),
    });
  }

  saveHospitalAdmin() {
    var isnew = 1;
    for (let i = 0; i < this.specificDepartment.length; i++) {
      if (this.hospitalAdminForm.get('dept_name').value == this.specificDepartment[i]) {
        this.hospitalAdminForm.get('dept_code').setValue(this.specificDepartment[i].code);
        this.hospitalAdminForm.get('dept_name').setValue(this.specificDepartment[i].value);
        console.log(this.specificDepartment[i].code);
        isnew = 0;
        break;
      }
    }
    if (isnew) {
      console.log((this.hospitalAdminForm.get('dept_name').value).value);
      var dept = (this.hospitalAdminForm.get('dept_name').value).value;
      var dept_name = dept + ' Department'
      this.hospitalAdminForm.get('dept_code').setValue(dept);
      this.hospitalAdminForm.get('dept_name').setValue(dept_name);
      this.hospitalAdminForm.get('code').setValue(dept);
      this.hospitalAdminForm.get('value').setValue(dept_name);
      this.hospitalService
        .createHospitalNewDept(this.hospitalAdminForm.value)
        .subscribe(
          (data) => {
            this.notificationService.success(`${data['message']}`, 'Med.Bot');
          },
          (err) => {
            this.notificationService.error(err.error.error_message, 'Med.Bot');
            console.log(err);
            this.uploadingData = false;
          }
        );
    }
    console.log(this.hospitalAdminForm.value);
    this.uploadingData = true;
    this.hospitalService
      .createHospitalDept(this.hospitalId, this.hospitalAdminForm.value)
      .subscribe(
        (data) => {
          this.notificationService.success(
            'Department Added Successfully For Hospital',
            'Med.Bot'
          );

          this.router.navigate(['/users']);
        },
        (err) => {
          this.notificationService.error(err.error.error_message, 'Med.Bot');
          console.log(err);
          this.uploadingData = false;
        }
      );
  }
  back() {
    this.location.back();
  }
  createNewDept() {
    this.addNewPopup = false;
    const formData = new FormData;
    formData.append('medical_system_name', this.createHospitalAdminForm.value.newSOM)
    formData.append('department_code', this.createHospitalAdminForm.value.newDepCode)
    formData.append('department_value', this.createHospitalAdminForm.value.newDept)
    formData.append('hospital_id', this.hospitalId)
    this.hospitalService
      .createHospitalDept(formData)
      .subscribe(
        (data) => {
          this.notificationService.success(`${data['message']}`, 'Med.Bot');
          this.router.navigate(['/users']);
        },
        (err) => {
        const status = err['status'];
          if(status == 400){
            this.notificationService.error(`${err.error['error_message']}`, 'Med.Bot');
            }
          else{
            this.notificationService.error(`${err.error['message']}`, 'Med.Bot');
          }
          console.log(err);
          this.uploadingData = false;
        }
      );
  }
  showHidePopup(val: any, result: any) {
    let visible = (result == 1 ? true : false);
    // console.log((this.hospitalAdminForm.get('dept_name').value).value);
    console.log((this.hospitalAdminForm.controls[`dept_name`].value));
    switch (val) {
      case 1:
        this.addNewPopup = visible;
        this.onlySOM=true;
        break;
      case 2:
        this.addNewPopup = visible;
        this.onlySOM=false;
        this.createHospitalAdminForm.get('newSOM').setValue((this.hospitalAdminForm.controls[`medicine_type`].value));
        break;
    }
  }
  onClearClick() {
    this.specificDepartment = [];
  }
  selectSOM(data){
    this.createHospitalAdminForm.get('newSOM').setValue(data);
  }
}
