.file-upload-icon {
  font-size: 27px;
  color: #20C0F4;
  margin-bottom: -12px;
  /* marr */
}

.fa-edit {
  color: #20c0f3;
  cursor: pointer;
}

.basic-data-btn {
  float: right;
}

.valid-upto-reg {
  width: 75%;
}

.col-lg-2.reg-file-up {
  margin-left: -50px
}


/* .file-name {
  text-align: center;
} */

.btn-primary {
  background-color: #20c0f3 !important;
  border: 1px solid #20c0f3 !important;
}

small.file-name {
  /* margin-left: -70px; */
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
}

.up-btn {
  margin-left: -50px;
  /* width: 75%; */
}

small.file-name2 {
  margin-left: -70px;
}

.cancel-btn {
  display: inline;
  /* float: right; */
  font-size: 15px;
  padding: 3px;
  margin: 5px;
  /* margin-right: 11px; */
}

.save-btn {
  margin-left: -35px;
}
.btn-primary[disabled] {
background-color: darkgray !important;
border-color: darkgray !important;
}
.btn-primary{
background-color: #09e5ab !important;
border-color: #09e5ab !important;
color: #fff;
font-size: 15px;
padding: 2px 10px 6px 13px;

}
.btn-file{
background-color: #20C0F4 !important;
border-color: #20C0F4 !important;
color: #fff !important;
}
.file-view-btn {
width: 200px !important;
}
.file-btn-alinment{
margin-top: 2rem!important;
}
.bs-datepicker{
display: block !important;
}
