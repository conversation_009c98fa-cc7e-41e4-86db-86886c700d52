import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { HaDoctorRegistrationComponent } from './ha-doctor-registration.component';

describe('HaDoctorRegistrationComponent', () => {
  let component: HaDoctorRegistrationComponent;
  let fixture: ComponentFixture<HaDoctorRegistrationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ HaDoctorRegistrationComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HaDoctorRegistrationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
