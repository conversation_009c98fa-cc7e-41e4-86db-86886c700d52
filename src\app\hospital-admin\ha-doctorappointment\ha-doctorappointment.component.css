
.availableSlots{
    background-color: #09dca4 !important;
    color: white!important;
    cursor: pointer!important;
    height: 32px !important;
  
  }
  .disabled{
    background-color:#FF6347 !important;
    color:#FF6347 !important;
    pointer-events: none !important;
    height: 32px !important;
  }
  .selectedAppointment{
    background-color:#20c0f3 !important;
  }
  .clearfix .time-list-bg{
    background-color:white !important;
    color: black;
  }
  
  .day-slot li {
    width: 12.28% !important;
  }
  .time-slot li {
    width: 12.28% !important;
  }
  .time-slot ul {
    margin-left: -14px !important;
  }
  
  .schedule-cont {
      padding: 6px !important;
  }
  
  .modalShowClass {
    display: block !important;
  }
  
  .modal-dialogBox {
    overflow-y: initial !important;
    min-width: 1000px;
  }
  
  
  .link-btn{
    cursor: pointer;
  }
  .btn-back{
    padding: 0px 6px;
  }
  .fas {
    color: #20c0f3;
    cursor: pointer;
  }
  
  /* Extra small devices (phones, 600px and down) */
  @media only screen and (max-width: 600px) {
    .example {background: red;}
    .modal-size{
      width: 30%;
      height: 300px;
      overflow: auto;
      margin: 4%;
    }
  }
  
  /* Small devices (portrait tablets and large phones, 600px and up) */
  @media only screen and (min-width: 600px) {
    .example {background: green;}
    .day-slot li span {
      display: block;
      font-size: 11px;
      text-transform: uppercase;
  }
  .modal-size{
    width: 30%;
    height: 300px;
    overflow: auto;
  }
  }
  
  /* Medium devices (landscape tablets, 768px and up) */
  @media only screen and (min-width: 768px) {
    .example {background: blue;}
    .time-slot li .timing {
      background-color:#FF6347;
      color: #FF6347;
      font-size: 11px;
    }
    .day-slot li span {
      display: block;
      font-size: 11px;
      text-transform: uppercase;
  }
  .modal-size{
    width: 60%;
    height: 300px;
    overflow: auto;
    margin: 7%;
  }
  }
  
  /* Large devices (laptops/desktops, 992px and up) */
  @media only screen and (min-width: 992px) {
    .example {background: orange;}
    .time-slot li .timing {
      background-color:#FF6347;
      color: #FF6347;
      font-size: 14px;
    }
    .day-slot li span {
      display: block;
      font-size: 16px;
      text-transform: uppercase;
  }
  .modal-size{
    width: 75%;
    height: 300px;
    overflow: auto;
  }
  }
  
  /* Extra large devices (large laptops and desktops, 1200px and up) */
  @media only screen and (min-width: 1200px) {
    .example {background: pink;}
    .time-slot li .timing {
      background-color:#FF6347;
      color: #FF6347;
      font-size: 14px;
    }
    .day-slot li span {
      display: block;
      font-size: 18px;
      text-transform: uppercase;
  }
  .modal-size{
    width: 100%;
    height: 500px;
    overflow: auto;
    margin: 0%;
  }
  }
  .cancel-btn {
    float: right;
    font-size: 15px;
    padding: 3px;
    margin: 5px;
    margin-right: 11px;
  }
  