<div class="content" *ngIf="!isLoading">
    <div class="container">
        <h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" (click)="back()"></i>Back</h5>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="booking-doc-info">
                            <a class="booking-doc-img">
                                <img [src]="doctorImageUrl" alt="User Image" />
                            </a>
                            <div class="booking-info">
                                <h4 id="doctorname">
                                    <a>{{ doctorName | titlecase }}</a>
                                </h4>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <h3>
                            Patient Details&nbsp;
                        </h3>
                        <div class="hsp-details">
                            <form [formGroup]="patientDetailForm" class="hsp-detail-form" (ngSubmit)="patientData()">
                                <div class="row">

                                    <div class="col-md-3 col-lg-3 col-sm-6">
                                        <label>Email <span class="text-danger">*</span></label>
                                        <input type="text" id="hsp-reg-num" formControlName="email" class="form-control"
                                            maxlength="50" required>
                                    </div>

                                    <div class="col-md-3 col-lg-3 col-sm-6" *ngIf="patientDetailIsAdded">
                                        <label>Name</label>
                                        <input type="text" id="hsp-reg-num" formControlName="name" class="form-control"
                                            readonly>
                                    </div>
                                    <div class="col-md-3 col-lg-3 col-sm-6" *ngIf="patientDetailIsAdded">
                                        <label>Phone Number</label>
                                        <input type="text" id="hsp-reg-num" formControlName="phone_number"
                                            class="form-control" readonly>
                                    </div>
                                    <div class="col-md-3 col-lg-3 col-sm-6" *ngIf="patientDetailIsAdded">
                                        <label>Date Of Birth<span class="text-danger">*</span></label>
                                        <input type="text" id="hsp-reg-num" formControlName="dob" class="form-control"
                                            readonly>
                                    </div>
                                </div>

                                <div class="row">


                                    <div class="col-md-12 mt-2 text-right">

                                        <button class="btn btn-primary hsp-btn" id="hspsave-btn"
                                            [disabled]="!patientDetailForm.valid">Submit</button>
                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>
                </div>
                <div class="card" *ngIf="patientDetailIsAdded">
                    <div class="card-body">
                        <h3>
                            Payment And Consultation Details&nbsp;
                        </h3>
                        <div class="hsp-details">
                            <form [formGroup]="paymentDetailForm" class="hsp-detail-form" (ngSubmit)="paymentData()">
                                <div class="row">

                                    <div class="col-md-3 col-lg-3 col-sm-6">
                                        <div class="form-group ">
                                            <label>Payment Type <span class="text-danger">*</span></label>
                                            <select class="form-control input-field-border select"
                                                name="transcationType" id="transcationType"
                                                formControlName="transcationType"
                                                >  
                                                <!-- (change)="onChange($event.target.value)" -->
                                                <option value="0" disabled>Select Payment Type</option>
                                                <option value="online" translate>Online</option>
                                                <option value="cash" translate>Cash</option>
                                            </select>

                                        </div>
                                    </div>

                                    <div class="col-md-3 col-lg-3 col-sm-6">
                                        <label>Consultation Type <span class="text-danger">*</span></label>
                                        <select class="form-control input-field-border select" name="consultationType"
                                            id="consultationType" formControlName="consultationType">
                                            <option value="0" disabled>Select Consultation Type</option>
                                            <option value="virtual" translate>Virtual</option>
                                            <option value="physical" translate>Physical</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 col-lg-3 col-sm-6">
                                        <label>Patient Type <span class="text-danger">*</span></label>
                                        <select class="form-control input-field-border select" name="patientType"
                                            id="patientType" formControlName="patientType">
                                            <option value="0" disabled>Select Patient Type</option>
                                            <option value="inpatient" translate>In Patient</option>
                                            <option value="outpatient" translate>Out Patient</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">


                                    <div class="col-md-12 mt-2 text-right">

                                        <button class="btn btn-primary hsp-btn" id="hspsave-btn"
                                            [disabled]="!paymentDetailForm.valid">Submit</button>
                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>
                </div>

                <div *ngIf="paymentDetailIsAdded">
                    <div class="row">
                        <div class="col-12 col-sm-4 col-md-6">
                            <h6 class="mb-1" id="appontmentDate">
                                {{ selctedAppontmentDate | date: "mediumDate" }}
                                &nbsp;{{selctedAppontmentDate|date:'hh:mm a'}}
                            </h6>
                            <p class="text-muted" id="selctedAppontmenttime">
                                {{ selctedAppontmentDate | date: "EEEE" }}
                            </p>
                        </div>
                        <div class="col-12 col-sm-8 col-md-6 text-sm-right">
                            <div class="bookingrange btn btn-white btn-sm mb-3">
                                <div style="float: right">
                                    <label class="form-group">Select Appointment Date</label>

                                    <input id="appointment-date" type="text" [minDate]="minDate" [maxDate]="maxDate"
                                        (ngModelChange)="nextWeek($event)" onkeydown="return false" class="form-control"
                                        [ngModelOptions]="{standalone: true}" [(ngModel)]="selectedAppointmentDate"
                                        bsDatepicker
                                        [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row form-row" *ngIf="bookingCompletionError">
                        <div class="col-12 col-md-6 col-lg-6 ">
                            <div class="card">
                                <ng-container class="form-group" *ngFor="let err of errorValue">
                                    <p class="text-danger">&nbsp;{{err.value}}</p>
                                </ng-container>
                            </div>

                        </div>
                    </div>
                    <div class="row">
                        <div col-md-2 class="ml-2">
                            <h6 class="ml-2">
                                <i class="fas fa-square" style="color: #09dca4"></i>
                                &nbsp;Avaliable
                            </h6>
                        </div>
                        <div col-md-2 class="ml-2">
                            <h6>
                                <i class="fas fa-square" style="color: red"></i>
                                &nbsp;Unavailable
                            </h6>
                        </div>
                        <div col-md-2 class="ml-2">
                            <h6>
                                <i class="fas fa-square" style="color: #20c0f3"></i>
                                &nbsp;Selected
                            </h6>
                        </div>

                        <div class="col-md-6"></div>
                    </div>
                    <!-- Schedule Widget -->
                    <div class="card booking-schedule schedule-widget">
                        <!-- Schedule Header -->
                        <div class="schedule-header">
                            <div class="row">
                                <div class="col-md-12">
                                    <!-- Day Slot -->
                                    <div class="day-slot">
                                        <ul>
                                            <li>
                                                <span>Time Slot</span>
                                            </li>

                                            <li *ngFor="let day of dayList">
                                                <span id="day-days">{{ day | date: "EEEE" }}</span>
                                                <span id="day-mediumDate" class="slot-date">{{ day | date: "mediumDate"
                                                    }}
                                                </span>
                                            </li>

                                            <!-- <li class="right-arrow">
                        <a >
                          <i class="fa fa-chevron-right"></i>
                        </a>
                      </li> -->
                                        </ul>
                                    </div>
                                    <!-- /Day Slot -->
                                </div>
                            </div>
                        </div>
                        <!-- /Schedule Header -->

                        <!-- Schedule Content -->
                        <div class="schedule-cont">
                            <div class="col">
                                <div class="col-md-12">
                                    <!-- Time Slot -->
                                    <div class="time-slot">
                                        <ul class="clearfix" *ngIf="!bookingSlotErr">
                                            <li class="time-list-bg" aria-disabled="true">
                                                <a class="timing time-list-bg" *ngFor="let time of timeList"
                                                    style=" height: '32px'">
                                                    <span class="time-list-bg">{{ time[0] }}</span>
                                                </a>
                                            </li>
                                            <li>
                                                <div *ngIf="day1.length > 0">
                                                    <a class="timing" *ngFor="let day of this.day1; let i = index"
                                                        (click)="getSelectedDate(day, i, 'day1', day1)" id="day1{{ i }}"
                                                        [ngClass]="{
                               'availableSlots':day.status === 'Available',
                               'disabled':day.status !== 'Available','selectedAppointment':day.uuid ===slotUuid } ">
                                                        <span>&#x20B9;&nbsp;{{day.amount}}</span>
                                                        <span></span>
                                                    </a>
                                                </div>
                                                <ng-container *ngIf="day1.length === 0">
                                                    <a class="timing" *ngFor="let day of this.timeList" id="day1"
                                                        [ngClass]="'disabled'">
                                                        <span></span> <span></span>
                                                    </a>
                                                </ng-container>
                                            </li>
                                            <li>
                                                <div *ngIf="day2.length > 0">
                                                    <a class="timing" *ngFor="let day of this.day2; let i = index"
                                                        id="day2{{ i }}" (click)="getSelectedDate(day, i, 'day2', day2)"
                                                        [ngClass]="{
                                'availableSlots':day.status == 'Available',
                                'disabled':day.status !== 'Available','selectedAppointment':day.uuid ===slotUuid } ">
                                                        <span>&#x20B9;&nbsp;{{day.amount}}</span>
                                                        <span></span>
                                                    </a>
                                                </div>
                                                <ng-container *ngIf="day2.length === 0">
                                                    <a class="timing" *ngFor="let day of this.timeList" id="day2"
                                                        [ngClass]="'disabled'">
                                                        <span></span> <span></span>
                                                    </a>
                                                </ng-container>
                                            </li>
                                            <li>
                                                <div *ngIf="day3.length > 0">

                                                    <a class="timing" *ngFor="let day of this.day3; let i = index"
                                                        id="day3{{ i }}" (click)="getSelectedDate(day, i, 'day3', day3)"
                                                        [ngClass]="{'availableSlots':day.status==='Available','disabled':day.status !=='Available','selectedAppointment':day.uuid ===slotUuid }">
                                                        <span>&#x20B9;&nbsp;{{day.amount}}</span>
                                                        <span></span>
                                                    </a>
                                                </div>
                                                <ng-container *ngIf="day3.length === 0">
                                                    <a class="timing" *ngFor="let day of this.timeList" id="day3"
                                                        [ngClass]="'disabled'">
                                                        <span></span>
                                                    </a>
                                                </ng-container>
                                            </li>
                                            <li>
                                                <div *ngIf="day4.length > 0">
                                                    <a class="timing" *ngFor="let day of this.day4; let i = index"
                                                        id="day4{{ i }}" (click)="getSelectedDate(day, i, 'day4', day4)"
                                                        [ngClass]="{'availableSlots':day.status==='Available','disabled':day.status !=='Available','selectedAppointment':day.uuid ===slotUuid }">
                                                        <span>&#x20B9;&nbsp;{{day.amount}}</span>
                                                        <span></span>
                                                    </a>
                                                </div>
                                                <ng-container *ngIf="day4.length === 0">
                                                    <a class="timing" *ngFor="let day of this.timeList" id="day4"
                                                        [ngClass]="'disabled'">
                                                        <span></span> <span></span>
                                                    </a>
                                                </ng-container>
                                            </li>
                                            <li>
                                                <div *ngIf="day5.length > 0">
                                                    <a class="timing" *ngFor="let day of this.day5; let i = index"
                                                        id="day5{{ i }}" (click)="getSelectedDate(day, i, 'day5', day5)"
                                                        [ngClass]="{'availableSlots':day.status==='Available','disabled':day.status !=='Available','selectedAppointment':day.uuid ===slotUuid }">
                                                        <span>&#x20B9;&nbsp;{{day.amount}}</span>
                                                        <span></span>
                                                    </a>
                                                </div>
                                                <ng-container *ngIf="day5.length === 0">
                                                    <a class="timing" *ngFor="let day of this.timeList" id="day5"
                                                        [ngClass]="'disabled'">
                                                        <span></span> <span></span>
                                                    </a>
                                                </ng-container>
                                            </li>
                                            <li>
                                                <div *ngIf="day6.length > 0">
                                                    <a class="timing" *ngFor="let day of this.day6; let i = index"
                                                        id="day6{{ i }}" (click)="getSelectedDate(day, i, 'day6', day6)"
                                                        [ngClass]="{'availableSlots':day.status==='Available','disabled':day.status !=='Available','selectedAppointment':day.uuid ===slotUuid }">
                                                        <span>&#x20B9;&nbsp;{{day.amount}}</span>
                                                        <span></span>
                                                    </a>
                                                </div>
                                                <ng-container *ngIf="day6.length === 0">
                                                    <a class="timing" *ngFor="let day of this.timeList" id="day6"
                                                        [ngClass]="'disabled'">
                                                        <span></span> <span></span>
                                                    </a>
                                                </ng-container>
                                            </li>
                                            <li>
                                                <div *ngIf="day7.length > 0">
                                                    <a class="timing" *ngFor="let day of this.day7; let i = index"
                                                        id="day7{{ i }}" (click)="getSelectedDate(day, i, 'day7', day7)"
                                                        [ngClass]="{'availableSlots':day.status==='Available','disabled':day.status !=='Available','selectedAppointment':day.uuid ===slotUuid }">
                                                        <span>&#x20B9;&nbsp;{{day.amount}}</span>
                                                        <span></span>
                                                    </a>
                                                </div>
                                                <ng-container *ngIf="day7.length === 0">
                                                    <a class="timing" *ngFor="let day of this.timeList" id="day7"
                                                        [ngClass]="'disabled'">
                                                        <span></span> <span></span>
                                                    </a>
                                                </ng-container>
                                            </li>
                                        </ul>
                                        <ul class="clearfix text-center" *ngIf="!!bookingSlotErr">
                                            <span style="color: red">No booking slots</span>
                                        </ul>
                                    </div>
                                    <!-- /Time Slot -->
                                </div>
                            </div>
                        </div>

                        <!-- /Schedule Content -->
                    </div>
                    <!-- /Schedule Widget -->
                    <!-- Submit Section -->
                    <div class="row">
                        <div col-md-2 class="ml-2">
                            <h6 class="ml-2">
                                <i class="fas fa-square" style="color: #09dca4"></i>
                                &nbsp;Avaliable
                            </h6>
                        </div>
                        <div col-md-2 class="ml-2">
                            <h6>
                                <i class="fas fa-square" style="color: red"></i>
                                &nbsp;Unavailable
                            </h6>
                        </div>
                        <div col-md-2 class="ml-2">
                            <h6>
                                <i class="fas fa-square" style="color: #20c0f3"></i>
                                &nbsp;Selected
                            </h6>
                        </div>

                        <div class="col-md-6"></div>
                    </div>

                    <div class="submit-section proceed-btn text-right" *ngIf="!bookingSlotErr">
                        <div class="form-check">
                            <input type="checkbox" id="tems-check" [(ngModel)]="acceptedTerms"
                                (change)="acceptTermsAndCondtion($event)" class="form-check-input" id="exampleCheck1"
                                [disabled]="!appointmentSelectedEvent" />
                            <label class="form-check-label" for="exampleCheck1" translate><b>
                                    <!-- I agree to share my medical history with any relevant Doctor(s) full
                                        details-->
                                   I confirm the slot</b> &nbsp;<a class="link-btn" (click)="fullDetails()"><u></u></a></label>
                        </div>
                        <br />
                        <button *ngIf="!booked"  data-toggle="modal"
                            data-target="#bookAppointment" class="btn btn-primary submit-btn"
                            [disabled]="!acceptedTerms">
                            Book Now
                        </button>
                    </div>
                    <!-- /Submit Section -->
                </div>

            </div>
        </div>
    </div>
</div>
<div *ngIf="isLoading">
    <app-loading-spinner></app-loading-spinner>
</div>

<!--T & C-->
<div id="termsModal" class="modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialogBox" role="document">
        <div class="modal-content modal-size">
            <div class="modal-header">
                <div class="tex-center">
                    <h4 class="modal-title text-center" translate>Patient Consent</h4>
                </div>
                <button type="button" class="close" data-dismiss="modal" id="close-model-btn" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="terms-model-body" style="font-size: 12px;">
                <div [innerHTML]="contentText">

                </div>
            </div>
            <div class="modal-footer">
                <div class="col-md-12">
                    <div class="row">

                        <div class="col-md-12 text-center">
                            <button type="button" id="cancel" class="btn btn-danger mr-5" data-dismiss="modal"
                                translate>
                                Cancel
                            </button>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- T & C-->
<div class="modal fade" id="bookAppointment" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Appointment Booking info</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">

                <div class="row">
                    <div class="col-md-4 col-sm-6">
                        <p>Doctor Name</p>
                    </div>
                    <div class="col-md-1 col-sm-2">
                        <p>:</p>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <p><strong>{{doctorName}}</strong></p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 col-sm-6">
                        <p>Date & Time</p>
                    </div>
                    <div class="col-md-1 col-sm-2">
                        <p>:</p>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <p>
                            <strong>{{selctedAppontmentDate|date:'mediumDate'}}&nbsp;
                                {{selctedAppontmentDate|date:'hh:mm a'}}</strong>
                        </p>
                    </div>
                </div>
                <div class="row" *ngIf="parentAppointmentId ==='booking'">
                    <div class="col-md-4 col-sm-6">
                        <p>Fees</p>
                    </div>
                    <div class="col-md-1 col-sm-2">
                        <p>:</p>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <p><strong>{{currency}}&nbsp;{{effective_fee}}</strong></p>
                    </div>
                </div>
                <div class="row" *ngIf="parentAppointmentId ==='booking'">
                    <div class="row" *ngIf="amount!=0">

                        <div class="col-md-12 col-sm-6 text-center " *ngIf="amount<=1000">
                            <span> Doctor fee-&nbsp;{{amount}} + Service Fee - {{platform_fee}}(10%)&nbsp; + GST -
                                {{gst}}(9% SGST + 9% CGST)&nbsp;</span>
                        </div>
                        <div class="col-md-12 col-sm-6 text-center " *ngIf="amount >1000">
                            <span> Doctor fee-&nbsp;{{amount}} + Service Fee-100(10%)&nbsp; {{platform_fee}} + GST-18
                                (9% SGST + 9% CGST)&nbsp;</span>
                        </div>

                    </div>
                </div>

                <div class="text-center">
                    <button type="button" class="btn btn-secondary btn-width" data-dismiss="modal">Cancel</button>
                    <button type="button" id="confirmBooking" class="btn btn-primary btn-width ml-2"
                        (click)="confirmBooking()">Confirmation</button>
                </div>
            </div>
        </div>
    </div>
</div>