<div class="card">
    <div class="card-body">
        <h4 class="card-title" translate>Education</h4>
        <div class="row form-row" *ngIf="formError">
        <div class="col-12 col-md-6 col-lg-6 ">
          <div class="card">
            <ng-container class="form-group" *ngFor="let err of errorValue">
              <p class="text-danger">{{err.value}}</p>
            </ng-container>
          </div>

          </div>
      </div>
        <div class="education-info">
            <form [formGroup]="qualificationForm">
                <div formArrayName="qualificationArray">
                    <ng-container *ngFor="let data of this.qualificationForm.controls.qualificationArray.value; let i=index;trackBy:trackFn" [formGroupName]="i">
                        <div class="row form-row education-cont">
                            <div *ngIf="!data['deleted']" class="col-12 col-md-10 col-lg-12" id="form{{i}}">
                                <div class="row form-row">
                                    <div class="col-12 col-md-6 col-lg-3 ">
                                        <div class="form-group">
                                          <label>Degree<span class="text-danger">*</span></label>
                                          <ng-select id="degree{{i}}"  class="custom list-with" formControlName="name" bindValue="code" [items]="dergeeList"  [searchable]="true" bindLabel="code" [clearable]="false" placeholder="{{'Select Degree' | translate}}" multiple
                                          required>
                                      </ng-select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6 col-lg-3">
                                        <div class="form-group">
                                          <label>College/Institution<span class="text-danger">*</span></label>
                                            <input id="institution{{i}}" type="text" class="form-control" formControlName="institution" placeholder="{{'College/Institution'|translate}}" pattern="[a-zA-Z ]*"  maxlength="50">
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6 col-lg-2">
                                        <div class="form-group">
                                          <label>Year<span class="text-danger">*</span></label>
                                            <input id="completionYear{{i}}" type="text" class="form-control" formControlName="year" placeholder="{{'Year of Completion'|translate}}" pattern="[0-9]*"  maxlength="4">
                                        </div>
                                    </div>
                                    <div *ngIf="data['edit'] || data['new']" class="col-6 col-md-2 col-lg-2 file-btn-alinment" id="uploadForm{{i}}">

                                      <div class="file-upload-btn" id="upload{{i}}" translate>
                                          <i class="fa fa-upload"></i>Upload File
                                          <input id="file{{i}}" type="file" (change)="updateFile($event,i)" class="upload" accept=".jpg,.pdf">
                                      </div>
                                      <small *ngIf="!data['file']" id="small{{i}}" class="form-text text-muted" translate>Allowed JPG or PDF. Max size of 2MB</small>
                                      <a *ngIf="data['file']" id="fileName{{i}}" [href]="data['document']||'#'" class="form-text doc-link text-muted">{{data['file']?.file_name || data['documentName']}}</a>
                                  </div>
                                  <div *ngIf="!data['edit'] && !data['new']" class="col-md-2 col-lg-2 upIcon file-btn-alinment" id="uploadIcon{{i}}">
                                      <div class="file-upload-icon file-upload-btn" id="uploadIcon{{i}}" (click)="viewFile(data['file'])" style="cursor:pointer;">
                                          <span translate ><i class="fa fa-view"></i> View File</span>
                                      </div>
                                      <!-- <a id="fileName{{i}}" [href]="data['document']" target="_blank" class="form-text doc-link text-muted">{{data['file']?.file_name || data['documentName']}}</a> -->
                                  </div>
                                    <div *ngIf="!data['edit'] && !data['new'] && !editing" class="col-4 col-md-3 col-lg-2 ed-edit file-btn-alinment"><span translate><i (click)="editQualification(i)" class="fa fa-edit"></i></span></div>
                                    <div *ngIf="data['edit'] || data['new']" class="col-4 col-md-3 col-lg-2 file-btn-alinment">
                                        <button type="button" id="cancel-btn" (click)="cancelUpdate(i)" class="btn btn-secondary cancel-btn" translate>Cancel</button>
                                        <button #qualSubmitButton (click)="saveQualification(i)" id="save{{i}}" class="btn btn-primary btn-primary-size" type="submit" translate>Save</button>
                                        <button id="del{{i}}" (click)="removeQualification(i)" class="del-btn"><i class="fa fa-trash-alt"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </form>
        </div>
        <div class="add-more">
            <p *ngIf="showAddMore" (click)="createQualificationForm(null)" id="addQualification" translate><i class="fa fa-plus-circle"></i> Add More</p>
        </div>
    </div>

</div>
