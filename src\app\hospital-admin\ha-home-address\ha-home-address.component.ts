import { delay } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit, EventEmitter, Output } from '@angular/core';
import { FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { DoctorService } from '../../doctor/doctor.service';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import * as Settings from '../../config/settings';
import { FixedSizeVirtualScrollStrategy } from '@angular/cdk/scrolling';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-ha-home-address',
  templateUrl: './ha-home-address.component.html',
  styleUrls: ['./ha-home-address.component.css'],
})
export class HaHomeAddressComponent implements OnInit {
  @Output() profileCompletion: EventEmitter<string> = new EventEmitter<string>();
  public homeAddressForm: FormGroup;
  public homeAddressFormArray: FormArray;
  homeAddressReadOnly = false;
  homeAddressEdit = true;
  public countryList = [];
  public homeAddressData = {};
  public readHomeAddress = false;
  selecthomeAddressEdit = 'true';
  public selectedHomeAddressIndex: number;
  doctorHomeAddressList: any[];
  showCancelBtn = false;
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  numberError = Settings.numberError;
  public doc_uuid: any;
  public uuid = null;
  constructor(
    private httpClient: HttpClient,
    private doctorService: DoctorService,
    private translate: TranslateService,
    private notificationService: ToastrService,
    private route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(
      url => {
        console.log("url", url);
        this.doc_uuid = url['uuid'];
        console.log(this.doc_uuid);
      }
    );
    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this.addHomeFormControl(null);
    this.getDoctorAddress();
    this.doctorService.getCountryDetail().subscribe(
      (data) => {
        this.countryList = Object.values(data);
      },
      (error) => {
        console.log(error);
      }
    );
  }
  addHomeFormControl(data) {
    if (data === null) {
      this.homeAddressForm = new FormGroup({
        uuid: new FormControl(),
        doctor: new FormControl(this.doc_uuid),
        practice_location: new FormControl(''),
        address_type: new FormControl('Home', [Validators.required, Validators.maxLength(50)]),
        line_1: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        line_2: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        city_town_village: new FormControl(
          null,
          [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')],
        ),
        district: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        taluk: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        state: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        country: new FormControl('India', [Validators.required]),
        postal_code: new FormControl(null, [Validators.required, Validators.maxLength(10), Validators.pattern('[a-zA-Z0-9 ]*')]),
      });
    } else {
      console.log('addrress', data);
      this.readHomeAddress = true;
      this.uuid = data.uuid;
      this.homeAddressForm = new FormGroup({
        uuid: new FormControl(data.uuid),
        doctor: new FormControl(this.doc_uuid),
        practice_location: new FormControl(''),
        address_type: new FormControl('Home', [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        line_1: new FormControl(data.line_1, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        line_2: new FormControl(data.line_2, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        city_town_village: new FormControl(
          data.city_town_village,
          [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')],
        ),
        district: new FormControl(data.district, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        taluk: new FormControl(data.taluk, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        state: new FormControl(data.state, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        country: new FormControl(data.country, [Validators.required]),
        postal_code: new FormControl(data.postal_code, [Validators.required, Validators.maxLength(10), Validators.pattern('[0-9]*')]),
      });
    }
  }


  saveHomeAddress() {
    this.doctorService.saveHaAddress(this.homeAddressForm.value, this.doc_uuid, this.uuid).subscribe(
      (data) => {

        if (this.homeAddressForm.controls['uuid'].value) {
          this.notificationService.success('Home address updated');
        } else {
          this.notificationService.success('Home address added');
        }

        this.readHomeAddress = true;
        data = [data];
        const doctorAddress = Object.values(data);
        console.log('doctorAddress0,', doctorAddress);
        this.doctorHomeAddressList = doctorAddress.filter(
          (obj) => obj.address_type === 'Home'
        );
        this.profileCompletion.emit();
      },
      (err) => {
        this.notificationService.error(
          'Home Address Updation Failed',
          'Med.Bot'
        );
      }
    );
  }

  trackFn(index) {
    return index;
  }
  editHomeAddress(i): void {
    this.readHomeAddress = false;
  }
  cancelHomeAddress(): void {
    if (this.doctorHomeAddressList.length > 0) {
      this.addHomeFormControl(this.doctorHomeAddressList[0]);
    } else {
      this.addHomeFormControl(null)
    }


  }


  getDoctorAddress() {
    this.doctorService.getHaAddressDetail(this.doc_uuid).subscribe(
      (data) => {
        console.log(data);
        data = data['results'];
        const doctorAddress = Object.values(data);
        this.doctorHomeAddressList = doctorAddress.filter(
          (obj) => obj.address_type === 'Home'
        );
        if (this.doctorHomeAddressList.length > 0) {
          this.addHomeFormControl(this.doctorHomeAddressList[0]);
        }
      },
      (err) => {
        console.log('err', err);
      }
    );
  }
}
