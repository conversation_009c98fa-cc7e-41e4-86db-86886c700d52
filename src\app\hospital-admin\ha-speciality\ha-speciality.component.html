<!-- Page Content -->
<div class="content">
    <h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" (click)="back()"></i>Back</h5>
    <div class="container-fluid">

        <div class="row">
            <div class="col-md-8 offset-md-2">

                <!-- Register Content -->
                <div class="account-content">
                    <div class="row align-items-center justify-content-center">

                        <div class="col-md-12 col-lg-6 login-right">
                            <div class="login-header">
                                <h3>Speciality </h3>
                            </div>

                            <!-- Register Form -->
                            <form [formGroup]="hospitalAdminForm">
                                <div class="form-group">
                                    <label for="system-of-medicine" translate>System of Medicine</label>
                                    <ng-select formControlName="medicine_type" [items]="systemOfMedicine"  (change)="getSpecificSpecialityData($event)">
                                    </ng-select>
                                </div>
                                <div class="form-group">
                                    <label for="speciality"  translate>Speciality</label>
                                    <ng-select formControlName="spec_name" [items]="specificSpeciality" bindLabel="value" [addTag]="true"  addTagText="add new speciality">
                                    </ng-select>
                                </div>
                                <!-- <div class="form-group form-focus">
                                    <input type="text" class="form-control floating" id="specialityname"
                                        formControlName="spec_name">
                                    <label class="focus-label"> Speciality Name</label>
                                </div>


                                <div class="form-group form-focus">
                                    <input type="text" class="form-control floating" id="specialitycode"
                                        formControlName="spec_code" [max]="10" [min]="10">
                                    <label class="focus-label">Speciality Code</label>
                                </div>



                                <div class="form-group form-focus">
                                    <input type="text" class="form-control floating" id="departmentid"
                                        formControlName="hospitaldepartment">
                                    <label class="focus-label"> Department Id</label>
                                </div> -->


                                <button id="save-hospital-admin" class="btn btn-primary btn-block btn-lg login-btn"
                                    type="submit" [disabled]="!hospitalAdminForm.valid"
                                    (click)="saveHospitalAdmin()">{{uploadingData ?'Uploading':'Save'}}</button>


                            </form>
                            <!-- /Register Form -->

                        </div>
                    </div>
                </div>
                <!-- /Register Content -->






            </div>
        </div>

    </div>

</div>
<!-- /Page Content -->