import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { HospitalService } from '../hospital-admin.service';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import { DoctorService } from 'src/app/doctor/doctor.service';
import { retry } from 'rxjs/operators';
@Component({
  selector: 'app-ha-speciality',
  templateUrl: './ha-speciality.component.html',
  styleUrls: ['./ha-speciality.component.css'],
})
export class HaSpecialityComponent implements OnInit {
  public hospitalAdminForm: FormGroup;
  // doctorList: any = [];
  //  specialityList: any = [];
  public doctorSerialNumber = 0;
  specialityisLoading: boolean;
  hospitalId: string;
  uploadingData = false;
  speciality: Object;
  public systemOfMedicine = ['Allopathy', 'Ayurveda', 'Chiropractic', 'Dental', 'Homoeopathy', 'Siddha', 'Traditional Chinese', 'Unani', 'Yoga and Naturopathy'];
  specificSpeciality: any = [];

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private hospitalService: HospitalService,
    private notificationService: ToastrService,
    private doctorService: DoctorService,
    private location: Location
  ) { }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe((parms) => {
      this.hospitalId = parms['id'];
    });
    this.addHospitalAdminFormControl();
    this.getSpecialityData();
  }
  addHospitalAdminFormControl() {
    this.hospitalAdminForm = new FormGroup({
      // email: new FormControl('', [Validators.required, Validators.email]),
      medicine_type: new FormControl('', Validators.required),
      spec_name: new FormControl('', Validators.required),
      spec_code: new FormControl(''),
      code: new FormControl(''),
      value: new FormControl(''),
      hospital: new FormControl(this.hospitalId),
    });
  }
  getSpecialityData() {
    this.doctorService.getSpeciality(this.hospitalId).subscribe(
      (data) => {
        console.log("speciality data : ", data);
        this.speciality = data;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  getSpeciality(specialityType) {
    var speciality = [];
    speciality = this.speciality[specialityType].filter(function (speciality) {
      return speciality.hospital == this.hospitalId || speciality.hospital == null;
    }.bind(this));
    return speciality;
  }
  getSpecificSpecialityData(data) {
    console.log(data);
    this.hospitalAdminForm.controls[`spec_name`].setValue([]);
    if (data == 'Allopathy') {
      this.specificSpeciality = this.speciality['Allopathy'];
      console.log(this.specificSpeciality);
    } else if (data == 'Ayurveda') {
      this.specificSpeciality = this.speciality['Ayurveda'];
    } else if (data == 'Dental') {
      this.specificSpeciality = this.speciality['Dental'];
    } else if (data == 'Homoeopathy') {
      this.specificSpeciality = this.speciality['Homoeopathy'];
    }
    else if (data == 'Siddha') {
      this.specificSpeciality = this.speciality['Siddha'];
    }
    else if (data == 'Unani') {
      this.specificSpeciality = this.speciality['Unani'];
    } else {
      this.specificSpeciality = [];
    }
  }

  saveHospitalAdmin() {
    this.uploadingData = true;
    var isNew = 1;
    if(this.specificSpeciality&&this.specificSpeciality.length>0){
      for (let i = 0; i < this.specificSpeciality.length; i++) {
        if (this.hospitalAdminForm.get('spec_name').value == this.specificSpeciality[i]) {
          this.hospitalAdminForm.get('spec_code').setValue(this.specificSpeciality[i].code);
          this.hospitalAdminForm.get('spec_name').setValue(this.specificSpeciality[i].value);
          console.log(this.specificSpeciality[i].code);
          isNew = 0;
          break;
        }
      }
    }
    if (isNew) {
      console.log((this.hospitalAdminForm.get('spec_name').value).value);
      var spec = (this.hospitalAdminForm.get('spec_name').value).value;
      this.hospitalAdminForm.get('spec_code').setValue(spec);
      this.hospitalAdminForm.get('spec_name').setValue(spec);
      this.hospitalAdminForm.get('code').setValue(spec);
      this.hospitalAdminForm.get('value').setValue(spec);
      this.hospitalService
        .createHospitalNewSpec(this.hospitalAdminForm.value)
        .subscribe(
          (data) => {
            this.notificationService.success(
              'New Speciality Added Successfully',
              'Med.Bot'
            );
            this.router.navigate(['/users']);
          },
          (err) => {
            this.notificationService.error(err.error.error_message, 'Med.Bot');
            console.log(err);
            this.uploadingData = false;
          }
        );
    }
    else{
    this.hospitalService
      .createHospitalSpec(this.hospitalId, this.hospitalAdminForm.value)
      .subscribe(
        (data) => {
          this.notificationService.success(
            'Speciality Added Successfully For Hospital',
            'Med.Bot'
          );

          this.router.navigate(['/users']);
        },
        (err) => {
          this.notificationService.error(err.error.error_message, 'Med.Bot');
          console.log(err);
          this.uploadingData = false;
        }
      );
    }
  }

  back() {
    this.location.back();
  }
  
}
