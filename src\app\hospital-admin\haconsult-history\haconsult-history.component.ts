import { ToastrService } from 'ngx-toastr';
import { timeStamp } from 'console';
import { Component, OnInit, ViewEncapsulation, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormControl, FormArray, FormBuilder, Validators } from '@angular/forms';
import { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { ActivatedRoute, Router } from '@angular/router';
import { DocumentModalComponent } from 'src/app/shared/document-modal/document-modal.component';
import { TeleConsultService } from '../../tele-consult/tele-consult.service';
import { DoctorService } from '../../doctor/doctor.service';
import { SuspendModalComponent } from '../../tele-consult/consultation/suspend-modal/suspend-modal.component'
import { NONE_TYPE } from '@angular/compiler';
import * as moment from 'moment';
import { SharedService } from '../../shared/shared.service';
import { AuthService } from '../../auth/auth.service';
import { Location } from '@angular/common';
declare var $: any;
@Component({
  selector: 'app-haconsult-history',
  templateUrl: './haconsult-history.component.html',
  styleUrls: ['./haconsult-history.component.css']
})
export class HaconsultHistoryComponent implements OnInit {
  @Input() patientUuid: any;
  @Input() doctorUuid: any;
  @Input() consultationId: any;
  // suspendReviewModalForm = new FormGroup({
  //   //Medical History
  //   suspendReviewMessage: new FormControl()
  // });

  // consultForm = this.formBuilder.group({
  //   //Medical History
  //   immunization_history: new FormControl(),
  //   past_medical_history: new FormControl(),
  //   appetite: new FormControl(),
  //   diet_history: new FormControl(),
  //   thirst: new FormControl(),
  //   sleep: new FormControl(),
  //   habits: new FormControl(),
  //   smoking: new FormControl(),
  //   alcohol: new FormControl(),
  //   drugs: new FormControl(),
  //   sexual_history: new FormControl(),
  //   additional_notes: new FormControl(),
  //   notes: new FormControl(),
  //   gender: new FormControl(),
  //   gynaecological_history: new FormControl(),
  //   age_of_menarche: new FormControl(),
  //   menstrual_history: new FormControl(),
  //   last_menstrual_period: new FormControl(null),
  //   number_of_pregnancy: new FormControl(),
  //   gravida: new FormControl(),
  //   para: new FormControl(),
  //   abortions: new FormControl(),
  //   social_history: new FormControl(),
  //   blood_pressure_systolic: new FormControl(),
  //   blood_pressure_diastolic: new FormControl(),
  //   pulse_rate: new FormControl(),
  //   spo2: new FormControl(),
  //   auscultation: new FormControl(),
  //   temperature: new FormControl(),
  //   ecg: new FormControl(),
  //   other_observations: new FormControl(),
  //   medicine: new FormControl(),
  //   diet: new FormControl(),
  //   recommendations: new FormControl(),
  //   chief_complaint: new FormControl(),
  //   history_of_present_illness: new FormControl(),
  //   //Physical Examination
  //   weight: new FormControl(),
  //   height: new FormControl(),
  //   bmi: new FormControl(),
  //   built: new FormControl(),
  //   nutrition: new FormControl(),
  //   clubbing_of_fingers: new FormControl(),
  //   nail_changes: new FormControl(),
  //   cyanosis: new FormControl(),
  //   icterus_jaundice: new FormControl(),
  //   pallor: new FormControl(),
  //   lymph_nodes: new FormControl(),
  //   oedema: new FormControl(),
  //   sclera: new FormControl(),
  //   // otherObservations: new FormControl(),
  //   //Systemic Examination
  //   respiratory_system: new FormControl(),
  //   gastro_intestinal_system: new FormControl(),
  //   cardio_vascular_system: new FormControl(),
  //   genitourinary_system: new FormControl(),
  //   musculoskeletal_system: new FormControl(),
  //   central_nervous_system: new FormControl(),
  //   eye: new FormControl(),
  //   ear: new FormControl(),
  //   nose: new FormControl(),
  //   mouth: new FormControl(),
  //   throat: new FormControl(),
  //   neck: new FormControl(),
  //   skin: new FormControl(),
  //   psychiatric_history: new FormControl(),
  //   //Diagnosis
  //   primary_diagnosis: new FormControl(),
  //   secondary_diagnosis: new FormControl(),
  //   differential_diagnosis: new FormControl(),
  //   final_diagnosis: new FormControl(),
  //   icd_10_codes: new FormControl(),
  //   //Investigation
  // });
  // investigationForm: FormGroup;
  // patientHistory = false;
  // currentSummary = false;
  // public female: boolean;
  // nofemale = false;
  // investigationsTaxonomy: any;
  // investigations = [];
  // haematologyItems = [];


  // biochemistryItems = [];

  // microbiologyItems = [];

  // clinicalPathologyItems = [];

  // pathologyItems = [];

  // serologyItems = [];

  // malariaItems = [];

  // filariasisItems = [];

  // dengueItems = [];

  // japaneseEncephalitisItems = [];

  // chikungunyaItems = [];

  // scrubTyphusItems = [];

  // leptospirosisItems = [];

  // brucellosisItems = [];

  // BRUCELLOSIS;

  // tuberculosisItems = [];

  // hivItems = [];

  // hepatitisBItems = [];

  // hepatitisCItems = [];

  // hepatitisAItems = [];

  // hepatitisEItems = [];

  // hbcItems = [];

  // otherDiagnosticTestsItems = [];

  // radiologyAndOtherDiagnosticTestsItems = [];

  // selected = [
  //   { id: 2, name: 'Clinical Pathology' },
  //   { id: 8, name: 'Leptrospirosis' }
  // ];
  // save = false;
  // showCaseHistory = false;
  // caseHistoryDetailed = false;
  // screenOptions: NgbModalOptions = {
  //   keyboard: false,
  //   centered: true,
  //   size: 'lg',
  //   windowClass: 'modal-xl'
  // };
  // drugs = [];
  // prescription = {};
  // appointmentId = '';

  // drugId;
  // investigationId = '';
  // sub: any;
  // joinedVideo = false;
  // deviceVideo = false;
  // videoLoaded = false;
  // onlyVideo = false;
  // onlyData = false;
  // videoAndData = false;
  // suspendInvestigationMessage = '';
  // suspendReviewMessage = '';
  // name = '';
  // prescriptionForm: FormGroup;
  // prescriptionArray: FormArray;
  // addedInvestigationCategories = [];
  // investigationsPayload = [];
  // savedData = {};
  // detailView = true;
  // loading = false;
  // public addDrugTagNowRef: (name) => void;
  // lmpDate: Date;
  // userType: string;
  // showAllTabes: boolean;
  // prescriptionTabDeactive: boolean;
  // consultationDocuments: any;
  // disabledDownloadPrescriptionBtn: boolean;
  // pastAppointments: any = [];
  // showConsultHistory: boolean;
  // selectedConsultationDate: FormControl = new FormControl('null');
  // allConsultationsData = [];
  // allData = false;
  // consultationDataList = [];
  // public medicValue = 'history';
  // consultationsLoading: boolean;
  // consultationsTotalPage: number;
  // consultationsCurrentPage: number;
  // public showPagination = false;
  // showBackBtn: boolean;
  // profilePicture = 'assets/img/doctors/doctor-thumb-02.png';
  // recordingData: any = [];
  // screenshot: any = [];
  // download: boolean;
  constructor(
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private router: Router,
    private formBuilder: FormBuilder,
    private teleConsultService: TeleConsultService,
    private notificationService: ToastrService,
    private sharedService: SharedService,
    private doctorService: DoctorService,
    private userService: AuthService,
    private location: Location) {
    // this.addDrugTagNowRef = (name) => this.addDrugFn(name);
  }

  ngOnInit(): void {
    // this.loading = true;
    // this.videoAndData = true;
    // this.sharedService.setActiveLink('');
    // this.sub = this.route
    //   .params
    //   .subscribe(params => {
    //     console.log(params['patient']);
    //     if (params['patient'] !== undefined) {
    //       this.showBackBtn = true;
    //       this.patientUuid = params['patient'];
    //       this.doctorUuid = params['doctor'];
    //       this.consultationId = params['consultid']
    //       this.selectedConsultationDate.setValue(this.consultationId);
    //     }
    //     this.userService.getUserDetail().subscribe(
    //       (data) => {
    //         this.sharedService.setUserName(data['username']);
    //         if (data['profile_picture'] !== null) {
    //           this.sharedService.setPicture(data['profile_picture']);
    //         }
    //       },
    //       (error) => {
    //         console.log(error);
    //       }
    //     );
    //     this.userType = localStorage.getItem('user_type');
    //     if (!!this.userType && this.userType == 'Doctor') {
    //       this.showAllTabes = true;
    //     } else {

    //       this.showAllTabes = true;

    //     }

    //   },
    //     err => {
    //       this.loading = false;
    //       console.log('ERROR:' + err);
    //     });

    // this.teleConsultService.getDrugsTaxonomy().subscribe(
    //   data => {
    //     this.drugs = Object.values(data);
    //     console.log(this.drugs);
    //   }
    // );

    // this.teleConsultService.getAllConsultationHistory(this.patientUuid, this.doctorUuid).subscribe(
    //   data => {
    //     const objData = data;
    //     // Object.keys(objData).map(key => this.allConsultationsData.push({[key]:objData[key]}));
    //     // console.log('fullList',this.allConsultationsData);
    //     this.allConsultationsData = Object.values(data);
    //     // this.consultationDataList = Object.values(data);
    //     // Object.keys(this.consultationDataList).map(key => this.underScoreToSpaceCaps([key]));
    //     console.log('list', this.consultationDataList);
    //     console.log('list2', this.allConsultationsData);
    //     this.getconsultationsList(1);

    //   }, error => {
    //     this.loading = false;
    //     console.log(error)
    //     const status = error['status'];
    //     if (status == 400) {
    //       this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
    //     }
    //     else {
    //       this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
    //     }

    //   }

    // )


    //investigation
    // this.investigationForm = this.formBuilder.group({
    //   keyAdvice: new FormControl(),
    //   others: new FormControl(),
    //   haematology: [[], Validators.required],
    //   biochemistryAndImmunoassay: [[], Validators.required],
    //   microbiology: [[], Validators.required],
    //   clinicalPathology: [[], Validators.required],
    //   pathology: [[], Validators.required],
    //   serology: [[], Validators.required],
    //   malaria: [[], Validators.required],
    //   filariasis: [[], Validators.required],
    //   dengue: [[], Validators.required],
    //   japaneseEncephalitis: [[], Validators.required],
    //   chikungunya: [[], Validators.required],
    //   scrubTyphus: [[], Validators.required],
    //   leptospirosis: [[], Validators.required],
    //   brucellosis: [[], Validators.required],
    //   tuberculosis: [[], Validators.required],
    //   hiv: [[], Validators.required],
    //   hepatitisB: [[], Validators.required],
    //   hepatitisC: [[], Validators.required],
    //   hepatitisA: [[], Validators.required],
    //   hepatitisE: [[], Validators.required],
    //   hbc: [[], Validators.required],
    //   otherDiagnosticTest: [[], Validators.required],
    //   radiologyAndOtherDiagnostics: [[], Validators.required],
    // });
    // this.teleConsultService.getInvestigationsTaxonomyHa().subscribe(
    //   data => {
    //     this.investigationsTaxonomy = data;
    //     Object.entries(this.investigationsTaxonomy).forEach(
    //       ([key, value]) => this.investigations[key] = []
    //     );
    //     this.haematologyItems = this.investigationsTaxonomy['HAEMATOLOGY'];
    //     this.biochemistryItems = this.investigationsTaxonomy['BIOCHEMISTRY AND IMMUNOASSAYS'];
    //     this.microbiologyItems = this.investigationsTaxonomy['MICROBIOLOGY'];
    //     this.clinicalPathologyItems = this.investigationsTaxonomy['CLINICAL PATHOLOGY '];
    //     this.pathologyItems = this.investigationsTaxonomy['PATHOLOGY'];
    //     this.serologyItems = this.investigationsTaxonomy['SEROLOGY'];
    //     this.malariaItems = this.investigationsTaxonomy['MALARIA'];
    //     this.filariasisItems = this.investigationsTaxonomy['FILARIASIS'];
    //     this.dengueItems = this.investigationsTaxonomy['DENGUE'];
    //     this.japaneseEncephalitisItems = this.investigationsTaxonomy['JAPANESE ENCEPHALITIS'];
    //     this.chikungunyaItems = this.investigationsTaxonomy['CHIKUNGUNYA'];
    //     this.scrubTyphusItems = this.investigationsTaxonomy['SCRUB TYPHUS'];
    //     this.leptospirosisItems = this.investigationsTaxonomy['LEPTOSPIROSIS'];
    //     this.brucellosisItems = this.investigationsTaxonomy['BRUCELLOSIS'];
    //     this.tuberculosisItems = this.investigationsTaxonomy['TUBERCULOSIS'];
    //     this.hivItems = this.investigationsTaxonomy['HIV'];
    //     this.hepatitisBItems = this.investigationsTaxonomy['HEPATITIS B'];
    //     this.hepatitisCItems = this.investigationsTaxonomy['HEPATITIS C'];
    //     this.hepatitisAItems = this.investigationsTaxonomy['HEPATITIS A'];
    //     this.hepatitisEItems = this.investigationsTaxonomy['HEPATITIS E'];
    //     this.hbcItems = this.investigationsTaxonomy['HBC (CORE ANTIBODIES)'];
    //     this.otherDiagnosticTestsItems = this.investigationsTaxonomy['OTHER DIAGNOSTIC TESTS'];
    //     this.radiologyAndOtherDiagnosticTestsItems = this.investigationsTaxonomy['RADIOLOGY & OTHER DIAGNOSTIC TESTS'];


    //   }
    // );
    // this.addPrescriptionForm();
  }

  // onCaseHistory(event) {
  //   // this.modalService.dismissAll();
  //   // this.modalService.open(CaseHistoryComponent, this.screenOptions).result.then((result) => {
  //   // });
  //   if (event.target.checked) {
  //     this.showCaseHistory = true;
  //   } else {
  //     this.showCaseHistory = false;
  //     this.caseHistoryDetailed = false;
  //   }
  // }

  // onShowPatientHistory() {
  //   this.showCaseHistory = true;
  //   this.patientHistory = true;
  //   this.currentSummary = false;
  // }

  // onHidePatientHistory() {
  //   this.showCaseHistory = false;
  //   this.caseHistoryDetailed = false;
  //   this.patientHistory = false;
  //   this.currentSummary = false;
  // }

  // onShowCurrentSummary() {
  //   this.currentSummary = true;
  //   this.showCaseHistory = false;
  //   this.caseHistoryDetailed = false;
  //   this.patientHistory = false;
  // }

  // onHideCurrentSummary() {
  //   this.currentSummary = false;
  // }

  // onReports() {

  // }

  // onSave() {
  //   this.save = true;
  // }

  // onFormSubmit() {

  // }

  // onTabClick(event: any) {
  //   console.log(event);
  //   console.log(event.tab.textLabel);
  //   this.save = false;

  // }
  // activeTabIndex = 0;
  // public isShow = false;
  // public toggleDiv() {
  //   this.isShow = !this.isShow;
  // }

  // openDoctorDashboard() {
  //   if (this.showAllTabes) {
  //     this.router.navigateByUrl('/doctor/dashboard')
  //   } else if (this.userType == 'Patient') {
  //     this.router.navigateByUrl('/hadashboard')
  //   }

  // }


  // onAddRow(prescriptionTable) {
  //   let tableRef = <HTMLTableElement>document.getElementById(prescriptionTable);
  //   // let newRow = tableR.insertBefore(-1)
  //   // TSection.insertBefore();
  //   let newRow = tableRef.insertRow(-1);
  //   console.log(newRow);
  //   // Insert a cell in the row at index 0
  //   let medicine = newRow.insertCell(0);
  //   medicine.innerHTML = "<td> <input type='text'> </td>";

  //   let morningDosage = newRow.insertCell(1);
  //   morningDosage.innerHTML = "<td> <input type='checkbox'> </td>";

  //   let afternoonDosage = newRow.insertCell(2);
  //   afternoonDosage.innerHTML = "<td> <input type='checkbox'> </td>";

  //   let nightDosage = newRow.insertCell(3);
  //   nightDosage.innerHTML = "<td> <input type='checkbox'> </td>";

  //   let beforeFood = newRow.insertCell(4);
  //   beforeFood.innerHTML = "<td> <input type='checkbox'> </td>";

  //   let days = newRow.insertCell(5);
  //   days.innerHTML = "<td> <input type='number' name='days' style='width: 50%;'> </td>";

  //   let remarks = newRow.insertCell(6);
  //   remarks.innerHTML = "<td> <input type='text' name='remarks'> </td>";

  //   // Append a text node to the cell
  //   // let newText = document.createTextNode('New bottom row');
  //   // newCell.appendChild(newText);
  // }

  // onGenderFemale() {
  //   this.female = true;
  // }

  // onGenderNoFemale() {
  //   this.female = false;
  // }


  // onVisitDate() {
  //   this.caseHistoryDetailed = true;
  // }

  // onReport() {
  //   this.modalService.open(DocumentModalComponent, { windowClass: "modalSize" });
  // }

  // addTagFn(name) {
  //   return { name: name, tag: true };
  // }

  // onBackCaseHistoryDetailed() {
  //   this.caseHistoryDetailed = false;
  // }

  // onKey(sectionName, fieldName, event) {
  //   this.teleConsultService.updateConsultation(this.consultationId, sectionName, fieldName, event.target.value).subscribe(data => {
  //   },
  //     err => {
  //       console.log('ERROR:' + err.message);
  //     });
  // }

  // onKeyPrescription(fieldName, event) {
  //   console.log(fieldName, event.target.value);
  //   // let obj = {fieldName: ''};
  //   // this.prescription[this.prescription.length+1];
  // }

  // onPrescription() {
  //   const data = this.prescriptionForm.get('prescriptionArray').value;
  //   this.processPrescriptionData(data);

  //   this.teleConsultService.addPrescription(this.consultationId, this.prescription).subscribe(data => {

  //     this.notificationService.success('Prescription Saved');
  //   },
  //     err => {
  //       console.log('ERROR:' + err.message);
  //     });
  // }

  // joinVideo() {
  //   this.joinedVideo = true;
  // }

  // joinDeviceVideo() {
  //   this.deviceVideo = true;
  // }

  // videoOnly() {
  //   this.onlyVideo = true;
  //   this.onlyData = false;
  //   this.videoAndData = false;
  //   // this.joinedVideo=false;
  // }

  // dataOnly() {
  //   this.onlyData = true;
  //   this.onlyVideo = false;
  //   this.videoAndData = false;
  //   // this.joinedVideo=false;
  // }

  // dataAndVideo() {
  //   this.videoAndData = true;
  //   this.onlyVideo = false;
  //   this.onlyData = false;
  //   // this.joinedVideo=false;
  // }

  // onSuspendInvestigationsPending() {
  //   const activeModal = this.modalService.open(SuspendModalComponent);
  //   activeModal.componentInstance.suspendType = 'investigations';
  //   activeModal.componentInstance.consultationId = this.consultationId;
  //   // this.teleConsultService.suspendConsultationInvestigationsPending(this.consultationId, this.suspendInvestigationMessage).subscribe( data => {
  //   //   this.router.navigateByUrl('/doctor/dashboard');
  //   // },
  //   // err =>{
  //   //   console.log('ERROR:' + err.message);
  //   // });
  // }

  // onSuspendReviewPending() {
  //   const activeModal = this.modalService.open(SuspendModalComponent);
  //   activeModal.componentInstance.suspendType = 'review';
  //   activeModal.componentInstance.consultationId = this.consultationId;
  //   // this.teleConsultService.suspendConsultationReviewPending(this.consultationId, this.suspendReviewMessage).subscribe( data => {
  //   //   this.router.navigateByUrl('/doctor/dashboard');
  //   // },
  //   // err =>{
  //   //   console.log('ERROR:' + err.message);
  //   // });
  // }


  // onComplete() {
  //   this.teleConsultService.completeConsultation(this.consultationId).subscribe(data => {
  //     this.router.navigateByUrl('/doctor/dashboard');
  //   },
  //     err => {
  //       console.log('ERROR:' + err.message);
  //     });
  // }

  // consultationOnlyStyle() {
  //   if (this.onlyData) {
  //     return { 'margin-left': '25%', width: '100%' };
  //   }
  // }

  // addDrugFn(name) {
  //   const drugData = {
  //     name: name,
  //     manufacturer: null,
  //     generic_name: null,
  //     dosage_form: null,
  //     description: null,
  //   };
  //   this.drugs.push(drugData);
  //   console.log(this.drugs);
  // }

  // addPrescriptionForm() {
  //   this.prescriptionForm = this.formBuilder.group({
  //     prescriptionArray: this.formBuilder.array([]),
  //   });
  // }

  // createPrescriptionForm(data) {
  //   this.prescriptionArray = this.prescriptionForm.get('prescriptionArray') as FormArray;
  //   if (data == null) {
  //     console.log('data inavailable')
  //     this.prescriptionArray.push(
  //       this.formBuilder.group({
  //         uuid: null,
  //         brand_name: new FormControl(''),
  //         before_food: new FormControl(false),
  //         morning: new FormControl(''),
  //         afternoon: new FormControl(''),
  //         night: new FormControl(''),
  //         days: new FormControl(''),
  //         notes: new FormControl(''),
  //       })
  //     );
  //   }
  //   else {
  //     console.log('data available', data);
  //     this.prescriptionArray.reset();
  //     data.forEach(prescriptionData => {
  //       let pres_drug = this.drugs.filter(
  //         drug => drug.name == prescriptionData['brand_name'],
  //       );
  //       console.log(pres_drug)
  //       this.prescriptionArray.push(
  //         this.formBuilder.group({
  //           uuid: prescriptionData['uuid'],
  //           brand_name: new FormControl(pres_drug),
  //           before_food: new FormControl(prescriptionData['before_food']),
  //           morning: new FormControl(prescriptionData['administration_instructions']?.morning),
  //           afternoon: new FormControl(prescriptionData['administration_instructions']?.afternoon),
  //           night: new FormControl(prescriptionData['administration_instructions']?.night),
  //           days: new FormControl(prescriptionData['administration_instructions']?.duration_days),
  //           notes: new FormControl(prescriptionData['administration_instructions']?.notes),
  //           // administration_instructions: this.formBuilder.group({
  //           //       parts_of_day: this.formBuilder.array([]),
  //           //       duration_days: new FormControl(''),
  //           //       notes: new FormControl('')
  //           //   })
  //           // deleted: false,
  //         })
  //       );
  //     });
  //   }
  // }

  // trackFn(index: any) {
  //   return index;
  // }

  // processPrescriptionData(data) {
  //   let prescription_consultation_data = [];
  //   data.forEach(element => {
  //     let days = [];
  //     const prescription = element?.brand_name;
  //     prescription.forEach(drug => {
  //       // console.log(drug);
  //       let preciseData = {
  //         uuid: drug['uuid'] || null,
  //         brand_name: drug['name'],
  //         medicine_type: drug['dosage_form'] || 'medicine',
  //         generic_name: drug['generic_name'] || null,
  //         before_food: element['before_food'],
  //         strength_unit: null,
  //         strength_value: null,
  //         administration_instructions: {
  //           // parts_of_day: days,
  //           morning: element?.morning || false,
  //           afternoon: element?.afternoon || false,
  //           night: element?.night || false,
  //           duration_days: element['days'],
  //           sos: false,
  //           dosage: null,
  //           notes: element['notes']
  //         }
  //       };
  //       // console.log(preciseData);
  //       prescription_consultation_data.push(preciseData);
  //     });
  //   });
  //   this.prescription = {
  //     prescription: {
  //       prescription: {
  //         drugs_prescribed: prescription_consultation_data,
  //       }
  //     }
  //   };
  //   console.log(this.prescription);
  // }


  // getConsultationData() {
  //   this.teleConsultService.getConsultationData(this.consultationId).subscribe(
  //     data => {

  //       this.showConsultHistory = true;
  //       this.savedData = data;
  //       const consultation_status = data['fulfilment_status'];
  //       if (data['prescription']['prescription']) {
  //         this.prescription = data['prescription']['prescription']['drugs_prescribed'];
  //         this.createPrescriptionForm(this.prescription);
  //       }
  //       else {
  //         this.createPrescriptionForm(null);
  //       }
  //       if (data['investigation']['tests_prescribed']?.['tests']) {
  //         this.investigationsPayload = data['investigation']['tests_prescribed']['tests'];
  //         this.renderInvestigations(this.investigationsPayload);
  //         if (this.detailView) {
  //           this.investigationForm.disable();
  //         }
  //       }
  //       else {
  //         if (this.detailView) {
  //           this.investigationForm.disable();
  //         }
  //       }
  //       if (data['vitalsigns']) {
  //         this.consultForm.get('blood_pressure_systolic').setValue(data['vitalsigns']['blood_pressure_systolic']);
  //         this.consultForm.get('blood_pressure_diastolic').setValue(data['vitalsigns']['blood_pressure_diastolic']);
  //         this.consultForm.get('temperature').setValue(data['vitalsigns']['temperature']);
  //         this.consultForm.get('pulse_rate').setValue(data['vitalsigns']['pulse_rate']);
  //         this.consultForm.get('auscultation').setValue(data['vitalsigns']['auscultation']);
  //         this.consultForm.get('ecg').setValue(data['vitalsigns']['ecg']);
  //         this.consultForm.get('spo2').setValue(data['vitalsigns']['spo2']);
  //         this.consultForm.get('additional_notes').setValue(data['vitalsigns']['additional_notes']);
  //       }
  //       if (data['medicalhistory']) {
  //         this.female = data['medicalhistory']['female'];
  //         console.log(this.female);
  //         this.consultForm.get('immunization_history').setValue(data['medicalhistory']['immunization_history']);
  //         this.consultForm.get('past_medical_history').setValue(data['medicalhistory']['past_medical_history']);
  //         this.consultForm.get('appetite').setValue(data['medicalhistory']['appetite']);
  //         this.consultForm.get('social_history').setValue(data['medicalhistory']['social_history']);
  //         this.consultForm.get('diet').setValue(data['medicalhistory']['diet']);
  //         this.consultForm.get('thirst').setValue(data['medicalhistory']['thirst']);
  //         this.consultForm.get('sleep').setValue(data['medicalhistory']['sleep']);
  //         this.consultForm.get('smoking').setValue(data['medicalhistory']['smoking']);
  //         this.consultForm.get('alcohol').setValue(data['medicalhistory']['alcohol']);
  //         this.consultForm.get('drugs').setValue(data['medicalhistory']['drugs']);
  //         this.consultForm.get('sexual_history').setValue(data['medicalhistory']['sexual_history']);
  //         this.consultForm.get('other_observations').setValue(data['medicalhistory']['other_observations']);
  //         this.consultForm.get('age_of_menarche').setValue(data['medicalhistory']['age_of_menarche']);
  //         this.consultForm.get('menstrual_history').setValue(data['medicalhistory']['menstrual_history']);
  //         if (data['medicalhistory']['last_menstrual_period'] !== null) {
  //           this.consultForm.get('last_menstrual_period').setValue(moment(data['medicalhistory']['last_menstrual_period']).format('DD-MM-YYYY'));
  //           this.lmpDate = data['medicalhistory']['last_menstrual_period'];
  //         }
  //         this.consultForm.get('number_of_pregnancy').setValue(data['medicalhistory']['number_of_pregnancy']);
  //         this.consultForm.get('gravida').setValue(data['medicalhistory']['gravida']);
  //         this.consultForm.get('para').setValue(data['medicalhistory']['para']);
  //         this.consultForm.get('abortions').setValue(data['medicalhistory']['abortions']);

  //         this.consultForm.get('history_of_present_illness').setValue(data['medicalhistory']['history_of_present_illness']);
  //         this.consultForm.get('chief_complaint').setValue(data['medicalhistory']['chief_complaint']);

  //         // this.consultForm.get('gynaecological_history').setValue(data['medicalhistory']['gynaecological_history']);
  //         //  this.consultForm.get('gender').setValue(data['medicalhistory']['female']);
  //       }
  //       if (data['physicalexamination']) {
  //         this.consultForm.get('weight').setValue(data['physicalexamination']['weight']);
  //         this.consultForm.get('height').setValue(data['physicalexamination']['height']);
  //         this.consultForm.get('bmi').setValue(data['physicalexamination']['bmi']);
  //         this.consultForm.get('nutrition').setValue(data['physicalexamination']['nutrition']);
  //         this.consultForm.get('nail_changes').setValue(data['physicalexamination']['nail_changes']);
  //         this.consultForm.get('clubbing_of_fingers').setValue(data['physicalexamination']['clubbing_of_fingers']);
  //         this.consultForm.get('cyanosis').setValue(data['physicalexamination']['cyanosis']);
  //         this.consultForm.get('icterus_jaundice').setValue(data['physicalexamination']['icterus_jaundice']);
  //         this.consultForm.get('pallor').setValue(data['physicalexamination']['pallor']);
  //         this.consultForm.get('lymph_nodes').setValue(data['physicalexamination']['lymph_nodes']);
  //         this.consultForm.get('oedema').setValue(data['physicalexamination']['oedema']);
  //         this.consultForm.get('sclera').setValue(data['physicalexamination']['sclera']);
  //       }
  //       if (data['systemicexamination']) {
  //         this.consultForm.get('respiratory_system').setValue(data['systemicexamination']['respiratory_system']);
  //         this.consultForm.get('gastro_intestinal_system').setValue(data['systemicexamination']['gastro_intestinal_system']);
  //         this.consultForm.get('cardio_vascular_system').setValue(data['systemicexamination']['cardio_vascular_system']);
  //         this.consultForm.get('genitourinary_system').setValue(data['systemicexamination']['genitourinary_system']);
  //         this.consultForm.get('musculoskeletal_system').setValue(data['systemicexamination']['musculoskeletal_system']);
  //         this.consultForm.get('central_nervous_system').setValue(data['systemicexamination']['central_nervous_system']);
  //         this.consultForm.get('eye').setValue(data['systemicexamination']['eye']);
  //         this.consultForm.get('ear').setValue(data['systemicexamination']['ear']);
  //         this.consultForm.get('nose').setValue(data['systemicexamination']['nose']);
  //         this.consultForm.get('mouth').setValue(data['systemicexamination']['mouth']);
  //         this.consultForm.get('throat').setValue(data['systemicexamination']['throat']);
  //         this.consultForm.get('neck').setValue(data['systemicexamination']['neck']);
  //         this.consultForm.get('skin').setValue(data['systemicexamination']['skin']);
  //         this.consultForm.get('psychiatric_history').setValue(data['systemicexamination']['psychiatric_history']);
  //       }
  //       if (data['diagnosis']) {
  //         this.consultForm.get('primary_diagnosis').setValue(data['diagnosis']['primary_diagnosis']);
  //         this.consultForm.get('secondary_diagnosis').setValue(data['diagnosis']['secondary_diagnosis']);
  //         this.consultForm.get('differential_diagnosis').setValue(data['diagnosis']['differential_diagnosis']);
  //         this.consultForm.get('final_diagnosis').setValue(data['diagnosis']['final_diagnosis']);
  //         this.consultForm.get('icd_10_codes').setValue(data['diagnosis']['icd_10_codes']);
  //       }
  //       if (consultation_status == "Not Started" || consultation_status == "Started") {
  //         this.detailView = true;
  //         this.loading = false;
  //       } else {
  //         this.detailView = true;
  //         this.dataOnly();
  //         this.investigationForm.disable();
  //         this.consultForm.disable();
  //         this.prescriptionForm.disable()
  //         this.loading = false;
  //       }
  //       this.loading = false;
  //     }, error => {
  //       console.log(error)
  //       const status = error['status'];
  //       if (status == 400) {
  //         this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
  //       }
  //       else {
  //         this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
  //       }
  //       this.showConsultHistory = true;
  //       this.loading = false;
  //     }
  //   );
  // }
  // //Investigation functionalities
  // updateInvestigation(category, event) {
  //   if (!this.addedInvestigationCategories.includes(category)) {
  //     this.addedInvestigationCategories.push(category);
  //   }
  //   this.investigations[category] = event;
  //   console.log(event);
  // }

  // processInvestigation(category) {
  //   console.log(category);
  //   const investigations_added = this.investigations[category];
  //   this.investigationsPayload = this.investigationsPayload.filter(
  //     (investig) => investig.category !== category
  //   );
  //   if (investigations_added.length > 0) {
  //     investigations_added.forEach(element => {
  //       // const len = this.investigationsPayload.length;
  //       const investData = {
  //         uuid: element['uuid'] || null,
  //         category: category,
  //         investigation_name: element['investigation_name']
  //       };

  //       // if(this.investigationsPayload.some(investig => investig.investigation_name === element['investigation_name'])){
  //       //   console.log('already available');
  //       // }
  //       // else{
  //       this.investigationsPayload.push(investData);
  //       // }
  //     });
  //     const data = {
  //       investigation: {
  //         key_advice: "Test_description",
  //         other_observations: "Test_description",
  //         tests_prescribed: {
  //           tests: this.investigationsPayload,
  //         }
  //       }
  //     };
  //     this.teleConsultService.addPrescription(this.consultationId, data).subscribe(data => {

  //       // this.investigationsPayload = [];

  //     },
  //       err => {
  //         console.log('ERROR:' + err.message);
  //       });
  //   }
  //   console.log(this.investigationsPayload);
  // }

  // renderInvestigations(data) {
  //   data.forEach(invObject => {
  //     if (invObject.category == 'HAEMATOLOGY') {
  //       let haem = this.investigationForm.get('haematology').value;
  //       console.log(haem);
  //       if (!haem.includes(invObject['uuid'])) {
  //         haem.push(invObject['uuid']);
  //         this.investigationForm.get('haematology').patchValue(
  //           haem);
  //       }
  //     }

  //     if (invObject.category == 'BIOCHEMISTRY AND IMMUNOASSAYS') {
  //       let bioc = this.investigationForm.get('biochemistryAndImmunoassay').value;
  //       if (!bioc.includes(invObject['uuid'])) {
  //         bioc.push(invObject['uuid']);
  //         this.investigationForm.get('biochemistryAndImmunoassay').patchValue(
  //           bioc);
  //       }
  //     }

  //     if (invObject.category == 'MICROBIOLOGY') {
  //       let micbio = this.investigationForm.get('microbiology').value;
  //       if (!micbio.includes(invObject['uuid'])) {
  //         micbio.push(invObject['uuid']);
  //         this.investigationForm.get('microbiology').patchValue(
  //           micbio);
  //       }
  //     }

  //     if (invObject.category == 'CLINICAL PATHOLOGY') {
  //       let clinicPath = this.investigationForm.get('clinicalPathology').value;
  //       if (!clinicPath.includes(invObject['uuid'])) {
  //         clinicPath.push(invObject['uuid']);
  //         this.investigationForm.get('clinicalPathology').patchValue(
  //           clinicPath);
  //       }
  //     }

  //     if (invObject.category == 'PATHOLOGY') {
  //       let path = this.investigationForm.get('pathology').value;
  //       if (!path.includes(invObject['uuid'])) {
  //         path.push(invObject['uuid']);
  //         this.investigationForm.get('pathology').patchValue(
  //           path);
  //       }
  //     }

  //     if (invObject.category == 'SEROLOGY') {
  //       let sero = this.investigationForm.get('serology').value;
  //       if (!sero.includes(invObject['uuid'])) {
  //         sero.push(invObject['uuid']);
  //         this.investigationForm.get('serology').patchValue(
  //           sero);
  //       }
  //     }

  //     if (invObject.category == 'MALARIA') {
  //       let mal = this.investigationForm.get('malaria').value;
  //       if (!mal.includes(invObject['uuid'])) {
  //         mal.push(invObject['uuid']);
  //         this.investigationForm.get('malaria').patchValue(
  //           mal);
  //       }
  //     }

  //     if (invObject.category == 'FILARIASIS') {
  //       let fil = this.investigationForm.get('filariasis').value;
  //       if (!fil.includes(invObject['uuid'])) {
  //         fil.push(invObject['uuid']);
  //         this.investigationForm.get('filariasis').patchValue(
  //           fil);
  //       }
  //     }

  //     if (invObject.category == 'DENGUE') {
  //       let den = this.investigationForm.get('dengue').value;
  //       if (!den.includes(invObject['uuid'])) {
  //         den.push(invObject['uuid']);
  //         this.investigationForm.get('dengue').patchValue(
  //           den);
  //       }
  //     }

  //     if (invObject.category == 'JAPANESE ENCEPHALITIS') {
  //       let den = this.investigationForm.get('japaneseEncephalitis').value;
  //       if (!den.includes(invObject['uuid'])) {
  //         den.push(invObject['uuid']);
  //         this.investigationForm.get('japaneseEncephalitis').patchValue(
  //           den);
  //       }
  //     }

  //     if (invObject.category == 'CHIKUNGUNYA') {
  //       let chik = this.investigationForm.get('chikungunya').value;
  //       if (!chik.includes(invObject['uuid'])) {
  //         chik.push(invObject['uuid']);
  //         this.investigationForm.get('chikungunya').patchValue(
  //           chik);
  //       }
  //     }

  //     if (invObject.category == 'SCRUB TYPHUS') {
  //       let scrubTyp = this.investigationForm.get('scrubTyphus').value;
  //       if (!scrubTyp.includes(invObject['uuid'])) {
  //         scrubTyp.push(invObject['uuid']);
  //         this.investigationForm.get('scrubTyphus').patchValue(
  //           scrubTyp);
  //       }
  //     }

  //     if (invObject.category == 'LEPTOSPIROSIS') {
  //       let lepto = this.investigationForm.get('leptospirosis').value;
  //       if (!lepto.includes(invObject['uuid'])) {
  //         lepto.push(invObject['uuid']);
  //         this.investigationForm.get('leptospirosis').patchValue(
  //           lepto);
  //       }
  //     }

  //     if (invObject.category == 'BRUCELLOSIS') {
  //       let bruce = this.investigationForm.get('brucellosis').value;
  //       if (!bruce.includes(invObject['uuid'])) {
  //         bruce.push(invObject['uuid']);
  //         this.investigationForm.get('brucellosis').patchValue(
  //           bruce);
  //       }
  //     }

  //     if (invObject.category == 'TUBERCULOSIS') {
  //       let tb = this.investigationForm.get('tuberculosis').value;
  //       if (!tb.includes(invObject['uuid'])) {
  //         tb.push(invObject['uuid']);
  //         this.investigationForm.get('tuberculosis').patchValue(
  //           tb);
  //       }
  //     }

  //     if (invObject.category == 'HIV') {
  //       let hiv = this.investigationForm.get('hiv').value;
  //       if (!hiv.includes(invObject['uuid'])) {
  //         hiv.push(invObject['uuid']);
  //         this.investigationForm.get('hiv').patchValue(
  //           hiv);
  //       }
  //     }

  //     if (invObject.category == 'HEPATITIS B') {
  //       let hepB = this.investigationForm.get('hepatitisB').value;
  //       if (!hepB.includes(invObject['uuid'])) {
  //         hepB.push(invObject['uuid']);
  //         this.investigationForm.get('hepatitisB').patchValue(
  //           hepB);
  //       }
  //     }

  //     if (invObject.category == 'HEPATITIS C') {
  //       let hepC = this.investigationForm.get('hepatitisC').value;
  //       if (!hepC.includes(invObject['uuid'])) {
  //         hepC.push(invObject['uuid']);
  //         this.investigationForm.get('hepatitisC').patchValue(
  //           hepC);
  //       }
  //     }

  //     if (invObject.category == 'HEPATITIS A') {
  //       let hepA = this.investigationForm.get('hepatitisA').value;
  //       if (!hepA.includes(invObject['uuid'])) {
  //         hepA.push(invObject['uuid']);
  //         this.investigationForm.get('hepatitisA').patchValue(
  //           hepA);
  //       }
  //     }

  //     if (invObject.category == 'HEPATITIS E') {
  //       let hepE = this.investigationForm.get('hepatitisE').value;
  //       if (!hepE.includes(invObject['uuid'])) {
  //         hepE.push(invObject['uuid']);
  //         this.investigationForm.get('hepatitisE').patchValue(
  //           hepE);
  //       }
  //     }

  //     if (invObject.category == 'HBC (CORE ANTIBODIES)') {
  //       let hbc = this.investigationForm.get('hbc').value;
  //       if (!hbc.includes(invObject['uuid'])) {
  //         hbc.push(invObject['uuid']);
  //         this.investigationForm.get('hbc').patchValue(
  //           hbc);
  //       }
  //     }

  //     if (invObject.category == 'OTHER DIAGNOSTIC TESTS') {
  //       let odt = this.investigationForm.get('otherDiagnosticTest').value;
  //       if (!odt.includes(invObject['uuid'])) {
  //         odt.push(invObject['uuid']);
  //         this.investigationForm.get('otherDiagnosticTest').patchValue(
  //           odt);
  //       }
  //     }

  //     if (invObject.category == 'RADIOLOGY & OTHER DIAGNOSTIC TESTS') {
  //       let rodt = this.investigationForm.get('radiologyAndOtherDiagnostics').value;
  //       if (!rodt.includes(invObject['uuid'])) {
  //         rodt.push(invObject['uuid']);
  //         this.investigationForm.get('radiologyAndOtherDiagnostics').patchValue(
  //           rodt);
  //       }
  //     }
  //   });
  // }

  // consultationDataDirectSave(category, type, event) {
  //   debugger;
  //   let sdata: any;
  //   if (type == 'last_menstrual_period') {
  //     // console.log(this.lmpDate);
  //     sdata = event;//moment(event).format('YYYY-MM-DD');
  //     console.log(sdata);
  //   }
  //   else {
  //     sdata = event.target.value;
  //   }
  //   // console.log(event.target.value);
  //   // if(type=='menstrual_history'){
  //   //    sdata = '2020-12-03';
  //   // console.log(event.target.value);
  //   // }
  //   let daat = {};
  //   daat[type] = sdata;
  //   let consultData = {};
  //   consultData[category] = daat;
  //   console.log(consultData);
  //   this.teleConsultService.addPrescription(this.consultationId, consultData).subscribe(data => {
  //     // console.log(type);
  //     // this.consultForm.get(type).setValue(sdata);
  //   },
  //     err => {
  //       console.log('ERROR:' + err.message);
  //     });
  // }

  // delPrescription(i) {
  //   console.log(i);
  //   const delData = this.prescriptionArray.at(i).value;
  //   this.prescriptionArray.removeAt(i);
  //   this.prescriptionForm = this.formBuilder.group({
  //     prescriptionArray: this.formBuilder.array([]),
  //   });

  //   let data = this.savedData['prescription']['prescription']['drugs_prescribed'];

  //   this.createPrescriptionForm(data);

  // }

  // loadFemData(value) {
  //   console.log(value);
  //   this.female = value;
  //   let daat = {};
  //   if (!!value) {
  //     daat['female'] = true;
  //   } else {
  //     daat['female'] = false;
  //   }
  //   let consultData = {};
  //   consultData['medical_history'] = daat;
  //   this.teleConsultService.addPrescription(this.consultationId, consultData).subscribe(data => {

  //   },
  //     err => {
  //       console.log('ERROR:' + err.message);
  //     })
  //   // this.consultForm.get('last_menstrual_period').setValue(moment(new Date(this.savedData['medicalhistory']['last_menstrual_period'])).format('DD-MM-YYYY'));
  //   // console.log(moment(this.savedData['medicalhistory']['last_menstrual_period']).format('DD-MM-YYYY'));
  // }

  // deActiveTab() {
  //   this.prescriptionTabDeactive = true;
  // }
  // getConsultaionDocuments(data, id) {
  //   this.teleConsultService.getDocument(id, data).subscribe(
  //     (data) => {
  //       const result = data['results']
  //       if (result.length > 0) {
  //         this.openFile(result[0]['file'])

  //       } else {
  //         this.notificationService.error('Pdf  file not found ')
  //       }


  //     },
  //     (error) => {
  //       console.log(error);
  //       this.disabledDownloadPrescriptionBtn = false;
  //       const status = error['status'];
  //       if (status == 400) {
  //         this.notificationService.error(
  //           `${error['statusText']}`,
  //           'Med.Bot'
  //         );
  //       } else {
  //         this.notificationService.error(
  //           `${error['statusText']}`,
  //           'Med.Bot'
  //         );
  //       }
  //     }
  //   );
  // }

  // getConsultationId(consultation_uuid) {
  //   this.loading = true;
  //   this.prescriptionTabDeactive = false;
  //   if (consultation_uuid == 'all') {
  //     this.consultationDataList = this.allConsultationsData;
  //     console.log(this.allConsultationsData);
  //     this.allData = true;
  //     this.showPagination = true;
  //     this.loading = false;
  //   }
  //   else {
  //     this.showPagination = false;
  //     this.consultationId = consultation_uuid;
  //     console.log(this.consultationId);
  //     this.consultationDataList = this.allConsultationsData.filter(obj => obj.uuid == consultation_uuid);
  //     this.allData = true;
  //     this.loading = false;
  //   }
  // }

  // downloadPrescription(id) {
  //   const data = 'Prescription';
  //   this.getConsultaionDocuments(data, id)
  // }

  // downloadInvestigation(id) {
  //   let investigation = false;
  //   const data = 'Investigation';
  //   this.getConsultaionDocuments(data, id)
  //   // for(let data of  this.consultationDocuments){
  //   //         if(data.purpose=='Investigation'){
  //   //           investigation= true;
  //   //           window.open(data.file)
  //   //         }
  //   // }
  //   // if(!investigation){

  //   //   this.notificationService.warning(
  //   //     `No investigation data`,
  //   //     'Med.Bot'
  //   //   );
  //   // }

  // }


  // underScoreToSpaceCaps(str) {
  //   const string = str.split('_');
  //   for (let i = 0; i < string.length; i++) {
  //     string[i] = string[i].charAt(0).toUpperCase() + string[i].slice(1);
  //   }
  //   return string.join(' ');
  // }

  // processInvestigationData(data, type) {

  //   let nameList = [];
  //   let investigationData = {};

  //   if (data['investigation']['tests_prescribed']) {
  //     data['investigation']['tests_prescribed'].tests.forEach(investigationObj => {
  //       if (!nameList.includes(investigationObj.category)) {
  //         nameList.push(investigationObj.category);
  //         investigationData[investigationObj.category] = [];
  //         investigationData[investigationObj.category].push(investigationObj.investigation_name);
  //       }
  //       else {
  //         investigationData[investigationObj.category].push(investigationObj.investigation_name);
  //       }
  //     });
  //     // console.log(investigationData);
  //     return investigationData[type];
  //   }
  //   else {
  //     return '';
  //   }
  // }
  // consultationsNextPageList() {
  //   this.consultationsCurrentPage = this.consultationsCurrentPage + 1;
  //   if (this.consultationsTotalPage >= this.consultationsCurrentPage) {
  //     this.getconsultationsList(this.consultationsCurrentPage);
  //   } else {
  //     this.consultationsCurrentPage = this.consultationsCurrentPage - 1;
  //   }
  // }
  // consultationsLastPageList() {
  //   this.getconsultationsList(this.consultationsTotalPage);
  // }
  // consultationsFirstPageList() {
  //   this.consultationsCurrentPage = 1;
  //   this.getconsultationsList(this.consultationsCurrentPage);
  // }
  // consultationsPreviousPageList() {
  //   this.consultationsCurrentPage = this.consultationsCurrentPage - 1;
  //   if (this.consultationsTotalPage >= this.consultationsCurrentPage && this.consultationsCurrentPage > 0) {
  //     this.getconsultationsList(this.consultationsCurrentPage);
  //   } else {
  //     this.consultationsCurrentPage = this.consultationsCurrentPage + 1;
  //   }
  // }

  // getconsultationsList(page) {
  //   this.consultationsLoading = true;
  //   this.consultationsTotalPage = 0;
  //   this.teleConsultService.getConsutlHistory(this.patientUuid, this.doctorUuid, page).subscribe(
  //     (data) => {
  //       this.pastAppointments = data['results'];
  //       console.log(this.pastAppointments);
  //       this.consultationsTotalPage = data['total_pages'];
  //       this.consultationsCurrentPage = data['page_number'];
  //       this.consultationsLoading = false;
  //       const value = this.selectedConsultationDate.value;
  //       if (value == 'all') {
  //         this.getConsultationId('all');
  //       } else {
  //         this.getConsultationId(this.consultationId);
  //       }
  //       this.loading = false;
  //     }, error => {
  //       this.consultationsLoading = false;
  //     })
  // }

  // back() {
  //   this.location.back();
  // }

  // getRecording(data) {
  //   const id = data['prescription']['consultation'];
  //   const purpose = 'Screenshot';
  //   this.teleConsultService.getRecordingData(id).subscribe(
  //     (data) => {
  //       this.recordingData = data
  //     }, error => {
  //       console.log(error);
  //     })
  //   this.teleConsultService.getDocument(id, purpose).subscribe(
  //     (data) => {
  //       this.screenshot = data['results'];
  //       // if(this.screenshot.length===0){
  //       //   this.notificationService.error(' image not found ');
  //       // }


  //     },
  //     (error) => {
  //       console.log(error);
  //       this.disabledDownloadPrescriptionBtn = false;
  //       const status = error['status'];
  //       if (status == 400) {
  //         this.notificationService.error(
  //           `${error['statusText']}`,
  //           'Med.Bot'
  //         );
  //       } else {
  //         this.notificationService.error(
  //           `${error['statusText']}`,
  //           'Med.Bot'
  //         );
  //       }
  //     }
  //   );
  // }
  // openVideo(videoFile) {
  //   if (videoFile !== null) {
  //     this.openFile(videoFile);
  //   } else {
  //     this.notificationService.error('Video file not found');
  //   }
  // }
  // downloadSummery(id) {
  //   const data = 'ConsultationSummary';
  //   this.getConsultaionDocuments(data, id)


  // }

  // downloadInvoice(id, user) {
  //   let user_type = this.userType;
  //   if (user) {
  //     user_type = user.user_type;
  //   }

  //   var invoice_type = "HospitalInvoice";
  //   if (user_type == "Partner") {
  //     invoice_type = "PartnerInvoice"
  //   } else if (user_type == "Patient") {
  //     invoice_type = "Invoice"
  //   }

  //   this.teleConsultService.getDocument(id, invoice_type).subscribe(
  //     (data) => {
  //       const result = data['results']
  //       if (result.length > 0) {
  //         this.openFile(result[0]['file'])

  //       } else {
  //         this.notificationService.error('Pdf  file not found ')
  //       }
  //     },
  //     (error) => {
  //       console.log(error);
  //       const status = error['status'];
  //       if (status == 400) {
  //         this.notificationService.error(
  //           `${error['statusText']}`,
  //           'Med.Bot'
  //         );
  //       } else {
  //         this.notificationService.error(
  //           `${error['statusText']}`,
  //           'Med.Bot'
  //         );
  //       }
  //     }
  //   );
  // }
  // downloadHistory() {
  //   $('#download_history_btn').prop('disabled', true);
  //   this.download = true;
  //   this.teleConsultService.getConsutlHistoryDocument(this.patientUuid, this.doctorUuid).subscribe(
  //     (data) => {
  //       this.download = false;
  //       $('#download_history_btn').prop('disabled', false);
  //       const result = data['results'];
  //       if (result.length > 0) {
  //         this.openFile(result?.[0]?.['file'])

  //       } else {
  //         this.notificationService.error('Pdf  file not found ');
  //       }


  //     },
  //     (error) => {
  //       $('#download_history_btn').prop('disabled', false);
  //       this.download = false;

  //       console.log(error);
  //       this.disabledDownloadPrescriptionBtn = false;
  //       const status = error['status'];
  //       if (status == 400) {
  //         this.notificationService.error(
  //           `${error['statusText']}`,
  //           'Med.Bot'
  //         );
  //       } else {
  //         this.notificationService.error(
  //           `${error['statusText']}`,
  //           'Med.Bot'
  //         );
  //       }
  //     }
  //   );



  // }
  // openFile(url) {
  //   const popUp = window.open(url);

  //   if (popUp == null || typeof (popUp) == 'undefined') {
  //     this.notificationService.warning('Please disable your pop-up blocker and download again.');

  //   }
  //   else {
  //     popUp.focus();
  //   }
  // }

}
