<div class="legend-cont">
    <h6 class="ml-2 legend">
      <i id="booked" class="fas fa-square"></i> &nbsp;Booked
    </h6>
    <h6 class="ml-2 legend">
      <i id="not-booked" class="fas fa-square"></i> &nbsp;Available
    </h6>
    <h6 class="ml-2 legend">
      <i id="unavail" class="fas fa-square"></i> &nbsp;Marked UnAvailable
    </h6>
    <h6 class="ml-2 legend">
      <i id="no-slot" class="fas fa-square"></i> &nbsp;No Slot Available
    </h6>
    <h6 class="ml-2 legend">
      <i id="selected" class="fas fa-square"></i> &nbsp;Selected
      <!-- <p class="legend">(click on the slot again to deselect)</p> -->
    </h6>
  </div>
  <div class="col-md-12">
    <div class="card">
      <div class="card-body top-ma">
        <div class="row">
          <div class="col-md-12">
            <div class="row row-no-gutters">
              <div class="col-md-4">
                <h4 class="mb-4">
                  <!-- <i
                    id="back-appt"
                    class="fas fa-chevron-circle-left"
                    (click)="returnToAppoinments()"
                  ></i> -->
                  Manage Appointments
                </h4>
              </div>
              <div class="col-md-4">
                <div class="row row-no-gutters">
                  <div class="col-md-6">
                    <h5 id="date-title" class="date-label">Select Date</h5>
                  </div>
                  <div class="col-md-6">
                    <input
                      id="date-drp-dwn"
                      type="text"
                      [maxDate]="maxDate"
                      [minDate]="minDate"
                      (ngModelChange)="getSelectedDateDays($event)"
                      onkeydown="return false"
                      class="form-control"
                      [ngModelOptions]="{ standalone: true }"
                      [(ngModel)]="todayDate"
                      bsDatepicker
                      [bsConfig]="{
                        showWeekNumbers: false,
                        isAnimated: true,
                        dateInputFormat: 'YYYY-MM-DD'
                      }"
                    />
                  </div>
                </div>
              </div>
              <div class="co-md-4">
                <button
                  *ngIf="slotsAvailable && blockedAppointments.length === 0"
                  id="mtsa-btn2"
                  [disabled]="appointments.length == 0"
                  class="slt-btn btn btn-info"
                  data-toggle="modal"
                  data-target="#confirmMaModal"
                >
                  Mark Slots Unavailable
                </button>
                <button
                  *ngIf="slotsAvailable && blockedAppointments.length > 0"
                  id="mtsa-btn2"
                  [disabled]="appointments.length == 0"
                  class="slt-btn btn btn-info"
                  data-toggle="modal"
                  data-target="#confirmMaModal"
                >
                  Mark Slots Available
                </button>
              </div>
              <p *ngIf="slotsAvailable && appointments.length == 0" class="text-danger legend-cont">
                *Select the slots to mark it unavailable
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <hr> -->
    <div *ngIf="isLoading">
      <app-haloading-spinner></app-haloading-spinner>
    </div>
      <table
        id="ma-table"
        class="table  table-center mb-0"
        style="overflow: auto" *ngIf="!isLoading"
      >
        <thead>
          <tr>
            <th>Time Slot</th>
  
            <th id="day-{{ i }}" *ngFor="let data of weekInfo; let i = index">
              &nbsp;{{ data["day"] }}
              <p class="sm-head">{{ data["date"] }}</p>
              <input
                *ngIf="slotsAvailable"
                (click)="modifySelect('slot ' + i, $event)"
                class="chk-box"
                type="checkbox"
                id="select-all-{{ i }}"
                value="slot{{ i }}"
                name="select"
              />
            </th>
          </tr>
        </thead>
  
        <tbody>
          <tr
            id="slot-row-{{ i }}"
            class="no-hr"
            *ngFor="let slotData of slots; let i = index"
          >
            <td class="no-border time-box">{{ slotData[0] }}</td>
            <td class="booked">
              <button
                id="slot{{ i }}-1"
                class="btn btn-booked"
                (click)="addAppointment(slotData[1], 'slot' + i + '-1')"
                [ngClass]="{
                  availCss: slotData[1]['status'] == 'Available',
                  bookedCss: slotData[1]['status'] == 'Booked',
                  noSlotCss: slotData[1]['status'] == 'No Slot',
                  blockedCss: slotData[1]['status'] == 'Blocked',
                  selectedCss: selectedIdList.includes('slot' + i + '-1')
                }"
              ></button>
            </td>
            <td class="booked">
              <button
                id="slot{{ i }}-2"
                class="btn btn-booked"
                (click)="addAppointment(slotData[2], 'slot' + i + '-2')"
                [ngClass]="{
                  availCss: slotData[2]['status'] == 'Available',
                  bookedCss: slotData[2]['status'] == 'Booked',
                  noSlotCss: slotData[2]['status'] == 'No Slot',
                  blockedCss: slotData[2]['status'] == 'Blocked',
                  selectedCss: selectedIdList.includes('slot' + i + '-2')
                }"
              ></button>
            </td>
            <td class="booked">
              <button
                id="slot{{ i }}-3"
                class="btn btn-booked"
                (click)="addAppointment(slotData[3], 'slot' + i + '-3')"
                [ngClass]="{
                  availCss: slotData[3]['status'] == 'Available',
                  bookedCss: slotData[3]['status'] == 'Booked',
                  noSlotCss: slotData[3]['status'] == 'No Slot',
                  blockedCss: slotData[3]['status'] == 'Blocked',
                  selectedCss: selectedIdList.includes('slot' + i + '-3')
                }"
              ></button>
            </td>
            <td class="booked">
              <button
                id="slot{{ i }}-4"
                class="btn btn-booked"
                (click)="addAppointment(slotData[4], 'slot' + i + '-4')"
                [ngClass]="{
                  availCss: slotData[4]['status'] == 'Available',
                  bookedCss: slotData[4]['status'] == 'Booked',
                  noSlotCss: slotData[4]['status'] == 'No Slot',
                  blockedCss: slotData[4]['status'] == 'Blocked',
                  selectedCss: selectedIdList.includes('slot' + i + '-4')
                }"
              ></button>
            </td>
            <td class="booked">
              <button
                id="slot{{ i }}-5"
                class="btn btn-booked"
                (click)="addAppointment(slotData[5], 'slot' + i + '-5')"
                [ngClass]="{
                  availCss: slotData[5]['status'] == 'Available',
                  bookedCss: slotData[5]['status'] == 'Booked',
                  noSlotCss: slotData[5]['status'] == 'No Slot',
                  blockedCss: slotData[5]['status'] == 'Blocked',
                  selectedCss: selectedIdList.includes('slot' + i + '-5')
                }"
              ></button>
            </td>
            <td class="booked">
              <button
                id="slot{{ i }}-6"
                class="btn btn-booked"
                (click)="addAppointment(slotData[6], 'slot' + i + '-6')"
                [ngClass]="{
                  availCss: slotData[6]['status'] == 'Available',
                  bookedCss: slotData[6]['status'] == 'Booked',
                  noSlotCss: slotData[6]['status'] == 'No Slot',
                  blockedCss: slotData[6]['status'] == 'Blocked',
                  selectedCss: selectedIdList.includes('slot' + i + '-6')
                }"
              ></button>
            </td>
            <td class="booked">
              <button
                id="slot{{ i }}-7"
                class="btn btn-booked"
                (click)="addAppointment(slotData[7], 'slot' + i + '-7')"
                [ngClass]="{
                  availCss: slotData[7]['status'] == 'Available',
                  bookedCss: slotData[7]['status'] == 'Booked',
                  noSlotCss: slotData[7]['status'] == 'No Slot',
                  blockedCss: slotData[7]['status'] == 'Blocked',
                  selectedCss: selectedIdList.includes('slot' + i + '-7')
                }"
              ></button>
            </td>
          </tr>
        </tbody>
      </table>
  
  </div>
    <p class="legend-cont" *ngIf="!slotsAvailable">No Slots Available</p>
    <p
      *ngIf="slotsAvailable && appointments.length == 0"
      class="text-danger legend-cont"
    >
      *Select the slots to mark it unavailable
    </p>
    <div *ngIf="slotsAvailable" class="text-center">
      <button
        id="mtsa-btn"
        *ngIf="slotsAvailable && blockedAppointments.length == 0"
        [disabled]="appointments.length == 0"
        class="slt-btn btn btn-info"
        data-toggle="modal"
        data-target="#confirmMaModal"
      >
        Mark the Slots Unavailable
      </button>
      <button
        *ngIf="slotsAvailable && blockedAppointments.length > 0"
        id="mtsa-btn"
        [disabled]="appointments.length == 0"
        class="slt-btn btn btn-info"
        data-toggle="modal"
        data-target="#confirmMaModal"
      >
        Mark Slots Available
      </button>
      <!-- <button id="cs-btn" [disabled]="selectedIdList.length==0" (click)="clearSelection()" class="slt-btn btn btn-info">Clear Selection</button> -->
    </div>
  
  
  <!-- Modal -->
  <div
    class="modal fade"
    id="confirmMaModal"
    tabindex="-1"
    role="dialog"
    aria-labelledby="confirmMaModalLabel"
    aria-hidden="true"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="confirmMaModalLabel">Confirmation</h5>
          <button
            id="mamodal-top-close"
            type="button"
            class="close"
            data-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div *ngIf="blockedAppointments.length === 0">
            <h5 id="confirm-ques">
              Are you sure to make unavailable the selected slots?
            </h5>
            <p id="slot-count">Slots Selected : {{ appointments.length }}</p>
            <p id="slot-book-count">
              Booked Slots : {{ bookedAppointments.length }}
            </p>
            <p class="text-danger" *ngIf="bookedAppointments.length === 1">
              This will cancel {{ bookedAppointments.length }} booked appointment
            </p>
            <p class="text-danger" *ngIf="bookedAppointments.length > 1">
              This will cancel {{ bookedAppointments.length }} booked appointments
            </p>
          </div>
          <div *ngIf="blockedAppointments.length > 0">
            <h5 id="confirm-ques">
              Are you sure to make available the selected slots?
            </h5>
            <p id="slot-count">
              Slots Selected : {{ blockedAppointments.length }}
            </p>
          </div>
        </div>
        <div class="modal-footer">
          <button
            id="cls-mamodal-btn"
            type="button"
            (click)="clearSelection()"
            class="btn btn-secondary"
            data-dismiss="modal"
          >
            Close
          </button>
          <button
            id="cnfrm-mamodal-btn"
            *ngIf="blockedAppointments.length === 0"
            type="button"
            (click)="markSlotsUnavailable(appointments)"
            class="btn btn-primary"
          >
            Confirm
          </button>
          <button
            id="cnfrm-mamodal-btn"
            *ngIf="blockedAppointments.length > 0"
            type="button"
            (click)="markSlotsAvailable(appointments)"
            class="btn btn-primary"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  </div>
  
