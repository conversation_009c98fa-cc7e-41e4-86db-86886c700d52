.disabled-pagination {
  color: darkgray !important;
  pointer-events: none !important;
}

.btn-search .btn {
  background-color: #0de0fe;
  border: 1px solid #0de0fe;
  color: #fff;
  height: 38px;
  font-weight: 500;
  font-size: 16px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

.warring-message {
  color: red;
}

.btn-back {
  padding: 0px 6px;
}

.fas {
  color: #20c0f3;
  cursor: pointer;
}

.check-aline {
  margin-top: 8px;
}

::ng-deep .ng-select .ng-select-container {
  color: #333;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ccc;
  min-height: 46px !important;
  align-items: center;
}

.back-head {
  margin-top: 0px;
  color: #20c0f3;
}