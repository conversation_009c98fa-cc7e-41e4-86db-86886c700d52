import { ToastrService } from 'ngx-toastr';
import { HttpClient } from '@angular/common/http';
import { delay } from 'rxjs/operators';
import {
  Validators,
  FormGroup,
  FormBuilder,
  FormArray,
  FormControl,
} from '@angular/forms';
import { DoctorService } from '../../doctor/doctor.service';
import { Component, OnInit, Input, OnChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import * as Settings from '../../config/settings';
import *  as moment from 'moment';
import { ActivatedRoute, Router } from '@angular/router';
declare var $: any;

@Component({
  selector: 'app-hadoctor-consultatiion',
  templateUrl: './hadoctor-consultatiion.component.html',
  styleUrls: ['./hadoctor-consultatiion.component.css'],
})
export class HadoctorConsultatiionComponent implements OnInit, OnChanges {
  @Input() selectedLocation: any;
  @Input() feeDetailAvailable: any;
  public consultingForm: FormGroup;
  public daysForm: FormGroup;
  public unavailabilityForm: FormGroup;
  public locations = [];
  public doctor = {};
  public location_data: any = [];
  public todayDate = new Date();
  public dataAvailable = false;
  public editAvailability = true;
  public submitted = false;
  public time = [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60];
  public time_slots = [{
    "slot_12": "0:00 AM",
    "slot_24": "0:00"
  },
  {
    "slot_12": "0:15 AM",
    "slot_24": "0:15"
  },
  {
    "slot_12": "0:30 AM",
    "slot_24": "0:30"
  },
  {
    "slot_12": "0:45 AM",
    "slot_24": "0:45"
  },
  {
    "slot_12": "1:00 AM",
    "slot_24": "1:00"
  },
  {
    "slot_12": "1:15 AM",
    "slot_24": "1:15"
  },
  {
    "slot_12": "1:30 AM",
    "slot_24": "1:30"
  },
  {
    "slot_12": "1:45 AM",
    "slot_24": "1:45"
  },
  {
    "slot_12": "2:00 AM",
    "slot_24": "2:00"
  },
  {
    "slot_12": "2:15 AM",
    "slot_24": "2:15"
  },
  {
    "slot_12": "2:30 AM",
    "slot_24": "2:30"
  },
  {
    "slot_12": "2:45 AM",
    "slot_24": "2:45"
  },
  {
    "slot_12": "3:00 AM",
    "slot_24": "3:00"
  },
  {
    "slot_12": "3:15 AM",
    "slot_24": "3:15"
  },
  {
    "slot_12": "3:30 AM",
    "slot_24": "3:30"
  },
  {
    "slot_12": "3:45 AM",
    "slot_24": "3:45"
  },
  {
    "slot_12": "4:00 AM",
    "slot_24": "4:00"
  },
  {
    "slot_12": "4:15 AM",
    "slot_24": "4:15"
  },
  {
    "slot_12": "4:30 AM",
    "slot_24": "4:30"
  },
  {
    "slot_12": "4:45 AM",
    "slot_24": "4:45"
  },
  {
    "slot_12": "5:00 AM",
    "slot_24": "5:00"
  },
  {
    "slot_12": "5:15 AM",
    "slot_24": "5:15"
  },
  {
    "slot_12": "5:30 AM",
    "slot_24": "5:30"
  },
  {
    "slot_12": "5:45 AM",
    "slot_24": "5:45"
  },
  {
    "slot_12": "6:00 AM",
    "slot_24": "6:00"
  },
  {
    "slot_12": "6:15 AM",
    "slot_24": "6:15"
  },
  {
    "slot_12": "6:30 AM",
    "slot_24": "6:30"
  },
  {
    "slot_12": "6:45 AM",
    "slot_24": "6:45"
  },
  {
    "slot_12": "7:00 AM",
    "slot_24": "7:00"
  },
  {
    "slot_12": "7:15 AM",
    "slot_24": "7:15"
  },
  {
    "slot_12": "7:30 AM",
    "slot_24": "7:30"
  },
  {
    "slot_12": "7:45 AM",
    "slot_24": "7:45"
  },
  {
    "slot_12": "8:00 AM",
    "slot_24": "8:00"
  },
  {
    "slot_12": "8:15 AM",
    "slot_24": "8:15"
  },
  {
    "slot_12": "8:30 AM",
    "slot_24": "8:30"
  },
  {
    "slot_12": "8:45 AM",
    "slot_24": "8:45"
  },
  {
    "slot_12": "9:00 AM",
    "slot_24": "9:00"
  },
  {
    "slot_12": "9:15 AM",
    "slot_24": "9:15"
  },
  {
    "slot_12": "9:30 AM",
    "slot_24": "9:30"
  },
  {
    "slot_12": "9:45 AM",
    "slot_24": "9:45"
  },
  {
    "slot_12": "10:00 AM",
    "slot_24": "10:00"
  },
  {
    "slot_12": "10:15 AM",
    "slot_24": "10:15"
  },
  {
    "slot_12": "10:30 AM",
    "slot_24": "10:30"
  },
  {
    "slot_12": "10:45 AM",
    "slot_24": "10:45"
  },
  {
    "slot_12": "11:00 AM",
    "slot_24": "11:00"
  },
  {
    "slot_12": "11:15 AM",
    "slot_24": "11:15"
  },
  {
    "slot_12": "11:30 AM",
    "slot_24": "11:30"
  },
  {
    "slot_12": "11:45 AM",
    "slot_24": "11:45"
  },
  {
    "slot_12": "12:00 Noon",
    "slot_24": "12:00"
  },
  {
    "slot_12": "12:15 PM",
    "slot_24": "12:15"
  },
  {
    "slot_12": "12:30 PM",
    "slot_24": "12:30"
  },
  {
    "slot_12": "12:45 PM",
    "slot_24": "12:45"
  },
  {
    "slot_12": "1:00 PM",
    "slot_24": "13:00"
  },
  {
    "slot_12": "1:15 PM",
    "slot_24": "13:15"
  },
  {
    "slot_12": "1:30 PM",
    "slot_24": "13:30"
  },
  {
    "slot_12": "1:45 PM",
    "slot_24": "13:45"
  },
  {
    "slot_12": "2:00 PM",
    "slot_24": "14:00"
  },
  {
    "slot_12": "2:15 PM",
    "slot_24": "14:15"
  },
  {
    "slot_12": "2:30 PM",
    "slot_24": "14:30"
  },
  {
    "slot_12": "2:45 PM",
    "slot_24": "14:45"
  },
  {
    "slot_12": "3:00 PM",
    "slot_24": "15:00"
  },
  {
    "slot_12": "3:15 PM",
    "slot_24": "15:15"
  },
  {
    "slot_12": "3:30 PM",
    "slot_24": "15:30"
  },
  {
    "slot_12": "3:45 PM",
    "slot_24": "15:45"
  },
  {
    "slot_12": "4:00 PM",
    "slot_24": "16:00"
  },
  {
    "slot_12": "4:15 PM",
    "slot_24": "16:15"
  },
  {
    "slot_12": "4:30 PM",
    "slot_24": "16:30"
  },
  {
    "slot_12": "4:45 PM",
    "slot_24": "16:45"
  },
  {
    "slot_12": "5:00 PM",
    "slot_24": "17:00"
  },
  {
    "slot_12": "5:15 PM",
    "slot_24": "17:15"
  },
  {
    "slot_12": "5:30 PM",
    "slot_24": "17:30"
  },
  {
    "slot_12": "5:45 PM",
    "slot_24": "17:45"
  },
  {
    "slot_12": "6:00 PM",
    "slot_24": "18:00"
  },
  {
    "slot_12": "6:15 PM",
    "slot_24": "18:15"
  },
  {
    "slot_12": "6:30 PM",
    "slot_24": "18:30"
  },
  {
    "slot_12": "6:45 PM",
    "slot_24": "18:45"
  },
  {
    "slot_12": "7:00 PM",
    "slot_24": "19:00"
  },
  {
    "slot_12": "7:15 PM",
    "slot_24": "19:15"
  },
  {
    "slot_12": "7:30 PM",
    "slot_24": "19:30"
  },
  {
    "slot_12": "7:45 PM",
    "slot_24": "19:45"
  },
  {
    "slot_12": "8:00 PM",
    "slot_24": "20:00"
  },
  {
    "slot_12": "8:15 PM",
    "slot_24": "20:15"
  },
  {
    "slot_12": "8:30 PM",
    "slot_24": "20:30"
  },
  {
    "slot_12": "8:45 PM",
    "slot_24": "20:45"
  },
  {
    "slot_12": "9:00 PM",
    "slot_24": "21:00"
  },
  {
    "slot_12": "9:15 PM",
    "slot_24": "21:15"
  },
  {
    "slot_12": "9:30 PM",
    "slot_24": "21:30"
  },
  {
    "slot_12": "9:45 PM",
    "slot_24": "21:45"
  },
  {
    "slot_12": "10:00 PM",
    "slot_24": "22:00"
  },
  {
    "slot_12": "10:15 PM",
    "slot_24": "22:15"
  },
  {
    "slot_12": "10:30 PM",
    "slot_24": "22:30"
  },
  {
    "slot_12": "10:45 PM",
    "slot_24": "22:45"
  },
  {
    "slot_12": "11:00 PM",
    "slot_24": "23:00"
  },
  {
    "slot_12": "11:15 PM",
    "slot_24": "23:15"
  },
  {
    "slot_12": "11:30 PM",
    "slot_24": "23:30"
  },
  {
    "slot_12": "11:45 PM",
    "slot_24": "23:45"
  }
  ];
  public selectedTime = 30;
  public days = [];
  public doctor_uuid: '';
  public uuid: null;
  public key = 'practice_location';
  public formDisable = false;
  public consultationForm: FormGroup;
  public chArray: FormArray;
  public editConsultingHour = false;
  public consultationData = {
    uuid: null,
    time_from: null,
    time_to: null,
    effective_from: null,
    effective_upto: null,
    consultation_duration: null,
  };
  public weekDays = {
    Sunday: false,
    Monday: false,
    Tuesday: false,
    Wednesday: false,
    Thursday: false,
    Friday: false,
    Saturday: false
  };
  public hourData = [];
  public unavailabilityData = [];
  public daysModel = {};
  public unavailabilityDates = {
    uuid: '',
    fromDate: '',
    endDate: ''
  };
  public unavailDataAvailable = false;
  public selectedIndex: any;
  public noCreate = false;
  public sendConsultationData = {
    doctor: null,
    uuid: null,
    practice_location: null,
    time_from: null,
    time_to: null,
    effective_from: null,
    effective_upto: null,
    days_of_the_week: null,
    consultation_duration: null,

  }
  public selectedDays = false;
  selectedIndexValue: any;
  hideDeleteNotification: boolean;
  public feeList = [];
  public today: any;

  constructor(
    private doctorService: DoctorService,
    private formBuilder: FormBuilder,
    private notificationService: ToastrService,
    private route: Router,
    private translate: TranslateService,
    private httpClient: HttpClient,
    private activatedRoute: ActivatedRoute,
  ) {
    this.activatedRoute.params.subscribe(
      url => {
        console.log("url", url);
        this.doctor_uuid = url['uuid'];
        console.log(this.doctor_uuid);
      }
    );
  }

  ngOnInit(): void {
    // this.activatedRoute.params.subscribe(
    //   url => {
    //     console.log("url", url);
    //     this.doctor_uuid = url['uuid'];
    //     console.log(this.doctor_uuid);
    //   }
    // );
    console.log(this.feeDetailAvailable);
    console.log(this.selectedLocation);

    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this.addForm();
    this.formatTodayDate();
    this.createDateTime("12:05:00");
    this.doctorService.getHaDoctorPracticeLocations(this.doctor_uuid).subscribe(
      data => {
        data = data['results'];
        this.location_data.push(Object.values(data));
        this.location_data = this.location_data[0];
        const len = this.location_data.length;
        for (let i = 0; i < len; i++) {
          let data = this.location_data[i];
          if (data['name'] != null) {
            this.locations.push(data);
          }
        }
        this.locations = Object.values(this.locations);
        this.selectedLocation = this.locations[0];
        this.updateLocation(event, this.selectedLocation);
      }
    );
    this.consultingForm = this.formBuilder.group({
      doctor: [this.doctor_uuid, Validators.required],
      uuid: [null, Validators.required],
      practice_location: [null, Validators.required],
      time_from: [null, Validators.required],
      time_to: [null, Validators.required],
      effective_from: [null, Validators.required],
      effective_upto: [null, Validators.required],
      days_of_the_week: [[], Validators.required],
      consultation_duration: [null, Validators.required],
    });

    this.daysForm = this.formBuilder.group({
      Sunday: [null, Validators.required],
      Monday: [null, Validators.required],
      Tuesday: [null, Validators.required],
      Wednesday: [null, Validators.required],
      Thursday: [null, Validators.required],
      Friday: [null, Validators.required],
      Saturday: [null, Validators.required],
    });
    this.unavailabilityForm = this.formBuilder.group({
      uuid: null,
      practice_location: this.selectedLocation.uuid,
      doctor: this.doctor_uuid,
      consulting_hours_group: [null, Validators.required],
      start_datetime: [null, Validators.required],
      end_datetime: [null, Validators.required],
    });
    this.doctorService.getHaDoctorUnavailability(this.doctor_uuid).subscribe(
      data => {
        data = data['results'];
        const len = Object.values(data).length;
        for (let i = 0; i < len; i++) {
          let sep_data = data[i];
          this.unavailabilityData.push(sep_data);
        }
      });
    if (this.route.url == '/hadoctor-practicelocation/' + this.doctor_uuid) {
      this.noCreate = false;
    }
    else {
      this.noCreate = true;
    }
  }

  ngOnChanges() {
    const uuid = sessionStorage.getItem('practice_location');
    this.selectedLocation.uuid = uuid;
    this.updateLocation(event, this.selectedLocation);
  }

  //api calls ends
  submitConsultingForm(id) {
    this.submitted = true;
    $('#consultModal').modal('hide');
    if (id === null) {
      const startDay = moment(this.consultingForm.controls['effective_from'].value).format('dddd');
      const endDay = moment(this.consultingForm.controls['effective_upto'].value).format('dddd');
      console.log(this.days);
      for (let day of this.days) {
        if (startDay == day || endDay == day) {
          this.selectedDays = true;
          break;
        }

      }

      this.sendConsultationData.doctor = this.doctor_uuid
      this.sendConsultationData.uuid = null,
        this.sendConsultationData.time_from = this.consultingForm.controls['time_from'].value,
        this.sendConsultationData.time_to = this.consultingForm.controls['time_to'].value,
        this.sendConsultationData.effective_from = moment(this.consultingForm.controls['effective_from'].value).startOf('day').format('YYYY-MM-DD'),
        this.sendConsultationData.effective_upto = moment(this.consultingForm.controls['effective_upto'].value).startOf('day').format('YYYY-MM-DD'),
        this.sendConsultationData.days_of_the_week = this.days,
        this.sendConsultationData.practice_location = this.selectedLocation['uuid'],
        this.sendConsultationData.consultation_duration = this.consultingForm.controls[
          'consultation_duration'
        ].value;

      // console.log(this.consultingForm.value);
      if (this.selectedDays === true) {
        this.doctorService.postHaConsultingHours(this.sendConsultationData, this.doctor_uuid).subscribe(
          data => {
            console.log(data);
            $('#consultModal').modal('hide');
            this.notificationService.success('Consulting Hours Created');
            this.route.navigateByUrl('/', { skipLocationChange: true }).then(() => {
              this.route.navigate([`hadoctor-practicelocation/${this.doctor_uuid}`]);
            });
            this.updateLocation(event, this.selectedLocation);
            // this.submitted = false;
          },
          (error) => {
            console.log(error);
            // this.submitted = false;
            const status = error['status'];
            if (status == 400) {
              const err1 = JSON.parse(error.error.error).error_message;
              const err = ((error['error']['error_details'] && error['error']['error_details']['validation_errors']) != undefined) ? error['error']['error_details']['validation_errors'] : (error['error']['error_message'] != undefined ? error['error']['error_message'] : '');
              if (err1) {
                this.notificationService.error(err1, 'Med.Bot');
              } else if (err) {
                this.notificationService.error(`${error['statusText']} Consulting Hours Creation failed`, 'Med.Bot');
              }
              else {
                this.notificationService.error(`${error['error']['error_message']}`, 'Med.Bot');
              }
            }
            else {
              // this.submitted = false;
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          }
        );
      } else {
        this.notificationService.warning(`Please Select Valid Days Of The Week`, 'Med.Bot');
      }
      this.submitted = false;

    }
    else {
      console.log(moment(this.consultingForm.controls['effective_upto'].value).format('YYYY-MM-DD'));
      this.sendConsultationData.uuid = id,
        this.sendConsultationData.doctor = this.doctor_uuid,
        this.sendConsultationData.time_from = this.consultingForm.controls['time_from'].value,
        this.sendConsultationData.time_to = this.consultingForm.controls['time_to'].value,
        this.sendConsultationData.effective_from = moment(this.consultingForm.controls['effective_from'].value).startOf('day').format('YYYY-MM-DD'),
        this.sendConsultationData.effective_upto = moment(this.consultingForm.controls['effective_upto'].value).startOf('day').format('YYYY-MM-DD'),
        this.sendConsultationData.days_of_the_week = this.days,
        this.sendConsultationData.practice_location = this.selectedLocation['uuid'],
        this.sendConsultationData.consultation_duration = this.consultingForm.controls[
          'consultation_duration'
        ].value,

        this.doctorService.patchHaConsultingHours(this.sendConsultationData, id, this.doctor_uuid,).subscribe(
          data => {
            $('#consultModal').modal('hide');
            this.notificationService.success('Consulting Hours Updated');
            if (this.consultationForm.controls.chArray['controls'][this.selectedIndexValue].value) {
              this.hideDeleteNotification = true;
              //this.deleteUnavailability(this.selectedIndexValue);
            }
            this.updateLocation(event, this.selectedLocation);

          },
          (error) => {
            console.log(error);
            const status = error['status'];
            if (status == 400) {
              // this.submitted = false;
              this.notificationService.error(`${error['statusText']} Consulting Hours Creation failed`, 'Med.Bot');
            }
            else {
              // this.submitted = false;
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
            $('#consultModal').modal('hide');
          }
        );
      this.submitted = false;
    }
  }

  updateDays() {
    const obj = this.daysForm.value;
    this.days = [];
    this.days = Object.keys(obj).filter((key) => obj[key] === true);
  }

  updateLocation(event, location = null) {
    if (!location) {
      this.selectedLocation = event;
    }
    this.doctorService.getHaConsultingHours(this.doctor_uuid).subscribe(
      data => {
        const dt = this.getDataWithId(data['results'], this.key, this.selectedLocation.uuid);
        if (dt.length == 0) {
          this.dataAvailable = false;
        } else {
          this.emptyFormArray();
          this.consultationForm = this.formBuilder.group({
            chArray: this.formBuilder.array([]),
          });
          this.chArray = this.consultationForm.get('chArray') as FormArray;
          for (let i = 0; i < dt.length; i++) {
            let checkedValue = this.formControlDays(dt[i]['days_of_the_week']);
            const ud = this.getDataWithId(this.unavailabilityData, 'consulting_hours_group', dt[i]['uuid']);
            let uad = {};
            if (ud.length <= 0 || ud[0] == undefined) {
              uad['uuid'] = null;
              uad['start_datetime'] = null;
              uad['end_datetime'] = null;
              uad['data_available'] = false;
              this.unavailDataAvailable = false;
            }
            else {
              this.unavailDataAvailable = true;
              uad['uuid'] = ud[0].uuid;
              uad['start_datetime'] = moment(ud[0].start_datetime).format('DD/MM/yyyy');//this.formatDate(ud[0].start_datetime);
              uad['end_datetime'] = moment(ud[0].end_datetime).format('DD/MM/yyyy');//this.formatDate(ud[0].end_datetime);
              uad['data_available'] = true;
            }
            this.chArray.push(
              this.formBuilder.group({
                doctor: this.doctor_uuid,
                uuid: dt[i]['uuid'],
                time_from: this.formatConsultationData(dt[i]['time_from']),
                time_to: this.formatConsultationData(dt[i]['time_to']),
                effective_from: dt[i]['effective_from'],
                effective_upto: dt[i]['effective_upto'],
                consultation_duration: dt[i]['consultation_duration'],
                sunday: checkedValue['Sunday'],
                monday: checkedValue['Monday'],
                tuesday: checkedValue['Tuesday'],
                wednesday: checkedValue['Wednesday'],
                thursday: checkedValue['Thursday'],
                friday: checkedValue['Friday'],
                saturday: checkedValue['Saturday'],
                ua_uuid: uad['uuid'],
                ua_start_datetime: uad['start_datetime'],
                ua_end_datetime: uad['end_datetime'],
                ua_data_available: uad['data_available']
              })
            );
          }
          this.dataAvailable = true;
          this.formDisable = true;
        }
        this.formDisable = true;
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }

  //formarray
  addForm() {
    this.consultationForm = this.formBuilder.group({
      chArray: this.formBuilder.array([]),
    });
  }

  addConsultingHour() {
    this.chArray = this.consultationForm.get('chArray') as FormArray;
    this.chArray.push(
      this.formBuilder.group({
        time_from: '',
        time_to: '',
        effective_from: '',
        effective_upto: '',
        consultation_duration: '',
      })
    );
  }

  emptyFormArray() {
    const control = this.consultationForm.get('chArray') as FormArray;
    for (let i = control.length - 1; i >= 0; i--) {
      control.removeAt(i);
    }
  }

  formControlDays(days) {
    const daysObj = {
      Sunday: false,
      Monday: false,
      Tuesday: false,
      Wednesday: false,
      Thursday: false,
      Friday: false,
      Saturday: false,
    };
    for (let i = 0; i < days.length; i++) {
      const key = days[i];
      daysObj[key] = true;
    }
    return daysObj;
  }

  editConsultingHours(i, edit: boolean) {

    if (edit) {
      this.editAvailability = false;
      this.editConsultingHour = true;
      this.selectedIndexValue = i;
    }
    else {
      this.editAvailability = true;
    }
    const data = this.consultationForm.controls.chArray['controls'][i].value;
    const time_from = data['time_from'];
    const time_to = data['time_to'];
    const effectivefrom = new Date(data['effective_from']);
    const effective_upto = new Date(data['effective_upto']);
    const consultation_duration = data['consultation_duration'];
    this.consultationData.uuid = data['uuid'];
    this.consultationData.effective_from = effectivefrom;
    this.consultationData.effective_upto = effective_upto;
    this.consultationData.consultation_duration = consultation_duration;
    this.consultationData.time_from = time_from;//this.toTwentyFourHours(time_from);
    this.consultationData.time_to = time_to;//this.toTwentyFourHours(time_to);
    this.daysModel = {
      Sunday: false,
      Monday: false,
      Tuesday: false,
      Wednesday: false,
      Thursday: false,
      Friday: false,
      Saturday: false
    };
    this.daysModel['Sunday'] = data['sunday'];
    this.daysModel['Monday'] = data['monday'];
    this.daysModel['Tuesday'] = data['tuesday'];
    this.daysModel['Wednesday'] = data['wednesday'];
    this.daysModel['Thursday'] = data['thursday'];
    this.daysModel['Friday'] = data['friday'];
    this.daysModel['Saturday'] = data['saturday'];
    $('#consultModal').modal('show');
  }



  //Unavailability
  markUnavailability(i) {
    this.hideDeleteNotification = false;
    this.selectedIndex = i;
    this.editAvailability = true;
    const chData = this.consultationForm.controls.chArray['controls'][i].value;
    this.consultationData = chData;
    // this.consultationData.time_from = this.toTwentyFourHours(chData['time_from']);
    // this.consultationData.time_to = this.toTwentyFourHours(chData['time_to']);
    console.log(this.consultationData, this.toTwentyFourHours(chData['time_from']));
    this.editConsultingHours(i, false);
    const un_dt = this.getDataWithId(this.unavailabilityData, 'consulting_hours_group', chData['uuid']);
    let data = un_dt[0] || null;
    console.log(chData['ua_uuid']);

    if (chData['ua_uuid'] != null) {
      this.unavailabilityForm.setValue({
        uuid: chData['ua_uuid'],
        doctor: this.doctor_uuid,
        consulting_hours_group: chData['uuid'],
        practice_location: this.selectedLocation['uuid'],
        start_datetime: moment(chData['ua_start_datetime'], 'DD/MM/yyyy').toDate(),//this.reverseDate(chData['ua_start_datetime']),
        end_datetime: moment(chData['ua_end_datetime'], 'DD/MM/yyyy').toDate()//this.reverseDate(chData['ua_end_datetime']),
      });
      console.log(this.unavailabilityForm.value);
      this.editConsultingHour = true;
    }
    else {
      this.unavailabilityDates.fromDate = null;
      this.unavailabilityDates.endDate = null;
      this.editConsultingHour = true;
    }
  }

  saveUnavailability() {
    $('#consultModal').modal('hide');
    const uuid = this.unavailabilityForm.controls['uuid'].value;
    const controls = this.chArray.at(this.selectedIndex);
    // console.log(controls);
    if (uuid == null) {
      this.unavailabilityForm.patchValue({
        doctor: this.doctor_uuid,
        start_datetime: moment(this.unavailabilityForm.controls['start_datetime'].value).startOf('day'),
        end_datetime: moment(this.unavailabilityForm.controls['end_datetime'].value).startOf('day')
      });
      const data = this.unavailabilityForm.value;
      console.log(uuid);
      this.doctorService.postHaUnavailability(data, this.doctor_uuid).subscribe(
        data => {
          $('#consultModal').modal('hide');
          this.notificationService.success('Slots unavailability updated', 'Med.Bot');
          this.route.navigateByUrl('/', { skipLocationChange: true }).then(() => {
            this.route.navigate([`hadoctor-practicelocation/${this.doctor_uuid}`]);
          });
          controls.get('ua_data_available').setValue(true);
          controls.get('ua_uuid').setValue(data['uuid']);
          controls.get('ua_start_datetime').setValue(this.formatDate(data['start_datetime']));
          controls.get('ua_end_datetime').setValue(this.formatDate(data['end_datetime']));

        }, error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    } else {
      console.log(this.unavailabilityForm.controls['start_datetime'].value);
      this.unavailabilityForm.patchValue({
        start_datetime: moment(this.unavailabilityForm.controls['start_datetime'].value).startOf('day'),
        end_datetime: moment(this.unavailabilityForm.controls['end_datetime'].value).startOf('day')
      });
      const data = this.unavailabilityForm.value;
      console.log(data);
      this.doctorService.patchHaUnavailability(uuid, data, this.doctor_uuid).subscribe(
        data => {
          controls.get('ua_start_datetime').setValue(this.formatDate(data['start_datetime']));
          controls.get('ua_end_datetime').setValue(this.formatDate(data['end_datetime']));
          $('#consultModal').modal('hide');
        }
      );
    }
  }

  deleteUnavailability(i) {
    const data = this.consultationForm.controls.chArray['controls'][i].value;
    this.doctorService.deleteHaUnavailabilityData(data['ua_uuid'], this.doctor_uuid).subscribe(
      data => {
        console.log(data);
        this.route.navigateByUrl('/', { skipLocationChange: true }).then(() => {
          this.route.navigate([`hadoctor-practicelocation/${this.doctor_uuid}`]);
        });
        this.consultationForm.controls.chArray['controls'][i].patchValue({
          ua_data_available: false,
          ua_uuid: null
        });
        this.unavailabilityForm.patchValue({
          start_datetime: null,
          end_datetime: null,
        });
        if (this.hideDeleteNotification) {
          this.notificationService.error('Unavailability slots deleted', 'Med.Bot');
        }

      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  //Unavailability ends

  newConsultingModal() {
    this.doctorService.getDoctorHaFees(this.doctor_uuid).subscribe(data => {
      const results = Object.values(data);
      this.showValidFee(results);
      if (this.feeList.length == 0) {
        this.notificationService.error('Incorporate consulting fees prior to generating consulting hours', 'Med.Bot');
      } else {
        $('#consultModal').modal('show');
        console.log('its working');
        this.editAvailability = false;
        this.daysModel = {
          Sunday: false,
          Monday: false,
          Tuesday: false,
          Wednesday: false,
          Thursday: false,
          Friday: false,
          Saturday: false
        };
        this.consultationData = {
          uuid: null,
          time_from: null,
          time_to: null,
          effective_from: null,
          effective_upto: null,
          consultation_duration: null,
        };
        this.editAvailability = false;
        this.editConsultingHour = false;
      }
    },
      error => {
        console.log(error);
      });
  }

  formatTodayDate() {
    this.todayDate = new Date();
    // const month = this.formatDateMonth(todaydate.getMonth());
    // const date = this.formatDateMonth(todaydate.getDate());
    // this.todayDate = todaydate.getFullYear() + '-' + month + '-' + date;
    // console.log(this.todayDate);
  }

  formatDateMonth(val) {
    if (val < 10) {
      const result = '0' + val;
      return result;
    } else {
      return val;
    }
  }

  formatConsultationData(data) {
    let time_data = data.split(":");
    if (parseInt(time_data[0]) < 12) {
      if (parseInt(time_data[0]) == 0) {
        time_data[0] = "12";
      }
      time_data = time_data[0] + ':' + time_data[1] + ' AM';
    }
    else if (parseInt(time_data[0]) == 12) {
      time_data = time_data[0] + ':' + time_data[1] + ' PM';
    }
    else {
      let convertTime = parseInt(time_data[0]) - 12;
      if (parseInt(time_data[0]) > 9) {
        time_data = convertTime.toString() + ':' + time_data[1] + ' PM';
      }
      else {
        time_data = '0' + convertTime.toString() + ':' + time_data[1] + ' PM';
      }
    }
    return time_data;
  }

  createTimeObj(data) {
    console.log(data);
    const hours = data.getHours();
    const minutes = data.getMinutes();
    const time = hours + ':' + minutes;
    return time;
  }

  getDataWithId(data, key, value) {
    this.hourData = [];
    for (let i = 0, length = data.length; i < length; i++) {
      if (data[i][key] == value) {
        this.hourData.push(data[i]);
      }
    }
    return this.hourData;
  }

  createDateTime(time) {
    let time_arr = time.split(':');
    let date = new Date();
    date.setHours(time_arr[0]);
    date.setMinutes(time_arr[1]);
  }

  toTwentyFourHours(date) {
    const type = date.split(' ');
    const timeType = type[1];
    if (timeType == 'AM') {
      const time = type[0] + ' AM';
      return time;
    }
    if (timeType == 'PM') {
      const tm = type[0].split(':');
      if (tm[0] == 12) {
        const time2 = tm[0] + ':' + tm[1] + ' PM';
        return time2;
      }
      else {
        const time2 = '0' + parseInt(tm[0]) + ':' + tm[1] + ' PM';
        return time2;
      }
    }
  }

  formatDate(date) {
    let d = new Date(date),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear();

    if (month.length < 2)
      month = '0' + month;
    if (day.length < 2)
      day = '0' + day;
    return [day, month, year].join('/');
  }

  reverseDate(date) {
    let d = date.split('/');
    return [d[2], d[1], d[0]].join('-');
  }

  showValidFee(results) {
    const today_date_formated = moment(this.today).format('YYYY-MM-DD');
    for (let fee of results) {
      if (fee.effective_upto == null) {
        this.feeList.push({
          amount: `${fee.amount}`,
          currency: `${fee.currency}`,
          doctor: `${fee.doctor}`,
          effective_from: `${fee.effective_from}`,
          effective_upto: `${fee.effective_upto}`,
          hospital: `${fee.hospital}`,
          uuid: `${fee.uuid}`,
        });
      } else {
        const effective_upto = moment(fee.effective_upto).format('YYYY-MM-DD');
        if (effective_upto >= today_date_formated) {
          this.feeList.push({
            amount: `${fee.amount}`,
            currency: `${fee.currency}`,
            doctor: `${fee.doctor}`,
            effective_from: `${fee.effective_from}`,
            effective_upto: `${fee.effective_upto}`,
            hospital: `${fee.hospital}`,
            uuid: `${fee.uuid}`,
          });
        }
      }
    }
  }

}
