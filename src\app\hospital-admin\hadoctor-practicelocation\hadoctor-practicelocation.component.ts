import { ToastrService } from 'ngx-toastr';
import { HttpClient } from '@angular/common/http';
import { delay } from 'rxjs/operators';
import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import {
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import { DoctorService } from '../../doctor/doctor.service';
import { TranslateService } from '@ngx-translate/core';
import * as Settings from '../../config/settings';
import { ActivatedRoute } from '@angular/router';
import { validateHorizontalPosition } from '@angular/cdk/overlay';
import { Location } from '@angular/common';
import { PlatformService } from 'src/app/platform/platform.service';

@Component({
  selector: 'app-hadoctor-practicelocation',
  templateUrl: './hadoctor-practicelocation.component.html',
  styleUrls: ['./hadoctor-practicelocation.component.css'],
})
export class HadoctorPracticelocationComponent implements OnInit {
  practiceLocationList = [];
  showAllForms: boolean;
  locationForm: FormGroup;
  public practiceLocationId: string;
  public practiceType = [
    { name: 'Own Practice', value: 'own_practice' },
    { name: 'Hospital Based', value: 'hospital_based' },
  ];
  public selectedLocation = {
    name: '',
    practice_type: '',
  };
  public selectedLocationCh = {};
  public edit = false;
  public saved = false;
  public create = false;
  public showLocationForm = false;
  public doctor_uuid = '';
  public uuid = null;
  public location_data = false;
  public newlocationForm: FormGroup;
  practice_location: string;
  public feeForm: FormGroup;
  public PracticeLocationList = [
    {
      uuid: '009db27e-e3fe-40de-8006-03f2279a3e14',
      name: 'Murthy Clinic2',
      practice_type: null,
      hospital_uuid: null,
      doctor: 'bf1ef8f0-0375-43c8-a5c4-a4088e6cd5f7',
    },
  ];
  public feesFormFieldValues = {
    practice_location: null,
    doctor: null,
    amount: null,
    effective_from: null,
    effective_upto: null,
  };
  showFeesForm = true;
  public feesList = [];
  feesId = null;
  minDate = new Date();
  maxDate = new Date(2015, 12, 30);
  public feesEditBtnShow = false;
  feesUUId = null;
  feeFormReadyOnly = false;
  cancelBtnShow = false;
  public clinicAddressForm: FormGroup;
  practiceLocation: string;
  public countryList: any = [];
  doctorClinicAddressList: any = [];
  clinicAddressReadOnly: boolean;
  public cancleBtnShow = false;
  public feeDetailAvailable = false;
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  showConsultingGroup: boolean;
  doctorName: string;
  constructor(
    private doctorService: DoctorService,
    private notificationService: ToastrService,
    private translate: TranslateService,
    private httpClient: HttpClient,
    private cd: ChangeDetectorRef,
    private route: ActivatedRoute,
    private location: Location,
    private platformService: PlatformService,
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(
      url => {
        console.log("url", url);
        this.doctor_uuid = url['uuid'];
        console.log(this.doctor_uuid);
      }
    );
    this.doctorService.checkProfileHaCompletion();
    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this.getPracticeLocation();
    this.getCountry();
    this.addFormControl(null);
    this.addFormControlData(null);
    this.getDoctorAddress(); this.locationForm = new FormGroup({
      practiceLocation: new FormControl(null),
    });
    this.getuserData();

  }
  getuserData() {
    this.platformService.getDoctorDetails(this.doctor_uuid).subscribe(
      data => {
        console.log(data['user'].username);
        this.doctorName = data['user'].username;
      }, error => {
        console.log(error);
      }
    );
  }
  goBack() {
    this.location.back();
  }

  setParacticeLocationId(event) {
    this.selectedLocationCh = event;
    this.practiceLocationId = event.uuid;
    this.practiceLocation = event.uuid;
    let singleLocation = [];
    sessionStorage.setItem('practice_location', `${event.uuid}`);
    const uuid = sessionStorage.getItem('practice_location');
    this.getSelectedLocationData(event);
    this.showLocationForm = false; // creation/edit form
    if (this.doctorClinicAddressList.length > 0) {
      singleLocation = this.doctorClinicAddressList.filter(
        (obj) => obj.practice_location === this.practiceLocation
      );
      if (singleLocation.length > 0) {
        this.addFormControlData(singleLocation[0]);
      } else {
        this.addFormControlData(null);
      }
    } else {
      this.addFormControlData(null);
    }

    let singleFees = [];
    this.feeDetailAvailable = false;
    if (this.feesList.length > 0) {
      singleFees = this.feesList.filter(
        (obj) => obj.practice_location === this.practiceLocation
      );
      if (singleFees.length > 0) {
        this.addFormControl(singleFees[0]);
        this.feeDetailAvailable = true;
      } else {
        this.addFormControl(null);
      }
    } else {
      this.addFormControl(null);
    }
  }

  getPracticeLocation() {
    this.doctorService.getHaDoctorPracticeLocations(this.doctor_uuid).subscribe(
      (data) => {
        const resp_data = data['results'];
        this.practiceLocationList = Object.values(resp_data);
        if (this.practiceLocationList.length > 0) {
          this.locationForm.controls['practiceLocation'].setValue(
            this.practiceLocationList[0].uuid
          );
          this.setParacticeLocationId(this.practiceLocationList[0]);
          this.selectedLocationCh = this.practiceLocationList[0];
          this.practiceLocation = this.practiceLocationList[0].uuid;
          sessionStorage.setItem(
            'practice_location',
            this.practiceLocationList[0].uuid
          );
          this.selectedLocationCh = this.practiceLocationList[0];
          this.practiceLocationId = this.practiceLocationList[0].uuid;
          this.doctor_uuid = this.practiceLocationList[0].doctor;
          this.getSelectedLocationData(this.practiceLocationList[0]);
          this.showAllForms = true;
          this.location_data = true;
        } else {
          this.showAllForms = false;
          this.location_data = false;
        }
      },
      // (error) => {
      //   console.log(error);
      //   const status = error['status'];
      //   if (status == 400) {
      //     this.notificationService.error(
      //       `${error['statusText']}`,
      //       'Med.Bot'
      //     );
      //   } else {
      //     this.notificationService.error(
      //       `${error['statusText']}`,
      //       'Med.Bot'
      //     );
      //   }
      // }
    );
  }

  getSelectedLocationData(event) {
    this.selectedLocation.name = event.name;
    const val = this.practiceType.find(
      ({ value }) => value === event.practice_type
    );
    this.selectedLocation.practice_type = val.name;
  }
  createNewLocationForm() {
    this.showLocationForm = true;
    this.newlocationForm = new FormGroup({
      uuid: new FormControl(null),
      name: new FormControl(null, [Validators.required, Validators.pattern('.*\\S.*[a-zA-Z]')]),
      practice_type: new FormControl(null, Validators.required),
    });
    this.edit = false;
    this.create = true;
  }



  createLocation() {
    this.doctorService.postHaPracticeLocation(this.newlocationForm.value, this.doctor_uuid).subscribe(
      (data) => {
        this.getPracticeLocation();
        this.addFormControlData(null);
        this.notificationService.success('Location Created', 'Med.Bot');
        this.showLocationForm = false;
        this.showAllForms = true;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );
  }

  editLocation() {
    this.showLocationForm = true;
    this.edit = true;
    this.create = false;
    const location_id = sessionStorage.getItem('practice_location');
    const location = this.practiceType.find(
      ({ name }) => name === this.selectedLocation.practice_type
    );
    this.newlocationForm = new FormGroup({
      doctor: new FormControl(this.doctor_uuid, Validators.required),
      uuid: new FormControl(this.practiceLocationId, Validators.required),

      name: new FormControl(this.selectedLocation.name, [Validators.required, Validators.pattern('[a-zA-Z ]*'), Validators.maxLength(50)]),
      practice_type: new FormControl(location.value, Validators.required),
      location: new FormControl(location_id, Validators.required),
    });
  }

  cancelLocation() {
    this.showLocationForm = false;
  }



  saveEditLocation() {
    const uuid = this.newlocationForm.controls['uuid'].value;
    this.doctorService.patchHaPracticalLocation(this.newlocationForm.value, uuid, this.doctor_uuid).subscribe(
      (data) => {
        this.getPracticeLocation();
        this.showLocationForm = false;
        this.notificationService.success('Location Updated', 'Med.Bot');
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );
  }

  submitNewLocationForm(type) {
    if (type == 'create') {
      this.createLocation();
    }
    if (type == 'edit') {
      this.saveEditLocation();
    }
  }
  // feess
  addFormControl(data) {
    if (data === null) {
      this.practiceLocation = sessionStorage.getItem('practice_location');
      this.feeForm = new FormGroup({
        practice_location: new FormControl(
          this.practice_location,
          Validators.required
        ),
        effective_upto: new FormControl(this.minDate, Validators.required),
        amount: new FormControl('', Validators.required),
        effective_from: new FormControl('', Validators.required),
        doctor: new FormControl(),
      });
      this.feesEditBtnShow = false;
      this.feeFormReadyOnly = false;
      this.feesId = null;
    } else {
      this.feesEditBtnShow = true;
      this.feeFormReadyOnly = true;
      this.feesId = data.uuid;
      this.feeForm = new FormGroup({
        practice_location: new FormControl(
          data.practice_location,
          Validators.required
        ),
        effective_upto: new FormControl(
          data.effective_upto,
          Validators.required
        ),
        amount: new FormControl(data.amount, Validators.required),
        effective_from: new FormControl(
          data.effective_from,
          Validators.required
        ),
        doctor: new FormControl(data.doctor),
      });
    }
  }

  updateDoctorFessDetail(data, id) {
    if (id === null) {
      return this.httpClient
        .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    } else {
      const editDate = { effective_upto: data['effective_upto'] };
      return this.httpClient
        .patch(
          `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/fees/${id}/`,
          editDate
        )
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
  }

  addFormControlData(data) {
    if (data === null) {
      this.showConsultingGroup = false;
      this.practiceLocation = sessionStorage.getItem('practice_location');
      this.clinicAddressReadOnly = false;
      this.cancleBtnShow = false;
      this.clinicAddressForm = new FormGroup({
        uuid: new FormControl(null),
        address_type: new FormControl('Clinic', Validators.required),
        practice_location: new FormControl(
          this.practiceLocation,
          Validators.required
        ),
        line_1: new FormControl(null, [Validators.required, Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        line_2: new FormControl(null, [Validators.required, Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        city_town_village: new FormControl(null, [Validators.required, Validators.pattern('[a-zA-Z ]*')]),
        district: new FormControl(null, [Validators.required, Validators.pattern('[a-zA-Z ]*')]),
        taluk: new FormControl(null, [Validators.required, Validators.pattern('[a-zA-Z ]*')]),
        state: new FormControl(null, [Validators.required, Validators.pattern('[a-zA-Z ]*')]),
        country: new FormControl('India', [Validators.required]),
        postal_code: new FormControl(null, [Validators.required, Validators.pattern('[a-zA-Z0-9]*'), Validators.maxLength(10)]),
      });
    } else {
      this.clinicAddressReadOnly = true;
      this.cancleBtnShow = false;
      this.showConsultingGroup = true;
      this.clinicAddressForm = new FormGroup({
        uuid: new FormControl(data.uuid, Validators.required),
        address_type: new FormControl(data.address_type, Validators.required),
        practice_location: new FormControl(
          data.practice_location,
          Validators.required
        ),
        line_1: new FormControl(data.line_1, [Validators.required, Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        line_2: new FormControl(data.line_2, [Validators.required, Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        city_town_village: new FormControl(
          data.city_town_village,
          [Validators.required, Validators.pattern('[a-zA-Z ]*')]
        ),
        district: new FormControl(data.district, [Validators.required, Validators.pattern('[a-zA-Z ]*')]),
        taluk: new FormControl(data.taluk, [Validators.required, Validators.pattern('[a-zA-Z ]*')]),
        state: new FormControl(data.state, [Validators.required, Validators.pattern('[a-zA-Z ]*')]),
        country: new FormControl(data.country, Validators.required),
        postal_code: new FormControl(data.postal_code, [Validators.required, Validators.pattern('[a-zA-Z0-9]*'), Validators.maxLength(10)]),
      });
    }
  }

  editClinicAddress() {
    this.clinicAddressReadOnly = false;
    this.cancleBtnShow = true;
  }

  saveHaAddress(doctor_uuid, data, uuid) {
    if (data.uuid === null) {
      return this.httpClient
        .post(
          `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital/${doctor_uuid}/addresses/`,
          data
        )
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    } else {
      return this.httpClient
        .patch(
          `${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hosptial/${doctor_uuid}/addresses/${uuid}/`,
          data
        )
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
  }

  saveClinicAddress() {
    this.practiceLocation = sessionStorage.getItem('practice_location');
    this.clinicAddressForm.controls['practice_location'].setValue(
      this.practiceLocation
    );
    const practiceLocationVale = this.clinicAddressForm.controls[
      'practice_location'
    ].value;
    if (practiceLocationVale !== null) {
      this.saveHaAddress(this.doctor_uuid, this.clinicAddressForm.value, this.uuid).subscribe(
        (data) => {
          this.doctorClinicAddressList.push(data);
          data = [data];
          const addressList = Object.values(data);
          const singleLocation = addressList.filter(
            (obj) => obj.practice_location === this.practiceLocation
          );
          this.addFormControlData(singleLocation[0]);
          this.notificationService.success(
            'Clinic Address Added Successfully',
            'Med.Bot'
          );
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            const postalCode = error['error']['postal_code'];
            if (postalCode) {
              const postalCodeErr = postalCode[0];

              this.notificationService.error(`postalCodeErr`, 'Med.Bot');
            } else {
              this.notificationService.error(
                `${error['statusText']}`,
                'Med.Bot'
              );
            }
          } else {
            this.notificationService.error(
              `${error['statusText']}`,
              'Med.Bot'
            );
          }
        }
      );
    } else {
      this.notificationService.error(
        'Clinic Address Updation Failed',
        'Med.Bot'
      );
    }
  }
  cancelClinicAddress() {
    this.cancleBtnShow = false;
    if (this.doctorClinicAddressList.length > 0) {
      const singleLocation = this.doctorClinicAddressList.filter(
        (obj) => obj.practice_location === this.practiceLocation
      );
      if (singleLocation.length > 0) {
        this.addFormControlData(singleLocation[0]);
      } else {
        this.addFormControlData(null);
      }
    } else {
      this.addFormControlData(null);
    }
  }

  getHaAddressDetail(doctor_uuid) {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital/${doctor_uuid}/addresses/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorAddress() {
    this.getHaAddressDetail(this.doctor_uuid).subscribe(
      (data) => {
        data = data[`results`];
        const doctorAddress = Object.values(data);
        this.doctorClinicAddressList = doctorAddress.filter(
          (obj) =>
            obj.address_type === 'Clinic' && obj.practice_location !== null
        );
        if (this.doctorClinicAddressList.length > 0) {
          const singleLocation = this.doctorClinicAddressList.filter(
            (obj) => obj.practice_location === this.practiceLocation
          );
          if (singleLocation.length > 0) {
            this.addFormControlData(singleLocation[0]);
            this.uuid = singleLocation[0].uuid;
          } else {
            this.addFormControlData(null);
          }
        }
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  getCountry() {
    this.doctorService.getCountryDetail().subscribe(
      (data) => {
        this.countryList = data;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );
  }
}

