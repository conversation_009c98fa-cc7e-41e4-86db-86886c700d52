import { PatientService } from './../../patient/patient.service';
import { DoctorService } from './../../doctor/doctor.service';
import { Component, OnInit } from '@angular/core';
import {Router} from '@angular/router';
import {ToastrService} from 'ngx-toastr';
import {SharedService} from '../../shared/shared.service';
import * as moment from 'moment';
import {AuthService} from '../../auth/auth.service'
import { Location } from '@angular/common';
import { validateHorizontalPosition } from '@angular/cdk/overlay';
@Component({
  selector: 'app-hadoctor-viewprofile',
  templateUrl: './hadoctor-viewprofile.component.html',
  styleUrls: ['./hadoctor-viewprofile.component.css'],
})
export class HadoctorViewprofileComponent implements OnInit {
  doctorId = '';
  userType = '';
  doctor = {};
  personalInfo = {};
  qualification = {};
  registration = {};
  degreeList = [];
  degreeString = '';
  public ckText = '';
  public profilePicture: string;
  public ckConfig = {
    bodyClass: 'txt-area',
  };
  public practiceLocations = [];
  public specialization = [];
  public isLoading= false;
  amount: any;
  showProfilePic: boolean;
  public user_name:string;
  yearsOfExperience: any;
  languages: any;
  instant_appointment_slot_available: boolean;
  slots_availability: any;
  consultinghoursgroups: any;
  today_available: boolean;
  constructor(
    private doctorService: DoctorService,
    private sharedService: SharedService,
    private patientService: PatientService,
    private router: Router,
    private notificationService: ToastrService,
    private userService: AuthService,
    private location:Location
  ) {}

  ngOnInit(): void {
    this.isLoading =true;
    this.showProfilePic= false;
    this.today_available=true;
    this.sharedService.setActiveLink('');
    const doctorId = localStorage.getItem('Doctor');
    this.userType = localStorage.getItem('user_type');
    this.userType = localStorage.getItem('user_type');
    // this.userService.getUserDetail().subscribe(
    //   (data) => {
    //     this.sharedService.setUserName(data['username']);
    //     if (data['profile_picture'] !== null) {
    //       this.sharedService.setPicture(data['profile_picture']);
    //     }
    //   },
    //   (error) => {
    //     console.log(error);
    //   }
    // );
    if (doctorId) {
      this.patientService.getdoctorProfile(doctorId).subscribe((data) => {
        this.personalInfo = data['user'];
        this.slots_availability=data['slots_availability'];
        if(this.personalInfo['profile_picture']){
          this.profilePicture = this.personalInfo['profile_picture'];
        }else{
          this.profilePicture =  'assets/img/doctors/doctor-thumb-02.png'
        }
        this.doctor = data;
        this.ckText = data['professional_summary'];
        data['speciality'].map((spec) =>
          this.specialization.push(spec['value'])
        );
        data['qualifications'].map((qual) =>
          this.degreeList.push(qual['name'])
        );

        this.instant_appointment_slot_available=data['instant_appointment_slot_available'];
        const today=moment().format('YYYY-MM-DD');
        //this.practiceLocations = data['practicelocations'];
        if(this.slots_availability != null){
        for(const pra of data['practicelocations']){
          this.consultinghoursgroups=[];
          for(const val of pra['consultinghoursgroups']){
            if(val.effective_upto >=today){
              this.today_available = false;
              this.consultinghoursgroups.push({consultation_duration:`${val.consultation_duration}`,days_of_the_week:val.days_of_the_week,
              doctor:val.doctor,effective_from:val.effective_from,effective_upto:val.effective_upto,
              hospital:val.hospital,time_from:val.time_from,time_to:val.time_to,timezone_name:val.timezone_name,uuid:val.uuid})
            }
          }
          this.practiceLocations.push({addresses:pra.addresses,consultinghoursgroups:this.consultinghoursgroups,
            doctor:pra.doctor,hospital:pra.hospital,name:pra.name,practice_type:pra.practice_type,uuid:pra.uuid
          })

        }
      }
        console.log(this.today_available);
        console.log('this.practiceLocations',this.practiceLocations);
        this.registration = data['registrations'][0];
        this.yearsOfExperience=data['years_of_experience'];
        this.languages=data['languages'];
        this.amount=data['doctor_fee']

        for(let fee of data['consultationfees'] ){
          const fromDate = moment(fee.effective_from).format('YYYY-MM-DD');
          const toDate=moment(fee.effective_upto).format('YYYY-MM-DD');
          if(today >=fromDate && today <= toDate )  {
                // this.amount = fee.amount;
          }


        }

        this.formatCHData();
        for (let i = this.degreeList.length; i > 0; i--) {
          this.degreeString = this.degreeString + ', ' + this.degreeList[i - 1];
          this.degreeString = this.degreeString.substring(1);
        }
        setTimeout(()=>{
          this.showProfilePic = true;
        },1000)
         this.isLoading =false;

      },error=>{
        console.log(error);
        this.showProfilePic= false;
        this.isLoading =false;
        const status = error['status'];
        if(status == 400){
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
    }
    this.formatCHData();
  }

  formatCHData() {
    let time_data = [];
    let day_data = {};
    this.practiceLocations.forEach((pl) => {
      pl['consultinghoursgroups'].forEach((ch) => {
        const days = ch['days_of_the_week'];
        ch['days_of_the_week'].forEach((day) => {
          day_data = {};
          day_data[day] =
            this.formatTime(ch['time_from']) +
            ' - ' +
            this.formatTime(ch['time_to']);
          time_data.push(day_data);
        });
        this.formatSameDayTimings(time_data);
      });
    });
  }

  formatTime(data) {
    data = data.split(':');
    let formatted_time = '';
    if (parseInt(data[0]) > 12) {
      const time_hour = parseInt(data[0]) - 12;
      const time_minute = data[1];
      const time_type = 'PM';
      formatted_time = time_hour + ':' + time_minute + ' ' + time_type;
    } else {
      const time_type = 'AM';
      formatted_time = data[0] + ':' + data[1] + ' ' + time_type;
    }
    return formatted_time;
  }

  saveProfSummary(data) {
    console.log(data.length);
    setTimeout(() => {

      this.doctorService.patchDoctorProfile(data).subscribe((data) => {

      });
    }, 2000);
  }

  formatSameDayTimings(time_list) {
    let days = [];
    time_list.forEach((data) => {
      const arr_dt = Object.keys(data);
      console.log(arr_dt[0]);
      days.push[arr_dt[0]];
    });
    console.log(this.practiceLocations);
  }
  bookAppointment() {
    console.log(this.practiceLocations);
    const doctorId = localStorage.getItem('Doctor');
    const practiceLocationId = this.practiceLocations[0].uuid;
    this.router.navigate([
      '/patient/appointment/',
      doctorId,
      practiceLocationId, 'booking'
    ]);
  }
  back() {
    this.location.back();
  }
  consultNow(){
    localStorage.setItem('public_instant_request','true');
    this.sharedService.removeSearchList();
    console.log(this.personalInfo)
    const query ='consult_now=true'+'&name='+this.personalInfo['username'] +'&fee_lte='+this.amount;
    const page=1;

   this.router.navigate(['/patient/search/', page,query]);
  }
}
