.fa-upload.ic {
    color: #fff;
    text-align: left;
}

.file-shown {
    padding-left: 10px;
    color: #999;
    font-size: 50px;
}

.fifteen_chars {
    display: block;
    white-space: nowrap;
    width: 12em;
    overflow: hidden;
    text-overflow: ellipsis;
}

.th {
    display: inline;
}

.no-file {
    padding-top: 20px;
}

::ng-deep .ng-select .ng-select-container {
    min-height: 45px !important;
}

::ng-deep .ng-select.ng-select-single .ng-select-container {
    height: 20px !important;
}
.filter-btn{
  margin-top: 2.1rem!important;
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .example {background: green;}
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .example {background: blue;}
  .choose_file_size{
    font-size: x-small !important;
  }
  .choose_file_width{
    width: 200%!important;
  }
  .btn-size{
    font-size: x-small !important;
  }
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .example {background: orange;}
  .choose_file_size{
    font-size: small !important;
  }
  .choose_file_width{
    width: 120%!important;
  }
  .btn-size{
    font-size: small !important;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .example {background: pink;}
  .choose_file_size{
    font-size: large !important;
  }
  .choose_file_width{
    width: 100%!important;
  }
  .btn-size{
    font-size: large !important;
  }
}
