<div class="container-fluid mt-2 pt-0" style="overflow-y: auto;
height: 373px;">
    <div class="row">
        <div class="col-md-8"></div>

    </div>

    <div class="col-md-12 ml-2 mb-3">
      <h4 class="th">Upload Reports</h4>
    <div class="row">
        <div  [ngClass]="{'col-md-4':(videoAndData||patientVideoAndData),'col-md-2':(!videoAndData&&!patientVideoAndData)}">
            <ng-select id="reportType" [items]="reportTypes" [formControl]="selectedDiagnosticReportName" [clearable]="false" [searchable]="false" bindLabel="testType" bindValue="id" placeholder="Report Type" (change)="getReportType($event)">
            </ng-select>
        </div>
        <div   [ngClass]="{'col-md-4':(videoAndData||patientVideoAndData),'col-md-2':(!videoAndData&&!patientVideoAndData)}">
            <input type="text" placeholder="Report Generated On"   [maxDate]="maxDate" [minDate]="minDate" onkeydown="return false" class="form-control" [(ngModel)]="reportDate" bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
        </div>
        <div [ngClass]="{'col-md-3':(videoAndData||patientVideoAndData),'col-md-2':(!videoAndData&&!patientVideoAndData)}">
            <div class="change-photo-btn choose_file_width" style="
              padding: 6px;
              margin-left: 0px;text-align: center;
              font-family: sans-serif;
              ;
            ">
                <span><i class="fa fa-upload ic choose_file_size" >&nbsp;Choose File</i></span>
                <input type="file" class="upload" id="medical-report" (change)="medicalReports($event)" accept=".jpg, .jpeg,.pdf" />
            </div>
            <small *ngIf="reportName">&nbsp;{{reportName}}</small>
        </div>
        <div   [ngClass]="{'col-md-3 mt-2':(videoAndData||patientVideoAndData),'col-md-2':(!videoAndData&&!patientVideoAndData)}">
            <button class="btn btn-primary btn-size" *ngIf="!showUploading" [disabled]="!selectedDiagnosticReportName.value || !reportFile" (click)="saveMedicalReport()">Save</button>
            <button class="btn btn-primary btn-size" *ngIf="showUploading"  >Uploading</button>

        </div>
    </div>
  </div>
    <div class="card-body">

        <div class="col-md-12 mb-2" >
          <form [formGroup]="searchForm">
          <div class="row">
            <div class="col-md-2.5 ml-1 mt-1">
              <h4 class="th">Medical Reports</h4>
            </div>
            <ng-container *ngIf="shwoFilter&&!videoAndData&&!patientVideoAndData">
            <div class="col-md-2" >
              <!-- <label>Report Type</label> -->
              <ng-select id="reportType" [items]="reportTypes" formControlName="reportType" [clearable]="false" [searchable]="false" bindLabel="testType" bindValue="id" placeholder="Report Type" >
              </ng-select>
            </div>
            <div class="col-md-2">
              <div class="form-group mb-0 ">
                <!-- <label translate>Report Generated On</label> -->
                <input
                [maxDate]="maxDate" [minDate]="minDate"
                placeholder="Report generated on" onkeydown="return false"
                class="form-control" formControlName="reportGeneratedOn"

                bsDatepicker
                [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
            </div>
            </div>
            <div class="col-md-2">
              <div class="form-group mb-0 ">
                <!-- <label translate>Report Updated On</label> -->
                <input
                [maxDate]="maxDate" [minDate]="minDate"
                placeholder="Report Updated on" onkeydown="return false"
                class="form-control"
                bsDatepicker formControlName="reportUpdatedOn"
                [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">

            </div>
            </div>
            <div class="col-md-1.5 ">
              <button class="btn btn-primary btn-size" [disabled]="!searchForm.touched && !searchForm.dirty" (click)="findReports()">Find</button>
              <button class="btn btn-secondary btn-size ml-1" [disabled]="!searchForm.touched && !searchForm.dirty" (click)="resetSearchForm()">Clear</button>
            </div>
          </ng-container>
            <div [ngClass]="{'col-md-8':(videoAndData||patientVideoAndData),'col-md-2':(!videoAndData&&!patientVideoAndData&&shwoFilter)}">
              <div class="form-check p-0 th" style="float:right;">
                <label class="form-check-label" for="exampleCheck1" translate>
                  <input type="checkbox" id="tems-check" (click)="getConsultationId($event,'All')" class="form-check-input check-aline" id="exampleCheck1">

                        All Reports &nbsp;&nbsp;&nbsp;&nbsp; </label>
                  <!-- <i class="fa fa-search" (click)="showFilterFields()"></i> -->
            </div>
            </div>
          </div>
          <ng-container *ngIf="shwoFilter&&(videoAndData||patientVideoAndData)">
            <div class="row">
            <div class="col-md-6 mt-3" >
              <!-- <label>Report Type</label> -->
              <ng-select id="reportType" [items]="reportTypes" formControlName="reportType" [clearable]="false" [searchable]="false" bindLabel="testType" bindValue="id" placeholder="Report Type" >
              </ng-select>
            </div>
            <div class="col-md-6 mt-3">
              <div class="form-group mb-0 ">
                <!-- <label translate>Report Generated On</label> -->
                <input
                [maxDate]="maxDate" [minDate]="minDate"
                placeholder="Report generated on" onkeydown="return false"
                class="form-control" formControlName="reportGeneratedOn"

                bsDatepicker
                [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
            </div>
            </div>
            <div class="col-md-6 mt-3">
              <div class="form-group mb-0 ">
                <!-- <label translate>Report Updated On</label> -->
                <input
                [maxDate]="maxDate" [minDate]="minDate"
                placeholder="Report Updated on" onkeydown="return false"
                class="form-control"
                bsDatepicker formControlName="reportUpdatedOn"
                [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">

            </div>
            </div>
            <div class="col-md-6 mt-3">
              <button class="btn btn-primary btn-size" [disabled]="!searchForm.touched && !searchForm.dirty" (click)="findReports()">Find</button>
              <button class="btn btn-secondary ml-1 btn-size" [disabled]="!searchForm.touched && !searchForm.dirty" (click)="resetSearchForm()">Clear</button>
            </div>
          </div>
          </ng-container>
        </form>
        </div>
        <!-- </div> -->
        <!-- </div> -->

        <div class="card card-table mb-0 mt-1" *ngIf="reportFiles.length > 0">
            <div class="float-right mt-3">
                <nav aria-label="Page navigation example" *ngIf="this.reportTotalPage > 1">
                    <ul class="pagination">
                        <li class="page-item" (click)="reportFirstPageList()" [ngClass]="{ 'disabled-pagination': reportCurrentPage === 1 }">
                            <a class="page-link">&lt;&lt;</a>
                        </li>
                        <li class="page-item" (click)="reportPreviousPageList()" [ngClass]="{ 'disabled-pagination': reportCurrentPage === 1 }">
                            <a class="page-link">&lt;</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link">page &nbsp;{{ reportCurrentPage }}&nbsp;of&nbsp;{{
                      reportTotalPage
                    }}</a
                  >
                </li>
                <li
                  class="page-item"
                  (click)="reportNextPageList()"
                  [ngClass]="{
                    'disabled-pagination': reportCurrentPage === reportTotalPage
                  }"
                >
                  <a class="page-link">&gt;</a>
                        </li>
                        <li class="page-item" (click)="reportLastPageList()" [ngClass]="{
                    'disabled-pagination': reportCurrentPage === reportTotalPage
                  }">
                            <a class="page-link">&gt;&gt;</a>
                        </li>
                    </ul>
                </nav>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-center mb-0">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Report Type</th>
                                <th>File Name</th>
                                <th>Report Generated On</th>
                                <th>Uploaded On</th>
                                <th>Action</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let file of reportFiles; let i = index">
                                <td>{{ i + 1 }}</td>
                                <td>{{ file.medical_report_type}}</td>
                                <td>{{ file.file_name }}</td>
                                <td>{{ file.report_generated_on | date:'mediumDate'}}</td>
                                <td>{{ file.created_at | date:'mediumDate'}}</td>
                                <td>
                                    <button class="btn btn-primary btn-sm btn-msg" (click)="openFile(file['file'])" data-toggle="modal">
                            View Report
                          </button>
                                </td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="float-right mt-3">
                <nav aria-label="Page navigation example" *ngIf="this.reportTotalPage > 1">
                    <ul class="pagination">
                        <li class="page-item" (click)="reportFirstPageList()" [ngClass]="{ 'disabled-pagination': reportCurrentPage === 1 }">
                            <a class="page-link">&lt;&lt;</a>
                        </li>
                        <li class="page-item" (click)="reportPreviousPageList()" [ngClass]="{ 'disabled-pagination': reportCurrentPage === 1 }">
                            <a class="page-link">&lt;</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link">page &nbsp;{{ reportCurrentPage }}&nbsp;of&nbsp;{{
                        reportTotalPage
                      }}</a
                    >
                  </li>
                  <li
                    class="page-item"
                    (click)="reportNextPageList()"
                    [ngClass]="{
                      'disabled-pagination': reportCurrentPage === reportTotalPage
                    }"
                  >
                    <a class="page-link">&gt;</a>
                        </li>
                        <li class="page-item" (click)="reportLastPageList()" [ngClass]="{
                      'disabled-pagination': reportCurrentPage === reportTotalPage
                    }">
                            <a class="page-link">&gt;&gt;</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
        <p *ngIf="reportFiles.length===0" class="text-center">No Files</p>
    </div>
    <!-- <p class="text-center">No Reports Available</p> -->

</div>
