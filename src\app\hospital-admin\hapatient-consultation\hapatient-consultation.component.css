.content-size{
  margin: 14px !important;
}
.fa-upload.ic {
  color: #fff;
  text-align: left;
}

.file-shown {
  padding-left: 10px;
  color: #999;
  font-size: 50px;
}
.fifteen_chars{
  display: block;
  white-space: nowrap;
  width: 12em;
  overflow: hidden;
  text-overflow: ellipsis;
}
.card.left-pane {
  background-color: #FFFFFF;
  border: solid 1px #F2f2F2;
}

.img-fluid {
  margin-top: 10px;
  margin-bottom: 10px;
  border: solid 1px #F2F2F2;
  border-radius: 50px;
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 40%;
  height: auto;
}

.left-pane p {
  text-align: center;
  /* margin-bottom: 3px; */
}

.doc-profile-image {
  padding-bottom: 90px;
  padding-right: 10px;
  padding-left: 10px;
}
.fas {
  color: #20c0f3;
  cursor: pointer; font-size: 30px !important;
}
p.doc-location {
  color: #20C0F3;
}
.vidOnly {
  position: relative;
  left: 10%;
  height: 650px !important;
  width: 50%;
  overflow: hidden;
}

  
  