<div class="content">
    <div class="container-fluid">
        <div class="row col-md-12 text-center mb-4" style="margin-left: 10%" *ngIf="showHeaderLinks">
            <a href="javascript:void(0);" class="active d-inline ml-4 mr-4 text-primary" id="dataAndVideo"
                (click)="dataAndVideo()">
                <h4 class="text-primary" *ngIf="videoAndData">
                    <u>Video and Consultation Data</u>
                </h4>
                <h4 class="text-primary" *ngIf="!videoAndData">
                    Video and Consultation Data
                </h4>
            </a>
            <a href="javascript:void(0);" class="d-inline ml-4 mr-4 text-primary" id="videoOnly" (click)="videoOnly()">
                <h4 class="text-primary" *ngIf="onlyVideo"><u> Video Only</u></h4>
                <h4 class="text-primary" *ngIf="!onlyVideo">Video Only</h4>
            </a>
            <a href="javascript:void(0);" class="d-inline ml-4 text-primary" id="consultation-data-only"
                (click)="dataOnly()">
                <h4 class="text-primary" *ngIf="onlyData">
                    <u> Consultation Data Only </u>
                </h4>
                <h4 class="text-primary" *ngIf="!onlyData">Consultation Data Only</h4>
            </a>
            <a class="btn btn-primary btn-lg ml-5 " style="color:white" (click)="viewPrescription()">
                Refresh
            </a>
        </div>
        <hr>
        <div class="row" *ngIf="videoAndData">
            <!-- <div class="col-md-4 offset-md-4 text-center form-group">
        <h6>Video size</h6>
        <button class="btn btn-info btn-sm mx-2" (click)="changeVideoSize('4')">2:2</button>
        <button class="btn btn-info btn-sm mx-2" (click)="changeVideoSize('3')">2:3</button>
        <button class="btn btn-info btn-sm mx-2"(click)="changeVideoSize('2')">1:3</button>
        <button class="btn btn-info btn-sm mx-2"(click)="changeVideoSize('1')">1:4</button>
      </div> -->
        </div>
        <form [formGroup]="patientConsultForm" (ngSubmit)="onFormSubmit()">
            <div class="row">
                <!--  profile card-->
                <!-- <div class="col-md-3 col-lg-3  col-xl-3">

          <div class=" left-pane" >
            <div class="card body">
              <div class="doc-profile-data" >
                <div class="spinner-border" role="status" *ngIf="!showProfilePic" style="margin: 10px 0px 0px 140px;">
                  <span class="sr-only">Loading...</span>
                </div>
                  <img [src]="profilePicture" class="img-fluid" alt="User Image" *ngIf="showProfilePic"  style="cursor: pointer;">
                  <p class="doc-name first-name"  style="cursor: pointer;"(click)="openPatientDashboard()">{{personalInfo['username']}}</p>
                  <p class="doc-name"  style="cursor: pointer;"(click)="openPatientDashboard()">Age : {{personalInfo['age']}}</p>
                  <p class="doc-name"  style="cursor: pointer;" (click)="openPatientDashboard()">Gender: {{personalInfo['gender']}}</p>
               </div>
              </div>
          </div>

      </div> -->

                <div class=" mt-4 column card resize"
                    [ngClass]="{'col-md-5 col-lg-5 col-xl-5': videoSize==0,'col-md-4 col-lg-4 col-xl-4': videoSize==3,'col-md-3 col-lg-3 col-xl-3': videoSize==2,'col-md-2 col-lg-2 col-xl-2': videoSize==1}"
                    [ngClass]="{'col-md-5 col-lg-5 col-xl-5 ':videoAndData, 'vidOnly':onlyVideo}"
                    [ngStyle]="{'width':onlyVideo ? '75%' : '750px','height':onlyData? '0px': '695px','background-color':onlyData?'white': '#99bee8', 'display':onlyData?'none': 'block' }">
                    <button class="btn btn-secondary btn-lg" (click)="joinVideo()" *ngIf="!joinedVideo"
                        [ngStyle]=" videoSize==0 ?{'margin': '36%'}: videoSize==3? {'margin': '36%'}:videoSize==2? {'margin': '30%'}:videoSize==1? {'margin': '20%'}:''">
                        Join Video
                    </button>
                    <app-consulting-video [consultationId]="consultationId" [participantName]="participantName"
                        *ngIf="joinedVideo" (showJoinButton)="showJoinVideoBtn($event)"></app-consulting-video>
                </div>
                <!-- <div class="col-md-3 col-lg-3  col-xl-3">
            <div class="left-pane ml-2 mr-3" >
              <div class="card body">
                <div class="doc-profile-data" >
                  <div class="spinner-border" role="status" *ngIf="!showDoctorProfilePic" style="margin: 10px 0px 0px 140px;">
                    <span class="sr-only">Loading...</span>
                  </div>
                    <img [src]="doctorProfilePicture" class="img-fluid" alt="User Image" *ngIf="showDoctorProfilePic">
                    <p class="doc-name first-name">Dr. {{doctorInfo['username']}}</p>
                    <p class="doc-speciality">{{degreeString}}</p>
                    <p class="doc-location" *ngFor="let location of practiceLocations"><i class="fas fa-map-marker-alt"></i>&nbsp;&nbsp;{{location.addresses?location.addresses.line_1+' , '+location.addresses.line_2+' , '+location.addresses.city_town_village+' , '+location.addresses.postal_code :"No address"}}</p>
                 </div>
                </div>
            </div>

        </div> -->

                <div [ngClass]="{'col-md-7 col-lg-7 col-xl-7': videoSize==0,'col-md-8 col-lg-8 col-xl-8': videoSize==3,'col-md-9 col-lg-9 col-xl-9': videoSize==2,'col-md-10 col-lg-10 col-xl-10': videoSize==1,'col-md-12 col-lg-12 col-xl-12':onlyData}"
                    *ngIf="showConsultation&&(videoAndData || onlyData)">
                    <app-haconsultation [patientVideoAndData]="videoAndData" [onlyData]="onlyData"
                        [patientVideoAndDataShow]="videoAndData"></app-haconsultation>
                    <div class="centered" *ngIf="isLoading">
                        <app-haloading-spinner></app-haloading-spinner>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>