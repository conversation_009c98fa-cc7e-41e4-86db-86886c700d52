<div class="container-fluid">
  <div class="breadcrumb-bar banner">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-12 col-12">
                <nav aria-label="breadcrumb" class="page-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);">{{'Admin'|translate}}</a></li>
                        <li #listHeader class="breadcrumb-item active" aria-current="page">{{'Dashboard' |translate}}</li>
                    </ol>
                </nav>
                <h2 #header class="breadcrumb-title">{{'Admin DashBoard' |translate}}</h2>
            </div>
        </div>
    </div>
</div>
  <div class="card mt-5">
    <div class="card-body">
      <h3 class="dashboard-title ml-1 mt-2 p-3">Your Activities</h3>
      <div class="col-md-12 mb-5">
        <button class="btn btn-info mx-2" id="add-doctor" (click)="addDoctor()">
          Add Doctor
        </button>
        <button class="btn btn-info mx-2 btn-height" id="add-admin" (click)="addAdmin()" >Create Admin</button>
        <button class="btn btn-info mx-2 btn-height" id="doctor-asssistant" (click)="createDoctorAssistant()" >
          Create Doctor Assistant
        </button>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-body">
  <div class="tab-content pt-0">
    <h4 class="dashboard-font-size  mb-2 p-3" >Doctor List</h4>
    <div class="appointment-tab">

      <!-- Doctor Tab -->
      <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
        <li class="nav-item">
          <a class="nav-link active" href="#doctor-list" data-toggle="tab">Doctor List</a>
        </li>
        <li class="nav-item">
          <a class="nav-link " href="#approved-doctor" data-toggle="tab">Approved Doctor</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#pending-doctor" data-toggle="tab">Pending Doctor </a>
        </li>
      </ul>
      <!-- /Doctor Tab -->

      <div class="tab-content">
         <!-- doctor list start -->
        <div class="tab-pane show active" id="doctor-list">
          <div class="card card-table mb-0"  >
            <div class="table-responsive" *ngIf="doctorList.length >0" >
              <table class="table table-hover table-center mb-0">
                <thead>
                  <tr>
                    <th>Name </th>
                    <th>System of Medicine</th>
                    <th>Speciality Name </th>
                    <!-- <th>Follow-up Review date</th> -->
                    <th>Action</th>

                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let doctor of doctorList; let i=index">
                    <td>
                      <h2 class="table-avatar">
                        <a  class="avatar avatar-sm mr-2">
                          <img class="avatar-img rounded-circle" src="assets/img/doctors/doctor-thumb-02.png" alt="User Image">
                        </a>
                        <a >{{doctor.user.username}}<span></span></a>
                      </h2>
                    </td>
                    <td>{{doctor.system_of_medicine}}</td>
                    <td>{{doctor.speciality.length> 0? doctor.speciality[0].Speciality_name:''}}</td>
                    <!-- <td>14 Nov 2019</td> -->
                    <button  class="btn btn-primary ml-2 mt-3" (click)="viewDetails(doctor.uuid)">view</button>
                    <button class="btn btn-primary mx-2 mt-3 " id="add-assistant" type="button" (click)="selectAssistant(doctor.uuid)" >Add Assistant</button>
                  </tr>

                </tbody>
              </table>

            </div>
            <div class="text-center mb-2 p-2 mt-2">
              <span class="appointmentList-no-data" *ngIf="doctorList.length ===0" style="color: orangered;">No Doctor Data</span>
            </div>
        </div>
        </div>
         <!-- doctor list end -->
        <!-- approved Tab -->
        <div class="tab-pane " id="approved-doctor">
          <div class="card card-table mb-0">
            <div class="card-body">
              <div class="table-responsive" *ngIf="approvedDoctor.length >0">
                <table class="table table-hover table-center mb-0">
                  <thead>
                    <tr>
                      <th> Name</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let data of approvedDoctor;let i=index">
                      <td>{{data.doctor_details.user.username}}</td>
                      <td>{{data.doctor_details.user.email}}</td>
                      <td>{{data.doctor_details.user.phone}}</td>
                      <td><button class="btn btn-primary" disabled id="approved">view</button></td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="text-center mb-2 p-2 mt-2">
                <span class="appointmentList-no-data" *ngIf="approvedDoctor.length ===0" style="color: orangered;">No approved doctor</span>
              </div>
            </div>
          </div>
        </div>
        <!-- /approved  Tab -->

        <!-- pending Tab -->
        <div class="tab-pane" id="pending-doctor">
          <div class="card card-table mb-0">
            <div class="card-body">
              <div class="table-responsive" *ngIf="pendingDoctor.length >0">
                <table class="table table-hover table-center mb-0">
                  <thead>
                    <tr>
                      <th> Name</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let data of pendingDoctor;let i=index">
                      <td>{{data.doctor_details.user.username}}    </td>
                      <td>{{data.doctor_details.user.email}}</td>
                      <td>{{data.doctor_details.user.phone}}</td>
                      <td><button class="btn btn-primary" disabled id="pending">view</button></td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="text-center mb-2 p-2 mt-2">
                <span class="appointmentList-no-data" *ngIf="pendingDoctor.length ===0" style="color: orangered;">No pending doctor</span>
              </div>
            </div>
          </div>
        </div>
        <!-- /pending  Tab -->

      </div>
    </div>


  </div>
  </div>
  </div>

  <div class="card">
    <div class="card-body">
    <div class="row">
      <div class="col-md-12">
        <h4 class="mb-2 dashboard-font-size" >User Data</h4>
        <div class="appointment-tab">

          <!-- Appointment Tab -->
          <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
            <li class="nav-item">
              <a class="nav-link active" href="#admin-user" data-toggle="tab">Admin User</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#doctor-assistant" data-toggle="tab">Doctor Assistant</a>
            </li>
          </ul>
          <!-- /Appointment Tab -->

          <div class="tab-content">

            <!-- Upcoming Appointment Tab -->
            <div class="tab-pane show active" id="admin-user">
              <div class="card card-table mb-0">
                <div class="card-body">
                  <div class="table-responsive" *ngIf="adminList.length >0">
                    <table class="table table-hover table-center mb-0">
                      <thead>
                        <tr>
                          <th> Name</th>
                          <th>Email</th>
                          <th>Phone</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let data of adminList;let i=index">
                          <td>{{data.username}}    </td>
                          <td>{{data.email}}</td>
                          <td>{{data.phone}}</td>
                          <td><button class="btn btn-primary" id="" disabled>view</button></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="text-center mb-2 p-2 mt-2">
                    <span class="appointmentList-no-data" *ngIf="adminList.length ===0" style="color: orangered;">No Admin Data</span>
                  </div>
                </div>
              </div>
            </div>
            <!-- /Upcoming Appointment Tab -->

            <!-- Today Appointment Tab -->
            <div class="tab-pane" id="doctor-assistant">
              <div class="card card-table mb-0">
                <div class="card-body">
                  <div class="table-responsive" *ngIf="assistantList.length >0">
                    <table class="table table-hover table-center mb-0">
                      <thead>
                        <tr>
                          <th> Name</th>
                          <th>Email</th>
                          <th>Phone</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let data of assistantList;let i=index">
                          <td>{{data.username}}    </td>
                          <td>{{data.email}}</td>
                          <td>{{data.phone}}</td>
                          <td><button class="btn btn-primary" disabled>view</button></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="text-center mb-2 p-2 mt-2">
                    <span class="appointmentList-no-data" *ngIf="assistantList.length ===0" style="color: orangered;">No Assistant Data</span>
                  </div>
                </div>
              </div>
            </div>
            <!-- /Today Appointment Tab -->

          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
</div>
<!-- Button trigger modal -->


<!-- Modal -->
<div class="modal fade" id="assistantModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Add Doctor Assistant</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <h4 translate>Doctor List</h4>
          <select class="form-control select" name="doctor" id="doctor" (change)="getAssisatantDatails($event.target.value)" >
    <option>Select</option>
    <option *ngFor="let assist of assistantList" value={{assist.uuid}}> {{assist.username}}</option>

   </select>
      </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" (click)="assignDoctorAssistant()">Add Assistant</button>
      </div>
    </div>
  </div>
</div>
