import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SharedService } from '../shared/shared.service';
import { ToastrService } from 'ngx-toastr';
import { HospitalService } from './hospital-admin.service';
import { Subscription } from 'rxjs';
import { HospitalModel } from './models/hospital.model';
declare var $: any;
@Component({
  selector: 'app-hospital-admin',
  templateUrl: './hospital-admin.component.html',
  styleUrls: ['./hospital-admin.component.css'],
})
export class HospitalAdminComponent implements OnInit {
  isLoading: boolean;
  doctorList: any = [];
  hospitalId: string;
  public id: string;
  public adminList = [];
  public assistantList: any = [];
  public approvedDoctor: any = [];
  public pendingDoctor: any = [];
  selectedDotorId: any;
  selectedAssistantId: any;
  private subscriptions: Subscription;
  hospital: HospitalModel;

  constructor(
    private router: Router,
    private sharedService: SharedService,
    private notificationService: ToastrService,
    private hospitalService: HospitalService,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.sharedService.setActiveLink('dashboard');
    this.subscriptions = this.hospitalService.currentHospitalDetails.pipe()
      .subscribe(value => {
        if (value && value.hospitalId != '') {
          this.hospital = Object.assign({}, value);
          this.hospitalId = this.hospital.hospitalId;
        } else {
          this.hospital = this.hospitalService.getHospitalDetails();
          this.hospitalId = this.hospital.hospitalId;
        }
        if (this.hospitalId) {
          this.getDoctorList(this.hospitalId);
          this.getDoctorAssociationStatus();
          this.getHospitalAdmin(this.hospitalId);
        }
      });
  }

  addDoctor() {
    this.router.navigate(['/add-doctor', this.hospitalId]);
  }

  createDoctorAssistant() {
    this.router.navigate(['/add-assistant', this.hospitalId]);
  }

  addAdmin() {
    this.router.navigate(['/add-admin', this.hospitalId]);
  }
  getDoctorList(id) {
    this.isLoading = true;
    this.hospitalService.getDoctorByHospital(id).subscribe(
      (data) => {
        this.doctorList = data['results'];
        console.log(this.doctorList);
        this.isLoading = false;
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.isLoading = false;
      }
    );
  }
  viewDetails(id) {
    this.router.navigate(['/doctor-profile/', id, this.hospitalId]);
  }
  getHospitalAdmin(id) {
    let admin = [];
    this.hospitalService.getHospitalAdmin(id).subscribe(
      (data) => {
        admin = data['results'];
        console.log(admin);
        this.adminList = admin.filter(
          (obj) => obj.user_type == 'HospitalAdmin'
        );
        console.log(this.adminList);
        this.assistantList = admin.filter(
          (obj) => obj.user_type === 'DoctorAssistant'
        );
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.isLoading = false;
      }
    );
  }
  getDoctorAssociationStatus() {
    let status = [];
    this.hospitalService.getAssociationData(this.hospitalId).subscribe(
      (data) => {
        status = data['results'];
        console.log('pending', status);
        this.pendingDoctor = status.filter(
          obj => obj.is_approved_by_doctor === false
        );
        this.approvedDoctor = status.filter(
          obj => obj.is_approved_by_doctor === true
        );

      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.isLoading = false;
      }
    );
  }
  selectAssistant(id) {
    this.selectedDotorId = id;
    $('#assistantModal').modal('show');
  }
  assignDoctorAssistant() {
    const data = { doctor_uuid: this.selectedDotorId, assistant_uuid: this.selectedAssistantId };
    this.hospitalService.assignAssistant(data).subscribe(data => {
      $('#assistantModal').modal('hide');

    }, err => {
      console.log(err);
      this.notificationService.error('Internal server error', 'Med.Bot');
    });

  }
  getAssisatantDatails(event) {
    this.selectedAssistantId = event;
    console.log(event);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
}
