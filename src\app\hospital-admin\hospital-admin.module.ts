import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HospitalAdminComponent } from './hospital-admin.component';
import {AddDoctorComponent} from './add-doctor/add-doctor.component';
import { UserAdminComponent } from './user-admin/user-admin.component';
import { DoctorAssistantComponent } from './doctor-assistant/doctor-assistant.component';
import { HospitalDoctorProfileComponent } from './hospital-doctor-profile/hospital-doctor-profile.component';
import {TranslateModule, TranslateLoader} from '@ngx-translate/core';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import{HospitalService} from './hospital-admin.service';
import { AdminProfileComponent } from './admin-profile/admin-profile.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { HospitalDetailComponent } from './hospital-detail/hospital-detail.component';
import { UsersComponent } from './users/users.component';
import { PartnerAdminComponent } from './partner-admin/partner-admin.component';
import { HaBasicprofileComponent } from './ha-basicprofile/ha-basicprofile.component';
import { HaHomeAddressComponent } from './ha-home-address/ha-home-address.component';
import { HaEducationComponent } from './ha-education/ha-education.component';
import { HaDoctorRegistrationComponent } from './ha-doctor-registration/ha-doctor-registration.component';
import { HaDepartmentComponent } from './ha-department/ha-department.component';
import { HaSpecialityComponent } from './ha-speciality/ha-speciality.component';
import { HospitalDoctorFeeComponent } from './hospital-doctor-fee/hospital-doctor-fee.component';
import { DatepickerModule, BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { DoctorAssistantProfileComponent } from './doctor-assistant-profile/doctor-assistant-profile.component';
import { DoctorAssistantAddressComponent } from './doctor-assistant-address/doctor-assistant-address.component';
import { PartnerProfileComponent } from './partner-profile/partner-profile.component';
import { PartnerAddressComponent } from './partner-address/partner-address.component';
import { HadoctorPracticelocationComponent } from './hadoctor-practicelocation/hadoctor-practicelocation.component';
import { HadoctorConsultatiionComponent } from './hadoctor-consultatiion/hadoctor-consultatiion.component';
import { HadocterManageAppointmentsComponent } from './hadocter-manage-appointments/hadocter-manage-appointments.component';
import { ModalModule } from 'ngb-modal';
import { HadoctorBookappointmentComponent } from './hadoctor-bookappointment/hadoctor-bookappointment.component';
import { HapatientConsultationComponent } from './hapatient-consultation/hapatient-consultation.component';
import { HaloadingSpinnerComponent } from './haloading-spinner/haloading-spinner.component';
import { HadoctorViewprofileComponent } from './hadoctor-viewprofile/hadoctor-viewprofile.component';
import { HaDoctorappointmentComponent } from './ha-doctorappointment/ha-doctorappointment.component';
import { HadashboardComponent } from './hadashboard/hadashboard.component';
import { BookingStatusComponent } from './booking-status/booking-status.component';
import { HaconsultingVideoComponent } from './haconsulting-video/haconsulting-video.component';
import { HamedicalReportComponent } from './hamedical-report/hamedical-report.component';
import { HaconsultationComponent } from './haconsultation/haconsultation.component';
import { HaconsultHistoryComponent } from './haconsult-history/haconsult-history.component';
import { SharedModule } from '../shared/shared.module';
import { HospitalSettingsComponent } from './hospital-settings/hospital-settings.component';
import { TeleConsultModule } from '../tele-consult/tele-consult.module';
@NgModule({
    declarations: [
      HospitalAdminComponent,
      AddDoctorComponent,
      UserAdminComponent,
      DoctorAssistantComponent,
      HospitalDoctorProfileComponent,
      AdminProfileComponent,
      HospitalDetailComponent,
      UsersComponent,
      PartnerAdminComponent,
      HaBasicprofileComponent,
      HaHomeAddressComponent,
      HaEducationComponent,
      HaDoctorRegistrationComponent,
      HaDepartmentComponent,
      HaSpecialityComponent,
      HospitalDoctorFeeComponent,
      DoctorAssistantProfileComponent,
      DoctorAssistantAddressComponent,
      PartnerProfileComponent,
      PartnerAddressComponent,
      HadoctorPracticelocationComponent,
      HadoctorConsultatiionComponent,
      HadocterManageAppointmentsComponent,
      HadoctorBookappointmentComponent,
      HapatientConsultationComponent,
      HaloadingSpinnerComponent,
      HadoctorViewprofileComponent,
      HaDoctorappointmentComponent,
      HadashboardComponent,
      BookingStatusComponent,
      HaconsultingVideoComponent,
      HamedicalReportComponent,
      HaconsultationComponent,
      HaconsultHistoryComponent,
      HospitalSettingsComponent,
    ],
    imports:[
      BrowserModule,
      BrowserAnimationsModule,
      RouterModule,
      FormsModule,
      CommonModule,
      NgSelectModule,
      ReactiveFormsModule,   TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient]
        }
      }),
      DatepickerModule.forRoot(),BsDatepickerModule.forRoot(),
      ModalModule,
      SharedModule,
      TeleConsultModule
    ],
    exports: [
      TranslateModule
    ],
    providers:[HospitalService],
  })
  export class HospitalAdminModule {}

export function HttpLoaderFactory(httpClient: HttpClient) {
  return new TranslateHttpLoader(httpClient);
}
