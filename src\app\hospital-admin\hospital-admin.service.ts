import { delay } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as Settings from './../config/settings';
import { BehaviorSubject, Subscription } from "rxjs";
import { SharedService } from '../shared/shared.service';
import { HospitalModel } from "./models/hospital.model";

@Injectable({
  providedIn: 'root'
})
export class HospitalService {
  hospital: HospitalModel;
  private subscriptions: Subscription[] = [];
  currentHospitalDetails: BehaviorSubject<HospitalModel>;

  constructor(
    private httpClient: HttpClient,
    private sharedService: SharedService,
  ) {
    this.hospital = new HospitalModel();
    this.currentHospitalDetails = new BehaviorSubject<HospitalModel>(this.hospital);
  }

  setHospitalDetails() {
    this.getUsers().subscribe(
      (data: any) => {
        if (data['uuid']) {
          this.hospital = new HospitalModel();
          const hospitalId = data['uuid'];
          localStorage.setItem('hstId', hospitalId);
          this.hospital.hospitalId = hospitalId;
          this.sharedService.setUserName(data['name']);
          if (data.logo_large) {
            localStorage.setItem('hstlogo', data.logo_large.file);
            this.hospital.hospitalLogo = data.logo_large.file;
          }
          this.currentHospitalDetails.next(this.hospital);
        } else {
          this.hospital = new HospitalModel();
          this.currentHospitalDetails.next(this.hospital);
        }
      },
      (err) => {
        console.log(err);
      }
    );
  }

  getHospitalDetails(): HospitalModel {
    const sb1 = this.currentHospitalDetails.pipe()
      .subscribe(value => {
        if (value && value.hospitalId != '') {
          this.hospital = Object.assign({}, value);
        } else {
          const hospitalId = localStorage.getItem('hstId');
          const hospitalLogo = localStorage.getItem('hstlogo');
          if (hospitalLogo) {
            this.hospital.hospitalLogo = hospitalLogo;
          }
          if (hospitalId) {
            this.hospital.hospitalId = hospitalId;
            this.currentHospitalDetails.next(this.hospital);
          }
        }
      });
    this.subscriptions.push(sb1);
    return this.hospital;
  }

  searchDocotor(id) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/qsearch/?q=${id}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  disAssociateDoctor(hospital_uuid, doctor_uuid) {
    const data = {};
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${hospital_uuid}/doctors/${doctor_uuid}/close/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorByHospital(id) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/doctors/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  createHospitalAdmin(id, data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/users/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  createHospitalDept(data, val?: any) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/add_medical_system_and_department/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  createHospitalNewDept(data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/add_medical_system_and_department/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getHospitalDept(id) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/department/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  createHospitalSpec(id, data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/speciality/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  createHospitalNewSpec(data) {
    // return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/specialities/create/`, data)
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/specialities/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getHospitalAdmin(id) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/users/?user_type=Patient`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  assosiateDoctor(id, data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/doctors/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateDoctorProfile(id, data) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/doctors/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getHospitalSettings(id) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/pattern_settings/`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateHospitalSettings(id,data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/pattern_settings/`,data)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getUsers() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/me/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getAssociationData(id) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/d-associations/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  assignAssistant(data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/me/a-associations/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  aassignDoctor(data) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getUsersById(id) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/users/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  setUsersById(data) {
    return this.httpClient.post(`${Settings.API_AUTH_URL_PREFIX}/api/h/hospitals/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
  }

  getPatientsList(id, page_number) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/users/?page=${page_number}&user_type=patient`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorsList(id, page_number) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/users/?page=${page_number}&user_type=doctor`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorsSearchList(id, query) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/users/${query}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getAssistantList(id, page_number) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/users/?page=${page_number}&user_type=doctorassistant`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getPartnerList(id, page_number) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/users/?page=${page_number}&user_type=partner`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorQualifications(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/qualifications/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDeptLst(page_number) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/departments_list/?page=${page_number}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDeptList(id, page_number) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/department/?page=${page_number}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  deleteDeptList(id, dept_id) {
    return this
      .httpClient
      .delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/department/${dept_id}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  deleteDeptLst(dept_id,hspt_id,dept_name){
    return this
      .httpClient
      .delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/departments_delete/${dept_id}/?hospital_id=${hspt_id}&department_name=${dept_name}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getSpecialityList(id, page_number) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/speciality/?page=${page_number}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  deleteSpecialityList(id, spec_id) {
    return this
      .httpClient
      .delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${id}/speciality/${spec_id}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorDetails(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/profile/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateDoctorQualification(uuid, qual_uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/qualifications/${qual_uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postDoctorQualification(uuid, data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/qualifications/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDegree(data) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/degrees/?system_of_medicine=${data}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateUserDetails(uuid, data) {
    return this
      .httpClient.patch(`${Settings.API_AUTH_URL_PREFIX}/api/auth/users/${uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorLanguages() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/languages/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getSpeciality() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/specialities/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getSystemOfMedicine() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/system_of_medicine/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  addSystemOfMedicine(data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/system_of_medicine/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorQualification() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchDoctorQualification(qual_uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/${qual_uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postDoctorQualifications(data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  deleteDoctorQualifications(qual_uuid) {
    return this
      .httpClient
      .delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/${qual_uuid}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  addDegree(data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/degrees/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getCountryDetail() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/countries/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorProfile() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/profile/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateRegistrationForm(data, registerfile) {
    const formData = new FormData();
    formData.append('file', registerfile);
    formData.append('data', JSON.stringify(data));
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/registrations/`, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getCouncils(data) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/councils/?system_of_medicine=${data}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateDoctorRegistration(uuid, reg_uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/registrations/${reg_uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorRegistrations(uuid) {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/registrations/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getApprovalMessages() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/?approval_request_status=Pending`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postHospital(data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchHospital(uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getHospitals(pageNumber) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/?page=${pageNumber}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getApprovedDoctor(pageNumber) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/admin-search/?approval_request_status=Approved&page=${pageNumber}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getPendingDoctor(pageNumber) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/admin-search/?approval_request_status=Pending&page=${pageNumber}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getRejectedDoctor(pageNumber) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/admin-search/?approval_request_status=Rejected&page=${pageNumber}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateSlugName(data) {
    return this.httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${data.uuid}/profile/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorPracticeLocations() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/practice_locations/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateTermsAndCondtion(id) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/terms/acceptance/`, id)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  submitForApproval() {
    const data = '';
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/profile/approval_request/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  createOTP(data:any){
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/auth/otp/merge_otp/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  otpVerification(data:any){
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/auth/otp/merge_validate_otp/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
}
