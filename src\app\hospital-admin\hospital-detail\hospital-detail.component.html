<!--BreadCrumbs-->
<div class="breadcrumb-bar">
  <div class="container-fluid">
    <div class="row align-items-center">
      <div class="col-md-12 col-12">
        <nav aria-label="breadcrumb" class="page-breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="javascript:void(0);">{{ "Admin" | translate }}</a>
            </li>
            <li #listHeader class="breadcrumb-item active" aria-current="page">
              {{ "Hospital" | translate }}
            </li>
          </ol>
        </nav>
        <h2 #header class="breadcrumb-title">{{ "Hospital " | translate }}</h2>
      </div>
    </div>
  </div>
</div>
<!--BreadCrumbs Ends-->
<div class="container-fluid">
  <div class="row">
    <div class="col-md-12 col-lg-12 col-xl-12">
      <h5 (click)="goBack()" class="mb-4 back-head ms">
        <i class="fas fa-chevron-circle-left"></i>Back
      </h5>
      <div class="hsptl-form">
        <div class="card">
          <div class="card-body">
            <h3>
              Hospital Details&nbsp;<i *ngIf="disabledHospitalForm" class="fa fa-edit"
                (click)="editHospitalDetails()"></i>
            </h3>
            <div class="hsp-details">
              <form [formGroup]="hospitalDetailForm" class="hsp-detail-form">
                <div class="row">
                  <div class="col-md-4 col-lg-4 col-sm-6">
                    <small>Name</small>
                    <input [readOnly]="disabledHospitalForm" id="hsp-name" formControlName="name" type="text"
                      class="form-control" />
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-6">
                    <small>Website Url</small>
                    <input [readOnly]="disabledHospitalForm" id="hsp-url" formControlName="url" type="text"
                      class="form-control" />
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-6">
                    <small>Email</small>
                    <input [readOnly]="disabledHospitalForm" type="text" id="hsp-reg-num" formControlName="email"
                      class="form-control" />
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4 col-lg-4 col-sm-6">
                    <small>Phone Number</small>
                    <input [readOnly]="disabledHospitalForm" id="hsp-phone" type="text" formControlName="phone_numbers"
                      class="form-control" />
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-6">
                    <small>Admin Name</small>
                    <input [readOnly]="disabledHospitalForm" id="admin-name" type="text"
                      formControlName="contact_person_name" class="form-control" />
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-6">
                    <small>Admin Email</small>
                    <input type="text" id="admin-email" formControlName="user_email" class="form-control" readonly />
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4 col-lg-4 col-sm-6">
                    <small>Admin Phone</small>
                    <input id="user-phone" type="text" formControlName="phone" class="form-control" readonly />
                  </div>
                </div>
                <div class="row">
                  <div class="col-12">
                    <small>Description</small>
                    <textarea [readOnly]="disabledHospitalForm" id="dist" formControlName="description"
                      class="form-control desc"></textarea>
                  </div>
                </div>
                <div class="col-md-12 col-sm-12 col-xs-12 mt-1 text-right" *ngIf="!disabledHospitalForm">
                  <button id="save-clinic-btn" (click)="save()" class="save-btn basic-data-btn" translate>
                    Save
                  </button>

                  <button class="btn btn-secondary cancel-btn" id="cancel-clinic-btn" (click)="cancelUpdate()"
                    *ngIf="!addressReadOnly" translate>
                    Cancel
                  </button>
                </div>
              </form>

              <form id="addressForm" [formGroup]="addressForm">
                <h4 class="card-title mt-2" translate>
                  Hospital Address
                  <i class="fa fa-edit" (click)="editAddress()" *ngIf="!!addressReadOnly"></i>
                </h4>
                <div class="col-md-12 text-right">
                  <a></a>
                </div>
                <div class="row form-row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label translate>Line 1<span class="text-danger">*</span></label>
                      <input type="text" class="form-control" id="line_1" formControlName="line_1"
                        [readonly]="addressReadOnly" maxlength="50" required />
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label translate>Line 2<span class="text-danger"></span></label>
                      <input type="text" class="form-control" id="line_2" formControlName="line_2"
                        [readonly]="addressReadOnly" maxlength="50" />
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <label translate>City/Town/Village<span class="text-danger">*</span></label>
                      <input type="text" class="form-control" id="city" formControlName="city_town_village"
                        [readonly]="addressReadOnly" required maxlength="50" />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label translate>District </label>
                      <input type="text" id="distric" class="form-control" [readonly]="addressReadOnly"
                        formControlName="district" maxlength="50" />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label translate>Taluk</label>
                      <input type="text" id="taluk" class="form-control" [readonly]="addressReadOnly"
                        formControlName="taluk" maxlength="50" />
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <label translate>State<span class="text-danger">*</span></label>
                      <input type="text" id="state" class="form-control" formControlName="state"
                        [readonly]="addressReadOnly" required maxlength="50" />
                    </div>
                  </div>
                  <div class="col-md-4 ng-select-container">
                    <div class="form-group">
                      <label translate>Country <span class="text-danger">*</span></label>

                      <ng-select id="country" class="custom" formControlName="country" bindValue="Name"
                        [items]="countryList" [readonly]="addressReadOnly" [searchable]="true" bindLabel="Name"
                        [clearable]="false" placeholder="{{ 'Select Country' | translate }}" multiple required>
                      </ng-select>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label translate>Postal Code<span class="text-danger">*</span></label>
                      <input type="text" id="postal_code" class="form-control" formControlName="postal_code"
                        [readonly]="addressReadOnly" required maxlength="10" />
                    </div>
                  </div>
                </div>
                <div class="col-md-12 col-sm-12 col-xs-12 text-right">
                  <button *ngIf="!addressReadOnly" [disabled]="!addressForm.valid" id="save-clinic-btn"
                    class="save-btn basic-data-btn" translate>
                    Save
                  </button>

                  <button class="btn btn-secondary cancel-btn" id="cancel-clinic-btn" (click)="cancelAddress()"
                    *ngIf="!addressReadOnly" translate>
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>