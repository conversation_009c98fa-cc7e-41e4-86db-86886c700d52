import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { HospitalService } from '../hospital-admin.service';
import { PatientService } from '../../patient/patient.service';
import { SharedService } from '../../shared/shared.service';
import { Location } from '@angular/common';

@Component({
  selector: 'app-hospital-detail',
  templateUrl: './hospital-detail.component.html',
  styleUrls: ['./hospital-detail.component.css']
})
export class HospitalDetailComponent implements OnInit {

  public hospitalsdata = {};
  hospitalDetailForm: FormGroup;
  disabledHospitalForm = true;
  public addressForm: FormGroup;
  practiceLocation: string;
  public countryList: any = [];
  doctorClinicAddressList: any = [];
  addressReadOnly: boolean;
  public cancleBtnShow = false;
  address: any = [];
  contactData: any = [];
  isLoading = true;
  constructor(private hospitalService: HospitalService,
    private notificationService: ToastrService,
    private patientService: PatientService,
    private sharedService: SharedService,
    private location: Location) { }

  ngOnInit(): void {
    this.sharedService.setActiveLink('hospital details');
    this.addFormControlData(null)
    this.getAdminProfile();
  }
  addProfileFormControl(data) {
    this.disabledHospitalForm = true;
    this.hospitalDetailForm = new FormGroup({
      uuid: new FormControl(data['uuid'], Validators.required),
      name: new FormControl(data['name'], Validators.required),
      url: new FormControl(data['url'], Validators.required),
      phone_numbers: new FormControl(data['phone_numbers'], Validators.required),
      email: new FormControl(data['email'], Validators.required),
      user_email: new FormControl(data['user']['email'], Validators.required),
      phone: new FormControl(data['user']['phone'], Validators.required),
      contact_person_name: new FormControl(data['contact_person_name'], Validators.required),
      description: new FormControl(data['description'], Validators.required),
    });
  }
  cancelAddress() {
    this.cancleBtnShow = false;
    if (this.address.length > 0) {
      this.addFormControlData(this.address[0]);
    } else {
      this.addFormControlData(null);
    }
  }

  addFormControlData(data) {
    if (data === null) {
      this.practiceLocation = sessionStorage.getItem('practice_location');
      this.addressReadOnly = false;
      this.cancleBtnShow = false;
      this.addressForm = new FormGroup({
        uuid: new FormControl(null),
        address_type: new FormControl('Home'),
        line_1: new FormControl(),
        line_2: new FormControl(),
        city_town_village: new FormControl(),
        district: new FormControl(),
        taluk: new FormControl(),
        state: new FormControl(),
        country: new FormControl(),
        postal_code: new FormControl(),
      });
    } else {
      this.addressReadOnly = true;
      this.cancleBtnShow = false;
      this.addressForm = new FormGroup({
        uuid: new FormControl(data.uuid),
        address_type: new FormControl(data.address_type),
        line_1: new FormControl(data.line_1),
        line_2: new FormControl(data.line_2),
        city_town_village: new FormControl(data.city_town_village),
        district: new FormControl(data.district),
        taluk: new FormControl(data.taluk),
        state: new FormControl(data.state),
        country: new FormControl(data.country),
        postal_code: new FormControl(data.postal_code),
      });
    }
  }
  // onSubmit() {
  //   this.hospitalService.updateDoctorProfile(this.doc_uuid,this.user_data).subscribe(
  //     (data) => {
  //       this.notificationService.success('Profile Update', 'Med.Bot');
  //       this.disabled = true;
  //
  //     },
  //     (error) => {
  //       console.log(error);
  //       this.notificationService.error('Updation Error', 'Med.Bot');
  //     }
  //   );
  // }
  cancelUpdate() {
    this.disabledHospitalForm = true;
    this.addProfileFormControl(this.hospitalsdata);
  }
  editHospitalDetails() {
    this.disabledHospitalForm = false;

  }
  getAdminProfile() {
    this.hospitalsdata = {
      'contact_person_name': 'vasanth',
      'uuid': 'f6e0dac8-5049-4ab0-8e3e-05ac94d49ab4',
      'description': 'hi',
      'email': '<EMAIL>',
      'name': 'vasanthakumar sankaran',
      'phone_numbers': '**********',
      'url': 'https://baba.hospital.in/',
      'user': {
        'email': '<EMAIL>',
        'password1': 'password123',
        'phone': '**********',
        'user_type': 'HospitalAdmin',
        'username': '<EMAIL>'
      }
    };
    this.addProfileFormControl(this.hospitalsdata);
  }
  addHospitalAddress() {

  }
  save() {

  }
  editAddress() {
    this.addressReadOnly = false;
    this.cancleBtnShow = true;
  }
  getAddress() {
    this.patientService.getAddressDetail().subscribe(
      (data) => {
        this.address = data;
        if (this.address.length > 0) {
          this.addFormControlData(this.address[0]);
        } else {
          this.addFormControlData(null);
        }
      },
      (err) => {
        console.log('err', err);
      }
    );
  }
  getCountry() {
    this.patientService.getCountryDetail().subscribe(
      (data) => {
        this.countryList = data;
      },
      (error) => {
        console.log(error);
      }
    );
  }
  goBack() {
    this.location.back();
  }
}
