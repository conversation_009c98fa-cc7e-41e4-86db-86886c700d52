<div class="card">
    <div class="card-body">
        <h4 class="card-title">Fee <i *ngIf="!newFee && !editFee" (click)="onEditFee()" class="fa fa-plus-circle"></i></h4>
        <!-- <p *ngIf="feeForm.effective_upto==null"> Fee Amount : Rs.{{currentFeeData.amount}}, Valid From : {{currentFeeData.effective_from}} </p> -->
        <div class="feeList" *ngFor="let fee of feeList; let i=index">
            <p id="fee{{i+1}}"> Fee Amount : Rs.{{fee.amount}},</p>
            <p>Valid From: {{fee.effective_from|date:'yyyy-MM-dd'}} </p>
            <p *ngIf="fee.effective_upto !=='null'">, Valid Upto : {{fee.effective_upto|date:'yyyy-MM-dd'}} </p>
        </div>
        <div *ngIf="editFee || !feeDataAvailable">
          <div class="form-group "  *ngIf="formError">
            <!-- <label style="color: orangered;" >Form Error</label> -->
            <div class="card mt-3">
              <ng-container *ngFor="let err of errorValue">
                <p class="text-danger">&nbsp;{{err.value}}</p>
              </ng-container>
            </div>
            </div>
            <form #_feeForm="ngForm" >
                <fieldset [disabled]="formDisable">
                    <div class="registrations-info">
                        <div class="row form-row reg-cont">
                            <div class="col-12 col-md-3">
                                <div class="form-group">
                                    <label>Amount<span class="text-danger">*</span></label>
                                    <input [(ngModel)]="feeForm.amount" id="fee-amount" onkeypress="return event.charCode >= 48 && event.charCode <= 57" type="text" maxlength="4" minlength="1" name="amount" class="form-control" autocomplete="off" #amount="ngModel" required>
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <div class="form-group">
                                    <label>Effective From<span class="text-danger">*</span></label>
                                    <!-- <input [(ngModel)]="feeForm.effective_from" type="date" id="fee-eff-date" name="effective_from" class="form-control" required> -->
                                    <!-- <input class="form-control" placeholder="yyyy-mm-dd" (click)="d.toggle()" name="dp" [(ngModel)]="model" ngbDatepicker #d="ngbDatepicker"> -->
                                    <input type="text" style="caret-color: transparent;" [minDate]="today" class="form-control" name="effective_from" id="fee-eff-date" [(ngModel)]="feeForm.effective_from" #dp="bsDatepicker" placeholder="Select Date" onkeydown="return false" autocomplete="off"
                                        bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }">
                                </div>
                            </div>
                            <div  class="col-12 col-md-3">
                                <div class="form-group">
                                  <label>Effective Upto<span class="text-danger">*</span></label>
                                    <input type="text" style="caret-color: transparent;" [(ngModel)]="feeForm.effective_upto" [minDate]="today" [min]="feeForm.effective_from" id="fee-edit1-date" name="effective_upto" class="form-control" #dp="bsDatepicker" placeholder="Select Date" onkeydown="return false" autocomplete="off"
                                    bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }" >
                                </div>
                            </div>
                            <div *ngIf="newFee || editFee" class="col-12 col-md-3 btns">
                                <button [disabled]="!feeDataAvailable" (click)="cancelFeeSubmit()" id="cancel-fee-btn" class="btn btn-secondary cancel-btn" translate>Cancel</button>
                                <button [disabled]="amount.invalid ||feeForm.amount ==null || feeForm.effective_from ==null || feeForm.effective_upto ==null" (click)="showWarningFeeNotification()" id="save-fee-btn" class="btn btn-primary" translate>Save</button>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </form>
        </div>
    </div>
</div>
<div class="modal fade" id="feeNotificationModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Fee  </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>Once the fee is created you are not allowed to create fee for previous dates.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary btn-sm" (click)="onFeeSubmit()">Save </button>
      </div>
    </div>
  </div>
</div>

