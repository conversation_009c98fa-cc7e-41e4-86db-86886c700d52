<div class="container-fluid">
  <div class="row">
    <div class="col-md-12 col-lg-12 col-xl-12">
      <h5 class="m-4 back-head ms" (click)="goBack()">
        <i class="fas fa-chevron-circle-left"></i>Back
      </h5>
      <div class="hsptl-form">
        <div class="card">
          <div class="card-body">
            <h3>
              Settings&nbsp;
            </h3>
            <div class="hsp-details">
              <form [formGroup]="hospitalDetailForm" class="hsp-detail-form">
                <div class="row">
                  <div class="col-md-3 col-lg-3 col-sm-6">
                    <small>Center ID</small>
                    <input id="hsp-name" formControlName="centerCode" type="text" class="form-control" minlength="3"
                      maxlength="6" autocomplete="off" />
                    <small *ngIf="hospitalDetailForm.controls.centerCode.errors?.minlength" class="text-danger">Center
                      Code must be at least 3 characters.&nbsp;</small>
                    <small *ngIf="hospitalDetailForm.controls.centerCode.errors?.maxlength" class="text-danger">Center
                      Code should be maximum of 6 characters.&nbsp;</small>
                    <small *ngIf="hospitalDetailForm.controls.centerCode.errors?.pattern" class="text-danger">Characters
                      only</small>
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-6">
                    <small>Patient ID</small>
                    <input id="hsp-name" formControlName="patientCode" type="text" class="form-control" minlength="3"
                      maxlength="6" autocomplete="off" />
                    <small *ngIf="hospitalDetailForm.controls.patientCode.errors?.minlength" class="text-danger">Center
                      Code must be at least 3 characters.&nbsp;</small>
                    <small *ngIf="hospitalDetailForm.controls.patientCode.errors?.maxlength" class="text-danger">Center
                      Code should be maximum of 6 characters.&nbsp;</small>
                    <small *ngIf="hospitalDetailForm.controls.patientCode.errors?.pattern" class="text-danger">Characters
                      only</small>
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-6">
                    <small>In Patient ID</small>
                    <input id="hsp-url" formControlName="inPatientCode" type="text" class="form-control" minlength="3"
                      maxlength="6" autocomplete="off" />
                    <small *ngIf="hospitalDetailForm.controls.inPatientCode.errors?.minlength" class="text-danger">In
                      Patient Code must be at least 3 characters.&nbsp;</small>
                    <small *ngIf="hospitalDetailForm.controls.inPatientCode.errors?.maxlength" class="text-danger">In
                      Patient Code should be maximum of 6 characters.&nbsp;</small>
                    <small *ngIf="hospitalDetailForm.controls.inPatientCode.errors?.pattern"
                      class="text-danger">Characters only</small>
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-6">
                    <small>Out Patient ID</small>
                    <input type="text" id="hsp-reg-num" formControlName="outPatientCode" class="form-control"
                      minlength="3" maxlength="6" autocomplete="off" />
                    <small *ngIf="hospitalDetailForm.controls.outPatientCode.errors?.minlength" class="text-danger">Out
                      Patient Code must be at least 3 characters.&nbsp;</small>
                    <small *ngIf="hospitalDetailForm.controls.outPatientCode.errors?.maxlength" class="text-danger">Out
                      Patient Code should be maximum of 6 characters.&nbsp;</small>
                    <small *ngIf="hospitalDetailForm.controls.outPatientCode.errors?.pattern"
                      class="text-danger">Characters only</small>
                  </div>
                </div>
                <div class="col-md-12 col-sm-12 col-xs-12 mt-1 text-right">
                  <button id="save-clinic-btn" (click)="save()" class="save-btn basic-data-btn"
                    [disabled]="!hospitalDetailForm.valid" translate>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>