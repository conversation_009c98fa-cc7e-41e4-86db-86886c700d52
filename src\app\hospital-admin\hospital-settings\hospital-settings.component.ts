import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { HospitalService } from '../hospital-admin.service';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import { SharedService } from 'src/app/shared/shared.service';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-hospital-settings',
  templateUrl: './hospital-settings.component.html',
  styleUrls: ['./hospital-settings.component.css']
})
export class HospitalSettingsComponent implements OnInit {
  disabledHospitalForm = true;
  hospitalDetailForm: FormGroup;
  charFormat = /^([a-zA-Z]*)$/;
  hospitalId: string;

  constructor(private hospitalService: HospitalService, private notificationService: ToastrService,
    private location: Location, private sharedService: SharedService, private route: ActivatedRoute,) {
    this.hospitalDetailForm = new FormGroup({
      centerCode: new FormControl('', [Validators.minLength(3), Validators.maxLength(6), Validators.pattern(this.charFormat)]),
      patientCode: new FormControl('', [Validators.minLength(3), Validators.maxLength(6), Validators.pattern(this.charFormat)]),
      inPatientCode: new FormControl('', [Validators.minLength(3), Validators.maxLength(6), Validators.pattern(this.charFormat)]),
      outPatientCode: new FormControl('', [Validators.minLength(3), Validators.maxLength(6), Validators.pattern(this.charFormat)]),
    });
  }

  ngOnInit(): void {
    this.sharedService.setActiveLink('hospital-settings');
    this.route.params.subscribe(
      url => {
        this.hospitalId = url['uuid'];
        if (!this.hospitalId) {
          this.hospitalId = localStorage.getItem('hstId');
        }
        this.getHospitalSettings(this.hospitalId);
      });
  }

  getHospitalSettings(id) {
    this.hospitalService.getHospitalSettings(id).subscribe(
      (data: any) => {
        // console.log(data);
        this.hospitalDetailForm.controls['centerCode'].setValue(data.center_pattern);
        this.hospitalDetailForm.controls['patientCode'].setValue(data.patient_pattern);
        this.hospitalDetailForm.controls['inPatientCode'].setValue(data.inpatient_pattern);
        this.hospitalDetailForm.controls['outPatientCode'].setValue(data.outpatient_pattern);
      },
      (err) => {
        console.log(err);
        this.notificationService.error(err.error.error_message, 'Med.Bot');
      }
    );
  }

  save() {
    let cCode = this.hospitalDetailForm.get('centerCode').value.trim();
    let pCode = this.hospitalDetailForm.get('patientCode').value.trim();
    let ipCode = this.hospitalDetailForm.get('inPatientCode').value.trim();
    let opCode = this.hospitalDetailForm.get('outPatientCode').value.trim();

    if (this.hospitalDetailForm.untouched) {
      this.notificationService.error('There are no alterations in the input field.', 'Med.Bot');
      return;
    } else if (cCode === "" && pCode === "" && ipCode === "" && opCode === "") {
      this.notificationService.error('Complete any one setting field.', 'Med.Bot');
      return;
    }

    const formData = new FormData();
    formData.append('center_code', this.hospitalDetailForm.get('centerCode').value);
    formData.append('patient_code', this.hospitalDetailForm.get('patientCode').value);
    formData.append('inpatient_code', this.hospitalDetailForm.get('inPatientCode').value);
    formData.append('outpatient_code', this.hospitalDetailForm.get('outPatientCode').value);
    this.hospitalService.updateHospitalSettings(this.hospitalId, formData).subscribe(
      (data) => {
        this.notificationService.success('Settings Updated successfully', 'Med.Bot');
      },
      (error) => {
        console.log(error);
        this.notificationService.error('Updation Error', 'Med.Bot');
      }
    );
  }

  goBack() {
    this.location.back();
  }
}
