<div class="card">
    <form id="homeAddressForm" [formGroup]="homeAddressForm">
        <div formArrayName="homeAddressArray">
            <ng-container
                *ngFor="let data of this.homeAddressForm.controls.homeAddressArray.value; let i=index;trackBy:trackFn"
                [formGroupName]="i">
                <div class="card-body" id="form{{i}}">

                    <h4 class="card-title" translate *ngIf="i==0">Center Address
                        <a *ngIf="!!readHomeAddress" (click)="editHomeAddress()"><i class="fa fa-edit"></i></a>
                    </h4>
                    <div>
                        <div class="row form-row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label translate>Line 1<span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="line1{{i}}" formControlName="line_1"
                                        autocomplete="off" maxlength="50" [readonly]="readHomeAddress">
                                    <div *ngIf="frmControls('homeAddressArray', i).controls['line_1'].invalid && (frmControls('homeAddressArray', i).controls['line_1'].dirty || frmControls('homeAddressArray', i).controls['line_1'].touched)"
                                        class="alert alert-danger">{{specialCharacterError}}</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label translate>Line 2<span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="line2{{i}}" formControlName="line_2"
                                        autocomplete="off" maxlength="50" [readonly]="readHomeAddress">
                                    <div *ngIf="frmControls('homeAddressArray', i).controls['line_2'].invalid && (frmControls('homeAddressArray', i).controls['line_2'].dirty || frmControls('homeAddressArray', i).controls['line_2'].touched)"
                                        class="alert alert-danger">{{specialCharacterError}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>City/Town/Village<span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="city{{i}}"
                                        formControlName="city_town_village" autocomplete="off"
                                        [readonly]="readHomeAddress" maxlength="50" pattern="[a-zA-Z ]*">
                                    <div *ngIf="frmControls('homeAddressArray', i).controls['city_town_village'].invalid && (frmControls('homeAddressArray', i).controls['city_town_village'].dirty || frmControls('homeAddressArray', i).controls['city_town_village'].touched)"
                                        class="alert alert-danger">{{alphabetsError}}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>Taluk<span class="text-danger">*</span></label>
                                    <input type="text" id="taluk{{i}}" class="form-control" formControlName="taluk"
                                        autocomplete="off" [readonly]="readHomeAddress" maxlength="50"
                                        pattern="[a-zA-Z ]*">
                                    <div *ngIf="frmControls('homeAddressArray', i).controls['taluk'].invalid && (frmControls('homeAddressArray', i).controls['taluk'].dirty || frmControls('homeAddressArray', i).controls['taluk'].touched)"
                                        class="alert alert-danger">{{alphabetsError}}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>District<span class="text-danger">*</span></label>
                                    <input type="text" id="distric{{i}}" class="form-control" formControlName="district"
                                        autocomplete="off" [readonly]="readHomeAddress" maxlength="50"
                                        pattern="[a-zA-Z ]*">
                                    <div *ngIf="frmControls('homeAddressArray', i).controls['district'].invalid && (frmControls('homeAddressArray', i).controls['district'].dirty || frmControls('homeAddressArray', i).controls['district'].touched)"
                                        class="alert alert-danger">{{alphabetsError}}</div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>State<span class="text-danger">*</span></label>
                                    <input type="text" id="state{{i}}" class="form-control" formControlName="state"
                                        autocomplete="off" [readonly]="readHomeAddress" maxlength="50"
                                        pattern="[a-zA-Z ]*">
                                    <div *ngIf="frmControls('homeAddressArray', i).controls['state'].invalid && (frmControls('homeAddressArray', i).controls['state'].dirty || frmControls('homeAddressArray', i).controls['state'].touched)"
                                        class="alert alert-danger">{{alphabetsError}}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>Country <span class="text-danger">*</span></label>
                                    <ng-select id="country" formControlName="country" [items]="countryList"
                                        [clearable]="false" [searchable]="true" bindLabel="Name" bindValue="Name"
                                        placeholder="{{'Select Country' | translate}}" multiple
                                        [readonly]="readHomeAddress">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label translate>Postal Code<span class="text-danger">*</span> </label>
                                    <input type="text" id="postal_code{{i}}" class="form-control"
                                        formControlName="postal_code" autocomplete="off" maxlength="10"
                                        [readonly]="readHomeAddress">
                                    <div *ngIf="frmControls('homeAddressArray', i).controls['postal_code'].invalid && (frmControls('homeAddressArray', i).controls['postal_code'].dirty || frmControls('homeAddressArray', i).controls['postal_code'].touched)"
                                        class="alert alert-danger">{{numberError}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12 text-right" *ngIf="!readHomeAddress">
                            <!-- && i==doctorHomeAddressList.length - 1 -->
                            <button id="canc-hme-addr" class="btn btn-secondary cancel-btn"
                                (click)="cancelHomeAddress()" type="button" translate>Cancel</button>
                            <!-- [disabled]="homeAddressForm.dirty=== true?false  :homeAddressForm.valid=== true? false:true" -->
                            <button id="save-hme-addr" class="btn btn-primary" (click)="saveHomeAddress(i)"
                                translate>Save</button>
                            <!-- [disabled]="!homeAddressForm.valid" -->

                        </div>
                        <br>
                    </div>


                </div>
            </ng-container>
        </div>
    </form>
</div>