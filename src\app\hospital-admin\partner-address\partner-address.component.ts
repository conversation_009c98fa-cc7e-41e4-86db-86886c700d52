import { delay } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit, EventEmitter, Output, Input } from '@angular/core';
import { FormGroup, FormArray, Validators, FormControl, FormBuilder } from '@angular/forms';
import { DoctorService } from '../../doctor/doctor.service';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import * as Settings from '../../config/settings';
import { FixedSizeVirtualScrollStrategy } from '@angular/cdk/scrolling';
import { ActivatedRoute } from '@angular/router';
@Component({
  selector: 'app-partner-address',
  templateUrl: './partner-address.component.html',
  styleUrls: ['./partner-address.component.css'],
})
export class PartnerAddressComponent implements OnInit {
  @Input() new;
  @Output() profileCompletion: EventEmitter<string> = new EventEmitter<string>();
  public homeAddressForm: FormGroup;
  public homeAddressArray: FormArray;
  homeAddressReadOnly = false;
  homeAddressEdit = true;
  public countryList = [];
  public homeAddressData = {};
  public readHomeAddress = false;
  selecthomeAddressEdit = 'true';
  public selectedHomeAddressIndex: number;
  doctorHomeAddressList: any[];
  showCancelBtn = false;
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  numberError = Settings.numberError;
  public partner_uuid: any;
  public uuid = null;
  constructor(
    private httpClient: HttpClient,
    private doctorService: DoctorService,
    private translate: TranslateService,
    private notificationService: ToastrService,
    private route: ActivatedRoute,
    private formBuilder: FormBuilder
  ) {
    console.log(this.new);
  }

  ngOnInit(): void {
    this.route.params.subscribe(
      url => {
        console.log("url", url);
        this.partner_uuid = url['uuid'];
        console.log(this.partner_uuid);
      }
    );
    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this.homeAddressForm = this.formBuilder.group({
      homeAddressArray: this.formBuilder.array([])
    });
    //this.addHomeFormControl(null);
    this.getDoctorAddress();
    this.doctorService.getCountryDetail().subscribe(
      (data) => {
        this.countryList = Object.values(data);
      },
      (error) => {
        console.log(error);
      }
    );

  }
  addHomeFormControl(data) {
    this.homeAddressArray = this.homeAddressForm.get('homeAddressArray') as FormArray;
    if (data === null) {
      this.homeAddressArray.push(
        this.formBuilder.group({
          // practice_location:new FormControl (''),
          uuid: new FormControl(null),
          partner: new FormControl(this.partner_uuid),
          address_type: new FormControl('Home', [Validators.required, Validators.maxLength(50)]),
          line_1: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
          line_2: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
          city_town_village: new FormControl(
            null,
            [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')],
          ),
          district: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
          taluk: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
          state: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
          country: new FormControl('India', [Validators.required]),
          postal_code: new FormControl(null, [Validators.required, Validators.maxLength(10), Validators.pattern('[0-9 ]*')]),
        }));
    } else {
      console.log('address', data);
      this.uuid = data.uuid;
      this.readHomeAddress = true;
      this.homeAddressArray.push(
        this.formBuilder.group({
          uuid: new FormControl(this.uuid),
          partner: new FormControl(this.partner_uuid),
          // practice_location:new FormControl (''),
          address_type: new FormControl('Home', [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
          line_1: new FormControl(data.line_1, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
          line_2: new FormControl(data.line_2, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
          city_town_village: new FormControl(
            data.city_town_village,
            [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')],
          ),
          district: new FormControl(data.district, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
          taluk: new FormControl(data.taluk, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
          state: new FormControl(data.state, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
          country: new FormControl(data.country, [Validators.required]),
          postal_code: new FormControl(data.postal_code, [Validators.required, Validators.maxLength(10), Validators.pattern('[0-9]*')]),
        }));
    }
  }


  saveHomeAddress(i) {
    this.doctorService.partnersaveAddress(this.homeAddressForm.get('homeAddressArray').value[i], this.partner_uuid, this.uuid).subscribe(
      (data) => {

        if (this.homeAddressForm.get('homeAddressArray').value[i] != null) {
          this.notificationService.success('Center address updated');
        } else {
          this.notificationService.success('Center address added');
        }

        this.readHomeAddress = true;
        data = [data];
        const doctorAddress = Object.values(data);
        console.log('doctorAddress0,', doctorAddress);
        this.doctorHomeAddressList = doctorAddress.filter(
          (obj) => obj.address_type === 'Home'
        );
        this.profileCompletion.emit();
      },
      (err) => {
        this.notificationService.error(
          'Center Address Updation Failed',
          'Med.Bot'
        );
      }
    );
  }

  trackFn(index) {
    return index;
  }
  editHomeAddress(): void {
    this.readHomeAddress = false;
  }
  cancelHomeAddress(): void {
    // if( this.doctorHomeAddressList.length > 0){
    //   this.addHomeFormControl (this.doctorHomeAddressList[0]);
    // }else{
    //   this.addHomeFormControl(null)
    // }
    this.homeAddressArray.clear();
    this.getDoctorAddress()
    // if (this.doctorHomeAddressList.length >= 0) {          
    //   for(var i=0; i<this.doctorHomeAddressList.length ;i++ ){
    //     this.addHomeFormControl(this.doctorHomeAddressList[i]);
    //   }
    // }

  }

  getDoctorAddress() {
    this.doctorService.getpartnerAddressDetail(this.partner_uuid).subscribe(
      (data) => {
        console.log(data);
        data = data['results'];
        const doctorAddress = Object.values(data);
        // this.doctorHomeAddressList = doctorAddress.filter(
        //   (obj) => obj.address_type === 'Home'
        // );
        this.doctorHomeAddressList = doctorAddress;
        if (this.doctorHomeAddressList.length >= 0) {
          for (var i = 0; i < this.doctorHomeAddressList.length; i++) {
            this.addHomeFormControl(this.doctorHomeAddressList[i]);
          }
        }
      },
      (err) => {
        console.log('err', err);
      }
    );
  }

  frmControls(controlName: string, index: number) {
    let controlList = this.homeAddressForm.get(controlName) as FormArray;
    const formGroup = controlList.controls[index] as FormGroup;
    return formGroup;
  }
}
