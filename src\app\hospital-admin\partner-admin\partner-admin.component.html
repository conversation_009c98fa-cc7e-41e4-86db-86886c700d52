<!-- Page Content -->
<div class="content">
    <h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" (click)="back()"></i>Back</h5>
    <div class="container-fluid">

        <div class="row">
            <div class="col-md-8 offset-md-2">

                <!-- Register Content -->
                <div class="account-content">
                    <div class="row align-items-center justify-content-center">

                        <div class="col-md-12 col-lg-6 login-right">
                            <div class="login-header">
                                <h3>Center </h3>
                            </div>

                            <!-- Register Form -->
                            <form action="doctor-dashboard.html" [formGroup]="hospitalAdminForm">
                                <div class="form-group form-focus">
                                    <input type="text" class="form-control floating" id="username"
                                        formControlName="username">
                                    <label class="focus-label"> Name</label>
                                    <div class="text-danger"
                                        *ngIf="hospitalAdminForm.get('username').errors?.required && (hospitalAdminForm.get('username').dirty || hospitalAdminForm.get('username').touched)">
                                        Name is required.
                                    </div>
                                </div>
                                <div class="form-group form-focus">
                                    <input type="text" class="form-control floating" id="email" formControlName="email">
                                    <label class="focus-label"> Email</label>
                                    <div class="text-danger"
                                        *ngIf="hospitalAdminForm.get('email').errors?.required && (hospitalAdminForm.get('email').dirty || hospitalAdminForm.get('email').touched)">
                                        email is required.
                                    </div>
                                    <div class="text-danger" *ngIf="hospitalAdminForm.get('email').errors?.email">
                                        Please enter a valid email address.
                                    </div>
                                </div>
                                <div class="form-group form-focus">
                                    <input type="password" class="form-control floating" id="password"
                                        formControlName="password1">
                                    <label class="focus-label">Password</label>
                                    <div class="text-danger"
                                        *ngIf="hospitalAdminForm.get('password1').errors?.required && (hospitalAdminForm.get('password1').dirty || hospitalAdminForm.get('password1').touched)">
                                        Password is required.
                                    </div>
                                </div>
                                <div class="form-group form-focus">
                                    <input type="text" class="form-control floating" id="number" formControlName="phone"
                                        [max]="10" [min]="10">
                                    <label class="focus-label">Contact Number</label>
                                    <div class="text-danger"
                                        *ngIf="hospitalAdminForm.get('phone').errors?.required && (hospitalAdminForm.get('phone').dirty || hospitalAdminForm.get('phone').touched)">
                                        Contact Number is required.
                                    </div>
                                </div>
                                <!-- <div class="form-group form-focus">
                                                        <input type="password" class="form-control floating">
                                                        <label class="focus-label">User Id</label>
                                                    </div> -->
                                <div class="form-group">
                                    <label>Select Center Type</label>
                                    <select class="form-control input-field-border mb-2" formControlName="center_type">
                                        <option value="own center">Own center</option>
                                        <option value="franchise center">Franchise center</option>
                                    </select>
                                </div>
                                <div class="text-danger"
                                    *ngIf="hospitalAdminForm.get('center_type').errors?.required && (hospitalAdminForm.get('center_type').dirty || hospitalAdminForm.get('center_type').touched)">
                                    Center Type is required.
                                </div>
                                <button id="save-hospital-admin" class="btn btn-primary btn-block btn-lg login-btn"
                                    type="submit" [disabled]="!hospitalAdminForm.valid"
                                    (click)="saveHospitalAdmin()">{{uploadingData ?'Uploading':'Save'}}</button>
                            </form>
                            <!-- /Register Form -->

                        </div>
                    </div>
                </div>
                <!-- /Register Content -->

            </div>
        </div>

    </div>

</div>
<!-- /Page Content -->