import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { HospitalService } from '../hospital-admin.service';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
@Component({
  selector: 'app-partner-admin',
  templateUrl: './partner-admin.component.html',
  styleUrls: ['./partner-admin.component.css'],
})
export class PartnerAdminComponent implements OnInit {
  public hospitalAdminForm: FormGroup;
  hospitalId: string;
  uploadingData = false;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private hospitalService: HospitalService,
    private notificationService: ToastrService,
    private location: Location
  ) { }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe((parms) => {
      this.hospitalId = parms['id'];
    });
    this.addHospitalAdminFormControl();
  }

  addHospitalAdminFormControl() {
    this.hospitalAdminForm = new FormGroup({
      email: new FormControl('', [Validators.required, Validators.email]),
      username: new FormControl('', Validators.required),
      phone: new FormControl('', Validators.required),
      password1: new FormControl('', Validators.required),
      // user_type: new FormControl('Patient', Validators.required),
      user_type: new FormControl('Partner', Validators.required),
      center_type: new FormControl('own center', Validators.required),
    });
  }

  saveHospitalAdmin() {
    this.uploadingData = true;
    this.hospitalService
      .createHospitalAdmin(this.hospitalId, this.hospitalAdminForm.value)
      .subscribe(
        (data) => {
          this.notificationService.success(
            'Center Admin Added Successfully',
            'Med.Bot'
          );
          this.router.navigate(['/users']);
        },
        (error) => {
          this.uploadingData = false;
          const status = error['status'];
          if (status == 400) {
            if (error.error.error_details.validation_errors) {
              let messages = '';
              for (let i = 0; i < Object.keys(error.error.error_details.validation_errors).length; i++) {
                const key = Object.keys(error.error.error_details.validation_errors)[i];
                messages = messages + ' ' + key + ': ' + error.error.error_details.validation_errors[key];
              }

              this.notificationService.error(
                `${messages}`,
                'Med.Bot'
              );
            } else {
              this.notificationService.error(
                `${error.error['error_message']}`,
                'Med.Bot'
              );
            }
          } else {
            this.notificationService.error('Internal server error', 'Med.Bot');
            console.log(error);
          }
        }
      );
  }

  back() {
    this.location.back();
  }
}
