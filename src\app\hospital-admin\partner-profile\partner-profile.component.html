<!--BreadCrumbs-->
<div class="breadcrumb-bar">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-12 col-12">
                <nav aria-label="breadcrumb" class="page-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);">{{'Admin'|translate}}</a></li>
                        <li #listHeader class="breadcrumb-item active" aria-current="page">{{'Center Profile'
                            |translate}}</li>
                    </ol>
                </nav>
                <h2 #header class="breadcrumb-title">{{'Center Profile' |translate}}</h2>
            </div>
        </div>
    </div>
</div>
<!--BreadCrumbs Ends-->
<div class="content card">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 col-lg-12 col-xl-12">
                <h5 (click)="goBack()" class="mb-4 back-head ms"><i class="fas fa-chevron-circle-left"></i>Back</h5>
                <!--profile form-->
                <div class="card">
                    <div class="card-body">
                        <!-- <h4 class="card-title" translate>Profile Picture</h4> -->
                        <div class="row form-row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="change-avatar">
                                        <div class="profile-img">
                                            <img [src]="doctorProfilePictureUrl" alt="User Image">
                                        </div>
                                        <!-- <div class="upload-img">
                                            <div class="change-photo-btn">
                                                <span><i class="fa fa-upload"></i> {{ profileUpload ? ('Upload Photo'|translate): 'Uploading'|translate}}</span>
                                                <input type="file" class="upload" id="profile-picture" [disabled]="disabledUploadPhotoBtn" (change)="doctorProfilePictureChange($event)" accept=".jpg, .png,">
                                            </div>
                                            <small class="form-text text-muted" translate>Allowed JPG or PNG. Max size of 2MB</small>
                                        </div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h4 class="card-title" translate>Personal Profile <i *ngIf="disabled" (click)="editProfile()"
                                class="fa fa-edit"></i></h4>
                        <div class="form-group " *ngIf="formError">
                            <!-- <label style="color: orangered;" >Form Error</label> -->
                            <div class="card">
                                <ng-container *ngFor="let err of errorValue">
                                    <p class="text-danger">&nbsp;{{err.value}}</p>
                                </ng-container>
                            </div>
                        </div>
                        <form [formGroup]="personalProfileForm">
                            <div class="row form-row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Full Name<span class="text-danger">*</span></label>
                                        <input id="fullname" class="form-control" type="text" name="username"
                                            formControlName="username" maxlength="25" pattern="[a-zA-Z ]*" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Email <span class="text-danger">*</span></label>
                                        <input id="email" type="email" name="email" class="form-control"
                                            formControlName="email" maxlength="50" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>First Name</label>
                                        <input id="firstname" type="text" class="form-control" name="first_name"
                                            formControlName="first_name" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.first_name.invalid && (personalProfileForm.controls.first_name.dirty || personalProfileForm.controls.first_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Middle Name</label>
                                        <input id="middlename" type="text" class="form-control" name="middle_name"
                                            formControlName="middle_name" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.middle_name.invalid && (personalProfileForm.controls.middle_name.dirty || personalProfileForm.controls.middle_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Last Name</label>
                                        <input id="lastname" type="text" class="form-control" name="last_name"
                                            formControlName="last_name" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.last_name.invalid && (personalProfileForm.controls.last_name.dirty || personalProfileForm.controls.last_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Phone Number<span class="text-danger">*</span></label>
                                        <input id="phone" type="text" class="form-control" formControlName="phone"
                                            pattern="[0-9]*" name="phone" maxlength="15" readonly>
                                        <div *ngIf="personalProfileForm.controls.phone.invalid && (personalProfileForm.controls.phone.dirty || personalProfileForm.controls.phone.touched)"
                                            class="alert alert-danger">{{numberError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Gender<span class="text-danger">*</span></label>
                                        <select class="form-control" name="gender" id="gender" formControlName="gender">
                                            <option *ngFor="let data of gender" [value]="data.value ">{{data.name}}
                                            </option>

                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-0 ">
                                        <label translate>Date of Birth<span class="text-danger">*</span></label>
                                        <input [maxDate]="maxDate" [minDate]="minDate" placeholder="DOB"
                                            onkeydown="return false" class="form-control"
                                            formControlName="date_of_birth" [readonly]="disabled" bsDatepicker
                                            [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }"
                                            [ngClass]="{'bs-datepicker':disabled==true}">
                                        <input class="form-control" formControlName="date_of_birth"
                                            *ngIf="disabled==true" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group float-right">
                                <button *ngIf="!disabled" type="button" id="save-btn" id="per-prof-btn"
                                    class="btn btn-primary" translate (click)="onSubmit()"
                                    [disabled]="!personalProfileForm.valid">Save</button>
                                <button *ngIf="!disabled" type="button" id="cancel-btn" (click)="cancelUpdate()"
                                    class="btn btn-secondary cancel-btn" translate>Cancel</button>

                            </div>
                            <!-- /Basic Information -->

                        </form>

                    </div>
                </div>
                <!--profile form ends-->


                <div class="col-md-12 text-right mb-2">
                    <button data-toggle="modal" data-target="#completeModal" class="btn btn-primary">Add
                        Address</button>
                </div>

                <!-- doctor-home-address -->
                <app-partner-address></app-partner-address>
                <div class="modal" id="completeModal" tabindex="-1" role="dialog" data-keyboard="false"
                    data-backdrop="static">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="completeModalLabel">Add Center Address</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="card">
                                    <div class="card">
                                        <form id="homeAddressForm" [formGroup]="homeAddressForm">
                                            <div>
                                                <div class="card-body">
                                                    <h4 class="card-title" translate>Center Address</h4>
                                                    <div>
                                                        <div class="row form-row">
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label translate>Line 1<span
                                                                            class="text-danger">*</span></label>
                                                                    <input type="text" class="form-control" id="line1"
                                                                        formControlName="line_1" autocomplete="off"
                                                                        maxlength="50">
                                                                    <div *ngIf="homeAddressForm.controls.line_1.invalid && (homeAddressForm.controls.line_1.dirty || homeAddressForm.controls.line_1.touched)"
                                                                        class="alert alert-danger">
                                                                        {{specialCharacterError}}</div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label translate>Line 2<span
                                                                            class="text-danger">*</span></label>
                                                                    <input type="text" class="form-control" id="line2"
                                                                        formControlName="line_2" autocomplete="off"
                                                                        maxlength="50">
                                                                    <div *ngIf="homeAddressForm.controls.line_2.invalid && (homeAddressForm.controls.line_2.dirty || homeAddressForm.controls.line_2.touched)"
                                                                        class="alert alert-danger">
                                                                        {{specialCharacterError}}</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-4">
                                                                <div class="form-group">
                                                                    <label translate>City/Town/Village<span
                                                                            class="text-danger">*</span></label>
                                                                    <input type="text" class="form-control" id="city"
                                                                        formControlName="city_town_village"
                                                                        autocomplete="off" maxlength="50"
                                                                        pattern="[a-zA-Z ]*">
                                                                    <div *ngIf="homeAddressForm.controls.city_town_village.invalid && (homeAddressForm.controls.city_town_village.dirty || homeAddressForm.controls.city_town_village.touched)"
                                                                        class="alert alert-danger">{{alphabetsError}}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="form-group">
                                                                    <label translate>Taluk<span
                                                                            class="text-danger">*</span></label>
                                                                    <input type="text" id="taluk" class="form-control"
                                                                        formControlName="taluk" autocomplete="off"
                                                                        maxlength="50" pattern="[a-zA-Z ]*">
                                                                    <div *ngIf="homeAddressForm.controls.taluk.invalid && (homeAddressForm.controls.taluk.dirty || homeAddressForm.controls.taluk.touched)"
                                                                        class="alert alert-danger">{{alphabetsError}}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="form-group">
                                                                    <label translate>District<span
                                                                            class="text-danger">*</span></label>
                                                                    <input type="text" id="distric" class="form-control"
                                                                        formControlName="district" autocomplete="off"
                                                                        maxlength="50" pattern="[a-zA-Z ]*">
                                                                    <div *ngIf="homeAddressForm.controls.district.invalid && (homeAddressForm.controls.district.dirty || homeAddressForm.controls.district.touched)"
                                                                        class="alert alert-danger">{{alphabetsError}}
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-4">
                                                                <div class="form-group">
                                                                    <label translate>State<span
                                                                            class="text-danger">*</span></label>
                                                                    <input type="text" id="state" class="form-control"
                                                                        formControlName="state" autocomplete="off"
                                                                        maxlength="50" pattern="[a-zA-Z ]*">
                                                                    <div *ngIf="homeAddressForm.controls.state.invalid && (homeAddressForm.controls.state.dirty || homeAddressForm.controls.state.touched)"
                                                                        class="alert alert-danger">{{alphabetsError}}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="form-group">
                                                                    <label translate>Country <span
                                                                            class="text-danger">*</span></label>
                                                                    <ng-select id="country" formControlName="country"
                                                                        [items]="countryList" [clearable]="false"
                                                                        [searchable]="true" bindLabel="Name"
                                                                        bindValue="Name"
                                                                        placeholder="{{'Select Country' | translate}}"
                                                                        multiple>
                                                                    </ng-select>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="form-group">
                                                                    <label translate>Postal Code<span
                                                                            class="text-danger">*</span> </label>
                                                                    <input type="text" id="postal_code"
                                                                        class="form-control"
                                                                        formControlName="postal_code" autocomplete="off"
                                                                        maxlength="10">
                                                                    <div *ngIf="homeAddressForm.controls.postal_code.invalid && (homeAddressForm.controls.postal_code.dirty || homeAddressForm.controls.postal_code.touched)"
                                                                        class="alert alert-danger">{{numberError}}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12 col-sm-12 col-xs-12 text-right">
                                                            <button id="save-hme-addr" class="btn btn-primary"
                                                                [disabled]="!homeAddressForm.valid"
                                                                (click)="saveHomeAddress()" data-dismiss="modal"
                                                                translate>Save</button>
                                                        </div>
                                                        <br>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <!-- <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" (click)="refundSubmit()"
                                    [disabled]="refundDisableCheck()" data-dismiss="modal">Submit</button> -->
                            </div>
                        </div>
                    </div>
                </div>


                <!-- <div class="text-center" *ngIf="userType ==='HospitalAdmin'&& approvalRequestStatus !== 'Rejected'">
                    <button type="submit" id="approve" (click)="onApprove()" class="btn btn-primary" *ngIf="approvalRequestStatus !== 'Approved'">Approve</button>&nbsp;
                    <button type="submit" id="reject" (click)="open(content)" class="btn btn-danger" >Decline</button>
                </div> -->
                <!-- <ng-template #content let-modal>
                    <div class="modal-header">
                        <h4 class="modal-title" id="modal-basic-title">Are You Sure?</h4>
                    </div>
                    <div class="modal-body">
                        <form>
                            <label>Please provide reason for declining the approval request</label>
                            <div class="form-group">
                                <textarea class="form-control" name="decline-reason" [(ngModel)]="declineReason"></textarea>
                                <small class="text-muted">min 50 characters needed</small>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer text-center">
                        <button type="submit" (click)="onDecline()" [disabled]="declineReason.length <= 50" class="btn btn-primary">Confirm</button>&nbsp;
                        <button type="submit" (click)="modal.close('Ok click')" class="btn btn-secondary">Cancel</button>
                    </div>
                </ng-template> -->
            </div>
        </div>
    </div>
</div>