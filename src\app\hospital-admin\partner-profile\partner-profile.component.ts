import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PlatformService } from '../../platform/platform.service'
import { AuthService } from '../../auth/auth.service';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { Component, OnInit, ViewChild, Output, EventEmitter, ElementRef } from '@angular/core';
import { FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';
import * as moment from 'moment';
import { DoctorService } from 'src/app/doctor/doctor.service';
import * as Settings from '../../config/settings';
@Component({
  selector: 'app-partner-profile',
  templateUrl: './partner-profile.component.html',
  styleUrls: ['./partner-profile.component.css'],
})
export class PartnerProfileComponent implements OnInit {

  @ViewChild('profileFieldset') profileFieldset: ElementRef;
  @Output() messageEvent: EventEmitter<string> = new EventEmitter<string>();
  @ViewChild('qualSubmitButton') qualSubmitButton: ElementRef;
  public partner_data = {};
  public approvalStatus = '';
  approvalBtn = true;
  public signUpload = true;
  doctorSignatureUrl = 'null';
  public changed = false;
  public profileUpload = true;
  public disabledUploadPhotoBtn = false;
  public personalProfileForm: FormGroup;
  public user_data = {};
  public disabled = true;
  public doctorProfilePictureUrl: string;
  public cancelbtn = false;
  profileFileSizeLarge = false;
  public feesEditBtnShow = false;
  public cancleBtnShow = false;
  public feeForm: FormGroup;
  userID: any;
  // qualification
  public partner_uuid = '';
  qualificationFrom: FormGroup;
  public qualificationArray: FormArray;
  public qualificationForm: FormGroup;
  public qualificationFormSubmit = true;
  public qualificationFile: File;
  public create = false;
  public termsandC = false;
  public contentHtml = "";
  public contentText = "";
  public disableApprovalBtn = true;
  public checkTCValue: boolean;
  public declineReason = '';
  public userType: string;
  approvalRequestStatus: any;
  public maxDate: Date;
  public minDate: Date;
  public profileCompletion: number;
  errorValue: any = [];
  formError: boolean;
  gender = [
    { value: '', name: 'Select' },
    { value: 'Male', name: 'Male' },
    { value: 'Female', name: 'Female' },
    { value: 'Prefer not to answer', name: 'Prefer not to answer' },
  ];
  public userDetails: any = {
    username: null,
    email: null,
    phone: null,
    gender: null,
    first_name: null,
    middle_name: null,
    last_name: null,
    date_of_birth: null,
    uuid: null
  };
  system_of_medicine: any = [];
  public homeAddressForm: FormGroup;
  countryList: any[];
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  numberError = Settings.numberError;
  constructor(
    // private userService: AuthService,
    private platformService: PlatformService,
    private notificationService: ToastrService,
    private doctorService: DoctorService,
    private router: Router,

    private route: ActivatedRoute,
    public translate: TranslateService,
    // private sharedService: SharedService,
    private modalService: NgbModal,
    private location: Location,
    private userService: AuthService,
  ) { }

  ngOnInit(): void {
    this.doctorService.getCountryDetail().subscribe(
      (data) => {
        this.countryList = Object.values(data);
      },
      (error) => {
        console.log(error);
      }
    );
    this.homeAddressForm = new FormGroup({
      // practice_location:new FormControl (''),
      uuid: new FormControl(null),
      partner: new FormControl(this.partner_uuid),
      address_type: new FormControl('Home', [Validators.required, Validators.maxLength(50)]),
      line_1: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
      line_2: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
      city_town_village: new FormControl(
        null,
        [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')],
      ),
      district: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
      taluk: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
      state: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
      country: new FormControl('India', [Validators.required]),
      postal_code: new FormControl(null, [Validators.required, Validators.maxLength(10), Validators.pattern('[0-9 ]*')]),
    });
    this.maxDate = new Date();
    this.minDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate() - 7672);
    this.minDate.setDate(this.minDate.getDate() - 36500);
    this.userType = localStorage.getItem('user_type');
    this.addProfileFromControl(null);

    this.approvalStatus = localStorage.getItem('profile_approved_status');

    // setTimeout(()=>{
    this.profileCompletion = parseInt(
      localStorage.getItem('profile_completion')
    );
    if (this.profileCompletion < 100) {
      this.notificationService.error('Please fill all mandatory fields marked(*) and click "submit for approval"', '', { timeOut: 20000 });
    }
    if (
      this.approvalStatus == 'Approved' ||
      this.approvalStatus == 'Rejected'

    ) {
      this.approvalBtn = false;
    }
    this.route.params.subscribe(
      url => {
        console.log("url", url);
        this.partner_uuid = url['uuid'];
        console.log(this.partner_uuid);
        this.getuserData(this.partner_uuid);
      }
    );
    this.doctorProfilePictureUrl = '../../../../assets/img/doctors/doctor-thumb-02.png';
    const lang = localStorage.getItem('pageLanguage');
    console.log('profile settings', lang);
    this.translate.use(lang);
    // this.checkTandC(null);

    // this.addQualFormControl();
    this.contentHtml = localStorage.getItem('content_html');
    this.contentText = localStorage.getItem('content_text');
    this.userService.getHaDoctorSignature(this.partner_uuid).subscribe(
      data => {
        if (data['results'].length > 0) {
          const result = data['results'].pop();
          this.doctorSignatureUrl = result?.['file'];
        }

      }, error => {
        this.notificationService.error('internal server error', 'Med.Bot')
      }
    );
  }
  saveHomeAddress() {
    this.doctorService.partnersaveAddress(this.homeAddressForm.value, this.partner_uuid, null).subscribe(
      (data) => {
        this.notificationService.success('Center address added');
        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
          this.router.navigate([`partner-profile/${this.partner_uuid}`]);
        });
        // this.router.navigateByUrl(`partner-profile/${this.partner_uuid}`);
        // this.router.events.subscribe((val) => {
        //   const nvigationEnd = val instanceof NavigationEnd;
        //   if (!!nvigationEnd) {
        //     location.reload();
        //   }
        // });
      },
      (err) => {
        this.notificationService.error(
          'Center Address Updation Failed',
          'Med.Bot'
        );
      }
    );
  }
  getuserData(doctorasst_uuid) {
    this.platformService.getDoctorasDetails(this.partner_uuid).subscribe(
      data => {
        this.partner_data = data;
        this.user_data = data['user'];
        console.log(this.partner_data);
        this.addProfileFromControl(this.partner_data);
        this.approvalRequestStatus = data['approval_request_status'];
        console.log(this.user_data);
        this.profileCompletion = data['profile_completion_percentage'];
        if (this.user_data['profile_picture'] !== null) {
          this.doctorProfilePictureUrl = this.user_data['profile_picture'];
        }
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }



  onSubmit() {
    this.errorValue=[];
    const dob = this.personalProfileForm.controls[`date_of_birth`].value;
    this.personalProfileForm.controls['date_of_birth'].setValue(moment(dob).format('DD-MM-YYYY'));
    this.userDetails.username = this.personalProfileForm.controls[`username`].value;
    this.userDetails.email = this.personalProfileForm.controls[`email`].value;
    this.userDetails.phone = this.personalProfileForm.controls[`phone`].value;
    this.userDetails.gender = this.personalProfileForm.controls[`gender`].value;
    this.userDetails.first_name = this.personalProfileForm.controls[`first_name`].value;
    this.userDetails.last_name = this.personalProfileForm.controls[`last_name`].value;
    this.userDetails.middle_name = this.personalProfileForm.controls[`middle_name`].value;
    this.userDetails.date_of_birth = moment(dob, 'DD-MM-YYYY').format('YYYY-MM-DD');
    this.userDetails.uuid = this.partner_uuid;
    this.platformService.updateUserDetails(this.partner_uuid, this.userDetails).subscribe(
      (data) => {

        this.notificationService.success('Profile Updated', 'Med.Bot');
        this.disabled = true;
        this.user_data = data['user'];
        this.addProfileFromControl(this.user_data);

      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.formError = true
          const err = error['error']['error_details']['validation_errors'];
          if (err) {
            const gender = err['gender'];
            const dob = err['date_of_birth'];
            if (gender && dob) {
              ;
              const genderError = 'Gender : ' + gender[0];
              const dobError = 'DOB : ' + dob[0];
              this.notificationService.error(
                `${genderError} ${dobError}`,
                'Med.Bot'
              );
              this.errorValue.push({ value: genderError }, { value: dobError });
            } else if (gender) {
              const genderError = 'Gender : ' + gender[0];
              this.notificationService.error(`${genderError}`, 'Med.Bot');
              this.errorValue.push({ value: genderError });
            } else if (dob) {
              const dobError = 'DOB : ' + dob[0];
              this.notificationService.error(` ${dobError}`, 'Med.Bot');
              this.errorValue.push({ value: dobError });
            } else {
              this.notificationService.error('Updation Error', 'Med.Bot');
            }
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }


      }
    );

  }

  editProfile() {
    this.disabled = false;
    this.personalProfileForm.get('gender').enable();
  }
  cancelUpdate() {
    this.personalProfileForm.get('gender').disable();
    this.disabled = true;
    this.addProfileFromControl(this.user_data);
  }


  doctorProfilePictureChange(event) {
    const file = event.target.files;

    if (file.length > 0) {
      this.profileFileSizeLarge = false;
      const selectedProfilePicture = file[0];
      console.log(selectedProfilePicture);
      // if (
      //   selectedProfilePicture.size < 2000000 &&
      //   (selectedProfilePicture.type === 'image/jpeg' ||
      //     selectedProfilePicture.type === 'image/jpg' ||
      //     selectedProfilePicture.type === 'image/png')
      // ) {
      //   this.disabledUploadPhotoBtn = true;
      //   this.profileUpload = false;
      //   this.userService.updateDoctorProfilePicture(selectedProfilePicture).subscribe(
      //     (data) => {
      //       this.user_data = data;
      //       this.doctorProfilePictureUrl = this.user_data['profile_picture'];
      //       this.sendProfileToChildComponent();
      //       this.profileUpload = true;
      //       this.sharedService.setPicture(this.doctorProfilePictureUrl);
      //       this.disabledUploadPhotoBtn = false;
      //       this.notificationService.success(
      //         'Profile Picture Update',
      //         'Med.Bot'
      //       );
      //     },
      //     (error) => {
      //       this.profileUpload = true;
      //       this.disabledUploadPhotoBtn = false;
      //       console.log(error);
      //       this.notificationService.error(
      //         'Error In Updating profile picture',
      //         'Med.Bot'
      //       );
      //     }
      //   );
      // } else {
      //   this.profileFileSizeLarge = true;
      // }
    } else {
      this.profileUpload = true;
      this.disabledUploadPhotoBtn = false;
      // this.notificationService.showError(
      //   'Please select  profile picture',
      //   'Med.Bot'
      // );
    }
  }

  sendProfileToChildComponent() {
    this.messageEvent.emit(this.doctorProfilePictureUrl);
  }


  goBack() {
    this.location.back();
  }

  onApprove() {
    if (this.profileCompletion < 10) {
      this.notificationService.warning("The doctor profile is incomplete.");
    } else {
      // this.notificationService.success('profile approved');
      this.platformService.hospitalacceptApproval(this.partner_uuid).subscribe(
        data => {
          this.notificationService.success('Doctor Profile Approved', 'Med.Bot');
          this.goBack();
        },
        error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        });
    }
  }

  onDecline() {
    console.log(this.declineReason);
    const data = { "rejection_reason": this.declineReason };
    this.platformService.hospitaldeclineApproval(this.partner_uuid, data).subscribe(
      data => {

        this.notificationService.success('Doctor Profile Declined', 'Med.Bot');
        this.modalService.dismissAll();
        this.goBack();
      },
      error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  open(content) {
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
    }, (reason) => {
    });
  }
  addProfileFromControl(data) {
    if (data === null) {
      this.disabled = false;
      this.personalProfileForm = new FormGroup({
        username: new FormControl('', [
          Validators.required,
          Validators.maxLength(50),
        ]),
        email: new FormControl('', [Validators.required, Validators.email]),
        first_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        middle_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        last_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        phone: new FormControl('', [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl('', [
          Validators.required,
          Validators.maxLength(10),
        ]),
        date_of_birth: new FormControl('', [
          Validators.required,
          Validators.maxLength(20),
        ]),
      });
    } else {
      this.disabled = true;
      this.personalProfileForm.get('gender').disable();
      this.personalProfileForm = new FormGroup({
        username: new FormControl(data.username, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        email: new FormControl(data.email, [
          Validators.required,
          Validators.email,
        ]),
        first_name: new FormControl(data.first_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')
        ]),
        middle_name: new FormControl(data.middle_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')
        ]),
        last_name: new FormControl(data.last_name, [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        phone: new FormControl(data.phone, [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl(data.gender, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        date_of_birth: new FormControl(
          moment(data.date_of_birth).format('DD-MM-YYYY'),
          [Validators.required, Validators.maxLength(25)]
        ),
      });
      this.personalProfileForm.get('gender').disable();
    }
  }







  checkProfileCompletionStatus() {
    console.log(this.approvalStatus);
    if (this.approvalStatus == 'null' || this.approvalStatus == null) {
      this.doctorService.getDoctorProfile().subscribe((data) => {
        let profile_completion = data['profile_completion_percentage'];
        localStorage.setItem('profile_completion', profile_completion);
        const completed_sections =
          data['profile_completion_status']?.completed_sections;
        this.profileCompletion = parseInt(profile_completion);
        if (!this.termsandC) {
          if (parseInt(profile_completion) == 100 && this.checkTCValue) {
            this.disableApprovalBtn = false;
          } else {
            this.disableApprovalBtn = true;
          }
        }
      });
    } else if (this.approvalStatus == 'Approved') {
      this.doctorService.getDoctorProfile().subscribe((data) => {
        let profile_completion = data['profile_completion_percentage'];
        localStorage.setItem('profile_completion', profile_completion);
        this.profileCompletion = parseInt(profile_completion);
        this.approvalBtn = false;
      });
    }
  }

  showTermsAndCondition() {
    this.contentHtml = localStorage.getItem('content_html');
    this.contentText = localStorage.getItem('content_text');

  }


  getSystemOfMedicine(data) {

    this.system_of_medicine = data;
  }




}






