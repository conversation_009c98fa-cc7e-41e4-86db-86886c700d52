input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}
.fas{
  color: #20c0f3;
  cursor: pointer;

}


.modal-img {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 30%;
}
.flexContainer{
  display: flex;
}
body {
  font-family: 'Varela Round', sans-serif;
}

.modal-confirm {
  color: #636363;
  width: 400px;
  margin: 30px auto;
}

.modal-confirm .modal-content {
  padding: 20px;
  border-radius: 5px;
  border: none;
  text-align: center;
  font-size: 14px;
}

.modal-confirm .modal-header {
  border-bottom: none;
  position: relative;
}

.modal-confirm h4 {
  text-align: center;
  font-size: 26px;
  margin: 30px 0 -10px;
}

.modal-confirm .close {
  position: absolute;
  top: -5px;
  right: -2px;
}

.modal-confirm .modal-body {
  color: #999;
}

.modal-confirm .modal-footer {
  border: none;
  text-align: center;
  border-radius: 5px;
  font-size: 13px;
  padding: 10px 15px 25px;
  margin: 0px 34px 0px;
}

.modal-confirm .modal-footer a {
  color: #999;
}

.modal-confirm .icon-box {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  border-radius: 50%;
  z-index: 9;
  text-align: center;
  border: 3px solid #f15e5e;
}

.modal-confirm .icon-box i {
  color: #f15e5e;
  font-size: 46px;
  display: inline-block;
  margin-top: 13px;
}

.modal-confirm .btn {
  color: #fff;
  border-radius: 4px;
  background: #60c7c1;
  text-decoration: none;
  transition: all 0.4s;
  line-height: normal;
  min-width: 120px;
  border: none;
  min-height: 40px;
  border-radius: 3px;
  margin: 0 5px;
  outline: none !important;
}

.modal-confirm .btn-info {
  background: #c1c1c1;
}

.modal-confirm .btn-info:hover,
.modal-confirm .btn-info:focus {
  background: #a8a8a8;
}

.modal-confirm .btn-danger {
  background: #f15e5e;
}

.modal-confirm .btn-danger:hover,
.modal-confirm .btn-danger:focus {
  background: #ee3535;
}

.modal-dialogBox {
  overflow-y: initial !important;
  min-width: 1000px;
}
.flexContainer{
  display: flex;
  justify-content: flex-end;
}
.disabled-pagination{
  color: darkgray !important;
  pointer-events: none !important;
}
a.link:hover {
  cursor: pointer;
}
.float-right {
margin-left: 80px;
}
.float-left {
margin-right: 80px;
}
.card {
margin-bottom: 5px !important;
}

.overlay {
position: fixed;
top: 0;
bottom: 0;
left: 0;
right: 0;
background: rgba(0, 0, 0, 0.7);
transition: opacity 500ms;
visibility: visible;
opacity: 1;
}


.archivePopup {
margin: 70px auto;
padding: 20px;
background: #fff;
border-radius: 5px;
min-width: 25%;
max-width: fit-content;
position: relative;
transition: all 5s ease-in-out;
}

.archivePopup h2 {
margin-top: 0;
color: #333;
font-family: Tahoma, Arial, sans-serif;
}
.archivePopup .close {
position: absolute;
top: 20px;
right: 30px;
transition: all 200ms;
font-size: 30px;
font-weight: bold;
text-decoration: none;
color: #333;
}
.archivePopup .close:hover {
color: #d80606;
cursor: pointer;
}
.archivePopup .content {
max-height: 30%;
overflow: auto;
}
.input-field {
width: 30px; 
height: 30px; 
margin: 5px; 
text-align: center;
font-size: 16px; 
border: 1px solid #ccc; 
border-radius: 5px; 
}

.input-field:focus {
outline: none;
}

.input-field:focus  {
outline: none;
border-color: blue; 
}

.input-field[type="text"] {
max-length: 1;
}
.input-field.invalid {
border-color: red; 
}

.countdown {
font-size: 16px;
font-weight: bold;
color: rgb(47, 0, 255);
}

.resend-otp{
color: #0000ff !important;
cursor: pointer;
font-size: 0.75rem;
}

.page-size{
.form-control{
  min-height: 38px !important;
}
}
.form-group {
  margin-bottom: 1.5rem;
}