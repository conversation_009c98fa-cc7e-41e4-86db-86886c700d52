<!-- Page Content -->
<div class="content">
	<h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" (click)="back()"></i>Back</h5>
	<div class="container-fluid">

		<div class="row">
			<div class="col-md-8 offset-md-2">

				<!-- Register Content -->
				<div class="account-content">
					<div class="row align-items-center justify-content-center">

						<div class="col-md-12 col-lg-6 login-right">
							<div class="login-header">
								<h3>Add Patient </h3>
							</div>

							<!-- Register Form -->
							<form action="doctor-dashboard.html" [formGroup]="hospitalAdminForm">
								<div class="form-group form-focus" >
									<input type="text" class="form-control floating" id="username"
										formControlName="username" [readonly]="refPatient">
									<label class="focus-label" >Patient Name</label>
									<div class="text-danger"
										*ngIf="hospitalAdminForm.get('username').errors?.required && (hospitalAdminForm.get('username').dirty || hospitalAdminForm.get('username').touched)">
										Name is required.
									</div>
								</div>
								<div class="form-group form-focus">
									<input type="text" class="form-control floating" id="email" formControlName="email" [readonly]="refPatient">
									<label class="focus-label"> Email</label>
									<div class="text-danger"
										*ngIf="hospitalAdminForm.get('email').errors?.required && (hospitalAdminForm.get('email').dirty || hospitalAdminForm.get('email').touched)">
										email is required.
									</div>
									<div class="text-danger" *ngIf="hospitalAdminForm.get('email').errors?.email">
										Please enter a valid email address.
									</div>
								</div>
								<div class="form-group form-focus"  *ngIf="!refPatient">
									<input type="password" class="form-control floating" id="password"
										formControlName="password1">
									<label class="focus-label" >Password</label>
									<div class="text-danger"
										*ngIf="hospitalAdminForm.get('password1').errors?.required && (hospitalAdminForm.get('password1').dirty || hospitalAdminForm.get('password1').touched)">
										Password is required.
									</div>
								</div>
								<div class="form-group form-focus" >
									<input type="text" class="form-control floating" id="number" formControlName="phone"
										[max]="10" [min]="10" [readonly]="refPatient">
									<label class="focus-label" >Contact Number</label>
									<div class="text-danger"
                                        *ngIf="hospitalAdminForm.get('phone').errors?.required && (hospitalAdminForm.get('phone').dirty || hospitalAdminForm.get('phone').touched)">
                                        Contact Number is required.
                                    </div>
								</div>
								<!-- <div class="form-group form-focus">
														<input type="password" class="form-control floating">
														<label class="focus-label">User Id</label>
													</div> -->

								<button id="save-hospital-admin" class="btn btn-primary btn-block btn-lg login-btn"
									type="submit" [disabled]="!hospitalAdminForm.valid"
									(click)="saveHospitalAdmin()">{{uploadingData ?'Uploading':'Save'}}</button>


							</form>
							<!-- /Register Form -->

						</div>
					</div>
				</div>
				<!-- /Register Content -->

			</div>
		</div>

	</div>
	<!-- add patient confirmation modal starts-->
	<div class="modal" id="addConfirmModal">
		<div class="modal-dialog modal-confirm">
			<div class="modal-content">
				<div class="modal-header">

					<h5 class="modal-title model-header-alinement">Add Patient</h5>
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						&times;
					</button>
				</div>
				<div class="modal-body">
					<p>This patient is already Registered in another hospital. Do you want to add this patient?</p>
				</div>
				<div class="modal-body">
					<p>The OTP will be sent to the patient's phone number that is already registered in the previous hospital.
						<br>"The patient's phone number and password will remain the same as recorded during his/her registration." </p>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-info" data-dismiss="modal" id="cancel-stop">
						No
					</button>
					<button type="button" class="btn btn-danger" id="proceed-cancel" (click)="createOTP()">
						Confirm
					</button>
				</div>
			</div>
		</div>
	</div>
	<!-- add patient confirmation modal ends -->

	<!-- otp confirmation modal starts-->
	<div class="modal" id="otpConfirmModal">
		<div class="modal-dialog modal-confirm">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title model-header-alinement">OTP Confirmation</h5>
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						&times;
					</button>
				</div>
				<div class="modal-body">
					<div [formGroup]="otpForm" class="mb-3">
						<input #otp1 id="otp1" formControlName="otp1" (input)="pass(otp1,otp2)" class="input-field"
							maxlength="1">
						<input #otp2 id="otp2" formControlName="otp2" (input)="pass(otp2,otp3)" class="input-field"
							maxlength="1">
						<input #otp3 id="otp3" formControlName="otp3" (input)="pass(otp2,otp4)" class="input-field"
							maxlength="1">
						<input #otp4 id="otp4" formControlName="otp4" class="input-field" maxlength="1">
						<label class="countdown ml-2" *ngIf="!resendOtp">{{seconds}}</label> 
						 <a class="ml-2 resend-otp" *ngIf="resendOtp" (click)="createOTP()"><u>Resend OTP</u></a>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-info" data-dismiss="modal" id="cancel-stop">
						No
					</button>
					<button type="button" class="btn btn-primary" id="proceed-cancel" (click)="otpVerification()">
						Verify
					</button>
				</div>
			</div>
		</div>
	</div>
	<!-- otp confirmation modal ends -->
</div>
<!-- /Page Content -->