import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl, Validators, FormBuilder } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { HospitalService } from '../hospital-admin.service';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import { PlatformService } from 'src/app/platform/platform.service';
import { param } from 'jquery';
declare var $: any;

@Component({
  selector: 'app-user-admin',
  templateUrl: './user-admin.component.html',
  styleUrls: ['./user-admin.component.css'],
})
export class UserAdminComponent implements OnInit {
  public hospitalAdminForm: FormGroup;
  hospitalId: string;
  uploadingData = false;
  otpForm: FormGroup;
  resendOtp: boolean;
  seconds = 90;
  otpValue: string = '';
  user_type: string;
  isMerge: boolean = false;
  patientId: string = '';
  refPatient: boolean= false;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private hospitalService: HospitalService,
    private platformService: PlatformService,
    private notificationService: ToastrService,
    private location: Location,
    private fb: FormBuilder
  ) {
    this.otpForm = this.fb.group({
      otp1: new FormControl('', Validators.required),
      otp2: new FormControl('', Validators.required),
      otp3: new FormControl('', Validators.required),
      otp4: new FormControl('', Validators.required),
    });
  }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe((parms) => {
      this.hospitalId = parms['id'];
    });
    this.addHospitalAdminFormControl();
    const patient =JSON.parse(localStorage.getItem("user")) ;
    if (patient != '' && patient !=undefined) {
      const email = localStorage.getItem("patientEmailId");
      this.hospitalAdminForm.get("username")?.setValue(patient.name);
      this.hospitalAdminForm.get("email")?.setValue(patient.email);
      this.hospitalAdminForm.get("phone")?.setValue(patient.ph_no);
      this.hospitalAdminForm.get("password1")?.setValue('*******');
      this.refPatient=true;
    }
    localStorage.removeItem("user");
  }

  addHospitalAdminFormControl() {
    this.hospitalAdminForm = new FormGroup({
      email: new FormControl('', [Validators.required, Validators.email]),
      username: new FormControl('', Validators.required),
      phone: new FormControl('', Validators.required),
      password1: new FormControl('', Validators.required),
      user_type: new FormControl('Patient', Validators.required),
    });
  }

  saveHospitalAdmin() {
    this.user_type = localStorage.getItem('user_type');
    this.uploadingData = true;
    const formData = new FormData();
    formData.append('email', this.hospitalAdminForm.value.email);
    formData.append('username', this.hospitalAdminForm.value.username);
    formData.append('phone', this.hospitalAdminForm.value.phone);
    formData.append('password1', this.hospitalAdminForm.value.password1);
    formData.append('user_type', this.hospitalAdminForm.value.user_type);
    if (this.isMerge) {
      formData.append('is_merged', `${this.isMerge}`);
    }
    this.hospitalService
      .createHospitalAdmin(this.hospitalId, formData)
      .subscribe(
        (data: any) => {
          this.notificationService.success(
            'Patient Added Successfully',
            'Med.Bot'
          );

          if (this.user_type == 'HospitalAdmin') {
            this.router.navigate(['/users']);
          } else if (this.user_type == 'DoctorAssistant') {
            this.router.navigate(['/addpatient']);
          } else if (this.user_type == 'Partner') {
            this.router.navigate(['/add-asst-pat']);
          }
        },
        (error) => {
          this.uploadingData = false;
          const status = error['status'];
          let validationError = (error.error.error_details ? error.error.error_details.validation_errors : undefined);

          if (status == 400) {

            if (validationError && validationError != undefined) {
              let messages = '';
              for (let i = 0; i < Object.keys(error.error.error_details.validation_errors).length; i++) {
                const key = Object.keys(error.error.error_details.validation_errors)[i];
                messages = messages + ' ' + key + ': ' + error.error.error_details.validation_errors[key];
              }

              this.notificationService.error(
                `${messages}`,
                'Med.Bot'
              );
            }
            else {
              this.notificationService.error(
                `${error.error['error_message']}`,
                'Med.Bot'
              );
            }
          }
          else if (status == '409') {
            $('#addConfirmModal').modal('show');
            this.notificationService.error(`${error.error['error_message']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error('Internal server error', 'Med.Bot');
            console.log(error);
          }
        }
      );
  }

  back() {
    this.location.back();
  }
  createOTP() {
    $('#addConfirmModal').modal('hide');
    const formData = new FormData();
    formData.append('email', this.hospitalAdminForm.value.email);
    this.hospitalService.createOTP(formData).subscribe(
      data => {
        // this.notificationService.success(`${data['message']}`, 'Med.Bot');
        this.notificationService.success('OTP Sent to your Phone Number', 'Med.Bot');
        $('#otpConfirmModal').modal('show');
        this.countdown();
      },
      error => {
        console.log(error);
        this.notificationService.error(error.error.error, 'Med.Bot');
      }
    );
  }
  otpVerification() {
    this.isMerge = true;
    const formData = new FormData();
    this.otpValue = this.otpForm.value.otp1 + this.otpForm.value.otp2 + this.otpForm.value.otp3 + this.otpForm.value.otp4;
    formData.append('type', 'Phone');
    formData.append('value', this.otpValue);
    formData.append('email', this.hospitalAdminForm.value.email);
    formData.append('email_or_phone_value', this.hospitalAdminForm.value.email);

    this.hospitalService.otpVerification(formData).subscribe(
      data => {
        this.saveHospitalAdmin();
        // this.notificationService.success(`${data['message']}`, 'Med.Bot');
        this.notificationService.success('OTP verified successfully', 'Med.Bot');
        $('#otpConfirmModal').modal('hide');
        if (this.user_type == 'HospitalAdmin') {
          this.router.navigate(['/users']);
        } else if (this.user_type == 'DoctorAssistant') {
          this.router.navigate(['/addpatient']);
        } else if (this.user_type == 'Partner') {
          this.router.navigate(['/add-asst-pat']);
        }
        this.isMerge = false;
      },
      error => {
        console.log(error);
        this.notificationService.error(error.error.error_message, 'Med.Bot');
        $('#otpConfirmModal').modal('show');
        this.isMerge = false;
      }
    );

  }
  countdown() {
    this.resendOtp = false;
    const countdownInterval = setInterval(() => {
      if (this.seconds >= 1) {
        this.seconds--;
      } else {
        clearInterval(countdownInterval);
        this.resendOtp = true;
        this.seconds = 90;
      }
    }, 1000);
  }
  pass(c: any, n: any) {
    var length = c.value.length;
    var maxlength = c.getAttribute("maxlength");
    if (length == maxlength && n != "") {
      n.focus();
    }
  }
}
