.dashboard-font-size {
    font-size: 1.5rem;
    font-weight: 500;
}

a.link:hover {
    cursor: pointer;
}

.modalSize {
    max-width: 1200px;
    max-height: 700px;
}

.btn-primary {
    border-radius: 5px;
}

#no-data {
    pointer-events: none;
}

.banner {
    background-color: #13A89E;
}

.mt-2 {
    color: #fff;
}

.add-hsp {
    background-color: #20c0f3 !important;
    margin-top: 20px;
    margin-left: 85px;
}

h5.back-head,
.fa-edit {
    color: #20c0f3;
    cursor: pointer;
}

.text-success {
    color: #20c0f3 !important;
}

.hsptl-form {
    margin: 25px;
}

.hsp-details {
    margin-bottom: 30px;
}

.hsp-details.row label {
    margin-bottom: 0px;
}

label {
    margin-bottom: 0px;
}

input {
    margin-bottom: 10px;
}

.hsp-btn {
    float: right;
    margin: 5px;
    margin-top: 10px;
}

.cnt-btn {
    margin: 3px;
    margin-top: 25px;
}

.desc {
    height: 100px;
    resize: none;
}

.mt-4 {
    margin-left: 20px;
}

.pt-3 {
    margin-top: 0px;
}

i {
    font-size: 23px;
    margin-bottom: 2px;
}

.btn {
    margin: 5px;
}

.disabled-pagination {
    color: darkgray !important;
    pointer-events: none !important;
}

ul li a span .active-tab {
    color: #EF548B !important;
    font-size: 18px;
}

.pagination-zindex {
    position: relative;
    z-index: 10;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}
.fas{
  color: #20c0f3;
  cursor: pointer;

}
.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  transition: opacity 500ms;
  visibility: visible;
  opacity: 1;
}


.deletePopup {
  margin: 70px auto;
  padding: 20px;
  background: #fff;
  border-radius: 5px;
  width: 35%;
  max-height: 55%;
  position: relative;
  transition: all 5s ease-in-out;
}

.deletePopup h2 {
  margin-top: 0;
  color: #333;
  font-family: Tahoma, Arial, sans-serif;
}
.deletePopup .close {
  position: absolute;
  top: 20px;
  right: 30px;
  transition: all 200ms;
  font-size: 30px;
  font-weight: bold;
  text-decoration: none;
  color: #333;
}
.deletePopup .close:hover {
  color: #d80606;
  cursor: pointer;
}
.deletePopup .content {
  max-height: 30%;
  overflow: auto;
}
.deletePopup-button-container {
    position: relative;
  }
  
.deletePopup-button {
    position: absolute;
    bottom: 0;
    right: 0;
  }