<div class="">
  <div class="breadcrumb-bar banner">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col-md-12 col-12">
          <nav aria-label="breadcrumb" class="page-breadcrumb">
            <ol class="breadcrumb">
              <li class="breadcrumb-item"><a href="javascript:void(0);">{{'Hospital Admin'|translate}}</a></li>
              <li #listHeader class="breadcrumb-item active" aria-current="page">{{'Dashboard' |translate}}</li>
            </ol>
          </nav>
          <h2 #header class="breadcrumb-title">{{'Hospital Admin DashBoard' |translate}}</h2>
        </div>
      </div>
    </div>
  </div>
  <div class="card mx-5 my-3">
    <div class="card-body">
      <div class="col-md-12">
        <button class="btn btn-info mx-2" id="add-doctor" (click)="addDoctor()">
          Add Doctor
        </button>
        <button class="btn btn-info mx-2 btn-height" id="partner-admin" (click)="addPartner()">Add Center</button>
        <button class="btn btn-info mx-2 btn-height" id="add-patient" (click)="addAdmin()">Add Patient</button>
        <button class="btn btn-info mx-2 btn-height" id="doctor-asssistant" (click)="createDoctorAssistant()">
          Add Doctor Assistant
        </button>
        <button class="btn btn-info mx-2 btn-height" id="ha-department" (click)="haDepartment()">
          Add Department
        </button>
        <button class="btn btn-info mx-2 btn-height" id="ha-specality" (click)="haSpeciality()">
          Add Speciality
        </button>

      </div>
    </div>
  </div>
  <div class="card mx-5">
    <div class="tab-content pt-0">

      <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded my-2 mx-1">
        <li class="nav-item">
          <a class="nav-link active" href="#doctor-list" data-toggle="tab">Doctor List
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#doctor-assistant" data-toggle="tab">Doctor Assistant List
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#patient-list" data-toggle="tab">Patient List
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#partner-list" data-toggle="tab">Center List</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#department-list" data-toggle="tab">Department List
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#speciality-list" data-toggle="tab">Speciality List
          </a>
        </li>
      </ul>

      <div class="tab-content">
        <!-- doctor list start -->
        <div class="tab-pane show active" id="doctor-list">
          <div *ngIf="doctorCount>0" class="container-fluid">
            <input type="text" id="name" style="cursor:pointer" class="form-control form-control-height col-md-3"
              [(ngModel)]="searchDoctorName" placeholder="Name">

            <div class="text-right col-md-3">
              <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-0" (click)="searchDoctor()">Search
                Doctors</button>
            </div>
          </div>
          <!-- <form id="searchForm" [formGroup]="searchForm">
          <div class="row">
              <div class="col-md-3 col-lg-3 col-xl-3">
                  <div class="form-group mr-1">
                      <input type="text" id="name" style="cursor:pointer" class="form-control form-control-height"
                          formControlName="name" placeholder="Name">
                  </div>
              </div>
          </div>
        </form>  -->
          <!-- <div class="col-md-9 col-lg-9 col-xl-9 text-right">
            <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-0" [disabled]="searchBtnDisabled"
              (click)="searchDoctor()">Search Doctors</button>


          </div> -->
          <div>
            <div *ngIf="!doctorisLoading">
              <div class="col-md-12 float-right pagination-zindex">
                <div class="float-right mt-3">
                  <nav aria-label="Page navigation example" *ngIf="this.doctorTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="firstDoctorPageList()"
                        [ngClass]="{ 'disabled-pagination': doctorCurrentPage === 1 }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="previousDoctorPageList()"
                        [ngClass]="{ 'disabled-pagination': doctorCurrentPage === 1 }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ doctorCurrentPage }}&nbsp;of&nbsp; {{ doctorTotalPage }}</a>
                      </li>
                      <li class="page-item" (click)="nextDoctorPageList()"
                        [ngClass]="{'disabled-pagination': doctorurrentPage === doctorTotalPage}">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="lastDoctorPageList()"
                        [ngClass]="{'disabled-pagination': doctorCurrentPage === doctorTotalPage}">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
              <div class="col-md-10">
                <h4 class="text-success">Doctors List</h4>
                <h4>Total Doctors - {{ doctorCount }}</h4>
                <div class="card card-table mb-0">
                  <div class="card-body">
                    <div class="table-responsive" *ngIf="doctorList.length > 0">
                      <table class="table table-hover table-center mb-0">
                        <thead>
                          <tr>
                            <th>#</th>
                            <th>Doctor Name</th>
                            <th>Speciality</th>
                            <th>System of Medicine</th>
                            <th>Schedule</th>
                            <th>Action</th>
                            <th>Report</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let data of doctorList; let i = index">
                            <th scope="row"><a>{{ doctorSerialNumber + i + 1 }}</a></th>
                            <td><a class="text-primary link" (click)="viewDetails(data.uuid)">{{ data.username }}</a></td>
                            <td>
                              <ng-container *ngFor="let speciality of data.speciality;">
                                {{speciality.value}}
                              </ng-container>
                            </td>
                            <td>{{data.som}}</td>
                            <td><button class="btn-primary btn-md mr-2"
                                (click)="addSchedule(data.uuid)">Schedule</button>
                            </td>
                            <td>
                              <button class="btn-primary btn-md mr-2" (click)="viewDetails(data.uuid)">View</button>
                            </td>
                            <td>
                              <button class="btn-primary btn-md mr-2"
                                (click)="onConsultationSummary(data.uuid,'doctor')">
                                Report
                              </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="centered" *ngIf="doctorisLoading">
          <app-loading-spinner></app-loading-spinner>
          </div> -->
            </div>
          </div>
        </div>


        <!-- depatmentList -->
        <div class="tab-pane" id="department-list">
          <div>
            <div *ngIf="!deptisLoading">
              <div class="col-md-12 float-right pagination-zindex">
                <div class="float-right mt-3">
                  <nav aria-label="Page navigation example" *ngIf="this.deptTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="firstDeptPageList()"
                        [ngClass]="{ 'disabled-pagination': deptCurrentPage === 1 }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="previousDeptPageList()"
                        [ngClass]="{ 'disabled-pagination': deptCurrentPage === 1 }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ deptCurrentPage }}&nbsp;of&nbsp; {{ deptTotalPage }}</a>
                      </li>
                      <li class="page-item" (click)="nextDeptPageList()" [ngClass]="{
                  'disabled-pagination': deptCurrentPage === deptTotalPage
                }">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="lastDeptPageList()" [ngClass]="{
                  'disabled-pagination': deptCurrentPage === deptTotalPage
                }">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
              <div class="col-md-10">
                <h4 class="text-success">Department List</h4>
                <h4>Total Departments - {{ deptCount }}</h4>
                <!-- <h2 >test</h2> -->
                <div class="card card-table mb-0">
                  <div class="card-body">
                    <div class="table-responsive" *ngIf="deptList.length > 0">
                      <table class="table table-hover table-center mb-0">
                        <thead>
                          <tr>
                            <th>#</th>
                            <th> Department Name</th>
                            <th>Acronym</th>
                            <th>Action</th>
                            <th>Report</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let data of deptList; let i = index">
                            <th scope="row"><a>{{ deptSerialNumber + i + 1 }}</a></th>
                            <!--<td><a class="text-primary link">{{ data.med_patient_id }}</a></td>-->
                            <td><a class="text-primary ">{{ data.department_value }}</a></td>
                            <td><a class="text-primary ">{{ data.department_code }}</a></td>
                            <td>
                              <button class="btn-danger btn-md mr-2" (click)="deleteDepartment(1,data)">Delete</button>
                            </td>
                            <td>
                              <button class="btn-primary btn-md mr-2"
                                (click)="onConsultationSummary(data.department,'department')">
                                Report
                              </button>
                            </td>
                            <!-- <td><a class="text-primary link">{{ data.gender }}</a></td>
            <td><a class="text-primary link">{{ data.date_of_birth}}</a></td> -->
                            <!-- <td>
              <button
                class="btn-primary btn-md mr-2" (click)="viewDetails(data.uuid)">View</button>
            </td> -->
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="centered" *ngIf="doctorisLoading">
            <app-loading-spinner></app-loading-spinner>
            </div> -->
            </div>
          </div>
        </div>

        <!-- doctor Assistant -->
        <div class="tab-pane" id="doctor-assistant">
          <div *ngIf="doctorasCount>0" class="container-fluid">
            <input type="text" id="name" style="cursor:pointer" class="form-control form-control-height col-md-3"
              [(ngModel)]="searchDoctorasName" placeholder="Name">

            <div class="text-right col-md-3">
              <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-0" (click)="searchDoctoras()">Search
                Doctor Assistant</button>
            </div>
          </div>
          <div>
            <div *ngIf="!doctorasLoading">
              <div class="col-md-12 float-right pagination-zindex">
                <div class="float-right mt-3">
                  <nav aria-label="Page navigation example" *ngIf="this.doctorasTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="firstDoctorasPageList()"
                        [ngClass]="{ 'disabled-pagination': doctorasCurrentPage === 1 }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="previousDoctorasPageList()"
                        [ngClass]="{ 'disabled-pagination': doctorasCurrentPage === 1 }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ doctorasCurrentPage }}&nbsp;of&nbsp; {{ doctorasTotalPage
                          }}</a>
                      </li>
                      <li class="page-item" (click)="nextDoctorasPageList()" [ngClass]="{
                  'disabled-pagination': doctorasCurrentPage === doctorasTotalPage
                }">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="lastDoctorasPageList()" [ngClass]="{
                  'disabled-pagination': doctorasCurrentPage === doctorasTotalPage
                }">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
              <div class="col-md-10">
                <h4 class="text-success">Doctor Assistant List</h4>
                <h4>Total Assistants - {{ doctorasCount }}</h4>
                <!-- <h2 >test</h2> -->
                <div class="card card-table mb-0">
                  <div class="card-body">
                    <div class="table-responsive" *ngIf="doctorasList.length > 0">
                      <table class="table table-hover table-center mb-0">
                        <thead>
                          <tr>
                            <th>#</th>
                            <th>Assistant Name</th>
                            <th>Hospital Name</th>
                            <th>Action</th>
                            <th>Report</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let data of doctorasList; let i = index">
                            <th scope="row"><a>{{ doctorasSerialNumber + i + 1 }}</a></th>
                            <td><a class="text-primary link" (click)="viewDetailsas(data.uuid)">{{ data.username }}</a></td>
                            <td><a class="text-primary ">{{ data.hospital_name }}</a></td>
                            <td>
                              <button class="btn-primary btn-md mr-2" (click)="viewDetailsas(data.uuid)">View</button>
                            </td>
                            <td>
                              <button class="btn-primary btn-md mr-2"
                                (click)="onConsultationSummary(data.uuid,'book_user')">
                                Report
                              </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="centered" *ngIf="doctorisLoading">
            <app-loading-spinner></app-loading-spinner>
            </div> -->
            </div>
          </div>
        </div>

        <!-- specialityList -->
        <div class="tab-pane" id="speciality-list">
          <div>
            <div *ngIf="!specialityisLoading">
              <div class="col-md-12 float-right pagination-zindex">
                <div class="float-right mt-3">
                  <nav aria-label="Page navigation example" *ngIf="this.specialityTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="firstSpecialityPageList()"
                        [ngClass]="{ 'disabled-pagination': specialityCurrentPage === 1 }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="previousSpecialityPageList()"
                        [ngClass]="{ 'disabled-pagination': specialityCurrentPage === 1 }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ specialityCurrentPage }}&nbsp;of&nbsp; {{ specialityTotalPage
                          }}</a>
                      </li>
                      <li class="page-item" (click)="nextSpecialityPageList()" [ngClass]="{
                  'disabled-pagination': specialityCurrentPage === specialityTotalPage
                }">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="lastSpecialityPageList()" [ngClass]="{
                  'disabled-pagination': specialityCurrentPage === specialityTotalPage
                }">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
              <div class="col-md-10">
                <h4 class="text-success">Speciality List</h4>
                <h4>Total Speciality - {{ specialityCount }}</h4>
                <!-- <h2 >test</h2> -->
                <div class="card card-table mb-0">
                  <div class="card-body">
                    <div class="table-responsive" *ngIf="specialityList.length > 0">
                      <table class="table table-hover table-center mb-0">
                        <thead>
                          <tr>
                            <th>#</th>
                            <th>Speciality Name</th>
                            <th>Speciality Code</th>
                            <th>Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let data of specialityList; let i = index">
                            <th scope="row"><a>{{ specialitySerialNumber + i + 1 }}</a></th>
                            <!--<td><a class="text-primary link">{{ data.med_patient_id }}</a></td>-->
                            <td><a class="text-primary">{{ data.spec_name }}</a></td>
                            <td><a class="text-primary">{{ data.spec_code }}</a></td>
                            <td>
                              <button class="btn-danger btn-md mr-2" (click)="deleteSpeciality(data)">Delete</button>
                            </td>

                            <!-- <td><a class="text-primary link">{{ data.gender }}</a></td>
            <td><a class="text-primary link">{{ data.date_of_birth}}</a></td> -->
                            <!-- <td>
              <button
                class="btn-primary btn-md mr-2" (click)="viewDetails(data.uuid)">View</button>
            </td> -->
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="centered" *ngIf="doctorisLoading">
            <app-loading-spinner></app-loading-spinner>
            </div> -->
            </div>
          </div>
        </div>

        <!-- Partner List -->
        <div class="tab-pane" id="partner-list">
          <div *ngIf="partnerCount>0" class="container-fluid">
            <input type="text" id="name" style="cursor:pointer" class="form-control form-control-height col-md-3"
              [(ngModel)]="searchPartnerName" placeholder="Name">

            <div class="text-right col-md-3">
              <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-0" (click)="searchPartner()">Search
                Center</button>
            </div>
          </div>

          <div>
            <div *ngIf="!partnerisLoading">
              <div class="col-md-12 float-right pagination-zindex">
                <div class="float-right mt-3">
                  <nav aria-label="Page navigation example" *ngIf="this.partnerTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="firstPartnerPageList()"
                        [ngClass]="{ 'disabled-pagination': partnerCurrentPage === 1 }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="previousPartnerPageList()"
                        [ngClass]="{ 'disabled-pagination': partnerCurrentPage === 1 }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ partnerCurrentPage }}&nbsp;of&nbsp; {{ partnerTotalPage
                          }}</a>
                      </li>
                      <li class="page-item" (click)="nextPartnerPageList()" [ngClass]="{
                    'disabled-pagination': partnerCurrentPage === partnerTotalPage
                  }">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="lastPartnerPageList()" [ngClass]="{
                    'disabled-pagination': partnerCurrentPage === partnerTotalPage
                  }">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
              <div class="col-md-10">
                <h4 class="text-success">Center List</h4>
                <h4>Total Centers - {{ partnerCount }}</h4>
                <!-- <h2 >test</h2> -->
                <div class="card card-table mb-0">
                  <div class="card-body">
                    <div class="table-responsive" *ngIf="partnerList.length > 0">
                      <table class="table table-hover table-center mb-0">
                        <thead>
                          <tr>
                            <th>#</th>
                            <th>Center ID</th>
                            <th>Center Name</th>
                            <th>Center Type</th>
                            <th>Hospital Name</th>
                            <th>Action</th>
                            <th>Report</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let data of partnerList; let i = index">
                            <th scope="row"><a>{{ partnerSerialNumber + i + 1 }}</a></th>
                            <td><a class="text-primary link" (click)="viewDetailpartner(data.uuid)">{{ data.partner_unique_id }}</a></td>
                            <td><a class="text-primary link" (click)="viewDetailpartner(data.uuid)">{{ data.username }}</a></td>
                            <td><a class="text-primary ">{{ data.center_type }}</a></td>
                            <td><a class="text-primary ">{{ data.hospital_name }}</a></td>
                            <td>
                              <button class="btn-primary btn-md mr-2"
                                (click)="viewDetailpartner(data.uuid)">View</button>
                            </td>
                            <td>
                              <button class="btn-primary btn-md mr-2"
                                (click)="onConsultationSummary(data.uuid,'book_user')">
                                Report
                              </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="centered" *ngIf="doctorisLoading">
              <app-loading-spinner></app-loading-spinner>
              </div> -->
            </div>
          </div>
        </div>

        <!----Patient List-->
        <div class="tab-pane" id="patient-list">
          <app-patient-list ></app-patient-list>
        </div>

      </div>
    </div>


    <!-- approved Tab -->

  </div>
</div>

<!-- Button trigger modal -->


<!-- Modal -->
<div class="modal fade" id="assistantModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Add Doctor Assistant</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <h4 translate>Doctor List</h4>
          <select class="form-control select" name="doctor" id="doctor"
            (change)="getAssisatantDatails($event.target.value)">
            <option>Select</option>
            <option *ngFor="let assist of assistantList" value={{assist.uuid}}> {{assist.username}}</option>

          </select>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" (click)="assignDoctorAssistant()">Add Assistant</button>
      </div>
    </div>
  </div>
</div>
<!-- delete department popup starts -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deletModalLabel">Add Doctor Assistant</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <h4 >Are you sure to delete"{{deleteDepartmentName}}"?</h4>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn-danger btn-md mr-2" (click)="deleteDepartment(2)">Delete</button>
        <button class="btn-primary btn-md mr-2" data-dismiss="modal">cancel</button>
      </div>
    </div>
  </div>
</div>
<!-- delete department popup end -->