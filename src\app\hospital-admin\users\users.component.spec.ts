import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SharedService } from '../../shared/shared.service';
import { ToastrService } from 'ngx-toastr';
import { HospitalService } from './../hospital-admin.service';
import { PlatformService } from '../../platform/platform.service';
import { AuthService } from './../../auth/auth.service';
declare var $:any;
@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.css'],
})
export class UsersComponent implements OnInit {
  isLoading: boolean;
  doctorList: any = [];
  hospitalId: string;
  adminProfile: any;
  public patientList=[];
  public id: string;
  public adminList = [];
  public assistantList: any = [];
  public patientSerialNumber= 0;
  public approvedDoctor: any = [];
  public pendingDoctor: any = [];
  patientisLoading :boolean;
  public patientCurrentPage: number;
  public patientapiSearchCall : boolean;
  public patientTotalPage : number;
  public patientCount : number;
  totalPage: number;
  currentPage: number;
  selectedDotorId: any;
  selectedAssistantId: any;

  constructor(
    private platformService: PlatformService,
    private router: Router,
    private authService: AuthService,
    private sharedService: SharedService,
    private notificationService: ToastrService,
    private hospitalService: HospitalService,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit(): void {
    
    this.sharedService.setActiveLink('dashboard');
    // this.id = "4c45cae4-6bf3-4548-b2c8-c4596fbc03ca";
    this.hospitalService.getUsers().subscribe(
      (data) => {
        this.hospitalId = data['uuid'];
        console.log(data);
        console.log('dadf')
        localStorage.setItem('hstId', this.hospitalId);
        this.sharedService.setUserName(data['name']);
        if (this.hospitalId) {
          this.getDoctorList(this.hospitalId);
          this.getDoctorAssociationStatus();
          this.getHospitalAdmin(this.hospitalId);
         this.getPatientList(this.patientCurrentPage);
        }
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
      }
    );


    
  }
  addDoctor() {
    this.router.navigate(['/add-doctor', this.hospitalId]);
  }
  createDoctorAssistant() {
    this.router.navigate(['/add-assistant', this.hospitalId]);
  }
  addAdmin() {
    this.router.navigate(['/add-admin', this.hospitalId]);
  }


  getDoctor(page){
    this.isLoading = true;
    window.scroll(0,0);
    this.hospitalService.getDoctorsList(page).subscribe(
      data => {
       this.doctorList = data['results'];
       this.totalPage = data['total_pages'];
       this.currentPage = data['page_number'];
       this.sharedService.searListBypage(
        this.doctorList,
        this.currentPage,
        null,
        this.totalPage
      );
      this.isLoading = false;

      },error=>{
        console.log(error);
        const status = error['status'];
        if(status == 400){
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );

  }




  getDoctorList(hospitalId) {
    this.isLoading = true;
    
    this.hospitalService.getUsersById(hospitalId).subscribe(
      (data) => {
        this.doctorList = data['results'];
        console.log(this.doctorList);
        this.isLoading = false;
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.isLoading = false;
      }
    );
  }
  viewDetails(id) {
    this.router.navigate(['/doctor-profile/', id, this.hospitalId]);
  }



  
  getHospitalAdmin(id) {
    let admin = [];
    this.hospitalService.getHospitalAdmin(id).subscribe(
      (data) => {
        admin = data['results'];
        console.log(admin);
        this.adminList = admin.filter(
          (obj) => obj.user_type == 'HospitalAdmin'
        );
        console.log(this.adminList);
        this.assistantList = admin.filter(
          (obj) => obj.user_type === 'DoctorAssistant'
        );
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.isLoading = false;
      }
    );
  }





  getPatientList(patientCurrentPage){
    this.patientapiSearchCall = true;
    this.patientTotalPage = 0;
    this.patientCount = 0;
    let psearchList = [];
    psearchList = this.sharedService.getpatientSearListBypage();
    if (psearchList.length > 0) {
      console.log('psearchList', psearchList);
      for (const data of psearchList) {
        if (data.page_number == patientCurrentPage) {
          this.patientapiSearchCall = false;
          this.patientTotalPage = data['total_pages'];
          this.patientCurrentPage = data['page_number'];
          this.patientList = data['results'];
          this.patientCount = data['count'];
          this.patientisLoading = false;
        }

      }
      if(!!this.patientapiSearchCall){
        this.getPatient(patientCurrentPage);
      }
    } else {
        this.getPatient(patientCurrentPage);
    }
 }

  getPatient(page){
    this.patientisLoading = true;
    window.scroll(0,0);
    this.platformService.getPatientsList(page).subscribe(
      data => {
       this.patientList = data['results'];
       console.log(this.patientList);
       this.patientTotalPage = data['total_pages'];
       this.patientCurrentPage = data['page_number'];
       this.patientCount = data['count'];
       this.sharedService.patientsearListBypage(
        this.patientList,
        this.patientCurrentPage,
        this.patientCount,
        this.patientTotalPage
      );
      this.patientisLoading = false;

      },error=>{
        console.log(error);
        const status = error['status'];
        if(status == 400){
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );

  }
  nextPatientPageList() {
    this.patientCurrentPage = this.patientCurrentPage + 1;
    if (this.patientTotalPage >= this.patientCurrentPage) {
      this.patientSerialNumber= (this.patientCurrentPage-1)*10;
      this.getPatientList(this.patientCurrentPage);

    } else {
      this.patientCurrentPage = this.patientCurrentPage - 1;
    }
  }

  lastPatientPageList() {
    this.patientSerialNumber= (this.patientTotalPage-1)*10;
    this.getPatientList(this.patientTotalPage);
  }
  firstPatientPageList() {
    this.patientSerialNumber=0;
    this.patientCurrentPage = 1;
    this.getPatientList(this.patientCurrentPage);
  }
  previousPatientPageList() {
    this.patientCurrentPage = this.patientCurrentPage - 1;
    if (this.patientTotalPage >= this.patientCurrentPage && this.patientCurrentPage > 0) {
      this.patientSerialNumber= (this.patientCurrentPage-1)*10;
      this.getPatientList(this.patientCurrentPage);
    } else {
      this.patientCurrentPage = this.patientCurrentPage + 1;
    }
  }





  getDoctorAssociationStatus() {
    let status = [];
    this.hospitalService.getAssociationData(this.hospitalId).subscribe(
      (data) => {
        status = data['results'];
        console.log('pending', status);
        this.pendingDoctor = status.filter(
          obj => obj.is_approved_by_doctor === false
        );
        this.approvedDoctor = status.filter(
          obj => obj.is_approved_by_doctor === true
        );

      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.isLoading = false;
      }
    );
  }




  selectAssistant(id){
    this.selectedDotorId = id;
    $('#assistantModal').modal('show');
  }
  assignDoctorAssistant(){
    const data = {doctor_uuid: this.selectedDotorId, assistant_uuid: this.selectedAssistantId};
    this.hospitalService.assignAssistant(data).subscribe(data => {
      $('#assistantModal').modal('hide');

    }, err => {
    console.log(err);
    this.notificationService.error('Internal server error', 'Med.Bot');
    });

  }
  getAssisatantDatails(event){
    this.selectedAssistantId =event;
console.log(event);
  }
}

