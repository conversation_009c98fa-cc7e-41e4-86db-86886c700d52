import { Injectable } from '@angular/core';
import { HttpRe<PERSON>, HttpH<PERSON><PERSON>, HttpEvent, HttpInterceptor, HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from '../auth/auth.service';

/**
 *
 *
 * @export
 * @class AccessTokenInterceptor
 * @implements {HttpInterceptor}
 */
@Injectable()
export class AccessTokenInterceptor implements HttpInterceptor {
    /**
     *Creates an instance of AccessTokenInterceptor.
     * @param {AuthService} authService
     * @memberof AccessTokenInterceptor
     */
    constructor (private authService: AuthService) {}
    /**
     *
     *
     * @param {HttpRequest<any>} request
     * @param {HttpHandler} next
     * @returns {Observable<HttpEvent<any>>}
     * @memberof AccessTokenInterceptor
     */
    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        const currentUser = this.authService.getAccessTokenDetails();
        if((request.url).toString().includes('vc-')){
            return next.handle(request);
        }
        if (currentUser && currentUser.access) {
            request = request.clone({
                setHeaders: {
                    Authorization: `Bearer ${currentUser.access}`
                }
            });
        }
        return next.handle(request);
    }
}
