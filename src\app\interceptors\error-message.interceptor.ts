import { AuthService } from 'src/app/auth/auth.service';
import { ToastrService } from 'ngx-toastr';
import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

/**
 *
 *
 * @export
 * @class ErrorMessageInterceptor
 * @implements {HttpInterceptor}
 */
@Injectable()
export class ErrorMessageInterceptor implements HttpInterceptor {
    /**
     *
     *
     * @private
     * @memberof ErrorMessageInterceptor
     */
    private refreshTokenInProgress = false;
    /**
     *
     *
     * @private
     * @type {BehaviorSubject<any>}
     * @memberof ErrorMessageInterceptor
     */
    private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(
        null
    );

    /**
     *Creates an instance of ErrorMessageInterceptor.
     * @memberof ErrorMessageInterceptor
     */
    constructor(private notifyService : ToastrService,
      private authService : AuthService) {}

    /**
     *
     *
     * @param {HttpRequest<any>} request
     * @param {HttpHandler} next
     * @returns {Observable<HttpEvent<any>>}
     * @memberof ErrorMessageInterceptor
     */
    intercept(
        request: HttpRequest<any>,
        next: HttpHandler
    ): Observable<HttpEvent<any>> {
        return next.handle(request).pipe(
            catchError(
                error => {
                    // if (error.status === 403) {
                    //   this.notifyService.showError('Oops!!!', 'Access Denied');
                    // }
                    // if (error.status === 404) {
                    //   this.notifyService.showError('Oops!!!', 'Page Not Found');
                    // }
                    if (error.status === 401){
                      this.notifyService.error("Please Try Again",'Sorry');
                    }
                    // if (error.status === 500) {
                    //   this.notifyService.error('Oops!!!', 'Internal Server Error');
                    // }
                    return throwError(error);
                }
            )
        );
    }
}
