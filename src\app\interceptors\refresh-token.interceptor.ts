import { Injectable } from '@angular/core';
import { HttpRequest, Http<PERSON><PERSON>ler, HttpEvent, HttpInterceptor, HttpErrorResponse } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { AuthService } from 'src/app/auth/auth.service';
import { switchMap, filter, take, catchError } from 'rxjs/operators';

/**
 *
 *
 * @export
 * @class RefreshTokenInterceptor
 * @implements {HttpInterceptor}
 */
@Injectable()
export class RefreshTokenInterceptor implements HttpInterceptor {
  /**
   *
   *
   * @private
   * @memberof RefreshTokenInterceptor
   */
  private refreshTokenInProgress = false;
  /**
   *
   *
   * @private
   * @type {BehaviorSubject<any>}
   * @memberof RefreshTokenInterceptor
   */
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(
    null
  );

  /**
   *Creates an instance of RefreshTokenInterceptor.
   * @param {AuthService} auth
   * @memberof RefreshTokenInterceptor
   */
  constructor(public auth: AuthService) { }

  /**
   *
   *
   * @param {HttpRequest<any>} request
   * @param {HttpHandler} next
   * @returns {Observable<HttpEvent<any>>}
   * @memberof RefreshTokenInterceptor
   */
  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(catchError(error => {
      if(error['error']['code']=='token_not_valid'){
        this.auth.logout();
      }else{
        if (
          request.url.includes('refreshtoken') ||
          request.url.includes('login')
        ) {
          if (request.url.includes('refreshtoken')) {
            this.auth.logout();
          }
          return throwError(error);
        }
        if (error.status !== 401) {
          return throwError(error);
        }
        if (error.status == 400) {
          this.auth.refreshAccessToken();
        }

        if (this.refreshTokenInProgress) {
          return this.refreshTokenSubject.pipe(
            filter(result => result !== null),
            take(1),
            switchMap(() => next.handle(this.addAuthenticationToken(request)))
          );
        } else {
          this.refreshTokenInProgress = true;
          this.refreshTokenSubject.next(null);
          return this.auth
            .refreshAccessToken()
            .pipe(switchMap((token: any) => {
              this.auth.updateAccessTokenDetails(token);
              this.refreshTokenInProgress = false;
              this.refreshTokenSubject.next(token);
              return next.handle(this.addAuthenticationToken(request));
            })).pipe(catchError((err: any) => {
              console.log('error', err);
              this.refreshTokenInProgress = false;
              this.auth.logout();
              this.handleError(error);
              return throwError(error);
            }));
        }
      }
    }));
  }

  /**
   *
   *
   * @param {*} request
   * @returns
   * @memberof RefreshTokenInterceptor
   */
  addAuthenticationToken(request) {
    const accessToken = this.auth.getAccessToken();
    if (!accessToken) {
      return request;
    }

    return request.clone({
      setHeaders: {
        Authorization: 'Bearer ' + this.auth.getAccessToken()
      }
    });
  }

  handleError(error: HttpErrorResponse) {
    if (error.status == 401) {
      this.auth.refreshAccessToken();
      // this.auth.logout;
    }
    else {
      return throwError(error);
    }
  }
}
