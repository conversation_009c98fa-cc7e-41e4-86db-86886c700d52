<div id="main">
    <div class="fof">
            <h1>Page Not Found</h1>
    </div>
</div>


<!-- <div class="pub-prof">
    <h1>404 Error</h1>
    <h1>Page Not Found</h1>
</div>  -->

<!--<p class="mb-0.05"></p>
<div class="row pub-prof">
    <div class="col-lg-3">
        <div class="card left-pane" >
            <div class="doc-profile-data" >
              <div class="spinner-border" role="status" *ngIf="!showProfilePic" style="margin: 10px 0px 0px 140px;">
                <span class="sr-only">Loading...</span>
              </div>
                <img [src]="profilePicture" class="img-fluid" alt="User Image">
                <p class="doc-name first-name">Dr. {{doctor.user.username}}</p>
                <p class="doc-speciality">{{ doctor.qualifications[0].name }}</p>
                <p class="doc-location" *ngFor="let location of practiceLocations"><i class="fas fa-map-marker-alt"></i>&nbsp;&nbsp;{{ location.name }}</p>
             </div>
        </div>

    </div>

    <div class="col-lg-9" >
        <div class="card right-pane">
            <div class="top-para">
                <div class="row">
                    <div class="col-lg-6 details">
                        <h3>Dr. {{doctor.user.username}}</h3>
                        <p class="doc-speciality right-pane">{{doctor.system_of_medicine}}</p>
                        <div class="btns">
                            <button *ngFor="let spec of specialization" class="btn btn-info sm">{{spec.value}}</button>
                        </div>
                    </div>
                    <div class="col-lg-2 offset-3">
                        <button class="btn btn-info m"><i class="fa fa-check-circle" aria-hidden="true"></i>
                          Available</button>
                    </div>
                
                </div>
            </div>
                <div class="cont-para">
                    <p class="sub-head">Consulting Hours</p>
                    <div *ngFor="let add of practiceLocations;let i=index" class="row ch-details">
                        <div class="col-lg-4">
                          <ng-container *ngIf="add['addresses']">
                            <p id="clinic-name" class="clinic-head" *ngIf="practiceLocations.length==1">{{add['name']}}</p>
                            <p id="clinic-name" class="clinic-head" *ngIf="practiceLocations.length>=2">{{i+1}}.{{add['name']}}</p>
                            <p class="cont-text">{{add['addresses']?add['addresses']['line_1'] :'No address'}}</p>
                            <p class="cont-text">{{add['addresses']? add['addresses']['line_2']: ''}}</p>
                            <p class="cont-text">{{add['addresses']['district']}}-{{add['addresses']['postal_code']}}</p>
                            <p class="cont-text"></p>
                          </ng-container>
                          <ng-container *ngIf="!add['addresses']">
                            <p class="clinic-head" *ngIf="practiceLocations.length==1">Address </p>
                            <p class="clinic-head" *ngIf="practiceLocations.length>=2"> {{i+1}}.Address </p>
                            <p class="cont-text">Online Consultation</p>
                          </ng-container>
                        </div>
                        <div class="col-lg-5">
                            <p id="timing-head" class="clinic-head">Timings</p>
                            <ng-container  *ngIf="add['consultinghoursgroups'].length>0">
                              <div *ngFor="let days of add['consultinghoursgroups']">
                                <div *ngFor="let day of days['days_of_the_week']">
                                  <ng-container *ngIf="days['time_from'] && days['time_to']">
                                    <div class="row"><p  class="day cont-text three_chars">&nbsp;&nbsp;{{day}} </p><p>&nbsp;&nbsp;:&nbsp;&nbsp;{{days['time_from']}} - {{days['time_to']}}</p></div>
                                  </ng-container>
                                </div>

                             </div>
                            </ng-container>

                            <ng-container  *ngIf="add['consultinghoursgroups'].length===0">
                              <p class="timing mt-2">No Slots </p>
                              </ng-container>

                        </div>
                        <div class=" col-lg-3 clinic-booking" *ngIf="amount !=='None'" >
                            <p id="fee-head" class="clinic-head">Fee</p>
                            <p class="cont-text fee">&#x20B9;&nbsp; {{doctor.consultationfees[0].amount}}/Visit</p>
                            <a class="view-pro-btn" id="book-appointment"  (click)="bookAppointment()" style="cursor:pointer" >Book Appointment</a>
                            <a class="view-pro-btn" id="consult-appointment" *ngIf="instant_appointment_slot_available"  (click)="consultNow()" style="cursor:pointer" >Consult Now</a>
                        </div>
                    </div>
                </div>    
                <hr>

                <div class="cont-para">
                    <h3 class="sub-head">Info</h3>
                    <div class="row ch-details">
                        <div class="col-lg-4">
                            <h3 id="dr-edu" class="clinic-head">Education</h3>
                            <div  *ngIf="degreeList.length==1">
                              <p *ngFor="let deg of degreeList; let i=index" class="cont-text"> {{deg.name}}</p>
                            </div>
                            <div *ngIf="degreeList.length>=2">
                              <p *ngFor="let deg of degreeList; let i=index" class="cont-text">{{i+1}}. {{deg.name}}</p>
                            </div>

                        </div>
                        <div class="col-lg-4">
                            <p id="dr-spec" class="clinic-head">Specialization</p>
                            <p *ngFor="let spec of specialization" class="cont-text">{{spec.value}}</p>
                        </div>
                        <div class="col-lg-4">
                          <p id="dr-reg" class="clinic-head">Registration</p>
                          <p class="cont-text">{{ doctor.registrations[0].number}}</p>
                      </div>
                    </div>
                    <div class="row ch-details">
                        <div class="col-lg-4">
                          <p id="dr-reg" class="clinic-head">Years of Experience</p>
                          <p class="cont-text">{{doctor.years_of_experience}}</p>
                      </div>
                      <div class="col-lg-4">
                        <p id="dr-reg" class="clinic-head">Language</p>
                        <p *ngFor="let lang of languages" class="cont-text">{{lang.value}}</p>
                    </div>
                    </div>

                </div>

        </div>        
    </div>    
</div> -->