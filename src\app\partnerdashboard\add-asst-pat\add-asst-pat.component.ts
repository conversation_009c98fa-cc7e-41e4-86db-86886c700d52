import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SharedService } from '../../shared/shared.service';
import { ToastrService } from 'ngx-toastr';
import { HospitalService } from './../../hospital-admin/hospital-admin.service';
import { PlatformService } from '../../platform/platform.service';
import { AuthService } from './../../auth/auth.service';
import { Subscription } from 'rxjs';
import { HospitalModel } from 'src/app/hospital-admin/models/hospital.model';
declare var $: any;
@Component({
  selector: 'app-add-asst-pat',
  templateUrl: './add-asst-pat.component.html',
  styleUrls: ['./add-asst-pat.component.css'],
})
export class AddAsstPatComponent implements OnInit {
  // isLoading: boolean;
  doctorList: any = [];
  public doctorasList: any = [];
  public partnerList: any = [];
  public deptList: any = [];
  public specialityList: any = [];
  hospitalId: string;
  adminProfile: any;
  public deptSerialNumber = 0;
  public specialitySerialNumber = 0;
  public partnerSerialNumber = 0;
  public id: string;
  public adminList = [];
  public assistantList: any = [];

  public doctorSerialNumber = 0;
  public doctorasSerialNumber = 0;
  public approvedDoctor: any = [];
  public pendingDoctor: any = [];
  specialityisLoading: boolean;
  partnerisLoading: boolean;
  deptisLoading: boolean;
  doctorasLoading: boolean;
  doctorisLoading: boolean;
  doctorassisLoading: boolean;
  assistantisLoading: boolean;

  totalPage: number;
  currentPage: number;
  selectedDotorId: any;
  selectedAssistantId: any;
  doctorCurrentPage = 1;
  doctorTotalPage: number;
  doctorCount: number;
  doctorasTotalPage: number;
  doctorasCurrentPage = 1;
  doctorasCount: number;
  partnerTotalPage: number;
  partnerCurrentPage = 1;
  partnerCount: number;
  deptTotalPage: number;
  deptCurrentPage = 1;
  deptCount: number;
  specialityTotalPage: number;
  specialityCurrentPage = 1;
  specialityCount: number;
  searchDoctorName = null;
  searchDoctorasName = null;
  private subscriptions: Subscription;
  hospital: HospitalModel;

  constructor(
    private platformService: PlatformService,
    private router: Router,
    private authService: AuthService,
    private sharedService: SharedService,
    private notificationService: ToastrService,
    private hospitalService: HospitalService,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.sharedService.setActiveLink('dashboard');
    this.subscriptions = this.hospitalService.currentHospitalDetails.pipe()
      .subscribe(value => {
        if (value && value.hospitalId != '') {
          this.hospital = Object.assign({}, value);
          this.hospitalId = this.hospital.hospitalId;
        } else {
          this.hospital = this.hospitalService.getHospitalDetails();
          this.hospitalId = this.hospital.hospitalId;
        }
        if (this.hospitalId) {
          this.getDoctorList(this.hospitalId, this.doctorCurrentPage);
          this.getDoctorAssociationStatus(this.hospitalId);
          this.getHospitalAdmin(this.hospitalId);
          this.getAssistantDetails(this.hospitalId, this.doctorasCurrentPage);
        }
      });
  }

  createDoctorAssistant() {
    this.router.navigate(['/doctor-assistant', this.hospitalId]);
  }

  addAdmin() {
    this.router.navigate(['/add-patient', this.hospitalId]);
  }

  addSchedule(uuid) {
    this.router.navigate([`hadoctor-practicelocation/${uuid}`]);
  }

  searchDoctor() {
    var query = '?page=1&user_type=doctor';
    if (this.searchDoctorName != null) {
      query += '&username=' + this.searchDoctorName;
    }
    console.log(query);
    this.hospitalService.getDoctorsSearchList(this.hospitalId, query).subscribe(
      (data) => {
        this.doctorList = data['results'];
        this.doctorTotalPage = data['total_pages'];
        this.doctorCurrentPage = data['page_number'];
        this.doctorCount = data['count'];
        console.log(this.doctorList);
        this.doctorisLoading = false;
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.doctorisLoading = false;
      }
    );
  }

  searchDoctoras() {
    var query = '?page=1&user_type=doctorassistant';
    if (this.searchDoctorasName != null) {
      query += '&username=' + this.searchDoctorasName;
    }
    console.log(query);
    this.hospitalService.getDoctorsSearchList(this.hospitalId, query).subscribe(
      (data) => {
        this.doctorasList = data['results'];
        this.doctorasTotalPage = data['total_pages'];
        this.doctorasCurrentPage = data['page_number'];
        this.doctorasCount = data['count'];
        console.log(this.doctorasList);
        this.doctorasLoading = false;
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.doctorasLoading = false;
      }
    );
  }

  // Doctor list pagination
  nextDoctorPageList() {
    this.doctorCurrentPage = this.doctorCurrentPage + 1;
    if (this.doctorTotalPage >= this.doctorCurrentPage) {
      this.doctorSerialNumber = (this.doctorCurrentPage - 1) * 10;
      this.getDoctorList(this.hospitalId, this.doctorCurrentPage);

    } else {
      this.doctorCurrentPage = this.doctorCurrentPage - 1;
    }
  }

  firstDoctorPageList() {
    this.doctorSerialNumber = 0;
    this.doctorCurrentPage = 1;
    this.getDoctorList(this.hospitalId, this.doctorCurrentPage);
  }

  previousDoctorPageList() {
    this.doctorCurrentPage = this.doctorCurrentPage - 1;
    if (this.doctorTotalPage >= this.doctorCurrentPage && this.doctorCurrentPage > 0) {
      this.doctorSerialNumber = (this.doctorCurrentPage - 1) * 10;
      this.getDoctorList(this.hospitalId, this.doctorCurrentPage);
    } else {
      this.doctorCurrentPage = this.doctorCurrentPage + 1;
    }
  }

  lastDoctorPageList() {
    this.doctorSerialNumber = (this.doctorTotalPage - 1) * 10;
    this.getDoctorList(this.hospitalId, this.doctorTotalPage);
  }

  // Doctor assistant list pagination
  nextDoctorasPageList() {
    this.doctorasCurrentPage = this.doctorasCurrentPage + 1;
    if (this.doctorasTotalPage >= this.doctorasCurrentPage) {
      this.doctorasSerialNumber = (this.doctorasCurrentPage - 1) * 10;
      this.getAssistantDetails(this.hospitalId, this.doctorasCurrentPage);

    } else {
      this.doctorasCurrentPage = this.doctorasCurrentPage - 1;
    }
  }

  firstDoctorasPageList() {
    this.doctorasSerialNumber = 0;
    this.doctorasCurrentPage = 1;
    this.getAssistantDetails(this.hospitalId, this.doctorasCurrentPage);
  }

  previousDoctorasPageList() {
    this.doctorasCurrentPage = this.doctorasCurrentPage - 1;
    if (this.doctorasTotalPage >= this.doctorasCurrentPage && this.doctorasCurrentPage > 0) {
      this.doctorasSerialNumber = (this.doctorasCurrentPage - 1) * 10;
      this.getAssistantDetails(this.hospitalId, this.doctorasCurrentPage);
    } else {
      this.doctorasCurrentPage = this.doctorasCurrentPage + 1;
    }
  }

  lastDoctorasPageList() {
    this.doctorasSerialNumber = (this.doctorTotalPage - 1) * 10;
    this.getAssistantDetails(this.hospitalId, this.doctorasTotalPage);
  }

  viewDetails(uuid) {
    this.router.navigate([`hospital-doctor-profile/${uuid}`]);
    // this.modalService.open(DocumentModalComponent,  { windowClass : "modalSize"});
  }

  viewDetailsas(uuid) {
    this.router.navigate([`doctor-assistant-profile/${uuid}`]);
    // this.modalService.open(DocumentModalComponent,  { windowClass : "modalSize"});
  }

  viewDetailpartner(uuid) {
    this.router.navigate([`partner-profile/${uuid}`]);
    // this.modalService.open(DocumentModalComponent,  { windowClass : "modalSize"});
  }

  getDoctorList(hospitalId, page) {
    this.doctorisLoading = true;
    this.doctorTotalPage = 0;
    this.doctorCount = 0;
    this.hospitalService.getDoctorsList(hospitalId, page).subscribe(
      (data) => {
        this.doctorList = data['results'];
        this.doctorTotalPage = data['total_pages'];
        this.doctorCurrentPage = data['page_number'];
        this.doctorCount = data['count'];
        console.log(this.doctorList);
        this.doctorisLoading = false;
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.doctorisLoading = false;
      }
    );
  }

  getHospitalAdmin(id) {
    let admin = [];
    this.hospitalService.getHospitalAdmin(id).subscribe(
      (data) => {
        admin = data['results'];
        console.log(admin);
        this.adminList = admin.filter(
          (obj) => obj.user_type == 'HospitalAdmin'
        );
        console.log(this.adminList);
        this.assistantList = admin.filter(
          // (obj) => obj.user_type === 'DoctorAssistant'
          (obj) => obj.user_type === 'Doctor'

        );
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.doctorisLoading = false;
      }
    );
  }

  getSpecialityDetails(hospitalId, page) {
    this.specialityisLoading = true;

    this.hospitalService.getSpecialityList(hospitalId, page).subscribe(
      (data) => {
        this.specialityList = data['results'];
        this.specialityTotalPage = data['total_pages'];
        this.specialityCurrentPage = data['page_number'];
        this.specialityCount = data['count'];
        console.log("specialityList");
        console.log(this.specialityList);
        this.specialityisLoading = false;
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.specialityisLoading = false;
      }
    );
  }

  getAssistantDetails(hospitalId, page) {
    this.doctorasLoading = true;
    this.doctorasTotalPage = 0;
    this.doctorasCount = 0;
    this.hospitalService.getAssistantList(hospitalId, page).subscribe(
      (data) => {
        this.doctorasList = data['results'];
        this.doctorasTotalPage = data['total_pages'];
        this.doctorasCurrentPage = data['page_number'];
        this.doctorasCount = data['count'];
        console.log("doctorasList");
        console.log(this.doctorasList);
        this.doctorasLoading = false;
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.doctorasLoading = false;
      }
    );
  }

  getDeptDetails(hospitalId, page) {
    this.deptisLoading = true;
    this.hospitalService.getDeptList(hospitalId, page).subscribe(
      (data) => {
        this.deptList = data['results'];
        this.deptTotalPage = data['total_pages'];
        this.deptCurrentPage = data['page_number'];
        this.deptCount = data['count'];
        console.log("deptList");
        console.log(this.deptList);
        this.deptisLoading = false;
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.deptisLoading = false;
      }
    );
  }

  getPartnerDetails(hospitalId, page) {
    this.partnerisLoading = true;

    this.hospitalService.getPartnerList(hospitalId, page).subscribe(
      (data) => {
        this.partnerList = data['results'];
        this.partnerTotalPage = data['total_pages'];
        this.partnerCurrentPage = data['page_number'];
        this.partnerCount = data['count'];
        console.log("partnerList");
        console.log(this.partnerList);
        this.partnerisLoading = false;
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.partnerisLoading = false;
      }
    );
  }

  getDoctorAssociationStatus(hospitalId) {
    let status = [];
    this.hospitalService.getAssociationData(this.hospitalId).subscribe(
      (data) => {
        status = data['results'];
        console.log('pending', status);
        this.pendingDoctor = status.filter(
          obj => obj.is_approved_by_doctor === false
        );
        this.approvedDoctor = status.filter(
          obj => obj.is_approved_by_doctor === true
        );

      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.doctorisLoading = false;
      }
    );
  }

  selectAssistant(id) {
    this.selectedDotorId = id;
    $('#assistantModal').modal('show');
  }

  assignDoctorAssistant() {
    const data = { doctor_uuid: this.selectedDotorId, assistant_uuid: this.selectedAssistantId };
    this.hospitalService.assignAssistant(data).subscribe(data => {
      $('#assistantModal').modal('hide');

    }, err => {
      console.log(err);
      this.notificationService.error('Internal server error', 'Med.Bot');
    });

  }

  getAssisatantDatails(event) {
    this.selectedAssistantId = event;
    console.log(event);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
}