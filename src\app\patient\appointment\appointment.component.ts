import { Component, OnInit, NgZone } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { BreadcrumbService } from '../breadcrumb-serice';
import { FormControl } from '@angular/forms';
import { DoctorService } from '../../doctor/doctor.service';
import { PatientService } from '../patient.service';
import * as moment from 'moment';
import { SharedService } from '../../shared/shared.service';
declare var Razorpay: any;
declare var $: any;
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
@Component({
  selector: 'app-appointment',
  templateUrl: './appointment.component.html',
  styleUrls: ['./appointment.component.css'],
})
export class AppointmentComponent implements OnInit {
  public singleDoctorData: any = [];
  public doctorId: string;
  public locationId: string;
  public doctorName: string;
  public selctedDate: FormControl;
  public reaminingTime: number;
  currentLocation: any;
  address: any;
  public day1: any = [];
  public day2: any = [];
  public day3: any = [];
  public day4: any = [];
  public day5: any = [];
  public day6: any = [];
  public day7: any = [];
  todayDate = moment();
  showPreviewsBtn = false;
  public timeList: any = [];
  dayList: any = [moment()];
  public dayAndTimeList: any = [];
  public effectiveUpto: string;
  public effectiveFrom: string;
  public startHour: number;
  public endHour: number;
  public startMinute: number;
  public endMinute: number;
  consultationDuration: number;
  public viewDayList: any = [];
  public acceptedTerms = false;
  public colors = 'green';
  public consultingHours: any = [];
  public daysOfWeek: any = [moment().format('DD/MM/YYYY')];
  public selectedAppointmentDate = moment().format('YYYY-MM-DD');
  public currentYear = moment().format('YYYY');
  public seventhDay = moment().add(7, 'days');
  public seventhDayFormatDate: string;
  public disabeldTime = false;
  public appointmentDates: any = [];
  public appoimentSlots: any = [];
  public dayCount = 0;
  public currentPage = 1;
  public totalPageCount: number;
  public selctedAppontmentDate: any;
  public daysList = [moment()];
  public availableDate: any = [];
  seventhDateAvailable: boolean;
  sixthDateAvailable: boolean;
  fifththDateAvailable: boolean;
  fourthDateAvailable: boolean;
  thirdDateAvailable: boolean;
  secondDateAvailable: boolean;
  firstDateAvailable: boolean;
  unavailableData = {
    uuid: null,
    to_datetime: null,
    from_datetime: null,
    doctor: null,
    status: null,
    practice_location: null,
    duration_minutes: null,
    appointment: null,
    time: null,
  };
  avialableDateIndex: number;
  public doctorImageUrl: string;
  bookingSlotErr = false;
  isLoading = false;
  daysArrayList: any = [];
  selectedSlot = {};
  slotUuid = '';
  pgwOptions = {};
  public paymentDetails = {};
  booked = false;
  appointmentSelectedEvent = false;
  appoimentSlotsSelected: boolean;
  public currency: string;
  public amount: number;
  bookingConfirmed: boolean;
  contentText: any;
  parentAppointmentId: string;
  public maxDate: Date;
  public minDate: Date;
  public bookingCompletionError: boolean;
  errorValue: any = [];
  effective_fee: any;
  gst: any;
  platform_fee: any;
  current_user_type: any;
  hospital_uuid: string;
  constructor(
    private router: Router,
    private activeRoute: ActivatedRoute,
    private breadcrumbService: BreadcrumbService,
    private doctorService: DoctorService,
    private patientService: PatientService,
    private sharedService: SharedService,
    private notificationService: ToastrService,
    private location: Location,
    private ngZone: NgZone
  ) { }

  ngOnInit(): void {
    this.current_user_type = localStorage.getItem('user_type');
    this.hospital_uuid = localStorage.getItem("hstId");

    this.maxDate = new Date();
    this.minDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate() + 30);
    this.doctorImageUrl = 'assets/img/doctors/doctor-thumb-02.png';
    for (let day = 1; day < 7; day++) {
      const dates = moment().add(day, 'days');
      const formateDay = moment(dates).format('DD/MM/YYYY');
      this.dayList.push(dates);
      this.daysOfWeek.push(formateDay);
    }

    this.seventhDayFormatDate = moment(this.seventhDay).format('YYYY-MM-DD');
    this.activeRoute.params.subscribe((params) => {
      this.doctorId = params['id'];
      this.locationId = params['location'];
      this.parentAppointmentId = params['apptId'];
      this.getConsentDocumnetData();
      this.getProfileData(this.doctorId);
      this.getAppointmentSlot(
        this.selectedAppointmentDate,
        this.seventhDayFormatDate
      );
    });
    this.breadcrumbService.setChildUrl('Appointment');
  }

  getAppointmentSlot(selectedAppointmentDate, seventhDayFormatDate) {
    this.isLoading = true;
    this.booked = false;
    this.appoimentSlots = [];
    this.availableDate = [];
    this.day1 = [];
    this.day2 = [];
    this.day3 = [];
    this.day4 = [];
    this.day5 = [];
    this.day6 = [];
    this.day7 = [];
    const parms = `?from_datetime=${selectedAppointmentDate}&to_datetime=${seventhDayFormatDate}`;
    this.doctorService.getDoctorAppointmentSlot(this.doctorId, parms).subscribe(
      (data) => {
        const list = Object.values(data);
        this.appoimentSlots = list.slice(1);
        this.timeList = list.slice(1);
        const availableDate = list[0].slice(1);
        this.avialableDateIndex = 1;
        for (const value of availableDate) {
          if (value === this.daysOfWeek[0]) {
            // tslint:disable-next-line: prefer-for-of
            for (let i = 0; i < this.appoimentSlots.length; i++) {
              const tempdata = this.appoimentSlots[i];
              const bindData = tempdata[this.avialableDateIndex];
              if (!!bindData) {
                const startDate = moment(bindData.from_datetime);
                if (startDate >= this.todayDate) {
                  this.day1.push(bindData);
                } else {
                  this.day1.push({ status: 'null' });
                }
              } else {
                this.day1.push({ status: 'null' });
              }
            }
            this.avialableDateIndex = this.avialableDateIndex + 1;
          } else if (value === this.daysOfWeek[1]) {
            // tslint:disable-next-line: prefer-for-of
            for (let i = 0; i < this.appoimentSlots.length; i++) {
              const tempdata = this.appoimentSlots[i];
              const bindData = tempdata[this.avialableDateIndex];
              if (!!bindData) {
                this.day2.push(bindData);
              } else {
                this.day2.push({ status: 'null' });
              }
            }
            this.avialableDateIndex = this.avialableDateIndex + 1;
          } else if (value === this.daysOfWeek[2]) {
            // tslint:disable-next-line: prefer-for-of
            for (let i = 0; i < this.appoimentSlots.length; i++) {
              const tempdata = this.appoimentSlots[i];
              const bindData = tempdata[this.avialableDateIndex];
              if (!!bindData) {
                this.day3.push(bindData);
              } else {
                this.day3.push({ status: 'null' });
              }
            }
            this.avialableDateIndex = this.avialableDateIndex + 1;
          } else if (value === this.daysOfWeek[3]) {
            // tslint:disable-next-line: prefer-for-of
            for (let i = 0; i < this.appoimentSlots.length; i++) {
              const tempdata = this.appoimentSlots[i];
              const bindData = tempdata[this.avialableDateIndex];
              if (!!bindData) {
                this.day4.push(bindData);
              } else {
                this.day4.push({ status: 'null' });
              }
            }
            this.avialableDateIndex = this.avialableDateIndex + 1;
          } else if (value === this.daysOfWeek[4]) {
            // tslint:disable-next-line: prefer-for-of
            for (let i = 0; i < this.appoimentSlots.length; i++) {
              const tempdata = this.appoimentSlots[i];
              const bindData = tempdata[this.avialableDateIndex];
              if (!!bindData) {
                this.day5.push(bindData);
              } else {
                this.day5.push({ status: 'null' });
              }
            }
            this.avialableDateIndex = this.avialableDateIndex + 1;
          } else if (value === this.daysOfWeek[5]) {
            // tslint:disable-next-line: prefer-for-of
            for (let i = 0; i < this.appoimentSlots.length; i++) {
              const tempdata = this.appoimentSlots[i];
              const bindData = tempdata[this.avialableDateIndex];
              if (!!bindData) {
                this.day6.push(bindData);
              } else {
                this.day6.push({ status: 'null' });
              }
            }
            this.avialableDateIndex = this.avialableDateIndex + 1;
          } else if (value === this.daysOfWeek[6]) {
            // tslint:disable-next-line: prefer-for-of
            for (let i = 0; i < this.appoimentSlots.length; i++) {
              const tempdata = this.appoimentSlots[i];
              const bindData = tempdata[this.avialableDateIndex];
              if (!!bindData) {
                this.day7.push(bindData);
              } else {
                this.day7.push({ status: 'null' });
              }
            }
            this.avialableDateIndex = this.avialableDateIndex + 1;
          }
        }
        this.bookingSlotErr = false;
        this.isLoading = false;
      },
      (error) => {
        this.bookingSlotErr = true;
        this.isLoading = false;
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          const message = error['error']['error']['message'];
          if (message) {
            this.notificationService.error(`${message}`, 'Med.Bot');
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  getProfileData(id) {
    this.patientService.getdoctorProfile(id).subscribe(
      (data) => {
        this.doctorName = data['user']['username'];
        const imageUrl = data['user']['profile_picture'];
        if (!!imageUrl) {
          this.doctorImageUrl = imageUrl;
        }
        const fees = data['consultationfees'];
        this.currency = fees[0].currency;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  setUnavialbleDate(dayString) {
    for (let i = 0; i < this.daysOfWeek.length - 1; i++) {
      if (this.daysOfWeek[i] === dayString) {
      }
    }
  }
  nextWeek(event) {
    this.daysOfWeek = [moment(event).format('DD/MM/YYYY')];
    this.dayList = [moment(event)];
    for (let day = 1; day < 7; day++) {
      const dates = moment(event).add(day, 'days');
      const formateDay = moment(dates).format('DD/MM/YYYY');
      this.dayList.push(dates);
      this.daysOfWeek.push(formateDay);
    }
    const seventhDay = moment(event).add(7, 'days');
    this.seventhDayFormatDate = moment(seventhDay).format('YYYY-MM-DD');
    const date = moment(event).format('YYYY-MM-DD');
    this.getAppointmentSlot(date, this.seventhDayFormatDate);
  }

  getSelectedDate(event: any, index: number, colume: string, data) {
    if (this.slotUuid === event.uuid) {
      this.slotUuid = null;
      this.acceptedTerms = false;
      this.appointmentSelectedEvent = false;
      this.appoimentSlotsSelected = false;
    } else {
      this.slotUuid = event.uuid;
      this.appointmentSelectedEvent = true;
      this.effective_fee = event.effective_fee;
      this.gst = event.gst;
      this.amount = event.amount;
      this.platform_fee = event.platform_service_fee;
      this.appoimentSlotsSelected = true;
      this.selctedAppontmentDate = moment(event.from_datetime);
    }

  }

  acceptTermsAndCondtion(event) {
    this.acceptedTerms = event.target.checked;
  }
  bookAppoinment() {
    $('#confirmBooking').prop('disabled', false);
    this.bookingConfirmed = true;
  }
  confirmBooking() {
    $('#confirmBooking').prop('disabled', true);
    if (
      !!this.appoimentSlotsSelected &&
      this.parentAppointmentId === 'booking'
    ) {

      if (this.effective_fee == 0) {
        $('#bookAppointment').modal('hide');
        this.isLoading = true;

        let patientType = this.current_user_type == "Patient" ?'outpatient':'';
        let hospitalId = this.current_user_type == "Patient" ? localStorage.getItem('patient_hospital_id') : this.hospital_uuid;
        // if ((hospitalId == '' || hospitalId == undefined || hospitalId == null) && this.current_user_type == "Patient") {
        //   patientType = 'Public';
        // }
        const appointmentData = { hospital_uuid: hospitalId, patient_type: patientType };
        this.sharedService
          .freeBooking(this.slotUuid, appointmentData)
          .subscribe(
            (data) => {

              const id = data['consultation_uuid'];
              this.ngZone
                .run(() => this.router.navigateByUrl(`/patient/booked/${id}`))
                .then();
            }, error => {
              console.log(error);
              const status = error['status'];
              if (status == 400 || status == 409) {
                const message = error['error']['error'];
                const error_message = error['error']['error_message']
                if (message) {
                  this.notificationService.error(`${message}`, 'Med.Bot');
                } else if (error_message) {
                  this.notificationService.error(`${error_message}`, 'Med.Bot');
                } else {
                  this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
                }

              }
              else {
                this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
              }
            })
      } else {
        const appointmentData = {};
        let hospitalId = this.current_user_type == "Patient" ? localStorage.getItem('patient_hospital_id') : this.hospital_uuid;
        this.sharedService
          .initiateBooking(this.slotUuid, appointmentData, hospitalId)
          .subscribe(
            (data) => {
              $('#bookAppointment').modal('hide');
              this.pgwOptions = data;
              this.pgwOptions['amount'] = data['order']['amount'];
              this.openRazorpayCheckout();
            },
            (error) => {
              console.log(error);
              const status = error['status'];
              if (status == 400 || status == 409) {
                const message = error['error']['error'];
                const error_message = error['error']['error_message']
                if (message) {
                  this.notificationService.error(`${message}`, 'Med.Bot');
                } else if (error_message) {
                  this.notificationService.error(`${error_message}`, 'Med.Bot');
                } else {
                  this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
                }

              }
              else {
                this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
              }
            }
          );
      }

    } else {
      const appointmentData = {};
      let reSchedule = localStorage.getItem("reSchedule");
      if (reSchedule == null || reSchedule == undefined) {
        reSchedule = '0';
      }
      if (this.parentAppointmentId.length > 20) {
        if ((this.current_user_type == "Patient" || this.current_user_type == "DoctorAssistant") && reSchedule != '1') {
          let hospitalId = this.current_user_type == "Patient" ? localStorage.getItem('patient_hospital_id') : this.hospital_uuid;
          const patient_uuid = localStorage.getItem("user_type") == 'Patient' ? localStorage.getItem("current_user_uuid") : localStorage.getItem("patient_uuid");
          const formData = new FormData();
          if (hospitalId != undefined && hospitalId != null) {
            formData.append('hospital_uuid', hospitalId);
          }
          formData.append('patient_uuid', patient_uuid);
          this.sharedService
            .followUpAppointment(this.slotUuid, this.parentAppointmentId, formData)
            .subscribe(
              (data) => {
                const id = data['patient_appointment']['uuid'];
                $('#bookAppointment').modal('hide');
                this.ngZone
                  .run(() => this.router.navigateByUrl(`/patient/booked/${id}`))
                  .then();
              },
              (error) => {
                console.log(error);
                const status = error['status'];
                if (status == 400) {
                  const message = error['error']['error']['message'];
                  if (message) {
                    this.notificationService.error(`${message}`, 'Med.Bot');
                  } else {
                    this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
                  }

                }
                else {
                  this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
                }
              }
            );
        }
        else {
          const patient_uuid = localStorage.getItem("user_type") == 'Patient' ?
            localStorage.getItem("current_user_uuid") : localStorage.getItem("patient_uuid");
          let hospital_uuid = localStorage.getItem("user_type") == 'Patient' ?
            localStorage.getItem("patient_hospital_id") : this.hospital_uuid;
          // hospital_uuid = (hospital_uuid!=null||hospital_uuid!=undefined)?hospital_uuid:'';
          const formData = new FormData();
          formData.append('parent_appointment_slot', localStorage.getItem("parentApptSlot"));
          formData.append('patient', patient_uuid);
          if (hospital_uuid != undefined && hospital_uuid != null) {
            formData.append('hospital_uuid', hospital_uuid);
          }

          if (reSchedule != '' && reSchedule !== undefined && reSchedule != null) {
            this.sharedService
              .reScheduledAppointmentForPatient(this.slotUuid, this.parentAppointmentId, formData)
              .subscribe(
                (data) => {
                  $('#bookAppointment').modal('hide');
                  this.ngZone
                    .run(() => this.router.navigateByUrl(`/booking-status/${data['consultation_uuid']}`))
                    .then();
                },
                (error) => {
                  console.log(error);
                  const status = error['status'];
                  if (status == 400) {
                    const message = error['error']['error'];
                    if (message) {
                      this.notificationService.error(`${message}`, 'Med.Bot');
                    } else {
                      this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
                    }

                  }
                  else {
                    this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
                  }
                }
              );
            localStorage.removeItem("patient_uuid");
            localStorage.removeItem("reSchedule");
            localStorage.removeItem("parentApptSlot");
          }

        }


      }

    }
  }
  openRazorpayCheckout() {
    this.isLoading = true;
    this.pgwOptions['modal'] = {
      ondismiss: () => {
        this.isLoading = false;
        this.notificationService.error(
          'Payment aborted. Please try again',
          'Med.Bot'
        );
      },
    };
    this.pgwOptions['handler'] = (response) => {
      this.sendPaymentDetails(response);
    };
    const options = this.pgwOptions;
    const rzp = new Razorpay(options);
    rzp.open();
  }

  sendPaymentDetails(response) {
    const orderId = this.pgwOptions['order']['id'];
    this.paymentDetails['razorpay_order_id'] = orderId;
    this.paymentDetails['razorpay_payment_id'] = response['razorpay_payment_id'];
    let hospitalId = this.current_user_type == "Patient" ? localStorage.getItem('patient_hospital_id') : '';

    const formData = new FormData();
    if ((hospitalId == '' || hospitalId == undefined || hospitalId == null) && this.current_user_type == "Patient") {
      formData.append('patient_type', 'Public');
    }
    formData.append('razorpay_order_id', this.paymentDetails['razorpay_order_id']);
    formData.append('razorpay_payment_id', this.paymentDetails['razorpay_payment_id']);
    this.sharedService
      .completeBooking(this.slotUuid, formData)
      .subscribe(
        (data) => {
          const id = data['consultation_uuid'];
          this.ngZone
            .run(() => this.router.navigateByUrl(`/patient/booked/${id}`))
            .then();
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          this.bookingCompletionError = true;
          if (status == 400 || status == 409) {
            const message = error['error']['error'];
            const error_message = error['error']['error_message']
            if (message) {
              this.errorValue.push({ value: `Payment failed : ${message}` });
              this.notificationService.error(`${message}`, 'Med.Bot');
            } else if (error_message) {
              this.notificationService.error(`${error_message}`, 'Med.Bot');
              this.errorValue.push({ value: `Payment failed : ${error_message}` });
            } else {
              this.errorValue.push({ value: `Payment failed : ${error['statusText']}` });
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }

          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          this.isLoading = false;
        }
      );
  }

  getConsentDocumnetData() {
    this.patientService.getConsentDocumnet().subscribe(
      (data) => {
        this.contentText = data['content_html'];
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  fullDetails() {
    $('#termsModal').modal('show');
  }
  back() {
    this.location.back();
  }
}
