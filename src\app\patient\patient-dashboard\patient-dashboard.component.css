body {
    font-family: 'Varela Round', sans-serif;
}

.modal-confirm {
    color: #636363;
    width: 400px;
    margin: 30px auto;
}

.modal-confirm .modal-content {
    padding: 20px;
    border-radius: 5px;
    border: none;
    text-align: center;
    font-size: 14px;
}

.modal-confirm .modal-header {
    border-bottom: none;
    position: relative;
}

.modal-confirm h4 {
    text-align: center;
    font-size: 26px;
    margin: 30px 0 -10px;
}

.modal-confirm .close {
    position: absolute;
    top: -5px;
    right: -2px;
}

.modal-confirm .modal-body {
    color: #999;
}

.modal-confirm .modal-footer {
    border: none;
    text-align: center;
    border-radius: 5px;
    font-size: 13px;
    padding: 10px 15px 25px;
    margin: 0px 34px 0px;
}

.modal-confirm .modal-footer a {
    color: #999;
}

.modal-confirm .icon-box {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    border-radius: 50%;
    z-index: 9;
    text-align: center;
    border: 3px solid #f15e5e;
}

.modal-confirm .icon-box i {
    color: #f15e5e;
    font-size: 46px;
    display: inline-block;
    margin-top: 13px;
}

.modal-confirm .btn {
    color: #fff;
    border-radius: 4px;
    background: #60c7c1;
    text-decoration: none;
    transition: all 0.4s;
    line-height: normal;
    min-width: 120px;
    border: none;
    min-height: 40px;
    border-radius: 3px;
    margin: 0 5px;
    outline: none !important;
}

.modal-confirm .btn-info {
    background: #c1c1c1;
}

.modal-confirm .btn-info:hover,
.modal-confirm .btn-info:focus {
    background: #a8a8a8;
}

.modal-confirm .btn-danger {
    background: #f15e5e;
}

.modal-confirm .btn-danger:hover,
.modal-confirm .btn-danger:focus {
    background: #ee3535;
}

.trigger-btn {
    display: inline-block;
    margin: 100px auto;
}

.apointment-cancel-btn {
    background-color: #FF6347;
    color: #fff;
}

.appointmentList-no-data {
    color: #000000;
}

.disabled {
    background-color: #FF6347 !important;
    color: #fff !important;
    pointer-events: none !important;
}

.disabled-pagination {
    color: darkgray !important;
    pointer-events: none !important;
}

.btn-height {
    font-size: 16px;
    font-weight: 600;
}

.center-screen {
    position: absolute;
    width: 300px;
    height: 200px;
    z-index: 15;
    top: 50%;
    left: 50%;
    margin: -100px 0 0 -150px;
}

.dashboard-font-size {
    font-size: 20px;
    font-weight: 500;
    border-bottom-width: 3px;
    color: #20c0f3;
    border-bottom-style: solid;
}

.dashboard-title {
    font-size: 1.5rem;
    font-weight: 500;
}
.report-title{
  font-size: 1.3rem;
  font-weight: 500;
}

.welcome-bar {
    font-size: 24px;
    border-radius: 4px;
    font-weight: 500;
}

.activity-btn {
    font-size: 20px;
    border-radius: 1.25rem;
    font-weight: 500;
}

.modal-dialogBox {
    overflow-y: initial !important;
    min-width: 1000px;
}

.center-alinement {
    margin-left: 40%;
    margin-right: 30%;
}

.model-header-alinement {
    font-size: 24px !important;
    ;
    margin: 25px 79px 0px 0px !important;
    ;
}

#diagnostic-report {
    width: 100%;
}

.fas,
.fa-upload,
.fa-file {
    color: #fff;
    cursor: pointer;
    font-size: 16px !important;
}

.change-photo-btn {

}

h3 {
    font-size: 24px !important;
    font-weight: 500 !important;
}

@media screen and (max-width: 1300px) {
    .btn-height {
        margin-top: 10px;
        font-size: 16px;
        font-weight: 600;
    }
}

.btn-info.disabled,
.btn-info:disabled {
    color: #fff;
    background-color: darkgray !important;
    border-color: darkgray !important;
}

.nm-size {
    font-size: 1em !important;
}

.fa-upload.ic {
    color: #fff;
    text-align: left;
}

.file-shown {
    padding-left: 10px;
    color: #999;
    font-size: 50px;
}

.fifteen_chars {
    display: block;
    white-space: nowrap;
    width: 12em;
    overflow: hidden;
    text-overflow: ellipsis;
}

::ng-deep .ng-select .ng-select-container {
    min-height: 45px !important;
}

::ng-deep .ng-select.ng-select-single .ng-select-container {
    height: 20px !important;
}

#hideMedicalReports {
    background-color: #138496;
}
.filter-btn{
  margin-top: 2.1rem!important;
}
.fa {
  font-size: 16px !important;
}


@media only screen and (max-width: 600px) {
  .history_margin{
    margin-top: 5rem!important;
  }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .history_margin{
    margin-top: 5rem!important;
  }
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .history_margin{
    margin-top: 5rem!important;
  }
  .pager_position{

  }
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {

  .pager_position{
    margin-top: -72px;
  }
  .tab_pager_position{
    float:  right !important;
  }
  .history_margin{
    margin-top: 5rem!important;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .history_margin{
    margin-top: 5rem!important;
  }
  .pager_position{
    margin-top: -72px;
  }
  .tab_pager_position{
    float:  right !important;
  }

}
