<div *ngIf="isLoading">
  <app-loading-spinner></app-loading-spinner>
</div>
<div *ngIf="!isLoading">
  <div class="col-md-12 card">
    <div class="row welcome-bar">
      <div class="col-md-8">
        <h3 class="mt-2 p-2">
          Welcome <strong>{{ userProfile.username }}</strong>&nbsp;
        </h3>
      </div>
    </div>
  </div>
  <div>
    <h3 class="mt-2 p-2">Your Activities</h3>
    <div class="mt-2 p-2">
      <div class="mt-2">
        <button class="btn btn-info btn-height" id="BookAppointment" (click)="searchDoctor()">
          <i class="fas fa-book"></i>&nbsp;Book Appointment
        </button>
        <button class="btn btn-info mx-2 btn-height" (click)="consultNow()" id="ConsoultNow"
          *ngIf="checkPatientIsPublic()">
          <i class="fas fa-book-medical"></i>&nbsp;Consult Now
        </button>
        <button class="btn btn-info mx-2 btn-height" (click)="consultNow()" id="ConsoultNow"
          *ngIf="!checkPatientIsPublic()">
          <i class="fas fa-book-medical"></i>&nbsp;Emergency Consult
        </button>
        <button class="btn btn-info mx-2 btn-height" *ngIf="!showReports" (click)="showMedicalReports(true)"
          id="medicalReports">
          <i class="fa fa-upload"></i>&nbsp; Medical Reports
        </button>
        <button class="btn btn-info mx-2 btn-height" *ngIf="showReports" (click)="showMedicalReports(false)"
          id="hideMedicalReports">
          <i class="fa fa-upload"></i>&nbsp; Medical Reports
        </button>
      </div>
    </div>

    <!-- message Start -->
    <div class="tab-content pt-0">
      <h4 class="dashboard-title mb-2" *ngIf="messageData.length > 0">
        New Message
      </h4>
      <div class="col-md-12 float-right">
        <div class="float-right">
          <nav aria-label="Page navigation example" *ngIf="this.messageTotalPage > 1">
            <ul class="pagination">
              <li class="page-item" (click)="messageFirstPageList()"
                [ngClass]="{ 'disabled-pagination': messageCurrentPage === 1 }">
                <a class="page-link">&lt;&lt;</a>
              </li>
              <li class="page-item" (click)="messagePreviousPageList()"
                [ngClass]="{ 'disabled-pagination': messageCurrentPage === 1 }">
                <a class="page-link">&lt;</a>
              </li>
              <li class="page-item">
                <a class="page-link">page &nbsp;{{ messageCurrentPage }}&nbsp;of&nbsp;{{
                  messageTotalPage
                  }}</a>
              </li>
              <li class="page-item" (click)="messageNextPageList()" [ngClass]="{
                  'disabled-pagination': messageCurrentPage === messageTotalPage
                }">
                <a class="page-link">&gt;</a>
              </li>
              <li class="page-item" (click)="messageLastPageList()" [ngClass]="{
                  'disabled-pagination': messageCurrentPage === messageTotalPage
                }">
                <a class="page-link">&gt;&gt;</a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
      <div class="card card-table mb-0" *ngIf="messageData.length > 0">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover table-center mb-0">
              <thead>
                <tr>
                  <th>No</th>
                  <th>Doctor Name</th>
                  <th>Action</th>
                  <th></th>
                  <th></th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let msg of messageData; let i = index">
                  <td>{{ i + messageSerialNumber + 1 }}</td>
                  <td>{{ msg["consultation"]["doctor_json"]["username"] }}</td>
                  <td class="fifteen_chars">{{ msg.text }}</td>
                  <td>
                    <button class="btn btn-primary btn-sm btn-msg" (click)="viewMessage(msg['text'])"
                      data-toggle="modal" data-target="#viewMessageModal">
                      View Message
                    </button>
                  </td>
                  <td></td>
                  <td></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>


      <div class="float-right mt-3">
        <nav aria-label="Page navigation example" *ngIf="this.messageTotalPage > 1">
          <ul class="pagination">
            <li class="page-item" (click)="messageFirstPageList()"
              [ngClass]="{ 'disabled-pagination': messageCurrentPage === 1 }">
              <a class="page-link">&lt;&lt;</a>
            </li>
            <li class="page-item" (click)="messagePreviousPageList()"
              [ngClass]="{ 'disabled-pagination': messageCurrentPage === 1 }">
              <a class="page-link">&lt;</a>
            </li>
            <li class="page-item">
              <a class="page-link">page &nbsp;{{ messageCurrentPage }}&nbsp;of&nbsp;{{
                messageTotalPage
                }}</a>
            </li>
            <li class="page-item" (click)="messageNextPageList()" [ngClass]="{
                'disabled-pagination': messageCurrentPage === messageTotalPage
              }">
              <a class="page-link">&gt;</a>
            </li>
            <li class="page-item" (click)="messageLastPageList()" [ngClass]="{
                'disabled-pagination': messageCurrentPage === messageTotalPage
              }">
              <a class="page-link">&gt;&gt;</a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
    <!-- message End -->
    <div *ngIf="!showReports" class="mt-2 p-2">
      <!-- Appointment Start -->
      <div class="tab-content  pt-0">
        <h4 class="dashboard-title mb-3">Appointment</h4>
        <div class="appointment-tab">
          <app-appointment-list></app-appointment-list>
        </div>
      </div>
      <!-- Appointment End -->
      <!-- Consultation History Start -->
      <div class="tab-content history_margin pt-0">
        <app-consultation-list></app-consultation-list>
      </div>
      <!-- Consultation History End -->

      <!-- Hospital Start -->
      <div class="tab-content pt-0 mb-1">
        <h4 class="dashboard-title mb-2" *ngIf="hospitalAppoitnment.length > 0">
          Hospital Appointment
        </h4>
        <div class="card card-table mb-0" *ngIf="hospitalAppoitnment.length > 0">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover table-center mb-0">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Date</th>
                    <th>Time</th>
                    <!-- <th>Follow-up Review date</th> -->
                    <th>View</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let consult of consultations; let i = index">
                    <td>
                      <h2 class="table-avatar">
                        <a class="avatar avatar-sm mr-2">
                          <img class="avatar-img rounded-circle" src="assets/img/doctors/doctor-thumb-02.png"
                            alt="User Image" />
                        </a>
                        <a>{{ consultations[i].doctor_json.user.username
                          }}<span>{{ consultations[i].dotor_json }}</span></a>
                      </h2>
                    </td>
                    <td>{{ consult.start_datetime | date: "mediumDate" }}</td>
                    <td>{{ consult.start_datetime | date: "hh:mm a" }}</td>
                    <!-- <td>14 Nov 2019</td> -->
                    <button class="btn btn-info ml-2 mt-3" (click)="viewPrescription(consult.uuid)">
                      Consult
                    </button>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>


      </div>
    </div>
    <div *ngIf="showReports" class="mt-2 p-2">
      <div class="tab-content mt-2 pt-0">
        <h4 class="dashboard-title mb-2 text-center">Medical Reports</h4>
        <div class="card">
          <div class="card-body">

            <div class="col-md-2">
              <h4 class="report-title mb-2">Upload Reports&nbsp;</h4>
            </div>
            <div class="row mb-5 mt-2">

              <div class="col-md-2 mx-3">
                <ng-select id="reportType" [items]="reportTypes" [formControl]="selectedDiagnosticReportName"
                  [clearable]="false" [searchable]="false" bindLabel="testType" bindValue="id" placeholder="Report Type"
                  (change)="getReportType($event)">
                </ng-select>
              </div>
              <div class="col-md-2">
                <input type="text" placeholder="Report Generated On" [maxDate]="maxDate" [minDate]="minDate"
                  onkeydown="return false" class="form-control" [(ngModel)]="reportDate" bsDatepicker [bsConfig]="{
                      showWeekNumbers: false,
                      isAnimated: true,
                      dateInputFormat: 'DD-MM-YYYY'
                    }" />
              </div>
              <div class="col-md-2">
                <div class="change-photo-btn" style="
                      padding-left: 5px !important;
                      margin-left: 0px;
                      font-family: sans-serif;
                      width: 100%;
                    ">
                  <span><i class="fa fa-upload ic" style="font-size: 16px !important">&nbsp;Choose File</i></span>
                  <input type="file" class="upload" id="medical-report" (change)="medicalReports($event)"
                    accept=".jpg, .jpeg,.pdf" />
                </div>
                <small *ngIf="reportName">&nbsp;{{ reportName }}</small>
              </div>
              <div class="col-md-3">
                <button class="btn btn-primary" *ngIf="!showUploading"
                  [disabled]="!selectedDiagnosticReportName || !reportFile" (click)="saveMedicalReport()">
                  Save
                </button>
                <button class="btn btn-primary" *ngIf="showUploading">
                  Uploading
                </button>
              </div>
            </div>


            <div class="tab-content pt-0">
              <div class="col-md-12 mt-1">
                <form [formGroup]="searchForm">
                  <div class="row">
                    <div class="col-md-2">
                      <h4 class="report-title mb-2">Available Reports&nbsp;</h4>
                    </div>
                    <ng-container>
                      <div class="col-md-2">
                        <!-- <label>Report Type</label> -->
                        <ng-select id="reportType" [items]="reportTypes" formControlName="reportType"
                          [clearable]="false" [searchable]="false" bindLabel="testType" bindValue="id"
                          placeholder="Report Type">
                        </ng-select>
                      </div>
                      <div class="col-md-2">
                        <div class="form-group mb-0">
                          <!-- <label translate>Report Generated On</label> -->
                          <input [maxDate]="maxDate" [minDate]="minDate" placeholder="Report generated on"
                            onkeydown="return false" class="form-control" formControlName="reportGeneratedOn"
                            bsDatepicker [bsConfig]="{
                              showWeekNumbers: false,
                              isAnimated: true,
                              dateInputFormat: 'DD-MM-YYYY'
                            }" />
                        </div>
                      </div>
                      <div class="col-md-2">
                        <div class="form-group mb-0">
                          <!-- <label translate>Report Updated On</label> -->
                          <input [maxDate]="maxDate" [minDate]="minDate" placeholder="Report Updated on"
                            onkeydown="return false" class="form-control" bsDatepicker formControlName="reportUpdatedOn"
                            [bsConfig]="{
                              showWeekNumbers: false,
                              isAnimated: true,
                              dateInputFormat: 'DD-MM-YYYY'
                            }" />
                        </div>
                      </div>
                      <div class="col-md-2">
                        <button class="btn btn-primary" [disabled]="!searchForm.touched && !searchForm.dirty"
                          (click)="findReports()">
                          Find
                        </button>
                        <button class="btn btn-secondary ml-4" [disabled]="!searchForm.touched && !searchForm.dirty"
                          (click)="resetSearchForm()">
                          Clear
                        </button>
                      </div>
                    </ng-container>
                    <div [ngClass]="{
                        'col-md-2': shwoFilter,
                        'col-md-10 float-right': shwoFilter
                      }">
                      <!-- <i class="fa fa-search float-right" (click)="showFilterFields()"></i> -->
                    </div>
                  </div>
                </form>
              </div>
              <div class="col-md-12 float-right">
                <div class="float-right">
                  <nav aria-label="Page navigation example" *ngIf="this.reportTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="reportFirstPageList()" [ngClass]="{
                          'disabled-pagination': reportCurrentPage === 1
                        }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="reportPreviousPageList()" [ngClass]="{
                          'disabled-pagination': reportCurrentPage === 1
                        }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ reportCurrentPage }}&nbsp;of&nbsp;{{
                          reportTotalPage
                          }}</a>
                      </li>
                      <li class="page-item" (click)="reportNextPageList()" [ngClass]="{
                          'disabled-pagination':
                            reportCurrentPage === reportTotalPage
                        }">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="reportLastPageList()" [ngClass]="{
                          'disabled-pagination':
                            reportCurrentPage === reportTotalPage
                        }">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
              <div class="card card-table mb-0 mt-3" *ngIf="reportFiles.length > 0">
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-hover table-center mb-0">
                      <thead>
                        <tr>
                          <th>No</th>
                          <th>Report Type</th>
                          <th>File Name</th>
                          <th>Uploaded On</th>
                          <th>Reports Generated On</th>
                          <th>Action</th>
                          <th></th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let file of reportFiles; let i = index">
                          <td>{{ i + reportSerialNumber + 1 }}</td>
                          <td>{{ file.medical_report_type }}</td>
                          <td class="fifteen_chars">{{ file.file_name }}</td>
                          <td>{{ file.created_at | date: "mediumDate" }}</td>
                          <td>
                            {{ file.report_generated_on | date: "mediumDate" }}
                          </td>
                          <td>
                            <button class="btn btn-primary btn-sm btn-msg" (click)="viewFile(file['file'])"
                              data-toggle="modal">
                              View Report
                            </button>
                          </td>
                          <td></td>
                          <td></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div class="text-center mb-2 p-2 mt-2">
                <span class="appointmentList-no-data" *ngIf="reportFiles.length === 0">No Files</span>
              </div>
              <div class="col-md-12 mt-3">
                <div class="float-right">
                  <nav aria-label="Page navigation example" *ngIf="this.reportTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="reportFirstPageList()" [ngClass]="{
                          'disabled-pagination': reportCurrentPage === 1
                        }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="reportPreviousPageList()" [ngClass]="{
                          'disabled-pagination': reportCurrentPage === 1
                        }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ reportCurrentPage }}&nbsp;of&nbsp;{{
                          reportTotalPage
                          }}</a>
                      </li>
                      <li class="page-item" (click)="reportNextPageList()" [ngClass]="{
                          'disabled-pagination':
                            reportCurrentPage === reportTotalPage
                        }">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="reportLastPageList()" [ngClass]="{
                          'disabled-pagination':
                            reportCurrentPage === reportTotalPage
                        }">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>

            </div>


          </div>
        </div>
      </div>
      <br />
    </div>
  </div>
  <!-- Hospital End -->

  <!-- Modal HTML -->
  <div class="modal fade" id="cancelModelConfirmation">
    <div class="modal-dialog modal-confirm">
      <div class="modal-content">
        <div class="modal-header">
          <div class="icon-box">
            <i class="material-icons">&#xE5CD;</i>
          </div>
          <h4 class="modal-title model-header-alinement">Are you sure?</h4>
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
            &times;
          </button>
        </div>
        <div class="modal-body">
          <p>Do you really want to cancel this appointment ?</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-info" data-dismiss="modal" id="cancel-stop">
            No
          </button>
          <button type="button" class="btn btn-danger" (click)="confirmCancel()" id="proceed-cancel">
            Yes
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- Button trigger modal -->
  <!-- Modal -->
  <div class="modal fade" id="cancelModel" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialogBox" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title center-alinement" id="exampleModalLabel">
            Cancel Appointment
          </h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-hover table-center mb-0">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Time</th>
                  <th>Doctor</th>

                  <!-- <th>Booking Date</th> -->
                  <th>Action</th>
                  <!--<th>Follow Up</th>
              <th>Status</th> -->
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of availableAppointmenList; let i = index">
                  <td>{{ data.start_datetime | date: "mediumDate" }}</td>
                  <td>
                    {{ data.start_datetime | date: "hh:mm a" }} to
                    {{ data.end_datetime | date: "hh:mm a" }}
                  </td>
                  <td>
                    <h2 class="table-avatar">
                      <a class="avatar avatar-sm mr-2">
                        <img class="avatar-img rounded-circle" [src]="
                            data.doctorProfilePic ==null
                              ? doctorImageUrl
                              : data.doctorProfilePic
                          " alt="User Image" />
                      </a>
                      <a>{{ data.doctorName ? data.doctorName : null }}</a>
                    </h2>
                  </td>

                  <td>
                    <input type="checkbox" id="CancelAppointment" (change)="
                        getCancelAppointmentId(
                          data.doctor_appointment_uuid,
                          $event
                        )
                      " [ngClass]="{ disabled: data.status !== 'Booked' }" />

                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer text-center">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">
            Close
          </button>
          <button type="button" class="btn btn-danger" [disabled]="cancelAppointmentData.length > 0 ? false : true"
            (click)="cancelConfirmation('id')" id="cancel-confirmation">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal fade" id="reportUploadModal" tabindex="-1" role="dialog" aria-labelledby="reportUploadModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialogBox" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="reportUploadModalLabel" style="margin: 0px 343px">
            Upload Diagnostic. Report
          </h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-hover table-center mb-0">
              <thead>
                <tr class="text-center">
                  <th>Report Type</th>
                  <th>Date</th>
                  <th>Time</th>
                  <th>Doctor</th>

                  <!-- <th>Booking Date</th> -->
                  <th>Action</th>
                  <!--<th>Follow Up</th>
              <th>Status</th> -->
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of availableAppointmenList; let i = index">
                  <td>
                    <ng-select id="diagnosticReportChoices-" [items]="reportTypes" [clearable]="false"
                      [searchable]="false" bindLabel="testType" bindValue="id" placeholder="Diagnostic Report Choices"
                      (change)="getReportType($event, i, data.uuid)">
                    </ng-select>
                  </td>
                  <td>{{ data.start_datetime | date: "mediumDate" }}</td>
                  <td>
                    {{ data.start_datetime | date: "hh:mm a" }} to
                    {{ data.end_datetime | date: "hh:mm a" }}
                  </td>
                  <td>
                    <h2 class="table-avatar">
                      <a class="avatar avatar-sm mr-2">
                        <img class="avatar-img rounded-circle" [src]="
                            data.doctor_user_json
                              ? data.doctor_user_json.profile_picture
                              : doctorImageUrl
                          " alt="User Image" />
                      </a>
                      <a>{{
                        data.doctor_user_json
                        ? data.doctor_user_json.username
                        : null
                        }}</a>
                    </h2>
                  </td>
                  <td>
                    <div class="change-photo-btn">
                      <span><i class="fa fa-upload"></i>
                        {{
                        fileUpload
                        ? ("Upload " | translate)
                        : i == selectedIndex
                        ? ("Uploading" | translate)
                        : ("Upload" | translate)
                        }}</span>
                      <input type="file" class="upload" id="diagnostic-report" (change)="uploadDiagnosticReport($event)"
                        accept=".jpg, .jpeg,.pdf" [disabled]="i == selectedIndex ? false : true" />
                    </div>
                  </td>

                  <td>

                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer text-center">
          <button type="button" class="btn btn-danger" data-dismiss="modal" id="diagnostic-report-close">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal -->
  <div class="modal fade" id="viewMessageModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Message</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <P>{{ doctorNotes }}</P>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">
            Close
          </button>
          <button type="button" class="btn btn-primary">Mark As Read</button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal fade" id="upload-report" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Report</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-4">
              <ng-select id="reportType" [items]="reportTypes" [formControl]="selectedDiagnosticReportName"
                [clearable]="false" [searchable]="false" bindLabel="testType" bindValue="id" placeholder="Report Type"
                (change)="getReportType($event)">
              </ng-select>
            </div>
            <div class="col-md-4 mb-1">
              <input type="text" [maxDate]="maxDate" [minDate]="minDate" placeholder="Report Generated On"
                onkeydown="return false" class="form-control" [(ngModel)]="reportDate" bsDatepicker [bsConfig]="{
                  showWeekNumbers: false,
                  isAnimated: true,
                  dateInputFormat: 'DD-MM-YYYY'
                }" />
            </div>
            <div class="col-md-4 mb-1">
              <div class="change-photo-btn" style="
                  padding-left: 5px !important;
                  margin-left: 0px;
                  width: 138px;
                  font-family: sans-serif;
                ">
                <span><i class="fa fa-upload ic">&nbsp;Choose File</i></span>
                <input type="file" class="upload" id="medical-report" (change)="medicalReports($event)"
                  accept=".jpg, .jpeg,.pdf" />
              </div>
              <small *ngIf="reportName">&nbsp;{{ reportName }}</small>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="col-md-12 text-center">
            <button class="btn btn-primary" (click)="saveMedicalReport()"
              [disabled]="!selectedDiagnosticReportName || !reportFile">
              Save
            </button>
            <button type="button" class="btn btn-secondary ml-2" data-dismiss="modal">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>