import { Component, OnInit } from '@angular/core';
import { PatientService } from '../patient.service';
import { AuthService } from '../../auth/auth.service';
import { ToastrService } from 'ngx-toastr';
import * as moment from 'moment';
import { saveAs } from 'file-saver';
import { SharedService } from '../../shared/shared.service';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
declare var $: any;
import * as Settings from '../../config/settings';
import jspdf from 'jspdf';
// import html2canvas from 'html2canvas'
import { FormControl, FormGroup } from '@angular/forms';
@Component({
  selector: 'app-patient-dashboard',
  templateUrl: './patient-dashboard.component.html',
  styleUrls: ['./patient-dashboard.component.css'],
})
export class PatientDashboardComponent implements OnInit {
  reportFile: any;
  appointmentSlotId: any;
  availableAppointmenList: Array<any> = [];
  age: number;
  disabledUploadBtn: boolean;
  fileUpload: boolean;
  fileSizeLarge: boolean;
  appointmentId: any = null;
  public appointmentList: any = [];
  public allAppointmentData: any = [];
  public doctorImageUrl: string;
  public appointmentSingleData = {
    status: null,
  };
  public currentPage = 1;
  public totalPage: number;
  isLoading = false;
  cancelAppointmentData: any = [];
  public currentDate = moment();
  public realTime = moment(this.currentDate).format(
    'DD/MM/YYYY hh:mm a'
  );
  public sub = moment(this.realTime, 'HH:mm').subtract(60, 'minutes');
  public userProfile: any = {};
  public patientProfilePicture = 'assets/img/doctors/doctor-thumb-02.png';
  reportTypes = [
    { id: 'Pathology Test', testType: 'Pathology Test' },
    { id: 'Prescription', testType: 'Prescription' },
    { id: 'X-Ray', testType: 'X-Ray' },
    { id: 'Ultra Sound Scan', testType: 'Ultra Sound Scan' },
    { id: 'CT Scan', testType: 'CT Scan' },
    { id: 'MRI Scan', testType: 'MRI Scan' },
    { id: 'Prescription', testType: 'Prescription' },
    { id: 'Other', testType: 'Other' },
  ];
  public selectedDiagnosticReportName = new FormControl(null);
  selectedIndex = null;
  patientUuid: string;
  messageData: any = [];
  messageList: any = [];
  prescriptionFile: any;
  appointmentApiSearchCall: boolean;
  public upcomingAppointmentCurrentPage: number;
  public upcomingAppointmentTotalPage: number;
  public messageCurrentPage: number;
  public messageTotalPage: number;
  public hospitalAppoitnment = [];
  cancelBtnDisabled: boolean;
  file: jspdf;
  contentDataURL: string;
  public messageSerialNumber = 0;
  doctorNotes: string;
  messageLoading: boolean;
  public minTime = Settings.consultaion_left_margin;

  todayStartedAppointments: any = [];
  todayMissedAppointments: any = [];
  uploadReport: boolean;
  showReports: boolean;
  reportName: any;
  reportFiles: any = [];
  consultationUuid: any = null;
  reportDate: any = null;
  reportCurrentPage: number;
  reportSerialNumber: number = 0;
  reportTotalPage: number;
  queryParms: string;
  showAllDocuments: any;
  searchForm: FormGroup;
  maxDate: Date;
  minDate: any;
  shwoFilter: boolean = true;
  showUploading = false;
  cancelAppointmentId: string;
  isPublicPatient: boolean;
  userType: String = 'patient';
  constructor(
    private patientService: PatientService,
    private userService: AuthService,
    private notificationService: ToastrService,
    private sharedService: SharedService,
    private router: Router,
  ) { }
  ngOnInit(): void {
    this.fileUpload = true;
    this.isLoading = true;
    this.upcomingAppointmentCurrentPage = 1;
    this.upcomingAppointmentTotalPage = 0;
    this.doctorImageUrl = 'assets/img/doctors/doctor-thumb-02.png';
    this.searchFormCOntrol();
    this.maxDate = new Date();
    this.minDate = new Date();
    this.minDate.setDate(this.minDate.getDate() - 36500);
    this.userService.getUserDetail().subscribe(
      (data) => {
        this.userProfile = data;
        this.patientUuid = this.userProfile['uuid'];
        this.queryParms = `?patient=${this.patientUuid}&page=1`;
        this.getreportList(this.queryParms);

        const today = moment(this.currentDate).format('YYYY');
        const dateOfBirth = moment(this.userProfile['date_of_birth']).format(
          'YYYY'
        );
        if (this.userProfile['date_of_birth']) {
          // tslint:disable-next-line: radix
          this.age = parseInt(today) - parseInt(dateOfBirth);
        }
        const profilepicture = this.userProfile['profile_picture'];
        if (profilepicture) {
          this.patientProfilePicture = profilepicture;

        }

       if(this.userProfile['gender']==''||this.userProfile['gender']==undefined||this.userProfile['gender']==null
      ||this.userProfile['phone']==''||this.userProfile['phone']==undefined||this.userProfile['phone']==null
      ||this.userProfile['date_of_birth']==''||this.userProfile['date_of_birth']==undefined||this.userProfile['date_of_birth']==null
      ||this.userProfile['age']==''||this.userProfile['age']==undefined||this.userProfile['age']==null){
        localStorage.setItem('isProfileCompleted','false');
      }else{
        localStorage.setItem('isProfileCompleted','true');
      }
         
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
    this.getPatientAppointment(this.currentPage);
    //this.getDiagnosticReportData();
    // this.getmessageList(1);
    this.getAppointmentsBySatus();
    this.getUpcomingAppointmentList(1);
    setInterval(() => {
      const url = this.router.url;
      if (url === '/patient/dashboard') {
        this.appointmentButtonEnableAndDisable()
      }

    }, 60000);

  }

  checkPatientIsPublic() {
    const patient_hospital_id = localStorage.getItem('patient_hospital_id');
    if (patient_hospital_id != null) {
      this.isPublicPatient = false;
      localStorage.setItem('isPublicPatient','false');
    }
    else {
      this.isPublicPatient = true;
      localStorage.setItem('isPublicPatient','true');

    }
    return this.isPublicPatient;
  }

  getPatientAppointment(currentPage) {
    this.appointmentApiSearchCall = true;
    this.totalPage = 0;
    let searchList = [];
    searchList = this.sharedService.getsearchAppointmentByPage();
    if (searchList.length > 0) {
      for (const data of searchList) {
        if (data.page_number == currentPage) {
          this.appointmentApiSearchCall = false;
          this.totalPage = data['total_pages'];
          this.currentPage = data['page_number'];
          this.allAppointmentData = data['results'];
        }

      }
      if (!!this.appointmentApiSearchCall) {
        this.getAllAppointment(currentPage);
      }
    } else {
      this.getAllAppointment(currentPage);
    }
  }
  getAllAppointment(page) {
    this.patientService.getAllPatientAppointment(page).subscribe(
      (data) => {
        this.totalPage = data['total_pages'];
        this.currentPage = data['page_number'];
        this.allAppointmentData = data['results'];
        this.sharedService.searchAppointmentByPage(
          this.allAppointmentData,
          this.currentPage,
          null,
          this.totalPage
        );
      }, error => {
        console.log(error);
        this.isLoading = false;
        const status = error['status'];

      });
  }
  getUpcomingAppointmentList(currentPage) {
    this.upcomingAppointmentCurrentPage = currentPage;
    this.upcomingAppointmentTotalPage = 0;
    const today = moment(this.currentDate).format('YYYY-MM-DD');
    const fulfilment_status = 'Not Started';
    const searchParms = `?start_datetime=${today}&status=Booked&fulfilment_status=${fulfilment_status}&page=${currentPage}`;
    this.patientService.getPatientAppointment(searchParms).subscribe(
      (data) => {
        this.upcomingAppointmentTotalPage = data['total_pages'];
        this.upcomingAppointmentCurrentPage = data['page_number'];
        this.availableAppointmenList = [];
        const result = data['results'];
        const todayDateAndTime = moment(this.currentDate).format('YYYY-MM-DD');
        let index = 0;
        this.cancelBtnDisabled = true;
        for (const value of result) {
          const doctorName = value.doctor_user_json.username;
          const doctor_user_uuid = value.doctor_user_uuid;
          const doctorProfilePic = value.doctor_user_json.profile_picture;
          const startDate = moment(value.start_datetime).format('YYYY-MM-DD');
          const day = moment(value.start_datetime);
          const endTime = moment(value.end_datetime)
          let minutes = this.currentDate.diff(day, 'minutes');
          let maxTime = endTime.diff(day, 'minutes');
          if (startDate >= todayDateAndTime && (value.status === 'Booked' && value.fulfilment_status == 'Not Started')) {
            if (minutes > Settings.consultaion_cancel_margin) {
              this.cancelBtnDisabled = true;
            } else
              if (day > this.currentDate) {
                this.cancelBtnDisabled = false;
              }
            if (minutes > this.minTime && maxTime > minutes) {
              // tslint:disable-next-line: max-line-length
              this.availableAppointmenList.push({
                start_datetime: `${value.start_datetime}`, end_datetime: `${value.end_datetime}`, doctorName: `${doctorName}`, doctorProfilePic: `${doctorProfilePic}`, doctor_appointment_uuid: `${value.doctor_appointment_uuid}`,
                disable: false, status: `${value.status}`, cancelButtonStatus: this.cancelBtnDisabled, consultation_uuid: `${value.consultation_uuid}`, doctor_user_uuid: doctor_user_uuid
              });
            } else {
              // tslint:disable-next-line: max-line-length
              this.availableAppointmenList.push({
                start_datetime: `${value.start_datetime}`, end_datetime: `${value.end_datetime}`, doctorName: `${doctorName}`, doctorProfilePic: `${doctorProfilePic}`, doctor_appointment_uuid: `${value.doctor_appointment_uuid}`,
                disable: true, status: `${value.status}`, cancelButtonStatus: this.cancelBtnDisabled, consultation_uuid: `${value.consultation_uuid}`, doctor_user_uuid: doctor_user_uuid
              });

            }
          }
          index = index + 1;
        }
        this.isLoading = false;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        this.isLoading = false;
      }
    );
  }
  getProfileData(id, consultationid) {
    this.patientService.getdoctorProfileData(id).subscribe(
      (data) => {
        this.messageData.push({
          username: data['username'],
          consultation: consultationid,
        });
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  getCancelAppointmentId(id, event) {
    if (event.target.checked) {
      this.cancelAppointmentData.push(id);
    } else {
      this.cancelAppointmentData = this.cancelAppointmentData.filter(
        (obj) => obj !== id
      );
    }
  }
  cancelConfirmation(id, index, cancelButtonStatus) {
    if (!cancelButtonStatus) {
      this.appointmentSlotId = id;
      this.cancelAppointmentId = '#cancelAppointmentId' + index;
      $(`${this.cancelAppointmentId}`).prop('disabled', true);
      $('#cancelModel').modal('hide');
      $('#cancelModelConfirmation').modal('show');
    } else {
      this.notificationService.error('You can cancel before 10 minutes of your consultation time only ');
    }

  }

  confirmCancel() {
    $('#cancelModelConfirmation').modal('hide');
    this.notificationService.warning('Please wait appointment  cancel is processing', 'Med.Bot');
    const status = { status: 'Cancelled' };
    this.patientService
      .cancelAppointment(this.appointmentSlotId, status)
      .subscribe(
        (data) => {
          this.removeAppointmentData(this.appointmentSlotId);
          $('#cancelModelConfirmation').modal('hide');
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            const errMessage = error['error']['error_message']
            if (errMessage == 'The appointment has been already cancelled') {
              this.notificationService.error(`${errMessage}`, 'Med.Bot');
              this.removeAppointmentData(this.appointmentSlotId);
            } else if (errMessage) {
              this.notificationService.error(`${errMessage}`, 'Med.Bot');
              $(`${this.cancelAppointmentId}`).prop('disabled', false);
            }
            else {
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
  }
  removeAppointmentData(id) {
    this.availableAppointmenList = this.availableAppointmenList.filter(
      (obj) => obj.doctor_appointment_uuid !== id
    );
    this.notificationService.success('Appointment cancelled ', 'Med.Bot');
  }
  nextPageList() {
    this.currentPage = this.currentPage + 1;
    if (this.totalPage >= this.currentPage) {
      this.getPatientAppointment(this.currentPage);
    } else {
      this.currentPage = this.currentPage - 1;
    }
  }

  lastPageList() {
    this.getPatientAppointment(this.totalPage);
  }
  firstPageList() {
    this.currentPage = 1;
    this.getPatientAppointment(this.currentPage);
  }
  previousPageList() {
    this.currentPage = this.currentPage - 1;
    if (this.totalPage >= this.currentPage && this.currentPage > 0) {
      this.getPatientAppointment(this.currentPage);
    } else {
      this.currentPage = this.currentPage + 1;
    }
  }

  uploadDiagnosticReport(event) {
    const file = event.target.files;
    if (file.length > 0) {
      this.fileSizeLarge = false;
      const selectedFile = file[0];

      if (
        selectedFile.size < 2000000 &&
        (selectedFile.type === 'image/jpeg' ||
          selectedFile.type === 'image/jpg' ||
          selectedFile.type === 'image/pdf' ||
          selectedFile.type === 'application/pdf')
      ) {
        this.disabledUploadBtn = true;
        this.fileUpload = false;
        const report = {
          report_type: this.selectedDiagnosticReportName.value,
          appointment: this.appointmentId,
        };
        this.patientService.sendDiagnosticReport(selectedFile, report).subscribe(
          (data) => {
            this.fileUpload = true;
            $('#uploadFile').modal('hide');
            this.notificationService.success(
              'Diagnostic Report Updated',
              'Med.Bot'
            );
          },
          (error) => {
            this.fileUpload = true;
            this.disabledUploadBtn = false;
            console.log(error);
            const status = error['status'];
            if (status == 400) {
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
            else {
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          }
        );
      } else {
        this.fileSizeLarge = true;
      }
    } else {
      this.fileUpload = true;
      this.disabledUploadBtn = false;
      this.notificationService.error('Please select  file', 'Med.Bot');
    }
  }

  downloadPdf(url: string) {
    const pdfUrl = url;
    const pdfName = 'your_pdf_file';
    saveAs(pdfUrl, pdfName);
  }


  getReportType(event, appointmentId = null) {
    // this.selectedIndex = index;
    this.selectedDiagnosticReportName.setValue(event.testType);
  }
  viewPrescription(patient_uuid, doctor_uuid, consult_id) {
    this.isLoading = true;
    this.router.navigate(['/consultation-history/', patient_uuid, doctor_uuid, consult_id]);
  }
  showMessageData(data) {
  }

  onConsult(apptId, doctor_user_uuid) {
    localStorage.setItem('doctor_id', doctor_user_uuid);
    this.patientService.joinConsultation(apptId).subscribe((data) => {
      this.router.navigate(['/patient/consultation'], {
        queryParams: { consultationId: apptId },
      });
      this.router.events.subscribe((val) => {
        const nvigationEnd = val instanceof NavigationEnd;
        if (!!nvigationEnd) {
          location.reload();
        }
      });
    });
  }
  bookAppointment(doctorid, practiceLocationId, parentConsultationUuid) {
    this.router.navigate([
      '/patient/appointment/',
      doctorid,
      practiceLocationId, parentConsultationUuid
    ]);
  }
  searchDoctor() {
    const query = 'consult_now=false'
    const page = 1;
    this.router.navigate(['/patient/search/', page, query]);
  }
  consultNow() {
    this.sharedService.removeSearchList();
    const query = 'consult_now=true';
    const page = 1;
    this.router.navigate(['/patient/search/', page, query]);
  }

  upcomingAppointmentNextPageList() {
    this.upcomingAppointmentCurrentPage = this.upcomingAppointmentCurrentPage + 1;
    if (this.upcomingAppointmentTotalPage >= this.upcomingAppointmentCurrentPage) {
      this.getUpcomingAppointmentList(this.upcomingAppointmentCurrentPage);
    } else {
      this.upcomingAppointmentCurrentPage = this.upcomingAppointmentCurrentPage - 1;
    }
  }

  upcomingAppointmentLastPageList() {
    this.getUpcomingAppointmentList(this.upcomingAppointmentTotalPage);
  }
  upcomingAppointmentFirstPageList() {
    this.upcomingAppointmentCurrentPage = 1;
    this.getUpcomingAppointmentList(this.upcomingAppointmentCurrentPage);
  }
  upcomingAppointmentPreviousPageList() {
    this.upcomingAppointmentCurrentPage = this.upcomingAppointmentCurrentPage - 1;
    if (this.upcomingAppointmentTotalPage >= this.upcomingAppointmentCurrentPage && this.upcomingAppointmentCurrentPage > 0) {
      this.getUpcomingAppointmentList(this.upcomingAppointmentCurrentPage);
    } else {
      this.upcomingAppointmentCurrentPage = this.upcomingAppointmentCurrentPage + 1;
    }
  }

  messageNextPageList() {
    this.messageCurrentPage = this.messageCurrentPage + 1;
    if (this.messageTotalPage >= this.messageCurrentPage) {
      this.getmessageList(this.messageCurrentPage);
      this.messageSerialNumber = (this.messageCurrentPage - 1) * 10;
    } else {
      this.messageCurrentPage = this.messageCurrentPage - 1;
    }
  }
  messageLastPageList() {
    this.messageSerialNumber = (this.messageTotalPage - 1) * 10;
    this.getmessageList(this.messageTotalPage);

  }
  messageFirstPageList() {
    this.messageCurrentPage = 1;
    this.messageSerialNumber = 0;
    this.getmessageList(this.messageCurrentPage);
  }
  messagePreviousPageList() {
    this.messageCurrentPage = this.messageCurrentPage - 1;
    if (this.messageTotalPage >= this.messageCurrentPage && this.messageCurrentPage > 0) {
      this.getmessageList(this.messageCurrentPage);
      this.messageSerialNumber = (this.messageCurrentPage - 1) * 10;
    } else {
      this.messageCurrentPage = this.messageCurrentPage + 1;
    }
  }
  getmessageList(page) {
    this.messageLoading = true;
    this.messageTotalPage = 0;
    this.patientService.getMessage().subscribe(
      (data) => {
        this.messageData = data['results'];
        this.messageTotalPage = data['total_pages'];
        this.messageCurrentPage = data['page_number'];
        this.messageLoading = false;

      },
      (error) => error => {
        this.messageLoading = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );

  }

  appointmentButtonEnableAndDisable() {
    const currentDate = moment();
    const result = this.availableAppointmenList;
    const todayDateAndTime = moment(currentDate).format('YYYY-MM-DD');
    let index = 0;
    this.availableAppointmenList = [];
    for (const value of result) {
      this.cancelBtnDisabled = true;
      const doctorName = value.doctorName;
      const doctor_user_uuid = value.doctor_user_uuid;
      const doctorProfilePic = value.doctorProfilePic;
      const startDate = moment(currentDate).format('YYYY-MM-DD');
      const day = moment(value.start_datetime);
      const endTime = moment(value.end_datetime)
      let minutes = currentDate.diff(day, 'minutes');
      let maxTime = endTime.diff(day, 'minutes');
      if (startDate >= todayDateAndTime && value.status === 'Booked') {
        if (minutes > Settings.consultaion_cancel_margin) {
          this.cancelBtnDisabled = true;
        } else
          if (day > this.currentDate) {
            this.cancelBtnDisabled = false;
          }
        if (minutes > this.minTime && maxTime > minutes) {
          // tslint:disable-next-line: max-line-length
          this.availableAppointmenList.push({
            start_datetime: `${value.start_datetime}`, end_datetime: `${value.end_datetime}`, doctorName: `${doctorName}`, doctorProfilePic: `${doctorProfilePic}`, doctor_appointment_uuid: `${value.doctor_appointment_uuid}`,
            disable: false, status: `${value.status}`, cancelButtonStatus: this.cancelBtnDisabled, consultation_uuid: `${value.consultation_uuid}`, doctor_user_uuid: doctor_user_uuid
          });
        } else {
          // tslint:disable-next-line: max-line-length
          this.availableAppointmenList.push({
            start_datetime: `${value.start_datetime}`, end_datetime: `${value.end_datetime}`, doctorName: `${doctorName}`, doctorProfilePic: `${doctorProfilePic}`, doctor_appointment_uuid: `${value.doctor_appointment_uuid}`,
            disable: true, status: `${value.status}`, cancelButtonStatus: this.cancelBtnDisabled, consultation_uuid: `${value.consultation_uuid}`, doctor_user_uuid: doctor_user_uuid
          });

        }
      }
      index = index + 1;
    }

  }

  viewMessage(notes) {
    this.doctorNotes = notes;
  }

  getAppointmentsBySatus() {
    const today = moment(this.currentDate).format('YYYY-MM-DD');
    const todayStartedSearchParms = `?start_datetime=${today}&end_datetime=${today}&fulfilment_status=Started`;
    const todayMissedSearchParms = `?start_datetime=${today}&end_datetime=${today}&fulfilment_status=Both Missed%2BPatient Missed%2BDoctor Missed`;
    this.patientService.getPatientAppointment(todayStartedSearchParms).subscribe(
      (data) => {
        this.todayStartedAppointments = data['results'];
      }, error => {
        console.log(error)
      });
    this.patientService.getPatientAppointment(todayMissedSearchParms).subscribe(
      (data) => {
        this.todayMissedAppointments = data['results'];
      }, error => {
        console.log(error)
      });
  }

  medicalReports(event) {
    this.reportFile = event.target.files[0];
    this.reportName = event.target.files[0]?.name;
  }

  showMedicalReports(status) {
    this.showReports = status;
  }

  saveMedicalReport() {
    this.showUploading = true;
    const file = this.reportFile;
    const type = this.selectedDiagnosticReportName.value;
    const data = { 'medical_report_type': type, 'appointment': this.consultationUuid, 'consultation': this.consultationUuid, 'report_generated_on': this.reportDate };
    this.patientService.postMedicalReport(file, data).subscribe(
      data => {
        this.showUploading = false;
        this.reportFiles.unshift(data);
        $('#upload-report').modal('hide');
        this.selectedDiagnosticReportName.setValue(null);
        this.reportDate = null;
        this.reportFile = null;
        this.reportName = null;
        this.notificationService.success('Report updated', 'Med.Bot')

      }, error => {
        this.notificationService.error('Internal server error', 'Med.Bot')
        console.log(error);
        this.showUploading = false;
      }
    );
  }
  getReportId(consultId) {
    this.consultationUuid = consultId;
  }
  viewFile(data) {
    window.open(data);
  }
  getConsultationId(event, id) {
    if (id == 'All' && event.target.checked) {
      const data = 'patient=' + this.patientUuid;
      // this.getreportList(data)
    } else {
      const data = 'consultation=' + this.consultationUuid;
      // this.getreportList( data);
    }

  }

  reportNextPageList() {
    this.reportCurrentPage = this.reportCurrentPage + 1;
    if (this.reportTotalPage >= this.reportCurrentPage) {
      this.queryParms = this.queryParms + `&page=${this.reportCurrentPage}`;
      this.getreportList(this.queryParms);
      this.reportSerialNumber = (this.reportCurrentPage - 1) * 10;
    } else {
      this.reportCurrentPage = this.reportCurrentPage - 1;
    }
  }
  reportLastPageList() {
    this.reportSerialNumber = (this.reportTotalPage - 1) * 10;
    this.queryParms = this.queryParms + `&page=${this.reportTotalPage}`;
    this.getreportList(this.queryParms);


  }
  reportFirstPageList() {
    this.reportCurrentPage = 1;
    this.reportSerialNumber = 0;
    this.queryParms = this.queryParms + `&page=${this.reportCurrentPage}`;
    this.getreportList(this.queryParms);
  }
  reportPreviousPageList() {
    this.reportCurrentPage = this.reportCurrentPage - 1;
    if (this.reportTotalPage >= this.reportCurrentPage && this.reportCurrentPage > 0) {
      this.queryParms = this.queryParms + `&page=${this.reportCurrentPage}`;
      this.getreportList(this.queryParms);
      this.reportSerialNumber = (this.reportCurrentPage - 1) * 10;
    } else {
      this.reportCurrentPage = this.reportCurrentPage + 1;
    }
  }
  getreportList(data) {
    this.reportTotalPage = 0;
    this.patientService.getMedicalReports(data).subscribe(
      data => {
        this.reportFiles = data['results'];
        this.reportTotalPage = data['total_pages'];
        this.reportCurrentPage = data['page_number'];
      }
    );
  }
  findReports() {
    let searchQueryParms = [];
    const reportType = this.searchForm.controls[`reportType`].value;
    const reportGeneratedOn = this.searchForm.controls[`reportGeneratedOn`].value;
    const reportUpdatedOn = this.searchForm.controls[`reportUpdatedOn`].value;
    if (reportType) {
      const value = 'medical_report_type=' + reportType;
      searchQueryParms.push(value);
    }
    if (reportGeneratedOn) {
      const value = 'generated_date=' + moment(reportGeneratedOn).format('YYYY-MM-DD');
      searchQueryParms.push(value);
    }
    if (reportUpdatedOn) {
      const value = 'created_date=' + moment(reportUpdatedOn).format('YYYY-MM-DD');
      searchQueryParms.push(value);
    }
    this.queryParms = `?patient=${this.patientUuid}&`;
    for (let i = 0; i < searchQueryParms.length; i++) {
      if (i === searchQueryParms.length - 1) {
        this.queryParms = this.queryParms + searchQueryParms[i];
      } else {
        this.queryParms = this.queryParms + searchQueryParms[i] + '&';
      }

    }
    this.getreportList(this.queryParms);
  }
  searchFormCOntrol() {
    this.searchForm = new FormGroup({
      reportType: new FormControl(null),
      reportGeneratedOn: new FormControl(null),
      reportUpdatedOn: new FormControl(null)
    })
  }
  showFilterFields() {
    if (this.shwoFilter == true) {
      this.shwoFilter = true;
    } else {
      this.shwoFilter = true;
    }

  }
  resetSearchForm() {
    this.searchForm.reset();
    this.queryParms = `?patient=${this.patientUuid}`
    this.getreportList(this.queryParms);
  }
}
