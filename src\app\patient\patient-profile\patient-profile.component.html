<!--profile form-->
<h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" (click)="back()"></i>Back</h5>
<div *ngIf="isLoading">
    <app-loading-spinner></app-loading-spinner>
</div>
<div *ngIf="!isLoading">
    <div class="card">
        <div class="card-body">
            <!-- <h4 class="card-title" translate>Profile Picture</h4> -->
            <div class="row form-row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="change-avatar">
                            <div class="profile-img">
                                <img [src]="doctorProfilePictureUrl" alt="User Image">
                            </div>
                            <div class="upload-img">
                                <div class="change-photo-btn">
                                    <span><i class="fa fa-upload"></i> {{ profileUpload ? ('Upload Photo'|translate):
                                        'Uploading'|translate}}</span>
                                    <input type="file" class="upload" id="profile-picture"
                                        [disabled]="disabledUploadPhotoBtn"
                                        (change)="doctorProfilePictureChange($event)" accept=".jpg, .png,">
                                </div>
                                <small class="form-text text-muted" translate>Photograph you upload here will be seen by
                                    the doctor when you book the appointment.</small>
                                <small class="form-text text-muted" translate>(Allowed JPG, JPEG or PNG. Max size of
                                    2MB)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <h4 class="card-title" translate>Personal Profile <i *ngIf="disabled" (click)="editProfile()"
                    class="fa fa-edit"></i></h4>
            <div class="form-group " *ngIf="formError">
                <textarea type="text" class="form-control text-wrap" name="error" autocomplete="name"
                    [(ngModel)]="errorValue" style="color: orangered;" disabled></textarea>
            </div>
            <form [formGroup]="personalProfileForm">
                <div class="row form-row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Full Name<span class="text-danger">*</span></label>
                            <input id="fullname" class="form-control" type="text" name="username"
                                formControlName="username" maxlength="25" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Email <span class="text-danger">*</span></label>
                            <input id="email" type="email" name="email" class="form-control" formControlName="email"
                                readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>First Name</label>
                            <input id="firstname" type="text" class="form-control" name="first_name"
                                formControlName="first_name" maxlength="25" autocomplete="off" [readonly]="disabled">

                            <div *ngIf="personalProfileForm.controls.first_name.invalid && (personalProfileForm.controls.first_name.dirty || personalProfileForm.controls.first_name.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Middle Name</label>
                            <input id="middlename" type="text" class="form-control" name="middle_name"
                                formControlName="middle_name" maxlength="25" autocomplete="off" [readonly]="disabled">
                            <div *ngIf="personalProfileForm.controls.middle_name.invalid && (personalProfileForm.controls.middle_name.dirty || personalProfileForm.controls.middle_name.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Last Name</label>
                            <input id="lastname" type="text" class="form-control" name="last_name"
                                formControlName="last_name" maxlength="25" pattern="[a-zA-Z ]*" autocomplete="off"
                                [readonly]="disabled" readonly>
                            <div *ngIf="personalProfileForm.controls.last_name.invalid && (personalProfileForm.controls.last_name.dirty || personalProfileForm.controls.last_name.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Phone Number<span class="text-danger">*</span></label>
                            <input id="phone" type="text" class="form-control" formControlName="phone" name="phone"
                                maxlength="15" pattern="[0-9]*" [readonly]="disabled" *ngIf="userType=='Patient'">
                            <input id="phone" type="text" class="form-control" formControlName="phone" name="phone"
                                maxlength="15" pattern="[0-9]*" *ngIf="userType!='Patient'" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Father Name</label>
                            <input id="fatherName" type="text" class="form-control" formControlName="fatherName"
                                name="fatherName" maxlength="15" [readonly]="disabled">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Mother Name</label>
                            <input id="motherName" type="text" class="form-control" formControlName="motherName"
                                name="motherName" maxlength="15" [readonly]="disabled">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Husband/Wife Name</label>
                            <input id="husbandName" type="text" class="form-control" formControlName="husbandName"
                                name="husbandName" maxlength="15" [readonly]="disabled">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Guardian Name</label>
                            <input id="guardianName" type="text" class="form-control" formControlName="guardianName"
                                name="guardianName" maxlength="15" [readonly]="disabled">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Age</label>
                            <input id="age" type="text" class="form-control" formControlName="age" name="age"
                                [readonly]="disabled">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Gender<span class="text-danger">*</span></label>
                            <select class="form-control" name="gender" id="gender" formControlName="gender">
                                <option *ngFor="let data of gender" [value]="data.value ">{{data.name}}</option>

                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-0 ">
                            <label translate>Date of Birth<span class="text-danger">*</span></label>
                            <input [maxDate]="maxDate" [minDate]="minDate" placeholder="DOB" onkeydown="return false"
                                class="form-control" formControlName="date_of_birth" [readOnly]="disabled"
                                *ngIf="disabled==false" bsDatepicker
                                [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
                            <input class="form-control" formControlName="date_of_birth" *ngIf="disabled==true" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-0">
                            <label translate> ABHA ID</label>
                            <a href="https://abha.abdm.gov.in/abha/v3/register" target="_blank" *ngIf="!disabled">Create ABHA</a>
                            <input id="abha_id" type="text" class="form-control" formControlName="abha_id"
                                pattern="[0-9]*" name="abha_id" maxlength="14" [readonly]="disabled" *ngIf="userType=='Patient'">
                                <input id="abha_id" type="text" class="form-control" formControlName="abha_id"
                                pattern="[0-9]*" name="abha_id" maxlength="14" readonly *ngIf="userType!='Patient'">
                            <div *ngIf="personalProfileForm.controls.abha_id.invalid && (personalProfileForm.controls.abha_id.dirty || personalProfileForm.controls.abha_id.touched)"
                                class="alert alert-danger">{{numberError}}</div>
                        </div>

                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-0">
                            <label translate>AADHAAR ID <span class="text-danger">*</span></label>
                            <input id="aadhar_id" type="text" class="form-control" formControlName="aadhar_id"
                                pattern="[0-9]*" name="aadhar_id" maxlength="12" [readonly]="disabled" *ngIf="userType=='Patient'">
                                <input id="aadhar_id" type="text" class="form-control" formControlName="aadhar_id"
                                pattern="[0-9]*" name="aadhar_id" maxlength="12" readonly *ngIf="userType!='Patient'">
                            <div *ngIf="personalProfileForm.controls.aadhar_id.invalid && (personalProfileForm.controls.aadhar_id.dirty || personalProfileForm.controls.aadhar_id.touched)"
                                class="alert alert-danger">{{numberError}}</div>
                        </div>
                    </div>
                </div>

                <div class="form-group float-right">

                    <button *ngIf="!disabled" type="button" id="save-btn" id="per-prof-btn" class="btn btn-primary"
                        translate (click)="onSubmit()" [disabled]="!personalProfileForm.valid">Save</button>
                    <button *ngIf="!disabled" type="button" id="cancel-btn" (click)="cancelUpdate()"
                        class="btn btn-secondary cancel-btn" translate>Cancel</button>

                </div>
                <!-- /Basic Information -->

            </form>
            <form id="addressForm" [formGroup]="addressForm">
                <h4 class="card-title" translate> Address <i class="fa fa-edit" (click)="editAddress()"
                        *ngIf="!!addressReadOnly"></i> </h4>
                <div class="col-md-12 text-right">
                    <a></a>
                </div>
                <div class="row form-row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Line 1<span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="line_1" formControlName="line_1"
                                [readonly]="addressReadOnly" maxlength="50" pattern="[a-zA-Z0-9,:/ ]*" required
                                autocomplete="off">
                            <div *ngIf="addressForm.controls.line_1.invalid && (addressForm.controls.line_1.dirty || addressForm.controls.line_1.touched)"
                                class="alert alert-danger">{{specialCharacterError}}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label translate>Line 2<span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="line_2" formControlName="line_2"
                                [readonly]="addressReadOnly" maxlength="50" pattern="[a-zA-Z0-9,:/ ]*" required
                                autocomplete="off">
                            <div *ngIf="addressForm.controls.line_2.invalid && (addressForm.controls.line_2.dirty || addressForm.controls.line_2.touched)"
                                class="alert alert-danger">{{specialCharacterError}}</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label translate>City/Town/Village<span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="city" formControlName="city_town_village"
                                [readonly]="addressReadOnly" required autocomplete="off" maxlength="50"
                                pattern="[a-zA-Z ]*">
                            <div *ngIf="addressForm.controls.city_town_village.invalid && (addressForm.controls.city_town_village.dirty || addressForm.controls.city_town_village.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label translate>Taluk <span class="text-danger">*</span></label>
                            <input type="text" id="taluk" class="form-control" [readonly]="addressReadOnly"
                                formControlName="taluk" autocomplete="off" maxlength="50" pattern="[a-zA-Z ]*">
                            <div *ngIf="addressForm.controls.taluk.invalid && (addressForm.controls.taluk.dirty || addressForm.controls.taluk.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label translate>District <span class="text-danger">*</span></label>
                            <input type="text" id="distric" class="form-control" [readonly]="addressReadOnly"
                                formControlName="district" required autocomplete="off" maxlength="50"
                                pattern="[a-zA-Z ]*">
                            <div *ngIf="addressForm.controls.district.invalid && (addressForm.controls.district.dirty || addressForm.controls.district.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label translate>State<span class="text-danger">*</span></label>
                            <input type="text" id="state" class="form-control" formControlName="state"
                                autocomplete="off" [readonly]="addressReadOnly" required maxlength="50"
                                pattern="[a-zA-Z ]*">
                            <div *ngIf="addressForm.controls.state.invalid && (addressForm.controls.state.dirty || addressForm.controls.state.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>
                    <div class="col-md-4 ng-select-container">
                        <div class="form-group">
                            <label translate>Country <span class="text-danger">*</span></label>

                            <ng-select id="country" class="custom" formControlName="country" bindValue="Name"
                                [items]="countryList" [readonly]="addressReadOnly" [searchable]="true" bindLabel="Name"
                                [clearable]="false" placeholder="{{'Select Country' | translate}}" multiple required>
                            </ng-select>

                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label translate>Postal Code<span class="text-danger">*</span></label>
                            <input type="text" id="postal_code" class="form-control" formControlName="postal_code"
                                [readonly]="addressReadOnly" autocomplete="off" required maxlength="10">
                            <div *ngIf="addressForm.controls.postal_code.invalid && (addressForm.controls.postal_code.dirty || addressForm.controls.postal_code.touched)"
                                class="alert alert-danger">{{alphanumericError}}</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-sm-12 col-xs-12 text-right">
                    <button *ngIf="!addressReadOnly" [disabled]="!addressForm.valid" id="save-clinic-btn"
                        class="btn btn-primary" (click)="saveAddress()" translate>Save</button>

                    <button class="btn btn-secondary cancel-btn" id="cancel-clinic-btn" (click)="cancelAddress()"
                        [disabled]="addressForm.dirty=== true?false  :addressForm.valid=== true? false:true"
                        *ngIf="!addressReadOnly" translate>Cancel</button>
                </div>
            </form>
        </div>
    </div>
    <!--profile form ends-->
    <!--kyc form-->
    <div class="card">
        <div class="card-body">
            <form [formGroup]="kycForm" id="kycForm">
                <h4 class="card-title"> Identity Details </h4>
                <div class="row form-row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <ng-select id="select-kycDocument" [items]="kycDocument" [clearable]="false"
                                [searchable]="false" bindLabel="kycDocument" [readonly]="kycDocumentReadOnly"
                                formControlName="kycDocumentName" placeholder="kyc Document"
                                (change)="getKycDocumentName($event)" [multiple]="false">
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-md-3">

                        <div class="upload-img" *ngIf="!kycDocumentReadOnly">
                            <div class="change-photo-btn " style="padding: 6px;">
                                <span><i class="fa fa-upload"></i> {{ fileUpload ? ('Choose file'|translate):
                                    'Selected'|translate}}</span>
                                <input type="file" class="upload" id="kyc-doc" [disabled]="kycUploadbuttonDisabeld"
                                    (change)="uploadKycReport($event)" accept=".jpg,.pdf">
                            </div>
                            <small class="form-text text-muted ml-5" *ngIf="fileUpload" translate>Allowed JPG ,JPEG or
                                Pdf. Max size of 2MB</small>
                            <small class="form-text text-muted ml-5 " style="text-align: center;"
                                *ngIf="!fileUpload">{{selectedKycDocument.name}}</small>
                        </div>
                        <div class="upload-img" *ngIf="kycDocumentReadOnly">
                            <div class="change-photo-btn " style="padding: 6px;">
                                <span><i class="fa fa-view"></i>View File</span>
                                <input type="text" class="upload" id="kyc-doc-view" (click)="viewKycDocument()">
                            </div>
                            <!-- <small class="form-text text-muted ml-4">&nbsp;{{selectedKycDocumentName}}</small> -->
                        </div>


                    </div>
                    <div class="col-md-2">

                        <button *ngIf="!kycDocumentId" id="save-kyc-btn" class="btn btn-primary mx-1"
                            (click)="saveKyc()" [disabled]="!kycForm.valid">{{ kycDataSaving ? 'Uploading':
                            'Save'}}</button>

                        <button class="btn btn-secondary cancel-btn" id="cancel-kyc-btn" (click)="deleteKyc()"
                            *ngIf="kycDocumentId" translate>Delete</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!--kyc form-->
    <!--Emergency Contact form-->
    <div class="card">
        <div class="card-body">
            <form [formGroup]="emergencyForm" #emergencyContact>
                <h4 class="card-title"> Emergency Contact <i class="fa fa-edit" *ngIf="emergencyFormReadOnly"
                        (click)="editContact()"></i> </h4>
                <div class="row form-row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <input id="name-full" class="form-control" formControlName="name" type="text" name="name"
                                placeholder="Full Name" [readOnly]="emergencyFormReadOnly" maxlength="50"
                                pattern="[a-zA-Z ]*">

                            <div *ngIf="emergencyForm.controls.name.invalid && (emergencyForm.controls.name.dirty || emergencyForm.controls.name.touched)"
                                class="alert alert-danger">{{alphabetsError}}</div>
                        </div>
                    </div>
                    <div class="col-md-3 ">
                        <div class="form-group">
                            <input id="mob-no" class="form-control" formControlName="phone_number" type="text"
                                maxlength="10" minlength="10" name="mob no" placeholder="Phone Number" maxlength="15"
                                pattern="[0-9]*" [readOnly]="emergencyFormReadOnly">
                            <div *ngIf="emergencyForm.controls.phone_number.invalid && (emergencyForm.controls.phone_number.dirty || emergencyForm.controls.phone_number.touched)"
                                class="alert alert-danger">{{numberError}}</div>
                        </div>
                    </div>
                    <div class="col-md-3 ">
                        <div class="form-group">
                            <ng-select id="select-Relation" [items]="relationship" [clearable]="false"
                                [searchable]="false" bindLabel="relationship" formControlName="relationship"
                                placeholder="relationship" [multiple]="false" [readonly]="emergencyFormReadOnly">
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-md-3" *ngIf="!emergencyFormReadOnly">
                        <button id="save-constact-btn" class="btn btn-primary" (click)="saveContact()"
                            [disabled]="!emergencyForm.valid" translate>Save</button>

                        <button class="btn btn-secondary cancel-btn" id="cancel-constact-btn" (click)="cancelContact()"
                            [disabled]="emergencyForm.dirty=== true?false  :emergencyForm.valid=== true? false:true"
                            translate>Cancel</button>


                    </div>
                </div>

            </form>
        </div>
    </div>
    <!--Emergency Contact-->

</div>
<!-- Phone number change confirmation modal starts-->
<div class="modal" id="addConfirmModal">
    <div class="modal-dialog modal-confirm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title model-header-alinement">Changing Phone number</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                    &times;
                </button>
            </div>
            <div class="modal-body">
                <p>New phone number detected, Are you sure want to update the following phone number to your profile?
                </p>
                <p>{{userDetails.phone}}</p>
                <p>once confirmed the otp will sent to your new mobile number</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-info" data-dismiss="modal" id="cancel-stop">
                    No
                </button>
                <button type="button" class="btn btn-danger" id="proceed-cancel" (click)="createOTP()">
                    Confirm
                </button>
            </div>
        </div>
    </div>
</div>
<!-- add patient confirmation modal ends -->

<!-- otp confirmation modal starts-->
<div class="modal" id="otpConfirmModal">
    <div class="modal-dialog modal-confirm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title model-header-alinement">OTP Confirmation</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                    &times;
                </button>
            </div>
            <div class="modal-body">
                <div [formGroup]="otpForm" class="mb-3">
                    <input #otp1 id="otp1" formControlName="otp1" (input)="pass(otp1,otp2)" class="input-field"
                        maxlength="1">
                    <input #otp2 id="otp2" formControlName="otp2" (input)="pass(otp2,otp3)" class="input-field"
                        maxlength="1">
                    <input #otp3 id="otp3" formControlName="otp3" (input)="pass(otp2,otp4)" class="input-field"
                        maxlength="1">
                    <input #otp4 id="otp4" formControlName="otp4" class="input-field" maxlength="1">
                    <label class="countdown ml-2" *ngIf="!resendOtp">{{seconds}}</label>
                    <a class="ml-2 resend-otp" *ngIf="resendOtp" (click)="createOTP()"><u>Resend OTP</u></a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-info" data-dismiss="modal" id="cancel-stop">
                    No
                </button>
                <button type="button" class="btn btn-primary" id="proceed-cancel" (click)="otpVerification()"
                    [disabled]="otpForm.invalid">
                    Verify
                </button>
            </div>
        </div>
    </div>
</div>
<!-- otp confirmation modal ends -->