import { HttpClient } from '@angular/common/http';
import { delay } from 'rxjs/operators';
import { AuthService } from '../../auth/auth.service';
import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  ViewChild,
} from '@angular/core';
import * as Settings from '../../config/settings';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { PatientService } from '../patient.service';
import {
  NgForm,
  FormGroup,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
declare var $: any;
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import { SharedService } from '../../shared/shared.service';
import * as moment from 'moment';
@Component({
  selector: 'app-profile',
  templateUrl: './patient-profile.component.html',
  styleUrls: ['./patient-profile.component.css'],
})
export class PatientProfileComponent implements OnInit {
  public changed = false;
  public profileUpload = true;
  public disabledUploadPhotoBtn = false;
  public personalProfileForm: FormGroup;
  public userData = {};
  public disabled = true;
  doctorProfilePictureUrl = 'assets/img/doctors/doctor-thumb-02.png';
  public cancelbtn = false;
  profileFileSizeLarge = false;
  @Output() messageEvent: EventEmitter<string> = new EventEmitter<string>();
  userID: any;
  public addressForm: FormGroup;
  public emergencyForm: FormGroup;
  public kycForm: FormGroup;
  practiceLocation: string;
  public countryList: any = [];
  doctorClinicAddressList: any = [];
  addressReadOnly: boolean;
  public cancleBtnShow = false;
  public kycDocument = [
    'Aadhar',
    'Passport',
    `Driver's License`,
    'PAN Card',
    'Ration Card',
    'Voter ID',
    'Other',
  ];
  fileSizeLarge: boolean;
  disabledUploadBtn: boolean;
  fileUpload: boolean;
  selectedKycDocument: File;
  public kycUploadbuttonDisabeld = true;
  kycDataSaving: boolean;
  kycDocumentReadOnly: boolean;
  showIdentityEdit = false;
  public kycDocumentId: string;
  selectedKycDocumentName: any;
  selectedKycFielData: any;
  relationship = [
    'Father',
    'Mother',
    'Husband',
    'Wife',
    'Sister',
    'Brother',
    'Friend',
    'Other',
  ];
  emergencyFormReadOnly: boolean;
  address: any = [];
  contactData: any = [];
  isLoading = true;
  public maxDate: Date;
  public minDate: Date;
  public namePattern = '^[A-Za-z -]+$';
  errorValue: any = [];
  formError: boolean;
  gender = [
    { value: '', name: 'Select' },
    { value: 'Male', name: 'Male' },
    { value: 'Female', name: 'Female' },
    { value: 'Prefer not to answer', name: 'Prefer not to answer' },
  ];
  public userDetails: any = {
    username: null,
    email: null,
    phone: null,
    gender: null,
    first_name: null,
    middle_name: null,
    last_name: null,
    date_of_birth: null,
    father_name: null,
    mother_name: null,
    husband_name: null,
    guardian_name: null,
    age: null,
    abha_id: null,
    aadhar_id: null
  };
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  numberError = Settings.numberError;
  public userType = localStorage.getItem('user_type');
  otpForm: FormGroup;
  resendOtp: boolean;
  seconds = 90;
  otpValue: string = '';
  user_type: string;
  patientCurrentNumber: number = 0;
  otpverified: boolean = false;
  constructor(
    private userService: AuthService,
    private patientService: PatientService,
    private formBuilder: FormBuilder,
    private router: Router,
    public translate: TranslateService,
    private sharedService: SharedService,
    private notificationService: ToastrService,
    private location: Location,
    private fb: FormBuilder
  ) {
    this.otpForm = this.fb.group({
      otp1: new FormControl('', Validators.required),
      otp2: new FormControl('', Validators.required),
      otp3: new FormControl('', Validators.required),
      otp4: new FormControl('', Validators.required),
    });
  }

  ngOnInit(): void {
    this.maxDate = new Date();
    this.minDate = new Date();
    this.minDate.setDate(this.minDate.getDate() - 36500);
    this.fileUpload = true;
    this.kycDocumentId = null;
    this.addProfileFromControl(null);
    this.addFormControlData(null);
    this.addEmergencyContactFormControl(null);
    this.addKycFormControl(null);
    this.getAddress();
    this.getuserData();
    this.getCountry();
    this.getKycDocumentData();
    this.getContactList();

  }

  getuserData() {

    this.userService.getUserDetail().subscribe(
      (data) => {
        this.userData = data;
        this.addProfileFromControl(data);
        if (this.userData['profile_picture'] !== null) {
          this.doctorProfilePictureUrl = this.userData['profile_picture'];
        }
        setTimeout(() => {
          this.isLoading = false;
        }, 2000);

      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        this.isLoading = true;
      }
    );
  }

  onSubmit() {
    this.errorValue = [];
    const dob = this.personalProfileForm.controls[`date_of_birth`].value;
    this.userDetails.username = this.personalProfileForm.controls[`username`].value;
    this.userDetails.email = this.personalProfileForm.controls[`email`].value;
    this.userDetails.phone = this.personalProfileForm.controls[`phone`].value;
    this.userDetails.gender = this.personalProfileForm.controls[`gender`].value;
    this.userDetails.first_name = this.personalProfileForm.controls[`first_name`].value;
    this.userDetails.last_name = this.personalProfileForm.controls[`last_name`].value;
    this.userDetails.middle_name = this.personalProfileForm.controls[`middle_name`].value;
    this.userDetails.date_of_birth = moment(dob, 'DD-MM-YYYY').format('YYYY-MM-DD');
    this.userDetails.father_name = this.personalProfileForm.controls[`fatherName`].value;
    this.userDetails.mother_name = this.personalProfileForm.controls[`motherName`].value;
    this.userDetails.husband_name = this.personalProfileForm.controls[`husbandName`].value;
    this.userDetails.guardian_name = this.personalProfileForm.controls[`guardianName`].value;
    this.userDetails.age = this.personalProfileForm.controls[`age`].value;
    this.userDetails.abha_id = this.personalProfileForm.controls[`abha_id`].value;
    this.userDetails.aadhar_id = this.personalProfileForm.controls[`aadhar_id`].value;
    if (this.otpverified == false && this.patientCurrentNumber != this.personalProfileForm.controls[`phone`].value) {
      $('#addConfirmModal').modal('show');
    }
    else {
      this.userService.updatePersonalProfile(this.userDetails).subscribe(
        (data) => {
          this.notificationService.success('Profile Updated', 'Med.Bot');
          this.userData = data;
          this.personalProfileForm.get('gender').disable();
          this.addProfileFromControl(data)
          this.disabled = true;
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            const err = error['error']['error_details']['validation_errors'];

            if (err) {
              const gender = err['gender'];
              const dob = err['date_of_birth'];
              if (gender && dob) {
                this.formError = true;
                const genderError = 'Gender : ' + gender[0];
                const dobError = 'DOB : ' + dob[0];
                this.notificationService.error(
                  `${genderError} ${dobError}`,
                  'Med.Bot'
                );
                this.errorValue.push({ value: genderError }, { value: dobError });
              } else if (gender) {
                this.formError = true;
                const genderError = 'Gender : ' + gender[0];
                this.notificationService.error(`${genderError}`, 'Med.Bot');
                this.errorValue.push({ value: genderError });
              } else if (dob) {
                this.formError = true;
                const dobError = 'DOB : ' + dob[0];
                this.notificationService.error(` ${dobError}`, 'Med.Bot');
                this.errorValue.push({ value: dobError });
              } else {
                let message = Object.values(err)[0][0] ?Object.keys(err)+': '+ Object.values(err)[0][0] : 'Validation Error';
                this.notificationService.error(message, 'Med.Bot');
              }
            } else {
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          } else if (status == 409) {
            this.notificationService.error(`${error['error']['error_message']}`, 'Med.Bot');
            $('#otpConfirmModal').modal('show');
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
  }

  onChange() {
    this.changed = true;
  }

  doctorProfilePictureChange(event) {
    const file = event.target.files;

    if (file.length > 0) {
      this.profileFileSizeLarge = false;
      const selectedProfilePicture = file[0];
      if (
        selectedProfilePicture.size < 2000000 &&
        (selectedProfilePicture.type === 'image/jpeg' ||
          selectedProfilePicture.type === 'image/jpg' ||
          selectedProfilePicture.type === 'image/png')
      ) {
        this.disabledUploadPhotoBtn = true;
        this.profileUpload = false;
        this.userService
          .updateDoctorProfilePicture(selectedProfilePicture)
          .subscribe(
            (data) => {
              this.userData = data;
              this.doctorProfilePictureUrl = this.userData['profile_picture'];
              this.sharedService.setPicture(this.doctorProfilePictureUrl);
              this.profileUpload = true;
              this.disabledUploadPhotoBtn = false;
              this.notificationService.success(
                'Profile Picture Updated',
                'Med.Bot'
              );
            },
            (error) => {
              this.profileUpload = true;
              this.disabledUploadPhotoBtn = false;
              console.log(error);
              const status = error['status'];
              if (status == 400) {
                this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
              }
              else {
                this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
              }
            }
          );
      } else {
        this.profileFileSizeLarge = true;
      }
    } else {
      this.profileUpload = true;
      this.disabledUploadPhotoBtn = false;
      this.notificationService.error(
        'Please select  profile picture',
        'Med.Bot'
      );
    }
  }

  sendProfileToChildComponent() {
    this.messageEvent.emit(this.doctorProfilePictureUrl);
  }
  editProfile() {
    this.disabled = false;
    this.personalProfileForm.get('gender').enable();
  }
  cancelUpdate() {
    this.personalProfileForm.get('gender').disable();
    this.disabled = true;
    this.addProfileFromControl(this.userData);
  }
  back() {
    this.location.back();
  }
  cancelAddress() {
    this.cancleBtnShow = false;
    if (this.address.length > 0) {
      this.addFormControlData(this.address[0]);
    } else {
      this.addFormControlData(null);
    }
  }

  addFormControlData(data) {
    if (data === null) {
      this.practiceLocation = sessionStorage.getItem('practice_location');
      this.addressReadOnly = false;
      this.cancleBtnShow = false;
      this.addressForm = new FormGroup({
        uuid: new FormControl(null),
        address_type: new FormControl('Home'),
        line_1: new FormControl('', [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')
        ]),
        line_2: new FormControl('', [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')
        ]),
        city_town_village: new FormControl('', [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')
        ]),
        district: new FormControl('', [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')
        ]),
        taluk: new FormControl('', [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')
        ]),
        state: new FormControl('', [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')
        ]),
        country: new FormControl('India', [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')
        ]),
        postal_code: new FormControl('', [
          Validators.maxLength(10), Validators.pattern('[a-zA-Z0-9 ]*')
        ]),
      });
    } else {
      this.addressReadOnly = true;
      this.cancleBtnShow = false;
      this.addressForm = new FormGroup({
        uuid: new FormControl(data.uuid, Validators.required),
        address_type: new FormControl(data.address_type, [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9 ]*')
        ]),
        line_1: new FormControl(data.line_1, [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')
        ]),
        line_2: new FormControl(data.line_2, [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')
        ]),
        city_town_village: new FormControl(
          data.city_town_village,
          [
            Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')
          ]
        ),
        district: new FormControl(data.district, [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')
        ]),
        taluk: new FormControl(data.taluk, [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')
        ]),
        state: new FormControl(data.state, [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')
        ]),
        country: new FormControl(data.country, [
          Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')
        ]),
        postal_code: new FormControl(data.postal_code, [
          Validators.maxLength(10), Validators.pattern('[a-zA-Z0-9 ]*')
        ]),
      });
    }
  }
  addEmergencyContactFormControl(data) {
    if (data === null) {
      this.emergencyFormReadOnly = false;
      this.emergencyForm = new FormGroup({
        uuid: new FormControl(null),
        name: new FormControl('', [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        phone_number: new FormControl('', [Validators.required, Validators.pattern('[0-9]*'), Validators.minLength(10), Validators.maxLength(15)]),
        relationship: new FormControl(null, Validators.required),
      });
    } else {
      this.emergencyFormReadOnly = true;
      this.emergencyForm = new FormGroup({
        uuid: new FormControl(data.uuid, Validators.required),
        name: new FormControl(data.name, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        phone_number: new FormControl(data.phone_number, [Validators.required, Validators.pattern('[0-9]*'), Validators.minLength(10), Validators.maxLength(15)]),
        relationship: new FormControl(data.relationship, Validators.required),
      });
    }
  }
  addKycFormControl(data) {
    this.kycForm = new FormGroup({
      kycDocumentName: new FormControl(null, Validators.required),
      kycFile: new FormControl('', Validators.required),
    });
  }
  editAddress() {
    this.addressReadOnly = false;
    this.cancleBtnShow = true;
  }
  saveAddress() {
    this.patientService.saveAddress(this.addressForm.value).subscribe(
      (data) => {
        this.addFormControlData(data);
        this.address = [data];
        this.addressReadOnly = true;
        this.cancleBtnShow = false;
        if (this.addressForm.get('uuid').valid) {
          this.notificationService.success(
            ' Address Updated Successfully',
            'Med.Bot'
          );
        } else {
          this.notificationService.success(
            ' Address Added Successfully',
            'Med.Bot'
          );
        };

      },
      (error) => {
        console.log(error);
        this.notificationService.error(
          ' Address Updation Failed',
          'Med.Bot'
        );
      }
    );
  }
  getCountry() {
    this.patientService.getCountryDetail().subscribe(
      (data) => {
        this.countryList = data;
      },
      (error) => {
        console.log(error);
      }
    );
  }
  getAddress() {
    this.patientService.getAddressDetail().subscribe(
      (data) => {


        this.address = data;
        if (this.address.length > 0) {
          this.addFormControlData(this.address[0]);
        } else {
          this.addFormControlData(null);
        }
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  uploadKycReport(event) {
    const file = event.target.files;
    if (file.length > 0) {
      this.fileSizeLarge = false;
      const selectedFile = file[0];
      if (selectedFile.size < 2000000) {
        if (selectedFile.name.length < 51) {
          if (selectedFile.type === 'image/jpeg' ||
            selectedFile.type === 'image/jpg' ||
            selectedFile.type === 'image/pdf' ||
            selectedFile.type === 'application/pdf') {
            this.selectedKycDocument = selectedFile;
            this.selectedKycDocumentName = selectedFile.name;
            this.kycForm.controls['kycFile'].setValue('file');
            this.fileUpload = false;
          } else {
            this.notificationService.error('Incorrect file format', 'Med.Bot');
            this.kycForm.controls['kycFile'].setValue('');
          }
        } else {
          this.kycForm.controls['kycFile'].setValue('');
          this.notificationService.warning("File name: Ensure this field has no more than 50 characters.");
        }


      } else {
        this.fileSizeLarge = true;
        this.notificationService.error('File size large,', 'Med.Bot');
        this.kycForm.controls['kycFile'].setValue('');
      }
    } else {
      this.fileUpload = true;
      this.disabledUploadBtn = false;
      this.kycForm.controls['kycFile'].setValue('');
      this.notificationService.error('Please select  file', 'Med.Bot');
    }
  }
  saveKyc() {
    this.kycDataSaving = true;
    const kycDocumentName = this.kycForm.controls[`kycDocumentName`].value;
    const data = { patient: this.userData['uuid'], category: kycDocumentName };
    this.patientService.sendKycReport(this.selectedKycDocument, data).subscribe(
      (data) => {

        const kycDocuments = data['k_documents'];
        this.selectedKycDocumentName = kycDocuments[0].file_name;
        this.kycDocumentReadOnly = true;
        this.showIdentityEdit = true;
        this.selectedKycFielData = kycDocuments[0].file;
        this.kycDocumentId = data['uuid'];
        this.kycForm.controls['kycDocumentName'].setValue(
          kycDocuments[0].category
        );
        this.kycForm.controls['kycFile'].setValue('file');
        this.kycDocumentReadOnly = true;
        this.kycDataSaving = false;
        this.fileUpload = true;
        this.notificationService.success('KYC Document Updated', 'Med.Bot');
      },
      (error) => {
        this.fileUpload = true;
        this.disabledUploadBtn = false;
        this.kycDataSaving = false;
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          const file = error['error']['error_details']['validation_errors']['file'];
          const fileName = error['error']['error_details']['validation_errors']['file_name']
          const fileType = error['error']['error_details']['validation_errors']['file_type'];
          if (file) {
            const fileErr = 'File :' + file[0];
            this.errorValue.push({ value: fileErr });
            this.notificationService.error(`${fileErr}`);
          } else if (fileType) {
            const fileTypeErr = 'File Type :' + fileType[0];
            this.errorValue.push({ value: fileTypeErr });
            this.notificationService.error(`${fileTypeErr}`);
          } else if (fileName) {
            const fileErr = 'File name :' + fileName[0];
            this.errorValue.push({ value: fileErr });
            this.notificationService.error(`${fileErr}`);

          } else {
            this.notificationService.error(`Error In Updating KYC Document ${error['statusText']}`, 'Med.Bot');

          }
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  deleteKyc() {
    this.patientService.deleteKycReport(this.kycDocumentId).subscribe(
      (data) => {

        this.kycDocumentId = null;
        this.showIdentityEdit = true;
        this.kycDocumentReadOnly = false;
        this.addKycFormControl(null);
        this.notificationService.warning('KYC Document Deleted', 'Med.Bot');
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']} Error In Delete KYC Document`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  saveContact() {
    this.patientService.saveContact(this.emergencyForm.value).subscribe(
      (data) => {
        this.contactData = [data];
        this.addEmergencyContactFormControl(data);
        if (this.emergencyForm.get('uuid').valid) {
          this.notificationService.success(
            'Emergency Contact Updated Successfully',
            'Med.Bot'
          );
        } else {
          this.notificationService.success(
            'Emergency Contact Added Successfully',
            'Med.Bot'
          );
        }

      },
      (error) => {
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  editKyc() {
    this.showIdentityEdit = false;
    this.kycDocumentReadOnly = false;
  }
  cancelContact() {
    if (this.contactData.length > 0) {
      this.addEmergencyContactFormControl(this.contactData[0]);
    } else {
      this.addEmergencyContactFormControl(null);
    }
  }
  editContact() {
    this.emergencyFormReadOnly = false;
  }
  getKycDocumentName(event) {
    const value = this.kycForm.controls[`kycDocumentName`].value;
    if (value.length > 0) {
      this.kycUploadbuttonDisabeld = false;
    } else {
      this.kycUploadbuttonDisabeld = true;
    }
  }
  getKycDocumentData() {
    this.patientService.getkycDetail().subscribe(
      (data) => {
        const results = data['results'];
        if (results.length > 0) {
          const deletedDataFilter = results.filter(
            (obj) => obj.deleted_at === null
          );
          if (deletedDataFilter.length > 0) {
            const kycDocuments = deletedDataFilter[0].k_documents;
            this.selectedKycDocumentName = kycDocuments[0].file_name;
            this.kycDocumentReadOnly = true;
            this.showIdentityEdit = true;
            this.selectedKycFielData = kycDocuments[0]?.file;
            this.kycDocumentId = deletedDataFilter[0]?.uuid;
            this.kycForm.controls['kycDocumentName'].setValue(
              kycDocuments[0]?.category
            );
            this.kycForm.controls['kycFile'].setValue('file');
          } else {
            this.showIdentityEdit = false;
            this.kycDocumentId = null;
          }
        } else {
          this.showIdentityEdit = false;
          this.kycDocumentId = null;
        }
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  viewKycDocument() {
    window.open(this.selectedKycFielData);
  }
  getContactList() {
    this.patientService.getContactDetail().subscribe(
      (data) => {
        const result = data['results'];
        if (result.length > 0) {
          this.contactData = result;
          this.addEmergencyContactFormControl(result[0]);
        }


      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  addProfileFromControl(data) {
    if (data === null) {
      this.disabled = false;
      this.personalProfileForm = new FormGroup({
        username: new FormControl('', [
          Validators.required,
          Validators.maxLength(50),
        ]),
        email: new FormControl('', [Validators.required, Validators.email]),
        first_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        middle_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        last_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        phone: new FormControl('', [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl('', [
          Validators.required,
          Validators.maxLength(10),
        ]),
        date_of_birth: new FormControl('', [
          Validators.required,
          Validators.maxLength(20),
        ]),
        fatherName: new FormControl(''),
        motherName: new FormControl(''),
        husbandName: new FormControl(''),
        guardianName: new FormControl(''),
        age: new FormControl(''),
        abha_id: new FormControl('', [Validators.maxLength(14), Validators.pattern('[0-9 ]*')]),
        aadhar_id: new FormControl('', [Validators.maxLength(16), Validators.pattern('[0-9 ]*')]),
      });
    } else {
      this.disabled = true;
      const dob = moment(data.date_of_birth).format('DD-MM-YYYY')
      this.personalProfileForm = new FormGroup({
        username: new FormControl(data.username, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        email: new FormControl(data.email, [
          Validators.required,
          Validators.email,
        ]),
        first_name: new FormControl(data.first_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')
        ]),
        middle_name: new FormControl(data.middle_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')
        ]),
        last_name: new FormControl(data.last_name, [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        phone: new FormControl(data.phone, [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl(data.gender, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        date_of_birth: new FormControl(
          dob,
          [Validators.required, Validators.maxLength(25)]
        ),
        fatherName: new FormControl(data.father_name),
        motherName: new FormControl(data.mother_name),
        husbandName: new FormControl(data.husband_name),
        guardianName: new FormControl(data.guardian_name),
        age: new FormControl(data.age),
        abha_id: new FormControl(data.abha_id ? data.abha_id : ''),
        aadhar_id: new FormControl(data.aadhar_id ? data.aadhar_id : '')
      });
      this.personalProfileForm.get('gender').disable();
      this.patientCurrentNumber = data.phone;
    }
  }

  createOTP() {
    $('#addConfirmModal').modal('hide');
    this.countdown();
    this.otpverified = true;
    this.onSubmit();

    // const formData = new FormData();
    // formData.append('email', this.personalProfileForm.value.email);
    // formData.append('phone_number', this.personalProfileForm.value.phone);
    // this.patientService.createOTP(formData).subscribe(
    //   data => {
    //     this.notificationService.success(`${data['message']}`, 'Med.Bot');
    //     $('#otpConfirmModal').modal('show');
    //     this.countdown();
    //   },
    //   error => {
    //     console.log(error);
    //     this.notificationService.error(error.error.error, 'Med.Bot');
    //   }
    // );
  }
  otpVerification() {
    const formData = new FormData();
    this.otpValue = this.otpForm.value.otp1 + this.otpForm.value.otp2 + this.otpForm.value.otp3 + this.otpForm.value.otp4;
    formData.append('type', 'Phone');
    formData.append('value', this.otpValue);
    formData.append('email', this.personalProfileForm.value.email);
    formData.append('phone', this.personalProfileForm.value.phone);
    formData.append('email_or_phone_value', this.personalProfileForm.value.email);

    this.patientService.otpVerification(formData).subscribe(
      data => {
        this.notificationService.success(`${data['message']}`, 'Med.Bot');
        $('#otpConfirmModal').modal('hide');
        this.disabled = true;
        this.onSubmit();
        if (this.user_type == 'HospitalAdmin') {
          this.router.navigate(['/users']);
        } else if (this.user_type == 'DoctorAssistant') {
          this.router.navigate(['/addpatient']);
        } else if (this.user_type == 'Partner') {
          this.router.navigate(['/add-asst-pat']);
        }
      },
      error => {
        console.log(error);
        this.notificationService.error(error.error.error_message, 'Med.Bot');
      }
    );
  }
  countdown() {
    this.resendOtp = false;
    const countdownInterval = setInterval(() => {
      if (this.seconds >= 1) {
        this.seconds--;
      } else {
        clearInterval(countdownInterval);
        this.resendOtp = true;
        this.seconds = 90;
      }
    }, 1000);
  }
  pass(c: any, n: any) {
    var length = c.value.length;
    var maxlength = c.getAttribute("maxlength");
    if (length == maxlength && n != "") {
      n.focus();
    }
  }
  createABHA() {
    const url = 'https://abha.abdm.gov.in/abha/v3/register';
    window.open(url, '_blank');
  }
}
