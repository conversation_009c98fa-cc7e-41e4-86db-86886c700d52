.modalShowClass {
  display: block !important;
  z-index: 9999999999999  !important;
}

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .example {background: red;}
  .modal-size{
    width: 90%;
    height: 500px;
    overflow: auto;
    margin: 4%;
  }
  .modal-dialog.t-c {
    overflow-y: initial !important;
  }
  .modal-body.t-c {
    height: 300px;
    overflow-y: auto;
  }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .example {background: green;}
  .modal-size{
    width: 90%;
    height: 300px;
    overflow: auto;
  }
  .modal-body.t-c {
    height: 500px;
    overflow-y: auto;
  }
  .modal-dialog.t-c {
    overflow-y: initial !important;
  }

}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .example {background: blue;}
  .modal-size{
    width: 100%;
    height: 500px;
    overflow: auto;
    margin: 0%;
  }
  .modal-dialog.t-c {
    overflow-y: initial !important;
    min-width: 700px;
  }
  .modal-body.t-c {
    height: 500px;
    overflow-y: auto;
  }
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .example {background: orange;}
  .modal-size{
    width: 150%;
    height: 600px;
    overflow: auto;
    margin-left: -100px;
  }
  .modal-dialog.t-c {
    overflow-y: initial !important;
    min-width: 1000px;
  }
  .modal-body.t-c {
    height: 500px;
    overflow-y: auto;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .example {background: pink;}
  .modal-size{
    width: 150%;
    height: 600px;
    overflow: auto;
    margin-left: -100px;
  }
  .modal-dialog.t-c {
    overflow-y: initial !important;
    min-width: 1000px;
  }
  .modal-body.t-c {
    height: 500px;
    overflow-y: auto;
  }
}
