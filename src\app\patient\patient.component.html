<!--BreadCrumbs-->
<div class="breadcrumb-bar">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-12 col-12">
              <div class="row">
                <div class="col-md-8">
                  <h2 #header class="breadcrumb-title">{{breadcrumbHeader |translate}} </h2>
                </div>
                <div class="col-md-4 text-right" *ngIf="breadcrumbHeader=='Tele Consult' &&doctorProfilePicture" >
                  <div class="avatar avatar-teleconsult avatar-sm mr-5">
                    <img [src]="doctorProfilePicture" class="avatar-img rounded-circle"alt="User Image">
                  </div>
                  <h5 class="doc-name first-name" style="color: white; display: inline;">Dr.{{doctorInfo['username']}}</h5><br>
                  <h6 class="doc-name first-name" style="color: white; display: inline;"> Regd.No. {{doctorId}}</h6><br>
                  <h6 class="doc-name first-name" style="color: white; display: inline;">  {{doctorSpl}}</h6><br>
                  <!-- <h5 class="doc-name first-name" style="color: white; display: inline;">{{doctorSOM}}</h5><br> -->
                  <!-- <h6 class="doc-name first-name" style="color: white; display: inline;">{{doctorDept}}</h6> -->
                </div>


              </div>
            </div>
        </div>
    </div>
</div>
<!--BreadCrumbs Ends-->
<!-- Page Content -->
<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 col-lg-12 col-xl-12">
                <div>
                    <router-outlet (activate)="onActivate($event)"></router-outlet>
                </div>
            </div>
        </div>
    </div>
</div>

 <div id="patientModal" class="modal" tabindex="-1" role="dialog" >
    <div class="modal-dialog t-c" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <div class="tex-center">
                    <h5 class="modal-title tex-center" translate>Terms and Conditions</h5>
                    <p translate>
                        Here are the terms & conditions governing the usage of Med.Bot by patient. Please read and accept before you can proceed further.
                    </p>
                </div>
                <button type="button" class="close" data-dismiss="modal" id="close-model-btn" (click)="closeModelPopup()" aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
            </div >
            <div class="modal-body t-c" id="terms-model-body">
              <div [innerHTML]="content_text">

              </div>
            </div>
            <div class="modal-footer">
                <div class="col-md-12">

                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="patient-consern-popup" tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content modal-size" >
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">Patient Consent</h5>

      </div>
      <div class="modal-body" id="terms-model-body" style="font-size: 12px; height: 300px;
      overflow-y: auto;">
        <div [innerHTML]="contentHtml">

        </div>
      </div>
      <div class="modal-footer">
        <input type="checkbox" id="t-c-checkbox" class="tandc" [(ngModel)]="acceptedTerms" (change)="acceptTermsAndCondtion($event)" >
        <p class="tandc">I agreed to the <a href="javascript:void(0);" data-toggle="modal" data-target="#myModal" (click)="showTermsAndCondition()">Terms and Conditions</a> of Med.Bot.
        </p>
        <button type="button" id="accept" class="btn btn-primary mr-2" [disabled]="acceptedTerms ? false : true" (click)="saveTermsAndcondition()" translate>
          Accept</button>
          <button type="button" id="cancel" class="btn btn-danger mr-5" data-dismiss="modal" (click)="closeConsernPopup()" translate>
          Cancel
          </button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">

      <div class="modal-body">
        Please accept terms and conditions. It is mandatory, to proceed to booking of appointments.
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" (click)="logOut()" >Logout </button>
        <button type="button" class="btn btn-secondary" (click)="closeLogoutPopup()">Close  </button>
      </div>
    </div>
  </div>
</div>
<!-- T & C-->
