import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from './../auth/auth.service';
import { Component, OnInit, Input} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {PatientService} from './patient.service';
import {SharedService} from '../shared/shared.service';
import {ToastrService} from 'ngx-toastr';
declare var $: any;
@Component({
  selector: 'app-patient',
  templateUrl: './patient.component.html',
  styleUrls: ['./patient.component.css'],
})
export class PatientComponent implements OnInit {
  breadcrumbHeader: string ;
  profilePictureUrl='assets/img/doctors/doctor-thumb-02.png';
  contentText=''
  termsAndconditionID: string;
  acceptedTerms: boolean;
  username=null;
  content_text: any;
  contentHtml: any;
  showDoctorProfilePic: boolean;
  doctorInfo: any={username:null};
  system_of_medicine: any;
  doctorProfilePicture: String;
  practiceLocations: any=[];
  degreeList: any=[];
  degreeString: string;
  doctorId: any;
  doctorSOM: any;
  doctorDept: any;
  doctorSpl: any;
  constructor(
    private translate: TranslateService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private patientService: PatientService,
    private sharedService: SharedService,
    private userService:AuthService,
    private notificationService:ToastrService

  ) {
   }

  ngOnInit(): void {
    if (this.userService.loggedIn()) {
      const lang = localStorage.getItem('pageLanguage');
      this.translate.use(lang);
      const userType = localStorage.getItem('user_type');
      if(userType == 'Doctor'){
        this.router.navigate(['/doctor/dashboard']);
      }
      else if(userType == 'PlatformAdmin'){
        this.router.navigate(['/platform-admin']);
      }
      this.userService.getUserDetail().subscribe(
        (data) => {
          this.username=data['username'];
          this.sharedService.setUserName(data['username']);
          if (data['profile_picture'] !== null) {
            this.sharedService.setPicture(data['profile_picture']);
          }
        },
        (error) => {
          console.log(error);
        }
      );
      this.breadcrumbHeader = this.activatedRoute.snapshot.children[0].data['title'];
      this.getTermsAndCondtion();
      this.getConsentDocumnetData();
    }else{
      this.userService.logout();
      clearInterval();
      this.sharedService.setPicture(this.profilePictureUrl);
      this.sharedService.setUserName (null);
      this.userService.setLogin(true);
      this.router.navigate(['/login']);
    }

  }
  onActivate(event){
    const url = this.router.url;
    const ar = url.split('/');
    const id = ar[ar.length - 1];
    if(id.includes('consult_now')){
      this.sharedService.setActiveLink('search');
    }else{
      this.sharedService.setActiveLink(id);
    }
    if(url.includes('consultation?consultationId')){
      const routerurl = this.router.url;
      const ar = routerurl.split('=');
      const id = ar[ar.length-1];
      this.patientService
      .getConsultationData(id)
      .subscribe((data) => {
        const doctorId = data['doctor_uuid'];
        this.getDoctorDetails(doctorId)
    },error=>{})
  }

    this.breadcrumbHeader = this.activatedRoute.snapshot.children[0].data[
      'title'
    ];
  }
  getDoctorDetails(id) {
    this.showDoctorProfilePic = false;
    this.patientService.getdoctorProfile(id).subscribe(
      (data) => {
        this.doctorInfo = data['user'];
        this.system_of_medicine = data['system_of_medicine'];
        if (this.doctorInfo['profile_picture']) {
          this.doctorProfilePicture = this.doctorInfo['profile_picture'];
        } else {
          this.doctorProfilePicture = 'assets/img/doctors/doctor-thumb-02.png';
        }
        this.doctorId=(data['registrations'][0]==undefined||data['registrations'][0]==null)?'':data['registrations'][0]['number'];
        this.doctorSOM=(data['system_of_medicine']==undefined||data['system_of_medicine']==null)?'':data['system_of_medicine'];
        this.doctorDept=(data['department'][0]==undefined||data['department'][0]==null)?'':data['department'][0]['value'];
        this.doctorSpl=(data['speciality'][0]==undefined||data['speciality'][0]==null)?'':data['speciality'][0]['value'];
        data['qualifications'].map((qual) =>
          this.degreeList.push(qual['name'])
        );

        this.practiceLocations = data['practicelocations'];
        for (let i = this.degreeList.length; i > 0; i--) {
          this.degreeString = this.degreeString + ', ' + this.degreeList[i - 1];
          this.degreeString = this.degreeString.substring(1);
        }
        setTimeout(() => {
          this.showDoctorProfilePic = true;
        }, 1000);
      },
      (error) => {
        console.log(error);
        this.showDoctorProfilePic = false;

        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
    saveTermsAndcondition() {
    this.patientService.updateTermsAndCondtion( this.termsAndconditionID).subscribe(
      (data) => {

        $('.modal').removeClass('modalShowClass');
        $('#myModal').modal('hide');
        $('#patient-consern-popup').modal('hide');
        this.notificationService.success(
          ' Terms and Condition  Accepted',
          'Med.Bot'
        );
      },
      (err) => {
        console.log(err);
        this.notificationService.error(
          ' Terms and Condition  Updation Error',
          'Med.Bot'
        );
      }
    );
  }
  acceptTermsAndCondtion(event) {
    this.acceptedTerms = event.target.checked;
  }
  closeModelPopup() {
    $('.modal').removeClass('modalShowClass');
    $('#patientModal').modal('hide');
    $('#patient-consern-popup').modal('show');

  }
  closeConsernPopup(){
    $('.modal').removeClass('modalShowClass');
    $('#logoutModal').modal({backdrop: 'static', keyboard: false});
    $('#logoutModal').modal('show');
  }
  logOut(){
    $('.modal').removeClass('modalShowClass');
    $('#logoutModal').modal('hide');
    localStorage.clear();
    this.router.navigate(['/login']);
  }
  closeLogoutPopup(){
    $('#logoutModal').modal('hide');
    $('#patient-consern-popup').modal({backdrop: 'static', keyboard: false});
    $('#patient-consern-popup').modal('show');
  }
  showTermsAndCondition(){
    $('#patient-consern-popup').modal('hide');
    $('#patientModal').modal('show');
  }
  getTermsAndCondtion() {
    this.patientService.getTermsAndCondtion().subscribe(data => {
      if (data['user'].hospital != null) {
        localStorage.setItem("patient_hospital_id", data['user'].hospital);
      }
      const terms_conditions_item_pending = data['terms_conditions_item_pending'];
      const terms_conditions_accepted = data['terms_conditions_accepted'];
      if (terms_conditions_accepted.length == 0 && terms_conditions_item_pending) {
        this.termsAndconditionID = terms_conditions_item_pending['uuid'];
        $('#patient-consern-popup').modal({ backdrop: 'static', keyboard: false });
        $('#patient-consern-popup').modal('show');

        this.content_text = terms_conditions_item_pending['content_html'];
      }
    })
  }
  getConsentDocumnetData() {
    this.patientService.getConsentDocumnet().subscribe(
      (data) => {
        this.contentHtml = data['content_html'];
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if(status == 400){
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
}
