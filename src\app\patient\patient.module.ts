import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
// doctor components
import { RouterModule } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { PatientComponent } from './patient.component';
import { PatientProfileComponent } from './patient-profile/patient-profile.component';

import { SearchDoctorComponent } from './search-doctor/search-doctor.component';
import { AppointmentComponent } from './appointment/appointment.component';

import {BreadcrumbService} from './breadcrumb-serice';
import { PatientDashboardComponent } from './patient-dashboard/patient-dashboard.component';
import {SharedModule} from '../shared/shared.module';
import { BookingSuccessComponent } from './booking-success/booking-success.component';
import { MatTabsModule } from '@angular/material/tabs';
import { NgxPrintModule } from 'ngx-print';
import { DatepickerModule, BsDatepickerModule } from 'ngx-bootstrap/datepicker';
@NgModule({
  declarations: [
    PatientComponent,
    PatientProfileComponent,
    SearchDoctorComponent,
    AppointmentComponent,

    PatientDashboardComponent,
    BookingSuccessComponent,

  ],
  imports: [
    BrowserModule,
    SharedModule,
    BrowserAnimationsModule,
    RouterModule,
    FormsModule,
    CommonModule,
    ReactiveFormsModule,
    NgSelectModule,
    MatTabsModule,
    NgMultiSelectDropDownModule,
    NgxPrintModule,

    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    BsDatepickerModule.forRoot(),
    DatepickerModule.forRoot(),
  ],
  exports: [SharedModule,],
  providers: [BreadcrumbService],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA ]
})
export class PatientModule {}

export function HttpLoaderFactory(httpClient: HttpClient) {
  return new TranslateHttpLoader(httpClient);
}
