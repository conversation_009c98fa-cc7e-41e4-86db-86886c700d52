import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as Settings from './../config/settings';
import { delay } from 'rxjs/operators';
// import { settings } from 'cluster';
import { Observable, Observer, BehaviorSubject } from 'rxjs';
import { webSocket } from "rxjs/webSocket";
@Injectable({
  providedIn: 'root',
})
export class PatientService {
  observer: Observer<number>;
  webSocketAvailable: BehaviorSubject<any> = new BehaviorSubject({});

  constructor(private httpClient: HttpClient) { }

  getMessages(): Observable<object> {
    const tokenObj = localStorage.getItem('currentUser');
    // console.log(JSON.parse(tokenObj)['access']);
    const accessToken = JSON.parse(tokenObj)['access'];
    const ws = webSocket(`${Settings.WEBSOCKET_BASE_URL}/ws?username=${accessToken}`);
    ws.subscribe(
      msg => {
        console.log(msg);
        localStorage.setItem('msg_id', msg['e_id']);
        if (msg['e_id']) {
          this.webSocketAvailable.next(msg);
        }
      }, error => {
        console.log(error)
      }, () => console.log('Observer got a complete notification')
    )
    return this.createObservable();
  }


  createObservable(): Observable<object> {
    return new Observable<any>(observer => {
      this.observer = observer;
      // console.log('observer',observer);
    });
  }
  getPatientDetailsByEmailId(email_id) {
    return this
      .httpClient
      .get(`${Settings.API_AUTH_URL_PREFIX}/api/auth/search/${email_id}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getPatientAppointment(query) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/appointments/${query}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getPatientAppointmentHa(current_user_uuid, query) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/doctor/hospital/appointments/${current_user_uuid}/${query}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getAllPatientAppointment(page) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/appointments/?page=${page}&sort_by=Descending`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getAllPatientAppointmentHa(current_user_uuid, page) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/doctor/hospital/appointments/${current_user_uuid}/?page=${page}&sort_by=Descending`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getdoctorProfile(id) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/profile/${id}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  cancelAppointment(id, data) {
    return this.httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/appointments/${id}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getAppointmentData(id) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/appointments/${id}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getAppointmentDataHospital(id) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/doctor/appointments/${id}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }


  sendDiagnosticReport(file, data) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('data', JSON.stringify(data));
    return this
      .httpClient
      .post(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/d_reports/`, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsentDocumnet() {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/consent-document/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDiagnosticReport() {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/d_reports/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }


  getCountryDetail() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/countries/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  sendKycReport(file, data) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('data', JSON.stringify(data));
    return this
      .httpClient
      .post(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/k_documents/`, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  deleteKycReport(id) {
    return this
      .httpClient
      .delete(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/k_documents/${id}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  saveAddress(data) {
    if (data.uuid === null) {
      return this
        .httpClient
        .post(`${Settings.API_AUTH_URL_PREFIX}/api/auth/me/addresses/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    } else {
      return this
        .httpClient
        .patch(`${Settings.API_AUTH_URL_PREFIX}/api/auth/me/addresses/${data.uuid}/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
  }
  getAddressDetail() {
    return this
      .httpClient
      .get(`${Settings.API_AUTH_URL_PREFIX}/api/auth/me/addresses/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getkycDetail() {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/k_documents/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getContactDetail() {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/contacts/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  saveContact(data) {
    if (data.uuid === null) {
      return this
        .httpClient
        .post(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/contacts/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    } else {
      return this
        .httpClient
        .patch(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/contacts/${data.uuid}/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
  }

  saveNotes(data, id) {
    return this
      .httpClient
      .post(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${id}/messages/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getMessage() {
    return this
      .httpClient
      .get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/me/messages/?seen=false&recipient_type=Patient`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsultationHistory(id, page, param?) {
    if (param == undefined || param == null) {
      param = 'fulfilment_status=Suspended%2BCompleted';
    }
    return this
      .httpClient
      .get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/?patient_user_uuid=${id}&page=${page}&${param}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsultationHistoryForBookUser(id, page, param?) {
    if (param == null || param == undefined) {
      param = 'fulfilment_status=Suspended%2BCompleted';
    }
    return this
      .httpClient
      .get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/?book_user_uuid=${id}&page=${page}&${param}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getPrescription(id) {
    return this
      .httpClient
      .get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${id}/documents/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getdoctorProfileData(id) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/users/${id}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  joinConsultation(uuid) {
    const data = {};
    return this.httpClient.post(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${uuid}/join/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsultationData(id) {
    return this
      .httpClient
      .get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${id}/data/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsultationDataHa(id) {
    return this
      .httpClient
      .get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${id}/data/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  sendScreenshot(data, consultation_uuid) {
    return this
      .httpClient
      .post(`${Settings.API_PATIENT_URL_PREFIX}api/c/consultations/${consultation_uuid}/documents/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postMedicalReport(file, data) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('data', JSON.stringify(data));
    return this
      .httpClient
      .post(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/m_reports/`, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postMedicalReportHa(file, data) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('data', JSON.stringify(data));
    return this
      .httpClient
      .post(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/m_reports/`, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postMedicalReportHospital(file, data, patient_uuid) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('data', JSON.stringify(data));
    return this
      .httpClient
      .post(`${Settings.API_PATIENT_URL_PREFIX}/api/p/m_reports/${patient_uuid}/`, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getMedicalReports(query) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/m_reports/${query}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  
  getMedicalReportsById(data) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/m_reports/?${data}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  postMedicalReportById(file, data) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('data', JSON.stringify(data));
    return this
      .httpClient
      .post(`${Settings.API_PATIENT_URL_PREFIX}/api/p/m_reports/`, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getMedicalReportData(query) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/m_reports/${query}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  updateTermsAndCondtion(id) {
    const data = { uuid: id }
    return this
      .httpClient
      .post(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/p_terms/acceptance/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getTermsAndCondtion() {

    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/me/profile/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  setWebScoketMsg() {
    const id = localStorage.getItem('msg_id')
    const data = { message_type: null, e_id: id };
    this.webSocketAvailable.next(data);
  }
  getPatientsEmailId(id, page_number, hspId) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${hspId}/users/?page=${page_number}&uuid=${id}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }
  createOTP(data: any) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/auth/otp/merge_otp/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  otpVerification(data: any) {
    return this.httpClient.post(`${Settings.API_DOCTOR_URL_PREFIX}/api/auth/otp/merge_validate_otp/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getHospital(uuid: string) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/ha_map/?page=1`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
}
