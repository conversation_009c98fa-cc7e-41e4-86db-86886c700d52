<!-- Page Content -->
<h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" (click)="back()"></i>Back</h5>
<div class="content">
    <div class="container-fluid">

        <form id="searchForm" [formGroup]="searchForm">
            <div class="row">
                <div class="col-md-3 col-lg-3 col-xl-3">
                    <div class="form-group mr-1">
                        <input type="text" id="name" style="cursor:pointer" class="form-control form-control-height" formControlName="name" placeholder="Name">
                    </div>
                </div>
                <div class="col-md-3 col-lg-3 col-xl-3">
                    <div class="form-group mr-1">
                        <input type="text" class="form-control form-control-height" style="cursor:pointer" id="Location" formControlName="location" placeholder="Location">
                    </div>
                </div>
                <div class="col-md-3 col-lg-3 col-xl-3">
                    <div class="form-group mr-1">
                        <input type="text" class="form-control form-control-height" style="cursor:pointer" id="postalCode" formControlName="postalCode" placeholder="Postal Code">
                    </div>
                </div>
                <div class="col-md-3 col-lg-3 col-xl-3">
                    <div class="form-group mr-1">
                        <ng-select id="select-systemOfMedicine" [items]="systemOfMedicine" [searchable]="true" bindLabel="systemOfMedicine" formControlName="systemOfMedicine" placeholder="System Of Medicine" [multiple]="false" (change)="getSpecificSpecialityData($event)">
                        </ng-select>
                    </div>
                </div>


            </div>
            <div class="row">
                <div class="col-md-3 col-lg-3 col-xl-3">
                    <div class="form-group mr-1">
                        <ng-select [items]="specificDepartment" [searchable]="true" bindLabel="specificDepartment" formControlName="speciality" placeholder="Speciality" [multiple]="false" (change)="setSpecificSpecialityData($event)">
                        </ng-select>
                    </div>
                </div>
                <div class="col-md-3 col-lg-3 col-xl-3">
                    <div class="form-group mr-1">
                        <ng-select id="select-lang" [items]="languages" [searchable]="true" bindLabel="value" formControlName="language" placeholder="Language" [multiple]="true"[maxSelectedItems]="4">
                        </ng-select>
                    </div>
                </div>
                <!-- <div class="col-md-3 col-lg-3 col-xl-3">
          <div class="form-group mr-1">
            <input type="number" class="form-control form-control-height"  style="cursor:pointer" id="minfees" formControlName="minfees" placeholder="Minimum Fees">
          </div>
        </div> -->
                <div class="col-md-3 col-lg-3 col-xl-3">
                    <div class="form-group mr-1">
                        <ng-select id="select-lang" [items]="feesList" [searchable]="true" bindLabel="feesList" formControlName="maxfees" placeholder="Maximum Fees" [multiple]="false">
                        </ng-select>
                        <!-- <input type="number" class="form-control form-control-height"  style="cursor:pointer" id="maxfees" formControlName="maxfees" placeholder="Maximum Fees"> -->
                    </div>
                </div>
                <!-- <div class="col-md-3 col-lg-3 col-xl-3"> -->
                    <!--  <div class="form-group mr-1">
            <input type="number" class="form-control form-control-height"  style="cursor:pointer" id="maxfees"  placeholder="Hospital">
            </div>-->
                <!-- </div> -->
                <div class="col-md-3 col-lg-3 col-xl-3" *ngIf="hospitalList.length>0">
                    <div class="form-group mr-1">
                        <ng-select id="select-lang" [items]="hospitalList" [searchable]="true" bindLabel="name" formControlName="hospitalLst" placeholder="Hospital" [multiple]="false"
                          (change)="onHospitalSelectionChange($event)" (clear)="onHospitalUnselect($event)">
                        </ng-select>
                        <!-- <input type="number" class="form-control form-control-height"  style="cursor:pointer" id="maxfees" formControlName="maxfees" placeholder="Maximum Fees"> -->
                    </div>
                </div>
                <div class="col-md-3 col-lg-3 col-xl-3">

                    <div class="form-check p-0">
                        <label class="form-check-label" for="exampleCheck1" translate>
              Do you want to consult now? &nbsp;&nbsp;&nbsp;&nbsp; </label>
                        <input type="checkbox" id="tems-check" [checked]="consultNow" (click)="getConsultNowValue($event)" class="form-check-input check-aline" id="exampleCheck1">

                    </div>
                </div>
                <div class="col-md-9 col-lg-9 col-xl-9 text-right">
                    <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-0" [disabled]="searchBtnDisabled" (click)="searchDoctor()">Search Doctors</button>


                </div>
            </div>
            <div class="text-right">
                <span *ngIf="showSearchFieldEmtyWarnig" class="mb-3 mr-0" style="color: red;">Enter Search Value</span>
            </div>

        </form>

        <div class="row mt-3">
            <div class="col-md-12 col-lg-12 col-xl-12">
                <div class="float-right">
                    <nav aria-label="Page navigation example" *ngIf=" this.totalPage>1">
                        <ul class="pagination">
                            <li class="page-item" (click)="firstPageList()" [ngClass]="{'disabled-pagination':currentPage===1}"><a class="page-link">&lt;&lt;</a></li>
                            <li class="page-item" (click)="previousPageList()" [ngClass]="{'disabled-pagination':currentPage===1}"><a class="page-link">&lt;</a></li>
                            <li class="page-item"><a class="page-link">page &nbsp;{{currentPage}}&nbsp;of&nbsp; {{totalPage}}</a></li>
                            <li class="page-item" (click)="nextPageList()" [ngClass]="{'disabled-pagination':currentPage=== totalPage}"><a class="page-link">&gt;</a></li>
                            <li class="page-item" (click)="lastPageList()" [ngClass]="{'disabled-pagination':currentPage=== totalPage}"><a class="page-link">&gt;&gt;</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
            <div class="col-md-12 col-lg-12 col-xl-12">

                <!-- Doctor Widget -->
                <div *ngIf="!isLoading ">
                    <ng-container *ngFor=" let doctor  of doctorList; let i= index">
                        <div class="card">
                            <div class="card-body">
                                <div class="doctor-widget">
                                    <div class="doc-info-left">
                                        <div class="doctor-img">
                                            <a>
                                                <img [src]="doctor.user.profile_picture ? doctor.user.profile_picture:doctorImageUrl" class="img-fluid" alt="User Image">
                                            </a>
                                            <div class="clinic-booking">
                                                <a class="view-pro-btn" id="view-profile-btn" style="cursor:pointer" (click)="viewProfile(doctor.uuid)">View Profile</a>
                                            </div>
                                        </div>
                                        <div class="doc-info-cont">
                                            <h4 class="doc-name"><a>Dr.{{doctor.user.username}}</a></h4>
                                            <div class="clini-infos">
                                                <ul>
                                                    <li><p class="far doc-speciality" *ngFor=" let degrees of doctor.qualifications; let i= index" style="font-size: small; font-family: Arial, Helvetica, sans-serif;">{{degrees.name}}, </p></li>
                                                </ul>
                                                <p class="doc-speciality">{{doctor.system_of_medicine}}</p>
                                                <p class="doc-speciality" *ngIf="doctor.today_available == null">Unavailable Today</p>
                                                <p class="doc-speciality" *ngIf="doctor.today_available != null">Available Today</p>
                                            </div>
                                            <!-- <div class="rating">
                        <i class="fas fa-star filled"></i>
                        <i class="fas fa-star filled"></i>
                        <i class="fas fa-star filled"></i>
                        <i class="fas fa-star filled"></i>
                        <i class="fas fa-star"></i>
                        <span class="d-inline-block average-rating">(17)</span>
                      </div> -->
                                  <!-- <div style="position: absolute;
                                                        bottom: 10px;
                                                        width: 50%;"> -->
                                            <div class="clinic-details">
                                                <p class="doc-location" *ngFor=" let location of doctor.practicelocations; let i= index"><i class="fas fa-map-marker-alt"></i>&nbsp;&nbsp;{{location.addresses?location.addresses.city_town_village
                                                    :"Online Consultation"}}</p>
                                                <!-- <ul class="clinic-gallery">
                          <li>
                            <a  data-fancybox="gallery">
                              <img src="assets/img/features/feature-01.jpg" alt="Feature">
                            </a>
                          </li>
                          <li>
                            <a  data-fancybox="gallery">
                              <img  src="assets/img/features/feature-02.jpg" alt="Feature">
                            </a>
                          </li>
                          <li>
                            <a  data-fancybox="gallery">
                              <img src="assets/img/features/feature-03.jpg" alt="Feature">
                            </a>
                          </li>
                          <li>
                            <a  data-fancybox="gallery">
                              <img src="assets/img/features/feature-04.jpg" alt="Feature">
                            </a>
                          </li>
                        </ul> -->
                                            <!-- </div> -->
                                            </div>
                                            <!-- <div class="clinic-services">
                        <span>Dental Fillings</span>
                        <span> Whitneing</span>
                      </div> -->
                                        </div>
                                    </div>
                                    <div class="doc-info-right">
                                        <div class="clini-infos">
                                            <ul>

                                                <li id="currency"><span id="money"><i class="far fa-money-bill-alt"></i>&#x20B9; &nbsp;&nbsp;{{doctor?.doctor_fee}}</span></li>
                                                <li><span id="exp"><i class="fa fa-medkit"></i>{{doctor.years_of_experience}} years experience</span></li>
                                                <li *ngFor=" let data of doctor.speciality;"><span id="spec"><i class="fa fa-stethoscope"></i>{{data.value}}</span></li>
                                            </ul>
                                        </div>
                                        <div class="clinic-booking">
                                            <a *ngIf="doctor.instant_appointment_slot_available" class="view-pro-btn" id="consult-now-btn" style="cursor:pointer" (click)="cosultNowAppointment(doctor.user.username,doctor.uuid,doctor)">Consult Now</a>
                                            <a class="view-pro-btn" id="book-appointment" style="cursor:pointer" (click)="roterNavigation(doctor.uuid, doctor.practicelocations[0])">Book Appointment</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="doctorList.length===0">
                        <div class="co-md-12 co-lg-12 col-xl-12 text-center">
                            <span class="warring-message">No Doctors Found</span>
                        </div>
                    </ng-container>
                </div>
                <!-- /Doctor Widget -->

                <div class="load-more float-right">
                    <nav aria-label="Page navigation example" *ngIf=" this.totalPage>1">
                        <ul class="pagination">
                            <li class="page-item" (click)="firstPageList()" [ngClass]="{'disabled-pagination':currentPage===1}"><a class="page-link">&lt;&lt;</a></li>
                            <li class="page-item" (click)="previousPageList()" [ngClass]="{'disabled-pagination':currentPage===1}"><a class="page-link">&lt;</a></li>
                            <li class="page-item"><a class="page-link">page &nbsp;{{currentPage}}&nbsp;of&nbsp; {{totalPage}}</a></li>
                            <li class="page-item" (click)="nextPageList()" [ngClass]="{'disabled-pagination':currentPage=== totalPage}"><a class="page-link">&gt;</a></li>
                            <li class="page-item" (click)="lastPageList()" [ngClass]="{'disabled-pagination':currentPage=== totalPage}"><a class="page-link">&gt;&gt;</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

    </div>

</div>

<div class="modal fade" id="consult-now-payment" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Appointment Booking Info</h5>
                <P class="mt-1"><span id="timer" style="color: tomato;" >03:00</span></P>
                <!-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button> -->
            </div>
            <div class="modal-body">
                <div  style="font-family: 'Open Sans', sans-serif !important;">
                    <div  *ngIf="!doctorAprovalStatusPending">
                        <div class="row">
                            <div class="col-md-4 col-sm-6">
                                <p>Doctor Name</p>
                            </div>
                            <div class="col-md-1 col-sm-2">
                                <p>:</p>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <p><strong>{{doctorName}}</strong></p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 col-sm-6">
                                <p>Date & Time</p>
                            </div>
                            <div class="col-md-1 col-sm-2">
                                <p>:</p>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <p>
                                    <strong>{{selctedAppontmentDate|date:'mediumDate'}}&nbsp;{{selctedAppontmentDate|date:'hh:mm a'}}</strong>
                                </p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 col-sm-6">
                                <p>Fee</p>
                            </div>
                            <div class="col-md-1 col-sm-2">
                                <p>:</p>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <p><strong>&#x20B9; &nbsp;&nbsp;{{effective_fee}}</strong></p>
                            </div>
                        </div>
                        <div class="row" *ngIf="amount !=0">

                          <div class="col-md-12 col-sm-6 text-center " *ngIf="amount<=1000">
                            <span> Doctor fee-&nbsp;{{amount}} + Service Fee - {{platform_fee}}(3%)&nbsp;  + GST - {{gst}}(9% SGST + 9% CGST)&nbsp;</span>
                          </div>
                          <div class="col-md-12 col-sm-6 text-center " *ngIf="amount >1000">
                            <span> Doctor fee-&nbsp;{{amount}} + Service Fee-100(10%)&nbsp; {{platform_fee}} + GST-18 (9% SGST + 9% CGST)&nbsp;</span>
                          </div>

                        </div>
                    </div>
                    <div  *ngIf="doctorAprovalStatusPending">
                        <p class="text-center">{{ bookingInitated==true?'Payment is processing,Please wait' :'Please wait for doctor approval'}}</p>

                    </div>
                </div>
            </div>

            <div class="modal-footer text-center">
                <div *ngIf="!doctorAprovalStatusPending">
                    <button type="button" class="btn btn-secondary btn-width" data-dismiss="modal" (click)="cancelInstantRequest()">Cancel</button>
                    <button type="button" class="btn btn-primary btn-width ml-2" (click)="consultNowChecking()">Confirm</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /Page Content -->

<div *ngIf="isLoading">
    <app-loading-spinner></app-loading-spinner>
</div>
