import { Component, OnInit, NgZone } from '@angular/core';
import { BreadcrumbService } from '../breadcrumb-serice';
import { Router, ActivatedRoute } from '@angular/router';
import { DoctorService } from '../../doctor/doctor.service';
import { FormGroup, FormControl } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import * as moment from 'moment';
import { SharedService } from '../../shared/shared.service';
import * as Settings from '../../config/settings';
import { PatientService } from '../patient.service';
import { param } from 'jquery';
import { DatePipe } from '@angular/common';

import { async } from 'rxjs/internal/scheduler/async';
import { DoctorConsultingHoursComponent } from 'src/app/doctor-assistant/practice-location/doctor-consulting-hours/doctor-consulting-hours.component';
declare var Razorpay: any;
declare var $: any;
@Component({
  selector: 'app-search-doctor',
  templateUrl: './search-doctor.component.html',
  styleUrls: ['./search-doctor.component.css'],
  providers: [DatePipe]
})
export class SearchDoctorComponent implements OnInit {
  public doctorList: any = [];
  public selectedCity: string;
  public languages: any = [];
  public languageQueryVaule: string;
  public searchForm: FormGroup;
  public systemOfMedicianValue = [];
  public specialityValue = [];
  public showSearchFieldEmtyWarnig = false;
  public searchFieldValue = false;
  public searchBtnDisabled = false;
  public speciality = {};
  public apiSearchCall: boolean;
  public systemOfMedicine = ['Allopathy', 'Ayurveda', 'Dental', 'Homoeopathy', 'Siddha', 'Unani'];
  public practiceType = [
    { name: 'Own Practice', value: 'own_practice' },
    { name: 'Hospital Based', value: 'hospital_based' },
  ];
  public fees = [];
  practiceLocationId: any;
  public amount: number;
  public currency: string;
  public selectedDoctorIndex = null;
  public currentPage = 1;
  public totalPage: number;
  isLoading = false;
  specialityQueryValue: string;
  systemOfMedicianQueryValue: string;
  searchQueryParms: any[];
  consultNow: boolean;
  doctorName: any;
  selctedAppontmentDate = moment();
  selectedSlot = {};
  slotUuid = '';
  pgwOptions = {};
  public paymentDetails = {};
  bookingData: any;
  appoimentSlotsSelected: any;
  instantRequestId: any;
  public route: string;
  doctorImageUrl = 'assets/img/doctors/doctor-thumb-02.png';
  public feesList = [
    0,
    100,
    200,
    300,
    400,
    500,
    600,
    700,
    800,
    900,
    1000,
    1100,
    1200,
    1300,
    1400,
    1500,
    1600,
    1700,
    1800,
    1900,
    2000,
    2100,
    2200,
    2300,
    2400,
    2500,
    2600,
    2700,
    2800,
    2900,
    3000,
    'All'
  ];
  public searchStringValue = null;
  query: any;
  doctorAppointmentUuid: any;
  doctorAprovalStatusPending: boolean;
  specificSpeciality: any = [];
  bookingInitated: boolean;
  sub: any;
  message_id: any;
  showInstantsAppointmentNotification: boolean;
  count: number = 0;
  splitString: string;
  downloadTimer: NodeJS.Timeout;
  platform_fee: number;
  gst: number
  effective_fee: any;
  showDoctorJoinedVideoNotification: boolean;
  showDoctorJoinedConsultationNotification: boolean;
  showDoctorInstantAppointmentDeclinedNotification: boolean;
  showDoctorEndedConsultationNotification: boolean;
  showDoctorIsBusyNotification: boolean;
  showInstantResponseTimedOutNotification: boolean;
  msgId: string = ''
  duration: number = Settings.popupDuration;
  public slots_availability: any;
  consultinghoursgroups: any;
  public practiceLocations = [];
  public todayAvailable: number;
  hospital_uuid = null;
  hospital_id: string;
  isPublicDoctor: boolean;
  specificDept = {};
  specificDepartment = [];
  hospitalList = [];
  defaultHspId: string;


  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private doctorService: DoctorService,
    private notificationService: ToastrService,
    private location: Location,
    private sharedService: SharedService,
    private ngZone: NgZone,
    private patientService: PatientService,
    private datePipe: DatePipe
  ) { }

  ngOnInit(): void {
    this.sharedService.setActiveLink('search');
    this.activatedRoute.params.subscribe((params) => {
      this.currentPage = params['page'];
      this.apiSearchCall = true;
      this.query = params['query'];
      const url = params['query'];
      if (url.includes('consult_now=false')) {
        this.consultNow = false;
        this.route = '/patient/search';
        if (url.includes('?')) {
          this.searchStringValue = url;
        } else {
          this.searchStringValue = '?' + url;
        }

      } else {
        this.searchStringValue = url;
        this.consultNow = true;
      }
      this.getDoctorList(this.currentPage);
      this.getHospitalList();
    });

    this.addSearchFormControl();
    if ((this.router.url).toString().includes('search')) {
      this.patientService.webSocketAvailable.subscribe(
        data => {
          if (data['e_id'] !== this.msgId && data['e_id'] !== undefined) {
            this.msgId = localStorage.getItem('msg_id');
            if (data['message_type'] == "Instant Appointment Approved") {
              // clearInterval(this.downloadTimer);
              this.doctorAprovalStatusPending = true;
              // this.isLoading = true;
              if (!!this.showInstantsAppointmentNotification) {
                this.confirmBooking();
              }
            } else
              if (data['message_type'] == "Instant Appointment Declined") {
                clearInterval(this.downloadTimer);
                this.doctorAprovalStatusPending = false;
                $('#consult-now-payment').modal('hide');
                this.isLoading = false;
                if (this.showDoctorInstantAppointmentDeclinedNotification) {
                  this.notificationService.warning(
                    'Instant Appointment Declined',
                    'Med.Bot'
                  );
                  this.patientService.setWebScoketMsg();
                  this.showDoctorInstantAppointmentDeclinedNotification = false;
                  location.reload();
                } else {
                  this.patientService.setWebScoketMsg();
                }
              } else if (data['message_type'] == "Doctor Joined Consultation") {
                // if (localStorage.getItem('user_type')== 'Patient'){
                if (this.showDoctorJoinedConsultationNotification) {
                  this.notificationService.success('Doctor Joined Consultation');
                  this.showDoctorJoinedConsultationNotification = false;
                }
                this.patientService.setWebScoketMsg();
                // }
              } else if (data['message_type'] == "Doctor Joined Video") {
                if (this.showDoctorJoinedVideoNotification) {
                  this.notificationService.success('Doctor Joined Video');
                  this.showDoctorJoinedVideoNotification = false;
                }
              } else if (data['message_type'] == "Doctor Ended Consultation") {
                this.patientService.setWebScoketMsg();
              } else if (data['message_type'] == "Instant Response TimedOut") {
                $('#consult-now-payment').modal('hide');
                this.notificationService.warning('Response TimedOut');
                this.patientService.setWebScoketMsg();
              } else if (data['message_type'] == "Doctor Is Busy") {
                $('#consult-now-payment').modal('hide');
                clearInterval(this.downloadTimer);
                if (this.showDoctorIsBusyNotification) {
                  this.notificationService.warning('Doctor is unavailable');
                  this.showDoctorIsBusyNotification = false;
                }
                // location.reload();
                this.patientService.setWebScoketMsg();
              }
          }
        });
      this.sub = this.patientService.getMessages().subscribe();
    }
    this.getLanguage();
    // this.getSpecialityData();
    this.getSystemOfMedicine();
  }
  //ngOninit Closed

  addSearchFormControl() {
    this.searchForm = new FormGroup({
      name: new FormControl(''),
      language: new FormControl(),
      speciality: new FormControl(),
      systemOfMedicine: new FormControl(),
      location: new FormControl(''),
      postalCode: new FormControl(''),
      minfees: new FormControl(''),
      maxfees: new FormControl(),
      hospitalLst: new FormControl()
    });
  }

  roterNavigation(id: string, location) {
    let profileStatus = localStorage.getItem('isProfileCompleted');
    if (profileStatus == 'false') {
      this.notificationService.warning('Please Complete your profile before book an appointment', 'Med.Bot');
    }
    else {
      this.practiceLocationId = location.uuid;
      if (this.practiceLocationId) {
        this.router.navigate([
          '/patient/appointment/',
          id,
          this.practiceLocationId,
          'booking',
        ]);
      }
    }
  }

  getDoctorList(page) {
    this.totalPage = 0;
    let searchList = [];
    this.isLoading = true;
    searchList = this.sharedService.getSearListBypage();
    if (searchList.length > 0) {
      for (const data of searchList) {
        if (data.page_number == page && data.query_string == this.query) {
          this.apiSearchCall = false;
          this.searchStringValue = data['query_string'];
          this.totalPage = data['total_pages'];
          this.currentPage = data['page_number'];
          this.doctorList = data['results'];
          this.isLoading = false;
        }
      }
      if (!!this.apiSearchCall) {
        this.getDoctor(this.currentPage);
      }
    } else {
      if (this.route === '/patient/search') {
        this.getDoctor(this.currentPage);
      } else {
        this.getConsultNowDoctor(this.currentPage);
      }
    }
  }

  getDoctor(page) {
    var hospital_uuid = localStorage.getItem("patient_hospital_id");
    this.doctorService.getDoctersBypage(page, this.searchStringValue, hospital_uuid).subscribe(
      (data) => {

        this.totalPage = data['total_pages'];
        this.currentPage = data['page_number'];
        this.doctorList = data['results'];
        // this.todayAvailable=data['available_today'];
        // console.log(this.todayAvailable);
        // console.log(this.doctorList[0].practicelocations);
        // let mydate = new Date();
        // let today = this.datePipe.transform(mydate, 'yyyy-MM-dd');
        // console.log(today);
        // console.log(this.doctorList.length);

        // for(let i=0; i<this.doctorList.length; i++){
        //   for(const val of this.doctorList)
        // }

        // this.slots_availability = this.doctorList['practicelocations'];
        // console.log(this.doctorList);
        // this.slots_availability = data['slots_availability'];
        // console.log(this.slots_availability);
        // for (let i=0;i<this.slots_availability.length; i++){
        //   console.log(this.slots_availability[i].consultinghoursgroups);
        // }

        this.sharedService.searListBypage(
          this.doctorList,
          this.currentPage,
          this.searchStringValue,
          this.totalPage
        );
        this.isLoading = false;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        this.isLoading = false;
      }
    );
  }

  getConsultNowDoctor(page) {
    var hospital_uuid = localStorage.getItem("patient_hospital_id");
    this.consultNow = true;
    this.doctorService.getDoctersByConsultNow(page, hospital_uuid).subscribe(
      (data) => {
        this.totalPage = data['total_pages'];
        this.currentPage = data['page_number'];
        this.doctorList = data['results'];
        this.isLoading = false;
        const public_instant_request = localStorage.getItem('public_instant_request');
        if (public_instant_request == 'true') {
          const doctorId = localStorage.getItem('Doctor');
          const doctorDetatils = this.doctorList.filter(obj => obj.uuid === doctorId);
          this.cosultNowAppointment(doctorDetatils[0]?.user?.username, doctorId, doctorDetatils[0]);
        }
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        this.isLoading = false;
      }
    );
  }

  getLanguage() {
    this.doctorService.getDoctorLanguages().subscribe(
      (data) => {
        this.languages = Object.values(data);
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  searchDoctor() {
    this.totalPage = 0;
    this.searchBtnDisabled = true;
    this.isLoading = true;
    this.splitString = '';
    this.searchQueryParms = [];
    if (this.searchForm.controls['name'].value) {
      const name = 'name=' + this.searchForm.controls['name'].value;
      this.searchFieldValue = true;
      this.searchQueryParms.push(name);
    }
    if (this.searchForm.controls['location'].value) {
      this.searchFieldValue = true;
      const location = 'location=' + this.searchForm.controls['location'].value;
      this.searchQueryParms.push(location);
    }
    if (this.searchForm.controls['postalCode'].value) {
      this.searchFieldValue = true;
      const postalCode =
        'postal_code=' + this.searchForm.controls['postalCode'].value;
      this.searchQueryParms.push(postalCode);
    }
    if (this.searchForm.controls['systemOfMedicine'].value) {
      this.searchFieldValue = true;
      this.systemOfMedicianQueryValue = 'system_of_medicine=';
      const systemOfMedicianValue = this.searchForm.controls['systemOfMedicine']
        .value;
      const data = this.systemOfMedicianQueryValue + systemOfMedicianValue;
      this.searchQueryParms.push(data);
    }
    if (this.searchForm.controls['speciality'].value) {

      this.searchFieldValue = true;
      this.specialityQueryValue = 'speciality=';
      const speciality = this.searchForm.controls['speciality'].value;

      const data = this.specialityQueryValue + speciality;
      this.searchQueryParms.push(data);
    }
    const language = this.searchForm.controls['language'].value;
    if (language) {
      if (language.length > 0) {
        for (let lan of this.searchForm.controls['language'].value) {
          this.splitString = this.splitString + lan.value + ',';

        }
        this.searchFieldValue = true;
        this.languageQueryVaule = 'languages=';
        // const lang = this.searchForm.controls['language'].value;
        const data = this.languageQueryVaule + this.splitString.substring(0, this.splitString.length - 1);

        this.searchQueryParms.push(data);
      }
    }

    if (this.searchForm.controls['minfees'].value) {
      this.searchFieldValue = true;
      const minFee = 'fee_gte=' + this.searchForm.controls['minfees'].value;

      this.searchQueryParms.push(minFee);
    }
    if (this.searchForm.controls['maxfees'].value || this.searchForm.controls['maxfees'].value == 0) {
      this.searchFieldValue = true;
      if (
        this.searchForm.controls['maxfees'].value === 'All'
      ) {

      } else {
        const maxFee = 'fee_lte=' + this.searchForm.controls['maxfees'].value;
        this.searchQueryParms.push(maxFee);
      }
    }

    if (this.consultNow) {
      const consultNow = 'consult_now=true&is_busy=False';
      this.searchQueryParms.push(consultNow);
      this.searchFieldValue = true;
    } else {
      const consultNow = 'consult_now=false';
      this.searchFieldValue = true;
      this.searchQueryParms.push(consultNow);
    }
    if (this.searchFieldValue) {
      let searchString = '?';
      for (let i = 0; i < this.searchQueryParms.length; i++) {
        if (i === this.searchQueryParms.length - 1) {
          searchString = searchString + this.searchQueryParms[i];
        } else {
          searchString = searchString + this.searchQueryParms[i] + '&';
        }
      }

      this.searchStringValue = searchString;
      this.showSearchFieldEmtyWarnig = false;
      this.hospital_uuid = localStorage.getItem("patient_hospital_id");
      this.doctorService.searchDoctors(searchString, this.hospital_uuid).subscribe(
        (data) => {
          this.totalPage = data['total_pages'];
          this.currentPage = data['page_number'];
          this.selectedDoctorIndex = null;
          this.doctorList = data['results'];
          this.searchFieldValue = false;
          this.searchBtnDisabled = false;
          this.isLoading = false;
          this.selectedCity = '';
        },
        (error) => {
          this.searchBtnDisabled = false;
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    } else {
      this.showSearchFieldEmtyWarnig = true;
      this.searchBtnDisabled = false;
      this.isLoading = false;
    }
  }
  nextPageList() {
    if (this.searchStringValue === null) {
      this.searchStringValue = 'consult_now=false';
    }
    this.currentPage = this.currentPage + 1;
    if (this.totalPage >= this.currentPage) {
      this.router.navigate([
        '/patient/search/',
        this.currentPage,
        this.searchStringValue,
      ]);
    } else {
      this.currentPage = this.currentPage - 1;
    }
  }

  lastPageList() {
    if (this.searchStringValue === null) {
      this.searchStringValue = 'consult_now=false';
    }
    this.router.navigate([
      '/patient/search/',
      this.totalPage,
      this.searchStringValue,
    ]);
  }
  firstPageList() {
    if (this.searchStringValue === null) {
      this.searchStringValue = 'consult_now=false';
    }
    this.currentPage = 1;
    this.router.navigate([
      '/patient/search/',
      this.currentPage,
      this.searchStringValue,
    ]);
  }
  previousPageList() {
    if (this.searchStringValue === null) {
      this.searchStringValue = 'consult_now=false';
    }
    this.currentPage = this.currentPage - 1;
    if (this.totalPage >= this.currentPage && this.currentPage > 0) {
      this.router.navigate([
        '/patient/search/',
        this.currentPage,
        this.searchStringValue,
      ]);
    } else {
      this.currentPage = this.currentPage + 1;
    }
  }
  back() {
    this.location.back();
  }
  checkDoctorIsPublic() {
    this.hospital_id = localStorage.getItem('hospital_id');
    if (this.hospital_id != null) {
      this.isPublicDoctor = false;
    }
    else {
      this.isPublicDoctor = true;
      this.hospital_id = null;
    }
    return this.isPublicDoctor;
  }
  async getSpecialityData() {
    this.checkDoctorIsPublic();
    if (this.checkDoctorIsPublic()) {
      try {
        const data = await this.doctorService.getSpecialitywithoutHospital1();
        this.speciality = data;
      } catch (error) {
        console.error('Error fetching specialties', error);
      }
      // this.doctorService.getSpecialitywithoutHospital().subscribe(
      //   (data) => {
      //     this.speciality = data;
      //   },
      //   (error) => {
      //     console.log(error);
      //     const status = error['status'];
      //     if (status == 400) {
      //       this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      //     }
      //     else {
      //       this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      //     }
      //   }
      // );
    }
    else {
      this.doctorService.getSpeciality(this.hospital_id).subscribe(
        (data) => {
          this.speciality = data;
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
  }
  getConsultNowValue(event) {
    this.consultNow = event.target.checked;
  }

  cosultNowAppointment(name, id, data) {
    let profileStatus = localStorage.getItem('isProfileCompleted');
    if (profileStatus == 'false') {
      this.notificationService.warning('Please Complete your profile before book an appointment', 'Med.Bot');
    }
    else {
      let timer = this.duration, minutes, seconds;
      minutes = Math.floor(timer / 60);
      seconds = Math.floor(timer % 60);
      minutes = minutes < 10 ? "0" + minutes : minutes;
      seconds = seconds < 10 ? "0" + seconds : seconds;
      const display = document.querySelector('#timer');
      display.textContent = minutes + ":" + seconds;
      localStorage.setItem('doctor_id', id);
      this.selctedAppontmentDate = moment();
      this.doctorAprovalStatusPending = false;
      this.doctorName = name;
      this.doctorAppointmentUuid = id;
      this.effective_fee = parseFloat(data.effective_fee);
      this.gst = parseFloat(data.gst);
      this.platform_fee = parseFloat(data.platform_fee);
      this.amount = this.effective_fee - (this.platform_fee + this.gst);
      $('#consult-now-payment').modal({ backdrop: 'static', keyboard: false });
    }
  }

  confirmBooking() {
    $('#consult-now-payment').modal('hide');
    clearInterval(this.downloadTimer);
    if (!!this.instantRequestId && this.showInstantsAppointmentNotification) {
      this.showInstantsAppointmentNotification = false;
      if (this.effective_fee == 0) {
        $('#consult-now-payment').modal('hide');
        this.isLoading = true;
        const appointmentData = {};
        this.sharedService
          .consultNowZeroBooking(this.instantRequestId, appointmentData)
          .subscribe(
            (data) => {
              this.patientService.setWebScoketMsg();
              const id = data['consultation_uuid'];
              const url = `/patient/consultation?consultationId=${id}`;
              this.doctorService.joinConsultation(id).subscribe((data) => {
                this.sub.unsubscribe();
                this.ngZone.run(() => this.router.navigateByUrl(`${url}`)).then();
              });
            }, error => {
              console.log(error);
              const status = error['status'];
              if (status == 400 || status == 409) {
                const message = error['error']['error'];
                const error_message = error['error']['error_message']
                if (message) {
                  this.notificationService.error(`${message}`, 'Med.Bot');
                } else if (error_message) {
                  this.notificationService.error(`${error_message}`, 'Med.Bot');
                } else {
                  this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
                }

              }
              else {
                this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
              }
            })
      } else {
        const appointmentData = {};
        this.sharedService
          .consultNowinitiateBooking(this.instantRequestId, appointmentData)
          .subscribe(
            (data) => {
              // this.patientService.setWebScoketMsg();
              this.pgwOptions = data;
              this.pgwOptions['amount'] = data['order']['amount'];
              this.openRazorpayCheckout();
            },
            (error) => {
              console.log(error);
              const status = error['status'];
              if (status == 400) {
                this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
              }
              else {
                this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
              }
            }
          );
      }
    } else {
      // this.patientService.webSocketAvailable.next(' ');;
    }
  }

  openRazorpayCheckout() {
    this.bookingInitated = true;
    this.pgwOptions['modal'] = {
      ondismiss: () => {
        this.notificationService.error(
          'Payment aborted. Please try again',
          'Med.Bot'
        );
        this.paymentFailed();
        this.doctorAprovalStatusPending = false;
        this.isLoading = false;
      },
    };
    this.pgwOptions['handler'] = (response) => {
      this.sendPaymentDetails(response);
    };
    const options = this.pgwOptions;
    const rzp = new Razorpay(options);
    rzp.open();
  }

  paymentFailed() {
    const paymentStatus = { status: 'Payment Failed' };
    this.sharedService
      .updateConsultNowStatus(this.instantRequestId, paymentStatus)
      .subscribe(
        (result) => {
          $('#consult-now-payment').modal('hide');
          clearInterval(this.downloadTimer);
          this.patientService.setWebScoketMsg();
        }, error => {
          console.log(error);
          this.isLoading = false;
        });
  }

  sendPaymentDetails(response) {
    const orderId = this.pgwOptions['order']['id'];
    this.paymentDetails['razorpay_order_id'] = orderId;
    this.paymentDetails['razorpay_payment_id'] = response['razorpay_payment_id'];
    this.paymentDetails['patient_type'] = 'instant';
    this.sharedService
      .completeConsultNow(this.instantRequestId, this.paymentDetails)
      .subscribe(
        (data) => {
          $('#consult-now-payment').modal('hide');
          clearInterval(this.downloadTimer);
          this.patientService.setWebScoketMsg();
          const id = data['consultation_uuid'];
          const url = `/patient/consultation?consultationId=${id}`;
          this.doctorService.joinConsultation(id).subscribe((data) => {
            this.sub.unsubscribe();
            this.ngZone.run(() => this.router.navigateByUrl(`${url}`)).then();
          });
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          this.doctorAprovalStatusPending = false;
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
  }

  consultNowChecking() {
    this.bookingInitated = false;
    const instantRequestPaylod = { doctor: this.doctorAppointmentUuid };
    this.sharedService.instantRequest(instantRequestPaylod).subscribe(
      (data) => {
        localStorage.setItem('public_instant_request', 'false');
        this.showInstantsAppointmentNotification = true;
        this.showDoctorJoinedVideoNotification = true;
        this.showDoctorJoinedConsultationNotification = true;
        this.showDoctorInstantAppointmentDeclinedNotification = true;
        this.showDoctorEndedConsultationNotification = true;
        this.showDoctorIsBusyNotification = true;
        this.showInstantResponseTimedOutNotification = true;
        this.doctorAprovalStatusPending = true;
        this.instantRequestId = data['uuid'];
        this.selctedAppontmentDate = moment(data['request_datetime']);
        const display = document.querySelector('#timer');
        this.timeRamainder(this.duration, display);
      },
      (error) => {
        const status = error['status'];
        if (status == 400) {
          const errMessage = error['error']['error_message']
          if (errMessage) {
            this.notificationService.error(`${errMessage}`, 'Med.Bot');
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }


  timeRamainder(duration, display) {
    let timer = duration, minutes, seconds;
    this.downloadTimer = setInterval(() => {
      minutes = Math.floor(timer / 60);
      seconds = Math.floor(timer % 60);

      minutes = minutes < 10 ? "0" + minutes : minutes;
      seconds = seconds < 10 ? "0" + seconds : seconds;
      if (minutes >= 0 && seconds >= 0) {
        display.textContent = minutes + ":" + seconds;
      }
      if (--timer == 0) {
        $('#consult-now-payment').modal('hide');
        $('#modal').modal('hide');

        // clearInterval(this.downloadTimer);
      }
      console.log(timer);

    }, 1000);
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
    const isPublicPatient = localStorage.getItem('isPublicPatient');
    if (isPublicPatient == 'true') {
      localStorage.removeItem('patient_hospital_id');
    }
  }
  viewProfile(id) {
    localStorage.setItem('Doctor', id);
    this.router.navigate(['/view-doctor/public-profile']);
  }

  getSpecificSpecialityData(data) {
    this.searchForm.controls[`speciality`].setValue(null);
    // if (data == 'Allopathy') {
    //   this.specificSpeciality = this.speciality['Allopathy'];
    // } else if (data == 'Ayurveda') {
    //   this.specificSpeciality = this.speciality['Ayurveda'];
    // } else if (data == 'Dental') {
    //   this.specificSpeciality = this.speciality['Dental'];
    // } else if (data == 'Homoeopathy') {
    //   this.specificSpeciality = this.speciality['Homoeopathy'];
    // }
    // else if (data == 'Siddha') {
    //   this.specificSpeciality = this.speciality['Siddha'];
    // }
    // else if (data == 'Unani') {
    //   this.specificSpeciality = this.speciality['Unani'];
    // } else {
    //   this.specificSpeciality = []
    // }
    this.specificDepartment = [];
    this.specificDept[data].departments.forEach(department => {
      this.specificDepartment.push(department.value)
    });
  }
  cancelInstantRequest() {
    localStorage.setItem('public_instant_request', 'false');
  }

  getSystemOfMedicine() {
    this.doctorService.getSystemOfMedicine().subscribe(
      (data) => {
        this.systemOfMedicine = Object.keys(data);
        this.specificDept = data;
      },
      (error) => {
        this.notificationService.error('Internal server error', 'Med.Bot');
        console.log(error);
      }
    );
  }
  setSpecificSpecialityData(data) {
    if (data != undefined) {
      this.searchForm.get('speciality').setValue(data);
    }
    else {
      this.searchForm.get('speciality').setValue(null);
    }
  }
  getHospitalList() {
    const uuid = localStorage.getItem('current_user_uuid');
    this.patientService.getHospital(uuid).subscribe((data) => {
      data[0].hospital.forEach(element => {
        this.hospitalList.push(element);
      });
    },
      (error) => {
        this.notificationService.error(`${error.statusText}`, 'Med.Bot');
        console.log(error);
      });
  }
  onHospitalSelectionChange(event: any) {
    const selectedHospital = this.searchForm.value.hospitalLst;
    const isPublicPatient = localStorage.getItem('isPublicPatient');
    if (isPublicPatient == 'false') {
      this.defaultHspId = localStorage.getItem('patient_hospital_id');
    }
    if (selectedHospital != null && selectedHospital != undefined) {
      localStorage.setItem('patient_hospital_id', selectedHospital['uuid']);
    }
    else {
      localStorage.setItem('patient_hospital_id', '');
    }
  }
  onHospitalUnselect(event: any) {
    const isPublicPatient = localStorage.getItem('isPublicPatient');
    if (isPublicPatient == 'false') {
      localStorage.setItem('patient_hospital_id', this.defaultHspId);
    } else {
      localStorage.setItem('patient_hospital_id', '');
    }
    this.searchDoctor();
  }

}
