<div class="content success-page-cont" *ngIf="!isLoading">
    <div class="container-fluid">

        <div class="row justify-content-center">
            <div class="col-lg-6">

                <!-- Success Card -->
                <div class="card success-card">
                    <div class="card-body">
                        <div class="success-cont">
                            <i class="fas fa-check"></i>
                            <h6>Approval(s) for the selected Doctors Completed Successfully!</h6>
                            <!-- <p>Appointment booked with <strong>{{docotorName}}</strong><br> on <strong>{{appointmentStartDate|date:'mediumDate'}} &nbsp;{{appointmentStartDate|date:'hh:mm a'}} &nbsp;to {{appointmentEndDate |date:'h:mm a'}}</strong></p>
                            <div class="row">
                              <div class="col-md-2">

                              </div>
                              <div class="col-md-8">
                                <input type="text" name="notes"  class="form-control" placeholder="Enter any note to Dr before Consulting">

                              </div>
                              <div class="col-md-2">

                              </div>

                            </div> -->
                            <a  class="btn btn-primary view-inv-btn mt-4" (click)="onClose()">Close</a>

                        </div>
                    </div>
                </div>
                <!-- /Success Card -->

            </div>
        </div>

    </div>
</div>
<div *ngIf="isLoading" class="centered">
  <app-loading-spinner></app-loading-spinner>
</div>
<!-- /Page Content -->
