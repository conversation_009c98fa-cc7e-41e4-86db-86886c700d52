import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-approval-success',
  templateUrl: './approval-success.component.html',
  styleUrls: ['./approval-success.component.css']
})
export class ApprovalSuccessComponent implements OnInit {
  isLoading=false;
  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  onClose(){
    this.router.navigateByUrl('/platform-admin/dashboard');
  }
}
