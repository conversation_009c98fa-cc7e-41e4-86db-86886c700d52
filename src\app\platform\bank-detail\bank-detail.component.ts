import { Component, OnInit } from '@angular/core';
import { ActivatedRoute,Router } from '@angular/router'
import { PlatformService } from '../platform.service'
import { Location } from '@angular/common';
// import { FormBuilder, FormGroup, Validators, FormArray, FormControl } from '@angular/forms';

@Component({
  selector: 'app-bank-detail',
  templateUrl: './bank-detail.component.html',
  styleUrls: ['./bank-detail.component.css']
})
export class BankDetailComponent implements OnInit {
doctorId: string;
// public bank: any;
ac_name: string;
ac_number:string;
ac_type:string;
bank_name:string;
branch:string;
ifsc:string;
// public bankAccountDetailForm: FormGroup;

  constructor(
    private route:ActivatedRoute,
    private platformService:PlatformService,
    private location:Location,
    // private formBuilder: FormBuilder,

  ) { }

  ngOnInit(): void {
    this.doctorId = this.route.snapshot.paramMap.get('uuid')
    this.platformService.getBankAccountDetail(this.doctorId).subscribe(
      data =>{
        this.ac_name = data['results'][0]['account_name'];
        this.ac_number = data['results'][0]['account_number'];
        this.ac_type = data['results'][0]['account_type'];
        this.bank_name = data['results'][0]['bank_name'];
        this.branch = data['results'][0]['branch_name'];
        this.ifsc = data['results'][0]['ifsc_code'];
        // console.log('bank',this.bank);       
      },
      error =>{
        console.log(error);
      }
    );

  }
  back() {
    this.location.back();
  }


}
