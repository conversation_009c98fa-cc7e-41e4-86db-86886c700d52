<div class="card">
    <h5 class="mb-4 ms"><i class="fas fa-chevron-circle-left" style=" color: #20C0F3;" (click)="back()"></i>Back</h5><div class="row mb-5">
    <div class="card-body">
    <div class="row">
      <div class="col-md-12">
        <h4 class="mb-2 dashboard-font-size" >Consultation Summary</h4>
        <div class="appointment-tab">
  
          <div class="col-md-3">
            <div class="form-group ">
                <input type="text" id="from_date" placeholder="From Date" onkeydown="return false" class="form-control input-field-border"[minDate]="" [maxDate]="" bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }">
                <input type="text" id="to_date" placeholder="To Date" onkeydown="return false" class="form-control input-field-border" [minDate]="" [maxDate]="" bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }">
                <select name="fulfilment" id="fulfilment">
                    <option value="all">All</option>
                    <option value="Started">Started</option>
                    <option value="Not Started">Not Started</option>
                    <option value="Completed">Completed</option>
                    <option value="Suspended">Suspended</option>
                    <option value="Cancelled">Cancelled</option>
                    <option value="Doctor Missed">Doctor Missed</option>
                    <option value="Patient Missed">Patient Missed</option>
                    <option value="Both Missed">Both Missed</option>
                  </select>
                  <select name="appointment_type" id="appointment_type">
                    <option value="all">All</option>
                    <option value="regular_appointment">Doctor Appointment</option>
                    <option value="instant_appointment">Doctor Instant Appointment</option>
                  </select>
                <button type="button" class="btn btn-primary" (click)="ngOnInit()">Submit</button>
              </div>
          </div>
    
          <!-- Appointment Tab -->
          <!-- <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
            <li class="nav-item">
              <a class="nav-link active" href="#admin-user" data-toggle="tab">Current Month</a>
            </li>
          </ul> -->
          <!-- /Appointment Tab -->
          <div class="tab-content">
  
            <!-- Upcoming Appointment Tab -->
            <div class="tab-pane show active" id="admin-user">
              <div class="col-md-12 tab_pager_position">
                <div class="tab_pager_position">
                  <nav
                    aria-label="Page navigation example"
                    *ngIf="this.earningReportTotalPage > 1"
                  >
                    <ul class="pagination pager_position" >
                      <li
                        class="page-item"
                        (click)="earningReportFirstPageList()"
                        [ngClass]="{
                          'disabled-pagination':
                            earningReportCurrentPage === 1
                        }"
                      >
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li
                        class="page-item"
                        (click)="earningReportPreviousPageList()"
                        [ngClass]="{
                          'disabled-pagination':
                            earningReportCurrentPage === 1
                        }"
                      >
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link"
                          >page &nbsp;{{
                            earningReportCurrentPage
                          }}&nbsp;of&nbsp; {{ earningReportTotalPage }}</a
                        >
                      </li>
                      <li
                        class="page-item"
                        (click)="earningReportNextPageList()"
                        [ngClass]="{
                          'disabled-pagination':
                            earningReportCurrentPage ===
                            earningReportTotalPage
                        }"
                      >
                        <a class="page-link">&gt;</a>
                      </li>
                      <li
                        class="page-item"
                        (click)="earningReportLastPageList()"
                        [ngClass]="{
                          'disabled-pagination':
                            earningReportCurrentPage ===
                            earningReportTotalPage
                        }"
                      >
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
              <div *ngIf="earningReportLoading">
                <app-loading-spinner></app-loading-spinner>
            </div>
              <div class="card card-table mb-0" *ngIf="!earningReportLoading">
                <div class="card-body">
                  <div class="table-responsive" >
                    <table class="table table-hover table-center mb-0">
                      <thead>
                        <tr>
                          <th>Sl.no</th>
                          <th>Patient Name</th>
                          <th>Consultation Date</th>
                          <th>Consultation Time</th>
                          <th>Doctor's Earning</th>
                          <th>Consultation Type</th>
                          <th>Consultation Status</th>
                          <!-- <th>Platform Share</th> -->
                          <!-- <th>Total</th> -->
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let data of currentMonthEarning;let i=index">
                          <td>{{earningReportSerialNumber+i+1}}</td>
                          <td>{{data.customer_name}}    </td>
                          <td>&nbsp;&nbsp;{{data.start_datetime | date:'dd-MM-yyyy'}}</td>
                          <td>&nbsp;{{data.start_datetime | date:'hh:mm'}}-&nbsp;{{data.end_datetime | date:'hh:mm a'}}</td>  
                          <td>{{data.net_amount}}</td>
                          <td>{{data.order_type}}</td>
                          <td>{{data.consultation_status}}</td>
                        </tr>
                        <tr *ngIf="currentMonthEarning.length >0">
                          <td></td>
                          <td></td>
                          <td></td>
                          <th>Total</th>
  
                          <th>{{doctorTotalAmount}}</th>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="text-center mb-2 p-2 mt-2">
                    <span class="appointmentList-no-data" *ngIf="currentMonthEarning.length ===0" >No Summary Data</span>
                  </div>
                </div>
              </div>
              <div class="col-md-12 float-right mt-2">
                <div class="float-right">
                  <nav
                    aria-label="Page navigation example"
                    *ngIf="this.earningReportTotalPage > 1"
                  >
                    <ul class="pagination">
                      <li
                        class="page-item"
                        (click)="earningReportFirstPageList()"
                        [ngClass]="{
                          'disabled-pagination':
                            earningReportCurrentPage === 1
                        }"
                      >
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li
                        class="page-item"
                        (click)="earningReportPreviousPageList()"
                        [ngClass]="{
                          'disabled-pagination':
                            earningReportCurrentPage === 1
                        }"
                      >
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link"
                          >page &nbsp;{{
                            earningReportCurrentPage
                          }}&nbsp;of&nbsp; {{ earningReportTotalPage }}</a
                        >
                      </li>
                      <li
                        class="page-item"
                        (click)="earningReportNextPageList()"
                        [ngClass]="{
                          'disabled-pagination':
                            earningReportCurrentPage ===
                            earningReportTotalPage
                        }"
                      >
                        <a class="page-link">&gt;</a>
                      </li>
                      <li
                        class="page-item"
                        (click)="earningReportLastPageList()"
                        [ngClass]="{
                          'disabled-pagination':
                            earningReportCurrentPage ===
                            earningReportTotalPage
                        }"
                      >
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
            </div>
            <!-- /Upcoming Appointment Tab -->
  
            <!-- Today Appointment Tab -->
            <div class="tab-pane" id="doctor-assistant">
              <div class="card card-table mb-0">
                <div class="card-body">
                  <div class="table-responsive" >
                    <table class="table table-hover table-center mb-0">
                      <thead>
                        <tr>
                          <th> Name</th>
                          <th>Email</th>
                          <th>Phone</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <!-- <tr *ngFor="let data of assistantList;let i=index">
                          <td>{{data.username}}    </td>
                          <td>{{data.email}}</td>
                          <td>{{data.phone}}</td>
                          <td><button class="btn btn-primary" disabled>view</button></td>
                        </tr> -->
                      </tbody>
                    </table>
                  </div>
                  <div class="text-center mb-2 p-2 mt-2">
                    <span class="appointmentList-no-data"  style="color: orangered;">No Assistant Data</span>
                  </div>
                </div>
              </div>
            </div>
            <!-- /Today Appointment Tab -->
  
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
  