import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { PlatformService } from '../platform.service'
// import { DoctorService } from '../../doctor/doctor.service';
import { ActivatedRoute,Router } from '@angular/router'
import {ToastrService} from 'ngx-toastr';
@Component({
  selector: 'app-consultation-summary',
  templateUrl: './consultation-summary.component.html',
  styleUrls: ['./consultation-summary.component.css']
})
export class ConsultationSummaryComponent implements OnInit {
  public doctorId: string;
  public currentMonthEarning:any=[];
  earningReportCurrentPage: any;
  public earningReportSerialNumber: number=0;
  earningReportTotalPage: any;
  earningReportLoading: boolean;
  public totalAmount:number;
  public doctorTotalAmount:number;
  public plateformTotalAmount:number;
  // from_date="2021-07-01";
  // to_date="2021-07-30";
  public filter:string;
  public fromDate:string;
  public toDate:string;
  constructor(
    private platformService:PlatformService,
    private route:ActivatedRoute,
    private notificationService:ToastrService,
    private location: Location
  ) { }


  ngOnInit(): void {
    this.earningReportLoading=true;
    this.earningReportSerialNumber=0;
 //   let form = this.FormGroup.controls;
    this.fromDate = (document.getElementById("from_date") as HTMLInputElement).value
    this.toDate = (document.getElementById("to_date") as HTMLInputElement).value
    console.log(this.fromDate);
    console.log(this.toDate);
    console.log('from date empty');
    // this.filter = '?page=1';
    this.doctorId = this.route.snapshot.paramMap.get('uuid')
    console.log('doctor id', this.doctorId);
    console.log('init successful');
    this.getEarningReportList(1);
  }

  earningReportNextPageList() {
    this.earningReportCurrentPage = this.earningReportCurrentPage + 1;
    if (this.earningReportTotalPage >= this.earningReportCurrentPage) {
      this.getEarningReportList(this.earningReportCurrentPage);
      this.earningReportSerialNumber= (this.earningReportCurrentPage-1)*10;
    } else {
      this.earningReportCurrentPage = this.earningReportCurrentPage - 1;
    }
  }

  earningReportLastPageList() {
    this.earningReportSerialNumber= (this.earningReportTotalPage-1)*10;
    this.getEarningReportList(this.earningReportTotalPage);

  }
  earningReportFirstPageList() {
    this.earningReportCurrentPage = 1;
    this.earningReportSerialNumber= 0;
    this.getEarningReportList(this.earningReportCurrentPage);
  }
  earningReportPreviousPageList() {
    this.earningReportCurrentPage = this.earningReportCurrentPage - 1;
    if (this.earningReportTotalPage >= this.earningReportCurrentPage && this.earningReportCurrentPage > 0) {
      this.getEarningReportList(this.earningReportCurrentPage);
      this.earningReportSerialNumber= (this.earningReportCurrentPage-1)*10;
    } else {
      this.earningReportCurrentPage = this.earningReportCurrentPage + 1;
    }
  }
  getEarningReportList(page){
    this.totalAmount=0;
    this.doctorTotalAmount=0;
    this.plateformTotalAmount=0;
    this.earningReportLoading=true;
    this.earningReportTotalPage=0;
//    const query='?from_date='+this.from_date+'&to_date='+this.to_date+'&page='+page;
//    const query='?page='+page;
    if (this.fromDate == '' && this.toDate == ''){
      var status = (<HTMLInputElement>document.getElementById("fulfilment")).value;
      var appointmentType = (<HTMLInputElement>document.getElementById("appointment_type")).value;
      console.log('status',status);
      console.log('appointment',appointmentType);
      // this.filter = '?page='+page;
      // this.filter = '?fulfilment_status=' + status + '&page='+page;
      if (status == "all"){
        if (appointmentType == "all"){
          this.filter = '?page='+page;
        }else{
          this.filter = '?appointment_type=' + appointmentType + '&page=' + page;
        }
      }else{
        if (appointmentType == "all"){
          this.filter = '?fulfilment_status=' + status + '&page='+page;
        }else{
          this.filter = '?fulfilment_status=' + status + '&appointment_type=' + appointmentType + '&page='+page;
        }
      }

      this.platformService.getConsultationSummary(this.doctorId, this.filter).subscribe((data) => {
        this.currentMonthEarning = data['results'];
        this.earningReportTotalPage = data['total_pages'];
        this.earningReportCurrentPage = data['page_number'];
        console.log('report list', data);
       this.earningReportLoading=false;
       for(let data of    this.currentMonthEarning){
              this.totalAmount=this.totalAmount+parseInt(data.gross_amount);
              this.doctorTotalAmount=this.doctorTotalAmount+parseInt(data.net_amount);
              this.plateformTotalAmount=  this.plateformTotalAmount+parseInt(data.platform_service_fee);
       }
  
      },error=>{
        this.earningReportLoading=false;
        const status = error['status'];
        if(status == 400){
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  
    }
    else{
      var status = (<HTMLInputElement>document.getElementById("fulfilment")).value;
      console.log('status',status);
      // this.filter = '?from_date='+this.fromDate+'&to_date='+this.toDate+'fulfilment_status' + status + '&page='+page;      
      // this.filter = '?page='+page;
      var appointmentType = (<HTMLInputElement>document.getElementById("appointment_type")).value;
      console.log('appointment',appointmentType);
      // this.filter = '?page='+page;
      // this.filter = '?fulfilment_status=' + status + '&page='+page;
      if (status == "all"){
        if (appointmentType == "all"){
          this.filter = '?from_date='+this.fromDate+'&to_date='+this.toDate+ '&page='+page;
        }else{
          this.filter = '?from_date='+this.fromDate+'&to_date='+this.toDate+'&fulfilment_status' + status + '&appointment_type=' + appointmentType + '&page=' + page;
        }
      }else{
        if (appointmentType == "all"){
          this.filter = '?from_date='+this.fromDate+'&to_date='+this.toDate + '&fulfilment_status=' + status + '&page='+page;
        }else{
          this.filter = '?from_date='+this.fromDate+'&to_date='+this.toDate + '&fulfilment_status=' + status + '&appointment_type=' + appointmentType + '&page='+page;
        }
      }
      // const query = '?from_date='+this.fromDate+'&to_date='+this.toDate+'&page='+page;
      this.platformService.getConsultationSummary(this.doctorId, this.filter).subscribe((data) => {
        this.currentMonthEarning = data['results'];
        this.earningReportTotalPage = data['total_pages'];
        this.earningReportCurrentPage = data['page_number'];
       this.earningReportLoading=false;
       for(let data of    this.currentMonthEarning){
              this.totalAmount=this.totalAmount+parseInt(data.gross_amount);
              this.doctorTotalAmount=this.doctorTotalAmount+parseInt(data.net_amount);
              this.plateformTotalAmount=  this.plateformTotalAmount+parseInt(data.platform_service_fee);
       }
  
      },error=>{
        this.earningReportLoading=false;
        const status = error['status'];
        if(status == 400){
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });  
    }

  }
  // getCsvReport(){
  //   this.doctorService.getEarningCsvData(this.doctorId).subscribe((data) => {

  //   },error=>{})
  // }

  back() {
    this.location.back();
  }
}
