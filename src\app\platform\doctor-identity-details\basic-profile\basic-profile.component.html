<div class="card">
    <div class="card-body">
        <h4 class="card-title" id="h-basic-profile">Basic Profile Data <i *ngIf="formDisabled" (click)="formEnable()"
                class="fa fa-edit"></i></h4>
        <div class="experience-info">
            <div class="row form-row experience-cont">
                <form [formGroup]="profileDataForm">
                    <div class="col-12 col-md-10 col-lg-11">
                        <div class="row form-row">
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label translate>Years of Experience<span class="text-danger">*</span></label>
                                    <ng-select id="select-yoe" formControlName="years_of_experience" [items]="Year"
                                        [searchable]="false" bindLabel="Year" placeholder="Select Year"
                                        [readonly]="formDisabled">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label translate>Language<span class="text-danger">*</span></label>
                                    <ng-select id="select-lang" formControlName="languages" [items]="languages"
                                        [searchable]="true" bindLabel="value" placeholder="Select Languages"
                                        multiple="true" [maxSelectedItems]="3" [readonly]="formDisabled">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="system-of-medicine" translate>System of Medicine<span class="text-danger">*</span></label>
                                    <ng-select formControlName="system_of_medicine" [items]="systemOfMedicine"
                                        [searchable]="false" bindLabel="systemOfMedicine" placeholder="Select"
                                        [readonly]="formDisabled"
                                        (change)="onChangeSystemOfMedicine($event,'changes')">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4" *ngIf="!isPublicDoctor">
                                <div class="form-group">
                                    <label for="department" translate>Department<span class="text-danger">*</span></label>
                                    <ng-select formControlName="department" [items]="specificDepartment"
                                        bindLabel="value" [readonly]="formDisabled" multiple="true"
                                        [maxSelectedItems]="1">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label translate>Speciality<span class="text-danger">*</span></label>
                                    <ng-select id="select-spe" formControlName="speciality" [items]="specificSpeciality"
                                        [maxSelectedItems]="3" [searchable]="true" bindLabel="value"
                                        placeholder="Select Speciality" multiple="true" [readonly]="formDisabled">
                                    </ng-select>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="practice_types" translate>Practice Type<span class="text-danger">*</span></label>
                                    <ng-select id="select-pra" formControlName="practice_types" [items]="practiceType"
                                        [searchable]="false" bindLabel="name" bindValue="value"
                                        placeholder="Select Practice Type" multiple="true" [readonly]="formDisabled">
                                    </ng-select>
                                </div>
                            </div>
                            <!-- <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label translate>Consultation Duration</label>
                                    <ng-select id="select-consult" formControlName="consultation_duration" [searchable]="false" bindLabel="time" placeholder="{{'Select Time' | translate}}" [readonly]="formDisabled">
                                        <ng-option [value]="time" *ngFor="let time of this.time"> {{time}}
                                        </ng-option>
                                    </ng-select>
                                </div>
                            </div> -->
                        </div>
                        <div>
                            <button *ngIf="!formDisabled" id="bp-save" class="btn btn-primary float-right" type="button"
                                [disabled]="!profileDataForm.valid" (click)="onSubmit()">{{ this.saving ? 'Saving....' :
                                'Save'|translate }}</button>
                            <button *ngIf="!formDisabled" id="bp-frm-disable" (click)="formDisable()" type="button"
                                class="btn btn-secondary cancel-btn" translate>Cancel</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>