<!--BreadCrumbs-->
<div class="breadcrumb-bar">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-12 col-12">
                <nav aria-label="breadcrumb" class="page-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);">{{'Admin'|translate}}</a></li>
                        <li #listHeader class="breadcrumb-item active" aria-current="page">{{'Doctor Profile'
                            |translate}}</li>
                    </ol>
                </nav>
                <h2 #header class="breadcrumb-title">{{'Doctor Profile' |translate}}</h2>
            </div>
        </div>
    </div>
</div>
<!--BreadCrumbs Ends-->
<div class="content card">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 col-lg-12 col-xl-12">
                <h5 (click)="goBack()" class="mb-4 back-head ms"><i class="fas fa-chevron-circle-left"></i>Back</h5>
                <!--profile form-->
                <div class="card">
                    <div class="card-body">
                        <!-- <h4 class="card-title" translate>Profile Picture</h4> -->
                        <div class="row form-row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="change-avatar">
                                        <div class="profile-img">
                                            <img [src]="doctorProfilePictureUrl" alt="User Image">
                                        </div>
                                        <!-- <div class="upload-img">
                                            <div class="change-photo-btn">
                                                <span><i class="fa fa-upload"></i> {{ profileUpload ? ('Upload Photo'|translate): 'Uploading'|translate}}</span>
                                                <input type="file" class="upload" id="profile-picture" [disabled]="disabledUploadPhotoBtn" (change)="doctorProfilePictureChange($event)" accept=".jpg, .png,">
                                            </div>
                                            <small class="form-text text-muted" translate>Allowed JPG or PNG. Max size of 2MB</small>
                                        </div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h4 class="card-title" translate>Personal Profile <i *ngIf="disabled" (click)="editProfile()"
                                class="fa fa-edit"></i></h4>
                        <div class="form-group " *ngIf="formError">
                            <!-- <label style="color: orangered;" >Form Error</label> -->
                            <div class="card">
                                <ng-container *ngFor="let err of errorValue">
                                    <p class="text-danger">&nbsp;{{err.value}}</p>
                                </ng-container>
                            </div>
                        </div>
                        <form [formGroup]="personalProfileForm">
                            <div class="row form-row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Full Name<span class="text-danger">*</span></label>
                                        <input id="fullname" class="form-control" type="text" name="username"
                                            formControlName="username" maxlength="25" pattern="[a-zA-Z ]*" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Email <span class="text-danger">*</span></label>
                                        <input id="email" type="email" name="email" class="form-control"
                                            formControlName="email" maxlength="50" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>First Name</label>
                                        <input id="firstname" type="text" class="form-control" name="first_name"
                                            formControlName="first_name" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.first_name.invalid && (personalProfileForm.controls.first_name.dirty || personalProfileForm.controls.first_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Middle Name</label>
                                        <input id="middlename" type="text" class="form-control" name="middle_name"
                                            formControlName="middle_name" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.middle_name.invalid && (personalProfileForm.controls.middle_name.dirty || personalProfileForm.controls.middle_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Last Name</label>
                                        <input id="lastname" type="text" class="form-control" name="last_name"
                                            formControlName="last_name" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.last_name.invalid && (personalProfileForm.controls.last_name.dirty || personalProfileForm.controls.last_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Phone Number<span class="text-danger">*</span></label>
                                        <input id="phone" type="text" class="form-control" formControlName="phone"
                                            pattern="[0-9]*" name="phone" maxlength="15" readonly>
                                        <div *ngIf="personalProfileForm.controls.phone.invalid && (personalProfileForm.controls.phone.dirty || personalProfileForm.controls.phone.touched)"
                                            class="alert alert-danger">{{numberError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Gender<span class="text-danger">*</span></label>
                                        <select class="form-control" name="gender" id="gender" formControlName="gender">
                                            <option *ngFor="let data of gender" [value]="data.value ">{{data.name}}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-0 ">
                                        <label translate>Date of Birth<span class="text-danger">*</span></label>
                                        <input [maxDate]="maxDate" [minDate]="minDate" placeholder="DOB"
                                            onkeydown="return false" class="form-control"
                                            formControlName="date_of_birth" [readonly]="disabled" bsDatepicker
                                            [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }"
                                            [ngClass]="{'bs-datepicker':disabled==true}">
                                        <input class="form-control" formControlName="date_of_birth"
                                            *ngIf="disabled==true" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group float-right">
                                <button *ngIf="!disabled" type="button" id="save-btn" id="per-prof-btn"
                                    class="btn btn-primary" translate (click)="onSubmit()"
                                    [disabled]="!personalProfileForm.valid">Save</button>
                                <button *ngIf="!disabled" type="button" id="cancel-btn" (click)="cancelUpdate()"
                                    class="btn btn-secondary cancel-btn" translate>Cancel</button>

                            </div>
                            <!-- /Basic Information -->

                        </form>

                    </div>
                </div>
                <!--profile form ends-->
                <!--Basic Profile Data-->
                <app-basic-profile (system_of_medicine)="getSystemOfMedicine($event)" ></app-basic-profile>

                <!--Qualifications -->
                <app-qualifications [system_of_medicine]="system_of_medicine" ></app-qualifications>
                <!-- doctor-registration -->
                <app-registrations [system_of_medicine]="system_of_medicine" ></app-registrations>
                <div class="text-center" *ngIf="userType ==='PlatformAdmin'&& approvalRequestStatus !== 'Rejected'">
                    <button type="submit" id="approve" (click)="onApprove()" class="btn btn-primary"
                        *ngIf="approvalRequestStatus !== 'Approved'">Approve</button>&nbsp;
                    <button type="submit" id="reject" (click)="open(content)" class="btn btn-danger">Decline</button>
                </div>
                <ng-template #content let-modal>
                    <div class="modal-header">
                        <h4 class="modal-title" id="modal-basic-title">Are You Sure?</h4>
                    </div>
                    <div class="modal-body">
                        <form>
                            <label>Please provide reason for declining the approval request</label>
                            <div class="form-group">
                                <textarea class="form-control" name="decline-reason"
                                    [(ngModel)]="declineReason"></textarea>
                                <small class="text-muted">min 50 characters needed</small>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer text-center">
                        <button type="submit" (click)="onDecline()" [disabled]="declineReason.length <= 50"
                            class="btn btn-primary">Confirm</button>&nbsp;
                        <button type="submit" (click)="modal.close('Ok click')"
                            class="btn btn-secondary">Cancel</button>
                    </div>
                </ng-template>
            </div>
        </div>
    </div>
</div>