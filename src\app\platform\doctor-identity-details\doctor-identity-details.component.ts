import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PlatformService } from './../platform.service';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { Component, OnInit, ViewChild, Output, EventEmitter, ElementRef } from '@angular/core';
import { FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';
import * as moment from 'moment';
import * as Settings from '../../config/settings';

@Component({
  selector: 'app-doctor-identity-details',
  templateUrl: './doctor-identity-details.component.html',
  styleUrls: ['./doctor-identity-details.component.css']
})
export class DoctorIdentityDetailsComponent implements OnInit {

  @ViewChild('profileFieldset') profileFieldset: ElementRef;
  @Output() messageEvent: EventEmitter<string> = new EventEmitter<string>();
  @ViewChild('qualSubmitButton') qualSubmitButton: ElementRef;
  public doc_data = {};
  public changed = false;
  public profileUpload = true;
  public disabledUploadPhotoBtn = false;
  public personalProfileForm: FormGroup;
  public user_data = {};
  public disabled = true;
  public doctorProfilePictureUrl: string;
  public cancelbtn = false;
  profileFileSizeLarge = false;
  public feesEditBtnShow = false;
  public cancleBtnShow = false;
  public feeForm: FormGroup;
  userID: any;
  // qualification
  public doc_uuid = '';
  qualificationFrom: FormGroup;
  public qualificationArray: FormArray;
  public qualificationForm: FormGroup;
  public qualificationFormSubmit = true;
  public qualificationFile: File;
  public create = false;
  public termsandC = false;
  public contentHtml = "";
  public contentText = "";
  public disableApprovalBtn = true;
  public checkTCValue: boolean;
  public declineReason = '';
  public userType: string;
  approvalRequestStatus: any;
  public maxDate: Date;
  public minDate: Date;
  public profileCompletion: number;
  errorValue: any = [];
  formError: boolean;
  gender = [
    { value: '', name: 'Select' },
    { value: 'Male', name: 'Male' },
    { value: 'Female', name: 'Female' },
    { value: 'Prefer not to answer', name: 'Prefer not to answer' },
  ];
  public userDetails: any = {
    username: null,
    email: null,
    phone: null,
    gender: null,
    first_name: null,
    middle_name: null,
    last_name: null,
    date_of_birth: null,
    uuid: null
  };
  system_of_medicine: any = [];
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  numberError = Settings.numberError;
  // isApproved: boolean=false;

  constructor(
    // private userService: AuthService,
    private platformService: PlatformService,
    private notificationService: ToastrService,
    // private router: Router,
    private route: ActivatedRoute,
    public translate: TranslateService,
    // private sharedService: SharedService,
    private modalService: NgbModal,
    private location: Location,
  ) { }

  ngOnInit(): void {
    this.maxDate = new Date();
    this.minDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate() - 7672);
    this.minDate.setDate(this.minDate.getDate() - 36500);
    this.userType = localStorage.getItem('user_type');
    this.addProfileFromControl(null);
    this.route.params.subscribe(
      url => {
        this.doc_uuid = url['uuid'];
        this.getuserData();
      }
    );
    this.doctorProfilePictureUrl = '../../../../assets/img/doctors/doctor-thumb-02.png';
    const lang = localStorage.getItem('pageLanguage');
    console.log('profile settings', lang);
    this.translate.use(lang);
    // this.checkTandC(null);

    // this.addQualFormControl();
    this.contentHtml = localStorage.getItem('content_html');
    this.contentText = localStorage.getItem('content_text');
  }
  getuserData() {
    this.platformService.getDoctorDetails(this.doc_uuid).subscribe(
      data => {

        this.doc_data = data;
        this.user_data = data['user'];
        this.addProfileFromControl(this.user_data);
        this.approvalRequestStatus = data['approval_request_status'];
        console.log(this.user_data);
        this.profileCompletion = data['profile_completion_percentage'];
        if (this.user_data['profile_picture'] !== null) {
          this.doctorProfilePictureUrl = this.user_data['profile_picture'];
        }
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }



  onSubmit() {
    this.errorValue=[];
    const dob = this.personalProfileForm.controls[`date_of_birth`].value;
    this.userDetails.username = this.personalProfileForm.controls[`username`].value;
    this.userDetails.email = this.personalProfileForm.controls[`email`].value;
    this.userDetails.phone = this.personalProfileForm.controls[`phone`].value;
    this.userDetails.gender = this.personalProfileForm.controls[`gender`].value;
    this.userDetails.first_name = this.personalProfileForm.controls[`first_name`].value;
    this.userDetails.last_name = this.personalProfileForm.controls[`last_name`].value;
    this.userDetails.middle_name = this.personalProfileForm.controls[`middle_name`].value;
    this.userDetails.date_of_birth = moment(dob, 'DD-MM-YYYY').format('YYYY-MM-DD');
    this.userDetails.uuid = this.doc_uuid;
    this.platformService.updateUserDetails(this.doc_uuid, this.userDetails).subscribe(
      (data) => {

        this.notificationService.success('Profile Updated', 'Med.Bot');
        this.disabled = true;
        this.user_data = data['user'];
        this.addProfileFromControl(this.user_data);

      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.formError = true
          const err = error['error']['error_details']['validation_errors'];
          if (err) {
            const gender = err['gender'];
            const dob = err['date_of_birth'];
            if (gender && dob) {
              ;
              const genderError = 'Gender : ' + gender[0];
              const dobError = 'DOB : ' + dob[0];
              this.notificationService.error(
                `${genderError} ${dobError}`,
                'Med.Bot'
              );
              this.errorValue.push({ value: genderError }, { value: dobError });
            } else if (gender) {
              const genderError = 'Gender : ' + gender[0];
              this.notificationService.error(`${genderError}`, 'Med.Bot');
              this.errorValue.push({ value: genderError });
            } else if (dob) {
              const dobError = 'DOB : ' + dob[0];
              this.notificationService.error(` ${dobError}`, 'Med.Bot');
              this.errorValue.push({ value: dobError });
            } else {
              this.notificationService.error('Updation Error', 'Med.Bot');
            }
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  editProfile() {
    this.disabled = false;
    this.personalProfileForm.get('gender').enable();
  }
  cancelUpdate() {
    this.personalProfileForm.get('gender').disable();
    this.disabled = true;
    this.addProfileFromControl(this.user_data);
  }


  doctorProfilePictureChange(event) {
    const file = event.target.files;

    if (file.length > 0) {
      this.profileFileSizeLarge = false;
      const selectedProfilePicture = file[0];
      console.log(selectedProfilePicture);
      // if (
      //   selectedProfilePicture.size < 2000000 &&
      //   (selectedProfilePicture.type === 'image/jpeg' ||
      //     selectedProfilePicture.type === 'image/jpg' ||
      //     selectedProfilePicture.type === 'image/png')
      // ) {
      //   this.disabledUploadPhotoBtn = true;
      //   this.profileUpload = false;
      //   this.userService.updateDoctorProfilePicture(selectedProfilePicture).subscribe(
      //     (data) => {
      //       this.user_data = data;
      //       this.doctorProfilePictureUrl = this.user_data['profile_picture'];
      //       this.sendProfileToChildComponent();
      //       this.profileUpload = true;
      //       this.sharedService.setPicture(this.doctorProfilePictureUrl);
      //       this.disabledUploadPhotoBtn = false;
      //       this.notificationService.success(
      //         'Profile Picture Update',
      //         'Med.Bot'
      //       );
      //     },
      //     (error) => {
      //       this.profileUpload = true;
      //       this.disabledUploadPhotoBtn = false;
      //       console.log(error);
      //       this.notificationService.error(
      //         'Error In Updating profile picture',
      //         'Med.Bot'
      //       );
      //     }
      //   );
      // } else {
      //   this.profileFileSizeLarge = true;
      // }
    } else {
      this.profileUpload = true;
      this.disabledUploadPhotoBtn = false;
      // this.notificationService.showError(
      //   'Please select  profile picture',
      //   'Med.Bot'
      // );
    }
  }

  sendProfileToChildComponent() {
    this.messageEvent.emit(this.doctorProfilePictureUrl);
  }


  goBack() {
    this.location.back();
  }

  onApprove() {
    if (this.profileCompletion < 100) {
      this.notificationService.warning("The doctor profile is incomplete.");
    } else {
      // this.notificationService.success('profile approved');
      this.platformService.acceptApproval(this.doc_uuid).subscribe(
        data => {
          // this.isApproved = true;
          this.notificationService.success('Doctor Profile Approved', 'Med.Bot');
          this.goBack();
        },
        error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        });
    }
  }

  onDecline() {
    console.log(this.declineReason);
    const data = { "rejection_reason": this.declineReason };
    this.platformService.declineApproval(this.doc_uuid, data).subscribe(
      data => {

        this.notificationService.success('Doctor Profile Declined', 'Med.Bot');
        this.modalService.dismissAll();
        this.goBack();
      },
      error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  open(content) {
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
    }, (reason) => {
    });
  }
  addProfileFromControl(data) {
    if (data === null) {
      this.disabled = false;
      this.personalProfileForm = new FormGroup({
        username: new FormControl('', [
          Validators.required,
          Validators.maxLength(50),
        ]),
        email: new FormControl('', [Validators.required, Validators.email]),
        first_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        middle_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        last_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        phone: new FormControl('', [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl('', [
          Validators.required,
          Validators.maxLength(10),
        ]),
        date_of_birth: new FormControl('', [
          Validators.required,
          Validators.maxLength(20),
        ]),
      });
    } else {
      this.disabled = true;
      this.personalProfileForm.get('gender').disable();
      this.personalProfileForm = new FormGroup({
        username: new FormControl(data.username, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        email: new FormControl(data.email, [
          Validators.required,
          Validators.email,
        ]),
        first_name: new FormControl(data.first_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')
        ]),
        middle_name: new FormControl(data.middle_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')
        ]),
        last_name: new FormControl(data.last_name, [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        phone: new FormControl(data.phone, [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl(data.gender, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        date_of_birth: new FormControl(
          moment(data.date_of_birth).format('DD-MM-YYYY'),
          [Validators.required, Validators.maxLength(25)]
        ),
      });
      this.personalProfileForm.get('gender').disable();
    }
  }
  getSystemOfMedicine(data) {

    this.system_of_medicine = data;
  }


}
