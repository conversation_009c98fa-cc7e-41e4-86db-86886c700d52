import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { PlatformService } from './../../platform.service';
import { FormGroup, FormArray, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, Input } from '@angular/core';
import { DoctorService } from 'src/app/doctor/doctor.service';
@Component({
  selector: 'app-qualifications',
  templateUrl: './qualifications.component.html',
  styleUrls: ['./qualifications.component.css']
})
export class QualificationsComponent implements OnInit {
  @Input() system_of_medicine: any;
  public qualificationForm: FormGroup;
  public qualificationArray: FormArray;
  public qualifications = [];
  public adding = false;
  public qualificationFile: any;
  public editing = false;
  public showAddMore = true;
  public qual_data = {
    name: '',
    institution: '',
    year: '',
    file: '',
  };
  public errorValue = [];
  public doc_uuid = '';
  public formError = false;
  dergeeList: unknown[];
  constructor(
    private platformService: PlatformService,
    private route: ActivatedRoute,
    private notificationService: ToastrService,
    private formBuilder: FormBuilder,
    private doctorService: DoctorService
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(
      url => {
        this.doc_uuid = url['uuid'];
        this.loadQualificationData();
      }
    );
    this.addQualificationArrayForm();
  }

  ngOnChanges() {
    if (this.system_of_medicine.length > 0 || this.system_of_medicine != '') {
      this.getDegrees();
    }
  }

  addQualificationArrayForm() {
    this.qualificationForm = this.formBuilder.group({
      qualificationArray: this.formBuilder.array([])
    });
  }

  createQualificationForm(data: Object) {
    this.qualificationArray = this.qualificationForm.get('qualificationArray') as FormArray;
    if (data == null) {
      this.qualificationArray.push(
        this.formBuilder.group({
          uuid: null,
          name: [null, Validators.required],
          institution: [null, Validators.required],
          year: [null, [Validators.required, Validators.pattern('[0-9]*')]],
          file: [null, Validators.required],
          document: [null, Validators.required],
          documentName: [null, Validators.required],
          edit: true,
          new: true,
          deleted: false,
        })
      );
      this.showAddMore = false;
      this.editing = true;
    }
    else {
      this.qualificationArray.push(
        this.formBuilder.group({
          uuid: data['uuid'],
          name: [{ value: data['name'], disabled: true }, Validators.required],
          institution: [{ value: data['institution'], disabled: true }, Validators.required],
          year: [{ value: data['year'], disabled: true }, Validators.required],
          file: data['q_documents'][0],
          documentName: [{ value: data['q_documents'][0]?.file_name }, Validators.required],
          document: data['q_documents'][0]?.file,
          edit: false,
          new: false,
          deleted: false,
        })
      );
    }
  }

  loadQualificationData() {
    if (this.doc_uuid != undefined) {
      this.platformService.getDoctorQualifications(this.doc_uuid).subscribe(
        data => {
          this.qualifications = data['results'];
          console.log(this.qualifications);
          for (let i = this.qualifications.length; i > 0; i--) {
            this.createQualificationForm(this.qualifications[i - 1]);
          }
        }, error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
    else {
      this.doctorService.getDoctorQualifications().subscribe(data => {
        this.qualifications = data['results'];
        console.log(this.qualifications);
        for (let i = this.qualifications.length; i > 0; i--) {
          this.createQualificationForm(this.qualifications[i - 1]);
        }
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      })

    }

  }

  trackFn(index: any) {
    return index;
  }
  saveQualification(i) {
    this.formError = false;
    this.errorValue = [];
    const data = this.qualificationForm.get('qualificationArray').value[i];
    const api_data = {
      'name': data['name'],
      'institution': data['institution'],
      'year': data['year'],
    };
    console.log(data, this.qualificationFile);
    if (data['uuid']) {
      console.log('patch');
      const formData = new FormData();
      formData.append('file', this.qualificationFile),
        formData.append('data', JSON.stringify(api_data));
      this.platformService.updateDoctorQualification(this.doc_uuid, data['uuid'], formData).subscribe(
        data => {
          this.notificationService.success(
            'Qualification Updated'
          );
          const control = this.qualificationArray.at(i);
          control.get('edit').setValue(false);
          this.disableQualControls(control);
          this.editing = false;

        },
        error => {
          const control = this.qualificationArray.at(i);
          control.get('edit').setValue(false);
          control.get('name').setValue(this.qual_data.name);
          control.get('institution').setValue(this.qual_data.institution);
          control.get('year').setValue(this.qual_data.year);
          control.get('file').setValue(this.qual_data.file);
          control.get('documentName').setValue(this.qual_data.file['file_name']);
          control.get('document').setValue(this.qual_data.file['file']);
          this.disableQualControls(control);
          this.editing = false;
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.formError = true;
            const file = error['error']['file'];
            const year = error['error']['year'];
            if (file) {
              const fileErr = 'File : ' + file[0];
              this.errorValue.push({ value: fileErr });
              this.notificationService.error(`${fileErr}`);
            } else if (year) {
              const yearErr = 'Year : ' + year[0];
              this.errorValue.push({ value: yearErr });
              this.notificationService.error(`${yearErr}`);
            } else {
              this.notificationService.error(`${error['statusText']}, Failed to add qualification`, 'Med.Bot');

            }
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
    else {
      if (!data['name'] || !data['year'] || !data['institution'] || !this.qualificationFile) {
        this.notificationService.error('All details are needed', 'Med.Bot');
        return;
      }
      console.log('post');
      api_data['doctor'] = this.doc_uuid;
      const formData = new FormData();
      formData.append('file', this.qualificationFile),
        formData.append('data', JSON.stringify(api_data));
      if (this.doc_uuid != undefined) {
        this.platformService.postDoctorQualification(this.doc_uuid, formData).subscribe(
          data => {
            this.notificationService.success(
              'Qualification Added'
            );
            const control = this.qualificationArray.at(i);
            control.get('uuid').setValue(data['uuid']);
            control.get('edit').setValue(false);
            control.get('new').setValue(false);
            control.get('name').setValue(data['name']);
            control.get('file').setValue(data['q_documents'][0]);
            control.get('institution').setValue(data['institution']);
            control.get('year').setValue(data['year']);
            control.get('document').setValue(data['q_documents'][0]['file']);
            control.get('documentName').setValue(data['q_documents'][0]['file_name']);
            this.disableQualControls(control);
            this.editing = false;
            this.showAddMore = true;
          }, error => {
            console.log(error);
            const status = error['status'];
            if (status === 400) {
              this.formError = true;
              const file = error['error']['error_details']['validation_errors']['file'];
              const year = error['error']['error_details']['validation_errors']['year'];
              const fileName = error['error']['error_details']['validation_errors']['file_name']
              const fileType = error['error']['error_details']['validation_errors']['file_type']
              if (file) {
                const fileErr = 'File :' + file[0];
                this.errorValue.push({ value: fileErr });
                this.notificationService.error(`${fileErr}`);
              } else if (year) {
                const yearErr = 'Year :' + year[0];
                this.errorValue.push({ value: yearErr });
                this.notificationService.error(`${yearErr}`);
              } else if (fileType) {
                const fileErr = 'File :' + fileType[0];
                this.errorValue.push({ value: fileErr })
                this.notificationService.error(
                  `${fileErr}`,
                  'Med.Bot'
                );

              } else if (fileName) {
                const fileErr = 'File Name' + fileName[0];
                this.errorValue.push({ value: fileErr })
                this.notificationService.error(
                  `${fileErr}`,
                  'Med.Bot'
                );

              } else {
                this.notificationService.error('Qualification Updation Failed');
              }
            } else {
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }

          }
        );
      }
      else {

        this.doctorService.postDoctorQualification(formData).subscribe(
          data => {
            this.notificationService.success(
              'Qualification Added'
            );
            const control = this.qualificationArray.at(i);
            control.get('uuid').setValue(data['uuid']);
            control.get('edit').setValue(false);
            control.get('new').setValue(false);
            control.get('name').setValue(data['name']);
            control.get('file').setValue(data['q_documents'][0]);
            control.get('institution').setValue(data['institution']);
            control.get('year').setValue(data['year']);
            control.get('document').setValue(data['q_documents'][0]['file']);
            control.get('documentName').setValue(data['q_documents'][0]['file_name']);
            this.disableQualControls(control);
            this.editing = false;
            this.showAddMore = true;
          }, error => {
            console.log(error);
            const status = error['status'];
            if (status === 400) {
              this.formError = true;
              const file = error['error']['error_details']['validation_errors']['file'];
              const year = error['error']['error_details']['validation_errors']['year'];
              const fileName = error['error']['error_details']['validation_errors']['file_name']
              const fileType = error['error']['error_details']['validation_errors']['file_type']
              if (file) {
                const fileErr = 'File :' + file[0];
                this.errorValue.push({ value: fileErr });
                this.notificationService.error(`${fileErr}`);
              } else if (year) {
                const yearErr = 'Year :' + year[0];
                this.errorValue.push({ value: yearErr });
                this.notificationService.error(`${yearErr}`);
              } else if (fileType) {
                const fileErr = 'File :' + fileType[0];
                this.errorValue.push({ value: fileErr })
                this.notificationService.error(
                  `${fileErr}`,
                  'Med.Bot'
                );

              } else if (fileName) {
                const fileErr = 'File Name' + fileName[0];
                this.errorValue.push({ value: fileErr })
                this.notificationService.error(
                  `${fileErr}`,
                  'Med.Bot'
                );

              } else {
                this.notificationService.error('Qualification Updation Failed');
              }
            } else {
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }

          }
        );
      }

    }

  }

  updateFile(event, i) {
    const file = event.target.files;
    console.log(file);
    const selectedFile = file[0];
    const fileNameLength = selectedFile.name;
    if (fileNameLength.length < 51) {
      if (selectedFile.size < 2000000) {
        if (
          selectedFile.type === 'image/jpeg' ||
          selectedFile.type === 'image/jpg' ||
          selectedFile.type === 'application/pdf'
        ) {
          this.qualificationFile = selectedFile;
          const control = this.qualificationArray.at(i);
          control.get('file').setValue(this.qualificationFile);
          control.get('document').setValue(null);
          control.get('documentName').setValue(this.qualificationFile['name']);
        } else {
          this.qualificationFile = selectedFile;
          const control = this.qualificationArray.at(i);
          control.get('file').setValue(null);
          control.get('document').setValue(null);
          control.get('documentName').setValue(null);
          this.notificationService.warning(' Check File Format');
        }
      } else {
        const control = this.qualificationArray.at(i);
        control.get('file').setValue(null);
        control.get('document').setValue(null);
        control.get('documentName').setValue(null);
        this.notificationService.warning('Choose file below 2MB');
      }
    } else {
      const control = this.qualificationArray.at(i);
      control.get('file').setValue(null);
      control.get('document').setValue(null);
      control.get('documentName').setValue(null);
      this.notificationService.warning("File name: Ensure this field has no more than 50 characters.");
    }

  }

  removeQualification(i) {
    const data = this.qualificationForm.get('qualificationArray').value[i];
    const uuid = data['uuid'];
    if (uuid) {
      this.platformService.deleteDoctorQualification(this.doc_uuid, uuid).subscribe(
        data => {
          console.log(this.qualificationArray);
          this.notificationService.warning(`Education data deleted Successfully`, 'Med.Bot');
          this.qualificationArray.at(i).get('deleted').setValue(true);
          this.showAddMore = true;
          this.editing = false;
        }, error => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }

        }
      );
    }
    else {
      this.qualificationArray.removeAt(i);
      this.showAddMore = true;
    }
  }

  editQualification(i) {
    this.editing = true;
    this.showAddMore = false;
    const control = this.qualificationArray.at(i);
    this.qual_data.name = control.get('name').value;
    this.qual_data.institution = control.get('institution').value;
    this.qual_data.year = control.get('year').value;
    this.qual_data.file = control.get('file').value;
    control.enable();
    control.get('edit').setValue(true);
    console.log(this.qualificationArray.at(i).value);
    this.qualificationFile = control.get('file').value;
  }

  cancelUpdate(i) {
    const control = this.qualificationArray.at(i);
    const uuid = control.get('uuid').value;
    if (uuid) {
      const file = control.get('file').value;
      console.log(this.qual_data);
      control.get('edit').setValue(false);
      control.get('name').setValue(this.qual_data.name);
      control.get('institution').setValue(this.qual_data.institution);
      control.get('year').setValue(this.qual_data.year);
      control.get('file').setValue(this.qual_data.file);
      control.get('documentName').setValue(this.qual_data.file['file_name']);
      control.get('document').setValue(this.qual_data.file['file']);
      this.disableQualControls(control);
    }
    else {
      this.qualificationArray.removeAt(i);
      this.showAddMore = true;
    }
    this.editing = false;
    this.showAddMore = true;
  }

  disableQualControls(control) {
    control.get('name').disable();
    control.get('institution').disable();
    control.get('year').disable();
  }

  viewFile(data) {
    console.log(data['file']);
    window.open(data['file']);
  }

  getDegrees() {
    this.platformService.getDegree(this.system_of_medicine).subscribe(
      (data) => {
        this.dergeeList = Object.values(data);
      }, error => {
        console.log(error)
      });
  }

  addNewDegree(value) {
    const data = { "degree_name": value };
  }

}
