<div class="card">
  <div class="card-body">
    <h4 class="card-title">
      Registrations
      <i *ngIf="!editing" id="edit-reg" (click)="editRegistrations()" class="fa fa-edit"></i>
    </h4>
    <div class="row form-row" *ngIf="formError">
      <div class="col-12 col-md-6 col-lg-6 ">
        <div class="">
          <ng-container class="form-group" *ngFor="let err of errorValue">
            <p class="text-danger">{{err.value}}</p>
          </ng-container>
        </div>
      </div>
    </div>
    <form [formGroup]="registerForm">
      <div formArrayName="registrationArray">
        <ng-container *ngFor="
            let item of this.registerForm.controls.registrationArray.value;
            let i = index;
            trackBy: trackFn
          " [formGroupName]="i">
          <div class="registrations-info">
            <div class="row form-row reg-cont">
              <div class="col-12 col-md-4  col-lg-4">
                <div class="form-group">
                  <label>Council<span class="text-danger">*</span></label>
                  <ng-select id="council" class="custom" formControlName="council" bindValue="code"
                    [items]="councilList" [readonly]="saved_registrstion" [searchable]="true" bindLabel="code"
                    [clearable]="false" placeholder="{{'Select Council' | translate}}" multiple required>
                  </ng-select>
                </div>
              </div>
              <div class="col-12 col-md-2 col-lg-2">
                <div class="form-group">
                  <label>Number<span class="text-danger">*</span></label>
                  <input type="text" formControlName="number" [readonly]="saved_registrstion" pattern="[a-zA-Z0-9 ]*"
                    placeholder="{{'Number'| translate}}" class="form-control" maxlength="50">
                </div>
              </div>
              <div class="col-12 col-md-4 col-lg-2">
                <div class="form-group">
                  <label>Registered On<span class="text-danger">*</span></label>
                  <input type="text" placeholder="From Date" class="form-control" formControlName="valid_upto"
                    *ngIf="!saved_registrstion" onkeydown="return false" class="form-control input-field-border"
                    [minDate]="" [maxDate]="" bsDatepicker
                    [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
                  <input type="text" class="form-control" readonly formControlName="valid_upto"
                    *ngIf="saved_registrstion">
                </div>
              </div>
              <div class="col-12  col-md-1 col-lg-2">
                <div *ngIf="!!registrationFileUpload">
                  <div class="file-upload-btn file-btn-alinment">
                    <span><i class="fa fa-upload"></i> {{ registrationFileUpload ? ('Upload File'|translate): 'View
                      File|translate'}}</span>
                    <input type="file" class="upload" [disabled]="saved_registrstion" formControlName="regiterFileName"
                      (change)="chooseRegistrationFile($event)" accept=".jpg,.pdf">
                  </div>
                  <small class="form-text text-muted" *ngIf="selectedFileName === ' '" translate>Allowed JPG or Pdf. Max
                    size of 2MB</small>
                  <small class="form-text text-muted" *ngIf="selectedFileName !== ' '">{{selectedFileName}}</small>
                </div>
                <div class="file-upload-btn file-btn-alinment" *ngIf="!registrationFileUpload">
                  <span translate><i class="fa fa-view"></i> View File</span>
                  <input type="t" class="upload" (click)="viewPdf()">
                </div>
              </div>
              <div class="col-12 col-md-1 col-lg-1 mt-4">
                <button id="save-prf-reg" class="btn btn-primary float-right" *ngIf="!saved_registrstion"
                  [disabled]="!registerForm.valid" (click)="saveRegisterForm(i)">{{!updating ?
                  ('Save'|translate):'Uploading'| translate}}</button>
                <button *ngIf="!saved_registrstion" id="bp-frm-disable" (click)="cancel()" type="button"
                  class="btn btn-secondary cancel-btn" translate>Cancel</button>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </form>
  </div>
</div>