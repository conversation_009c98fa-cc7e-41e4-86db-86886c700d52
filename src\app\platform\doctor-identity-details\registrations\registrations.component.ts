import { ActivatedRoute } from '@angular/router';
import { PlatformService } from './../../platform.service';
import { ToastrService } from 'ngx-toastr';
import { Component, OnInit, Input } from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';

@Component({
  selector: 'app-registrations',
  templateUrl: './registrations.component.html',
  styleUrls: ['./registrations.component.css'],
})
export class RegistrationsComponent implements OnInit {
  public registrationData = {
    council: null,
    number: null,
    valid_upto: null,
    file: null,
  };
  public registrationFileUpload = true;
  registrationFile: File;
  doctorRegisteredData = {};
  doc_uuid = '';
  fileUrl: any;
  userID: any;
  content_text: string[];
  content_html: any;
  termsAndconditionID: string;
  saved_registrstion = false;
  acceptedTerms = false;
  registerFileSizeLarge = false;
  profileFileSizeLarge = false;
  public disabledUploadPhotoBtn = false;
  public registrationArray: FormArray;
  public registerForm: FormGroup;
  public user_data = {};
  public register_form_data = {};
  public selectedFileName: string = ' ';
  public updating: boolean = false;
  public editing = false;
  public regData = {};
  public maxDate = moment().format('YYYY-MM-DD');
  formError: boolean;
  public errorValue: any = [];
  @Input() system_of_medicine: any;
  councilList: any = [];
  constructor(
    private platformService: PlatformService,
    private route: ActivatedRoute,
    private translate: TranslateService,
    private _FormBuilder: FormBuilder,
    private notificationService: ToastrService
  ) { }

  ngOnInit(): void {
    const lang = localStorage.getItem('pageLanguage');
    this.translate.use(lang);
    this.route.params.subscribe((url) => {
      this.doc_uuid = url['uuid'];
      this.getRegistedData();
    });
    this.addFormControl(null);
  }

  ngOnChanges() {
    if (this.system_of_medicine.length > 0 || this.system_of_medicine != '') {
      this.getCouncils();
    }
    console.log(this.system_of_medicine);
  }

  addFormControl(data) {
    if (data === null) {
      this.editing = true;
      this.registerForm = this._FormBuilder.group({
        registrationArray: this._FormBuilder.array([]),
      });
      this.addResgistration();
    } else {
      this.editing = false;
      const valid_upto = moment(data['valid_upto']).format('DD-MM-YYYY')
      this.registerForm = this._FormBuilder.group({
        registrationArray: this._FormBuilder.array(['data']),
      });
      this.registrationArray = this.registerForm.get(
        'registrationArray'
      ) as FormArray;
      this.registrationArray.removeAt(0);
      this.registrationArray.push(
        this._FormBuilder.group({
          uuid: data['uuid'],
          council: new FormControl({ value: data['council'], disabled: true }, [
            Validators.required,
            Validators.pattern('.*\\S.*[a-zA-z0-9 ]'),
          ]),
          number: new FormControl({ value: data['number'], disabled: true }, [
            Validators.required,
            Validators.pattern('.*\\S.*[a-zA-z ]'),
          ]),
          valid_upto: new FormControl(
            { value: valid_upto, disabled: true },
            Validators.required
          ),
          regiterFileName: '',
          edit: false,
        })
      );
      this.selectedFileName = data['r_documents'][0]['file_name'];
    }
  }
  addResgistration() {
    this.registrationArray = this.registerForm.get(
      'registrationArray'
    ) as FormArray;
    this.registrationArray.push(
      this._FormBuilder.group({
        uuid: new FormControl(null),
        council: new FormControl('', [
          Validators.required,
          Validators.pattern('.*\\S.*[a-zA-z ]'),
        ]),
        number: new FormControl('', [
          Validators.required,
          Validators.pattern('.*\\S.*[a-zA-z0-9 ]'),
        ]),
        valid_upto: new FormControl('', Validators.required),
        regiterFileName: new FormControl('', Validators.required),
      })
    );
  }

  trackFn(index) {
    return index;
  }
  chooseRegistrationFile(event: any) {
    const file = event.target.files;
    this.registrationFile = file[0];
    const fileNameLength = this.registrationFile.name
    if (fileNameLength.length < 51) {
      if (
        this.registrationFile.size < 2000000 &&
        (this.registrationFile.type === 'image/jpeg' ||
          this.registrationFile.type === 'image/jpg' ||
          this.registrationFile.type === 'application/pdf')
      ) {
        this.selectedFileName = this.registrationFile.name;
        this.registerFileSizeLarge = false;
      } else {
        const control = this.registrationArray.at(0);
        control.get('regiterFileName').setValue(null);
        this.registerFileSizeLarge = true;
        this.notificationService.warning('File Size Large && Check File Format');
      }
    } else {
      const control = this.registrationArray.at(0);
      control.get('regiterFileName').setValue(null);
      this.notificationService.warning("File name: Ensure this field has no more than 50 characters.");
    }
  }

  saveRegisterForm(i): void {
    this.formError = false;
    this.errorValue = [];
    this.updating = true;
    const registervalus = this.registerForm.controls['registrationArray'];
    const sendRegisterValue = this.registrationArray.controls[0]['controls'];
    console.log('sendregistervalue');
    console.log(sendRegisterValue); //registervalus[i];
    this.registrationData.council = sendRegisterValue?.council.value;
    this.registrationData.number = sendRegisterValue?.number.value;
    this.registrationData.valid_upto = moment(
      sendRegisterValue?.valid_upto.value
    ).format('YYYY-MM-DD');
    console.log(this.registrationData);
    const formData = new FormData();
    formData.append('file', this.registrationFile),
      formData.append('data', JSON.stringify(this.registrationData));

    if (sendRegisterValue?.uuid.value != null) {

      this.platformService
        .updateDoctorRegistration(
          this.doc_uuid,
          sendRegisterValue?.uuid.value,
          formData
        )
        .subscribe(
          (data) => {
            this.updating = false;
            this.notificationService.success(
              'Registration Form Updated',
              'Med.Bot'
            );
            this.addFormControl(data);
            this.saved_registrstion = true;
            this.editing = false;
            this.register_form_data = data;
            const registerDocument = this.register_form_data['r_documents'];
            this.fileUrl = registerDocument[0].file;
            this.registrationFileUpload = false;
          },
          (error) => {
            console.log(error);
            this.updating = false;
            const status = error['status'];
            if (status === 400) {
              this.formError = true;
              const file = error['error']['error_details']['validation_errors']['file'];
              const fileName = error['error']['error_details']['validation_errors']['file_name']
              const fileType = error['error']['error_details']['validation_errors']['file_type']
              const year = error['error']['error_details']['validation_errors']['year'];
              const numberError = error['error']['error_details']['validation_errors']['number'];
              if (!!numberError) {
                const rgisterNumberError = 'Number :' + numberError[0];
                this.errorValue.push({ value: rgisterNumberError })
                this.notificationService.error(
                  `${rgisterNumberError}`,
                  'Med.Bot'
                );
              } else if (fileType) {
                const fileErr = 'File :' + fileType[0];
                this.errorValue.push({ value: fileErr })
                this.notificationService.error(
                  `${fileErr}`,
                  'Med.Bot'
                );

              } else if (file) {
                const fileErr = 'File :' + file[0];
                this.errorValue.push({ value: fileErr })
                this.notificationService.error(
                  `${fileErr}`,
                  'Med.Bot'
                );

              } else if (fileName) {
                const fileErr = 'File Name' + fileName[0];
                this.errorValue.push({ value: fileErr })
                this.notificationService.error(
                  `${fileErr}`,
                  'Med.Bot'
                );

              } else if (year) {
                const yearError = 'Year :' + year[0];
                this.errorValue.push({ value: yearError })
                this.notificationService.error(
                  `${yearError}`,
                  'Med.Bot'
                );

              } else {
                this.notificationService.error(
                  'Registration Form Updation Error',
                  'Med.Bot'
                );
              }
            } else {
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          }
        );
    }
    else {
      console.log('post element');
      this.platformService.postDoctorRegistrations(
        this.doc_uuid,
        this.registrationData,
        this.registrationFile
      ).subscribe(
        (data) => {
          this.updating = false;
          this.notificationService.success(
            'Registration Form Uploaded',
            'Med.Bot'
          );
          this.addFormControl(data);
          this.saved_registrstion = true;
          this.register_form_data = data;
          const registerDocument = this.register_form_data['r_documents'];
          this.fileUrl = registerDocument[0].file;
          this.registrationFileUpload = false;
          // pc_stat = (parseInt(pc_stat) + 20).toString();
          // localStorage.setItem('profile_completion', pc_stat);
        },
        (error) => {
          console.log(error);
          this.updating = false;
          const status = error['status'];
          if (status === 400) {
            this.formError = true;
            const file = error['error']['error_details']['validation_errors']['file'];
            const fileName = error['error']['error_details']['validation_errors']['file_name']
            const fileType = error['error']['error_details']['validation_errors']['file_type']
            const year = error['error']['error_details']['validation_errors']['year'];
            const numberError = error['error']['error_details']['validation_errors']['number'];
            if (!!numberError) {
              const rgisterNumberError = 'Number :' + numberError[0];
              this.errorValue.push({ value: rgisterNumberError })
              this.notificationService.error(
                `${rgisterNumberError}`,
                'Med.Bot'
              );
            } else if (file) {
              const fileErr = 'File :' + file[0];
              this.errorValue.push({ value: fileErr })
              this.notificationService.error(
                `${fileErr}`,
                'Med.Bot'
              );

            } else if (fileType) {
              const fileErr = 'File :' + fileType[0];
              this.errorValue.push({ value: fileErr })
              this.notificationService.error(
                `${fileErr}`,
                'Med.Bot'
              );

            } else if (fileName) {
              const fileErr = 'File Name' + fileName[0];
              this.errorValue.push({ value: fileErr })
              this.notificationService.error(
                `${fileErr}`,
                'Med.Bot'
              );

            } else if (year) {
              const yearError = 'Year :' + year[0];
              this.errorValue.push({ value: yearError })
              this.notificationService.error(
                `${yearError}`,
                'Med.Bot'
              );

            } else {
              this.notificationService.error(
                'Registration Form Updation Error',
                'Med.Bot'
              );
            }
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
  }

  viewPdf() {
    window.open(this.fileUrl);
  }

  editRegistrations() {
    const i = 0;
    const control = this.registrationArray.at(i);
    control.get('valid_upto').enable();
    this.saved_registrstion = false;
    this.registrationFileUpload = true;
    this.editing = true;
  }

  getRegistedData(): void {
    this.platformService.getDoctorRegistrations(this.doc_uuid).subscribe(
      (data) => {
        data = data['results'];

        const length = Object.values(data).length;
        if (length > 0) {
          this.registrationFileUpload = false;
          this.saved_registrstion = true;
          this.doctorRegisteredData = data[0];
          this.regData = data[0];
          this.addFormControl(this.doctorRegisteredData);
          const list = Object.values(data);
          const fileData = list[0].r_documents;
          this.fileUrl = fileData[0]['file'];
          this.selectedFileName = fileData[0]['file_name'];
        } else {
          this.addFormControl(null);
        }
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );
  }

  cancel() {
    this.saved_registrstion = true;
    this.editing = false;
    this.registrationFileUpload = false;
    this.doctorRegisteredData = this.regData;
    this.addFormControl(this.doctorRegisteredData);
  }

  getCouncils() {
    this.platformService.getCouncils(this.system_of_medicine).subscribe(
      (data) => {
        this.councilList = Object.values(data);
      }, error => {

      });
  }
}
