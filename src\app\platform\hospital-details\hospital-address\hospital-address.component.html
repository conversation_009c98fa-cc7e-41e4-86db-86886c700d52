<div class="card">
    <div class="card-body">
        <form [formGroup]="hospitalAddressForm" class="hsp-add-form">
            <div formArrayName="addressArray">
                <div *ngFor="let data of this.address.controls; let i=index;" [formGroupName]="i">
                    <h4 class="card-title" id="accnt-det">Address {{i+1}}
                        <i id="editIcon" *ngIf="!frmControls('addressArray', i).controls['edit'].value"
                            (click)="editAddressDetail(i)" class="fa fa-edit"></i>
                    </h4>

                    <div class="row form-row">
                        <input type="text" class="form-control" formControlName="uuid" maxlength="50" hidden>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Address Type<span class="text-danger">*</span></label>
                                <select id="address_type" class="form-control input-field-border select"
                                    formControlName="address_type" required>
                                    <option value=null disabled>Select Address Type</option>
                                    <option value="Headquarters" translate>Head Quarters</option>
                                    <option value="Branch" translate>Branch</option>
                                </select>
                                <div class="text-danger"
                                    *ngIf="frmControls('addressArray', i).controls['address_type'].errors">
                                    <span
                                        *ngIf="frmControls('addressArray', i).controls['address_type'].errors?.required && (frmControls('addressArray', i).controls['address_type'].dirty || frmControls('addressArray', i).controls['address_type'].touched)">
                                        Address Type is required
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Line 1<span class="text-danger">*</span></label>
                                <input id="line_1" type="text" class="form-control" formControlName="line_1"
                                    maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('addressArray', i).controls['line_1'].errors">
                                    <span
                                        *ngIf="frmControls('addressArray', i).controls['line_1'].errors?.required && (frmControls('addressArray', i).controls['line_1'].dirty || frmControls('addressArray', i).controls['line_1'].touched)">
                                        Line 1 is required
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Line 2<span class="text-danger">*</span></label>
                                <input id="line_2" type="text" class="form-control" formControlName="line_2"
                                    maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('addressArray', i).controls['line_2'].errors">
                                    <span
                                        *ngIf="frmControls('addressArray', i).controls['line_2'].errors?.required && (frmControls('addressArray', i).controls['line_2'].dirty || frmControls('addressArray', i).controls['line_2'].touched)">
                                        Line 2 is required
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row form-row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>District<span class="text-danger">*</span></label>
                                <input id="district" type="text" class="form-control" formControlName="district"
                                    maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('addressArray', i).controls['district'].errors">
                                    <span
                                        *ngIf="frmControls('addressArray', i).controls['district'].errors?.required && (frmControls('addressArray', i).controls['district'].dirty || frmControls('addressArray', i).controls['district'].touched)">
                                        District is required
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>City/Town/Village<span class="text-danger">*</span></label>
                                <input id="city_town_village" type="text" class="form-control"
                                    formControlName="city_town_village" maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('addressArray', i).controls['city_town_village'].errors">
                                    <span
                                        *ngIf="frmControls('addressArray', i).controls['city_town_village'].errors?.required && (frmControls('addressArray', i).controls['city_town_village'].dirty || frmControls('addressArray', i).controls['city_town_village'].touched)">
                                        City/Town is required
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Taluk<span class="text-danger">*</span></label>
                                <input type="text" class="form-control" formControlName="taluk" maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('addressArray', i).controls['taluk'].errors">
                                    <span
                                        *ngIf="frmControls('addressArray', i).controls['taluk'].errors?.required && (frmControls('addressArray', i).controls['taluk'].dirty || frmControls('addressArray', i).controls['taluk'].touched)">
                                        Taluk is required
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row form-row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>State<span class="text-danger">*</span></label>
                                <input id="state" type="text" class="form-control" formControlName="state"
                                    maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('addressArray', i).controls['state'].errors">
                                    <span
                                        *ngIf="frmControls('addressArray', i).controls['state'].errors?.required && (frmControls('addressArray', i).controls['state'].dirty || frmControls('addressArray', i).controls['state'].touched)">
                                        State is required
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Country<span class="text-danger">*</span></label>
                                <input id="country" type="text" class="form-control" formControlName="country"
                                    maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('addressArray', i).controls['country'].errors">
                                    <span
                                        *ngIf="frmControls('addressArray', i).controls['country'].errors?.required && (frmControls('addressArray', i).controls['country'].dirty || frmControls('addressArray', i).controls['country'].touched)">
                                        Country is required
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Pincode<span class="text-danger">*</span></label>
                                <input type="text" class="form-control" formControlName="postal_code" maxlength="50"
                                    required>
                                <div class="text-danger"
                                    *ngIf="frmControls('addressArray', i).controls['postal_code'].errors">
                                    <span
                                        *ngIf="frmControls('addressArray', i).controls['postal_code'].errors?.required && (frmControls('addressArray', i).controls['postal_code'].dirty || frmControls('addressArray', i).controls['postal_code'].touched)">
                                        Pincode is required
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="frmControls('addressArray', i).controls['edit'].value" id="edit"
                        class="form-group text-right">
                        <button id="ba-save" type="submit" class="btn btn-primary save-btn"
                            [disabled]="!hospitalAddressForm.valid" (click)="saveHospitalAddress(i)">Save</button>
                        <button id="ba-canc" type="button" class="btn btn-secondary cancel-btn"
                            (click)="cancelEdit(i)">Cancel</button>
                    </div>
                </div>
            </div>
        </form>
        <!-- <div *ngIf="msgShow" class="add-more">
            <p>No Data</p>
        </div> -->
    </div>
</div>