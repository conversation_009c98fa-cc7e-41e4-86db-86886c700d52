import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FormGroup, FormBuilder, FormArray, Validators } from '@angular/forms';
import { PlatformService } from './../../platform.service';
import { Component, OnInit } from '@angular/core';
import { camelize } from "src/app/shared/util/camelize";

@Component({
  selector: 'app-hospital-address',
  templateUrl: './hospital-address.component.html',
  styleUrls: ['./hospital-address.component.css']
})
export class HospitalAddressComponent implements OnInit {
  hospitalAddressForm: FormGroup;
  addressArray: FormArray;
  hospital_address_set = [];
  hsp_uuid = localStorage.getItem('hospital');
  msgShow = false;
  constructor(
    private platformService: PlatformService,
    private formBuilder: FormBuilder,
    private notificationService: ToastrService,
    private route: ActivatedRoute,
  ) {
    this.hospitalAddressForm = this.formBuilder.group({
      addressArray: this.formBuilder.array([]),
    });
  }

  ngOnInit(): void {
    this.route.params.subscribe(
      url => {
        this.hsp_uuid = url['uuid'];
        this.getAddressSet(this.hsp_uuid);
      });
  }

  getAddressSet(uuid) {
    this.platformService.getHospitalAddresses(uuid).subscribe(
      data => {
        this.hospital_address_set = data['results'];
        const addr = this.hospital_address_set;
        if (addr.length == 0) {
          this.msgShow = true;
          this.addHospitalAddress();
        } else {
          for (let i = addr.length; i > 0; i--) {
            this.createHospitalAddress(addr[i - 1]);
          }
        }
        console.log(this.hospital_address_set);
      }
    );
  }

  addHospitalAddress() {
    const userType = localStorage.getItem('user_type');
    if (userType === 'HospitalAdmin') {
      this.createHospitalAddress(null);
    }
  }

  createHospitalAddress(data: Object) {
    this.addressArray = this.hospitalAddressForm.get('addressArray') as FormArray;
    console.log(this.addressArray);
    if (data == null) {
      this.addressArray.push(
        this.formBuilder.group({
          uuid: null,
          address_type: [null, Validators.required],
          line_1: [null, Validators.required],
          line_2: [null, Validators.required],
          district: [null, Validators.required],
          taluk: [null, Validators.required],
          state: [null, Validators.required],
          country: [null, Validators.required],
          city_town_village: [null, Validators.required],
          postal_code: [null, Validators.required],
          edit: true,
        })
      );
      console.log(this.addressArray.get('addressArray'));
    }
    else {
      this.addressArray.push(
        this.formBuilder.group({
          uuid: data['uuid'],
          address_type: [{ value: data['address_type'], disabled: true }, Validators.required],
          line_1: [{ value: data['line_1'], disabled: true }, Validators.required],
          line_2: [{ value: data['line_2'], disabled: true }, Validators.required],
          district: [{ value: data['district'], disabled: true }, Validators.required],
          taluk: [{ value: data['taluk'], disabled: true }, Validators.required],
          state: [{ value: data['state'], disabled: true }, Validators.required],
          country: [{ value: data['country'], disabled: true }, Validators.required],
          city_town_village: [{ value: data['city_town_village'], disabled: true }, Validators.required],
          postal_code: [{ value: data['postal_code'], disabled: true }, Validators.required],
          edit: false,
        })
      );
    }
  }

  saveHospitalAddress(i) {
    const data = this.hospitalAddressForm.get('addressArray').value[i];
    if (data['uuid']) {
      this.platformService.patchHospitalAddress(this.hsp_uuid, data['uuid'], data).subscribe(
        data => {
          const control = this.addressArray.at(i);
          control.get('edit').setValue(false);
          this.addressArray.at(i).disable();
          this.notificationService.success('Address Updated', 'Med.Bot');
        }
      );
    }
    else {
      this.platformService.postHospitalAddress(this.hsp_uuid, data).subscribe(
        data => {
          this.cancelEdit(i);
          this.getAddressSet(this.hsp_uuid);
          this.notificationService.success('Address Added', 'Med.Bot');
        },
        error => {
          console.log(error);
          const status = error['status'];
          if (status === 400) {
            if (error.error.error_details.validation_errors) {
              let messages = '';
              for (let i = 0; i < Object.keys(error.error.error_details.validation_errors).length; i++) {
                const key = Object.keys(error.error.error_details.validation_errors)[i];
                messages = messages + ' ' + camelize(key) + ': ' + error.error.error_details.validation_errors[key];
              }
              this.notificationService.error(`${messages}`, 'Med.Bot');
            } else {
              this.notificationService.error(`${error.error['error_message']}`, 'Med.Bot');
            }
          }
        });
    }
  }

  get address(): FormArray {
    return this.hospitalAddressForm.get("addressArray") as FormArray
  }

  frmControls(controlName: string, index: number) {
    let controlList = this.hospitalAddressForm.get(controlName) as FormArray;
    const formGroup = controlList.controls[index] as FormGroup;
    return formGroup;
  }

  cancelEdit(i: any) {
    const control = this.addressArray.at(i);
    const uuid = control.get('uuid').value;
    if (uuid == null) {
      this.addressArray.removeAt(i);
    }
    else {
      const control = this.addressArray.at(i);
      control.get('edit').setValue(false);
      control.get('address_type').disable();
      control.get('line_1').disable();
      control.get('line_2').disable();
      control.get('district').disable();
      control.get('state').disable();
      control.get('taluk').disable();
      control.get('city_town_village').disable();
      control.get('country').disable();
      control.get('postal_code').disable();
    }
  }

  editAddressDetail(i: any) {
    const control = this.addressArray.at(i);
    control.get('edit').setValue(true);
    this.addressArray.at(i).enable();
  }
}
