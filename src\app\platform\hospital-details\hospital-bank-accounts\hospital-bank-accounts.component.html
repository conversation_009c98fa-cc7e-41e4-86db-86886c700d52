<div class="card">
    <div class="card-body">
        <form [formGroup]="bankAccountDetailForm">
            <div formArrayName="bankAccountDetailArray">
                <ng-container *ngFor="let data of this.bankDetails.controls; let i=index;" [formGroupName]="i">
                    <h4 class="card-title" id="accnt-det">Account Detail {{i+1}}
                        <!-- <i id="editIcon" *ngIf="!frmControls('bankAccountDetailArray', i).controls['edit'].value"
                            (click)="editBankAccountDetail(i)" class="fa fa-edit"></i>&nbsp;
                        <i id="delete"
                            *ngIf="!frmControls('bankAccountDetailArray', i).controls['edit'].value && !newForm"
                            (click)="removeBankAccount(i)" class="fa fa-trash-alt" aria-hidden="true"></i> -->
                    </h4>
                    <h3>Hospital Details&nbsp;<i
                            *ngIf="!frmControls('bankAccountDetailArray', i).controls['edit'].value"
                            (click)="editBankAccountDetail(i)" class="fa fa-edit"></i></h3>
                    <div class="row form-row">
                        <input type="text" class="form-control" formControlName="uuid" maxlength="50" hidden>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Account Name<span class="text-danger">*</span></label>
                                <input id="account_name" type="text" class="form-control" formControlName="account_name"
                                    maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('bankAccountDetailArray', i).controls['account_name'].errors">
                                    <span
                                        *ngIf="frmControls('bankAccountDetailArray', i).controls['account_name'].errors?.required && (frmControls('bankAccountDetailArray', i).controls['account_name'].dirty || frmControls('bankAccountDetailArray', i).controls['account_name'].touched)">
                                        Account Name is required
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Account Number<span class="text-danger">*</span></label>
                                <input id="account_number" type="text" class="form-control"
                                    formControlName="account_number" maxlength="50" required >
                                <div class="text-danger"
                                    *ngIf="frmControls('bankAccountDetailArray', i).controls['account_number'].errors">
                                    <span
                                        *ngIf="frmControls('bankAccountDetailArray', i).controls['account_number'].errors?.required && (frmControls('bankAccountDetailArray', i).controls['account_number'].dirty || frmControls('bankAccountDetailArray', i).controls['account_number'].touched)">
                                        Account Number is required
                                    </span>
                                    <span
                                        *ngIf="frmControls('bankAccountDetailArray', i).controls['account_number'].errors?.pattern ">
                                        Account Number is accepted only in numbers
                                    </span>
                                </div>

                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Account Type<span class="text-danger">*</span></label>
                                <ng-select id="account_type" formControlName="account_type" [items]="accountTypes"
                                    [searchable]="false" bindLabel="accountTypes" placeholder="Select" required>
                                </ng-select>
                                <div class="text-danger"
                                    *ngIf="frmControls('bankAccountDetailArray', i).controls['account_type'].errors">
                                    <span
                                        *ngIf="frmControls('bankAccountDetailArray', i).controls['account_type'].errors?.required && (frmControls('bankAccountDetailArray', i).controls['account_type'].dirty || frmControls('bankAccountDetailArray', i).controls['account_type'].touched)">
                                        Account Type is required
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row form-row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Bank Name<span class="text-danger">*</span></label>
                                <input id="bank_name" type="text" class="form-control" formControlName="bank_name"
                                    maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('bankAccountDetailArray', i).controls['bank_name'].errors">
                                    <span
                                        *ngIf="frmControls('bankAccountDetailArray', i).controls['bank_name'].errors?.required && (frmControls('bankAccountDetailArray', i).controls['bank_name'].dirty || frmControls('bankAccountDetailArray', i).controls['bank_name'].touched)">
                                        Bank Name is required
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Branch Name<span class="text-danger">*</span></label>
                                <input id="branch_name" type="text" class="form-control" formControlName="branch_name"
                                    maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('bankAccountDetailArray', i).controls['branch_name'].errors">
                                    <span
                                        *ngIf="frmControls('bankAccountDetailArray', i).controls['branch_name'].errors?.required && (frmControls('bankAccountDetailArray', i).controls['branch_name'].dirty || frmControls('bankAccountDetailArray', i).controls['branch_name'].touched)">
                                        Branch Name is required
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>IFSC Code<span class="text-danger">*</span></label>
                                <input id="ifsc_code" type="text" class="form-control" formControlName="ifsc_code"
                                    maxlength="50" required>
                                <div class="text-danger"
                                    *ngIf="frmControls('bankAccountDetailArray', i).controls['ifsc_code'].errors">
                                    <span
                                        *ngIf="frmControls('bankAccountDetailArray', i).controls['ifsc_code'].errors?.required && (frmControls('bankAccountDetailArray', i).controls['ifsc_code'].dirty || frmControls('bankAccountDetailArray', i).controls['ifsc_code'].touched)">
                                        IFSC Code is required
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="frmControls('bankAccountDetailArray', i).controls['edit'].value" id="edit"
                        class="form-group text-right">
                        <button id="ba-save" type="submit" class="btn btn-primary save-btn"
                            [disabled]="!bankAccountDetailForm.valid" (click)="saveAccountDetail(i)">Save</button>
                        <button id="ba-canc" type="button" class="btn btn-secondary cancel-btn"
                            (click)="cancelEdit(i)">Cancel</button>
                    </div>

                </ng-container>
            </div>
        </form>
        <div *ngIf="showAddMore" class="add-more">
            <!-- <p id="add-ba" (click)="createBankAccountDetailForm(null)"><i class="fa fa-plus-circle"></i> Add Bank Account</p> -->
        </div>
        <!-- <div *ngIf="msgShow" class="add-more">
            <p>No Data</p>
        </div> -->
    </div>
</div>