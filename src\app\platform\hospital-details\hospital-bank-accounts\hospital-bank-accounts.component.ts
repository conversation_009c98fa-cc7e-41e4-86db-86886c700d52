import { PlatformService } from './../../platform.service';
import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { camelize } from "src/app/shared/util/camelize";
@Component({
  selector: 'app-hospital-bank-accounts',
  templateUrl: './hospital-bank-accounts.component.html',
  styleUrls: ['./hospital-bank-accounts.component.css']
})
export class HospitalBankAccountsComponent implements OnInit {

  public bankAccountDetailForm: FormGroup;
  public bankAccountDetailArray: FormArray;
  public edit = false;
  public newForm = false;
  public showAddMore = true;
  public bankAccountList = [];
  public accountTypes = ['Savings Account', 'Current Account'];
  public doctor = {};
  public hsp_uuid = localStorage.getItem('hospital');
  msgShow = false;

  constructor(
    private formBuilder: FormBuilder,
    private platformService: PlatformService,
    private notificationService: ToastrService,
    private route: ActivatedRoute,
  ) {
    this.bankAccountDetailForm = this.formBuilder.group({
      bankAccountDetailArray: this.formBuilder.array([]),
    });
  }

  ngOnInit(): void {
    this.route.params.subscribe(
      url => {
        this.hsp_uuid = url['uuid'];
        this.loadBankData();
      });
  }

  loadBankData() {
    this.platformService.getHospitalBankAccounts(this.hsp_uuid).subscribe(
      data => {
        this.bankAccountList = data['results'];
        const banks = this.bankAccountList;
        if (banks.length == 0) {
          this.msgShow = true;
          this.addBankDetails();
        } else {
          for (let i = banks.length; i > 0; i--) {
            this.createBankAccountDetailForm(banks[i - 1]);
          }
        }
      }
    );
  }

  addBankDetails() {
    const userType = localStorage.getItem('user_type');
    if (userType === 'HospitalAdmin') {
      this.createBankAccountDetailForm(null);
    }
  }

  createBankAccountDetailForm(data: Object) {
    this.bankAccountDetailArray = this.bankAccountDetailForm.get('bankAccountDetailArray') as FormArray;
    if (data == null) {
      this.bankAccountDetailArray.push(
        this.formBuilder.group({
          uuid: null,
          account_type: [null, Validators.required],
          account_name: [null, Validators.required],
          account_number: [null,( Validators.required, Validators.pattern('[0-9 ]*'))],
          bank_name: [null, Validators.required],
          branch_name: [null, Validators.required],
          ifsc_code: [null, Validators.required],
          edit: true,
        })
      );
      this.showAddMore = false;
      this.newForm = true;
    }
    else {
      this.bankAccountDetailArray.push(
        this.formBuilder.group({
          uuid: data['uuid'],
          account_type: [{ value: data['account_type'], disabled: true }, Validators.required],
          account_name: [{ value: data['account_name'], disabled: true }, Validators.required],
          account_number: [{ value: data['account_number'], disabled: true }, ( Validators.required, Validators.pattern('[0-9 ]*'))],
          bank_name: [{ value: data['bank_name'], disabled: true }, Validators.required],
          branch_name: [{ value: data['branch_name'], disabled: true }, Validators.required],
          ifsc_code: [{ value: data['ifsc_code'], disabled: true }, Validators.required],
          edit: false,
        })
      );
    }
  }

  get bankDetails(): FormArray {
    return this.bankAccountDetailForm.get("bankAccountDetailArray") as FormArray
  }

  frmControls(controlName: string, index: number) {
    let controlList = this.bankAccountDetailForm.get(controlName) as FormArray;
    const formGroup = controlList.controls[index] as FormGroup;
    return formGroup;
  }

  saveAccountDetail(i) {
    const data = this.bankAccountDetailForm.get('bankAccountDetailArray').value[i];
    if (data['uuid']) {
      this.platformService.patchHospitalBankAccounts(this.hsp_uuid, data['uuid'], data).subscribe(
        data => {
          const control = this.bankAccountDetailArray.at(i);
          control.get('edit').setValue(false);
          this.bankAccountDetailArray.at(i).disable();
          this.notificationService.success('Bank Account Updated', 'Med.Bot');
        }
      );
    }
    else {
      this.platformService.postHospitalBankAccounts(this.hsp_uuid, data).subscribe(
        data => {
          this.cancelEdit(i);
          this.loadBankData();
          this.notificationService.success('Bank Account Added', 'Med.Bot');
        },
        error => {
          console.log(error);
          const status = error['status'];
          if (status === 400) {
            if (error.error.error_details.validation_errors) {
              let messages = '';
              for (let i = 0; i < Object.keys(error.error.error_details.validation_errors).length; i++) {
                const key = Object.keys(error.error.error_details.validation_errors)[i];
                messages = messages + ' ' + camelize(key) + ': ' + error.error.error_details.validation_errors[key];
              }
              this.notificationService.error(`${messages}`, 'Med.Bot');
            } else {
              this.notificationService.error(`${error.error['error_message']}`, 'Med.Bot');
            }
          }
        });
    }
  }

  removeBankAccount(i) {
    const data = this.bankAccountDetailForm.get('bankAccountDetailArray').value[i];
    const uuid = data['uuid'];
    if (uuid) {
      this.platformService.deleteHospitalBankAccounts(this.hsp_uuid, uuid).subscribe(
        data => {
          const control = this.bankAccountDetailForm.get('bankAccountDetailArray') as FormArray;
          control.removeAt(i);
          this.loadBankData();
          this.notificationService.success('Bank Account Deleted', 'Med.Bot');
        },
        error => {
          console.log(error);
          this.notificationService.error('Bank Account Deletion Failed', 'Med.Bot');
        }
      );
    }
    else {
      this.bankAccountDetailArray.removeAt(i);
      this.showAddMore = true;
    }
  }

  emptyFormArray() {
    const control = this.bankAccountDetailForm.get(
      'bankAccountDetailArray'
    ) as FormArray;
    for (let i = control.length - 1; i >= 0; i--) {
      control.removeAt(i);
    }
  }

  editBankAccountDetail(i: any) {
    this.newForm = false;
    const control = this.bankAccountDetailArray.at(i);
    control.get('edit').setValue(true);
    control.get('account_type').enable();
    control.get('account_name').enable();
    control.get('account_number').enable();
    control.get('bank_name').enable();
    control.get('branch_name').enable();
    control.get('ifsc_code').enable();
    this.showAddMore = false;
  }

  cancelEdit(i: any) {
    const control = this.bankAccountDetailArray.at(i);
    const uuid = control.get('uuid').value;
    if (uuid == null) {
      this.removeBankAccount(i);
    }
    else {
      const control = this.bankAccountDetailArray.at(i);
      control.get('edit').setValue(false);
      control.get('account_type').disable();
      control.get('account_name').disable();
      control.get('account_number').disable();
      control.get('bank_name').disable();
      control.get('branch_name').disable();
      control.get('ifsc_code').disable();
    }
    this.showAddMore = true;
  }


}
