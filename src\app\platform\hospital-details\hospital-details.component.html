<div class="breadcrumb-bar banner">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-12 col-12">
                <nav aria-label="breadcrumb" class="page-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);">{{'Admin'|translate}}</a></li>
                        <li #listHeader class="breadcrumb-item active" aria-current="page">{{'Hospital View'
                            |translate}}</li>
                    </ol>
                </nav>
                <h2 #header class="breadcrumb-title">{{'Hospital Detail View' |translate}}</h2>
            </div>
        </div>
    </div>
</div>
<div class="content hsp" *ngIf="!isLoading">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 col-lg-12 col-xl-12">
                <div class="hsptl-form">
                    <h5 class="back-head" (click)="goBack()"><i class="fas fa-chevron-circle-left"></i>Back</h5>
                    <!--<i class="fa fa-edit"></i></h4-->
                    <div class="card ">
                        <div class="card-body">
                            <h3>Hospital Details&nbsp;<i *ngIf="!edit" (click)="editHspDetails()"
                                    class="fa fa-edit"></i></h3>
                            <div class="hsp-details">
                                <form [formGroup]="hospitalViewForm" class="hsp-detail-form">
                                    <div class="row">
                                        <input id="hsp-uuid" formControlName="uuid" type="text" class="form-control"
                                            hidden>
                                        <div class="col-md-3 col-lg-3 col-sm-6">
                                            <label>Name <span class="text-danger">*</span></label>
                                            <input id="hsp-name" formControlName="name" type="text" class="form-control"
                                                maxlength="50" pattern="[a-zA-Z0-9 ]*" required>
                                            <div class="text-danger"
                                                *ngIf="hospitalViewForm.get('name').errors?.required && (hospitalViewForm.get('name').dirty || hospitalViewForm.get('name').touched)">
                                                Name is required.
                                            </div>
                                            <div class="text-danger"
                                                *ngIf="hospitalViewForm.get('name').errors?.pattern">
                                                Only alphabet and Number format are permitted
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-lg-3 col-sm-6">
                                            <label>Website Url </label>
                                            <input id="hsp-url" formControlName="url" type="text" class="form-control"
                                                maxlength="50" placeholder="www.example.com">
                                            <div class="text-danger"
                                                *ngIf="(hospitalViewForm.get('url').dirty || hospitalViewForm.get('url').touched)&& hospitalViewForm.get('url').errors?.pattern">
                                                Invalid URL Format
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-lg-3 col-sm-6">
                                            <label>Email <span class="text-danger">*</span></label>
                                            <input type="text" id="hsp-reg-num" formControlName="email"
                                                class="form-control" maxlength="50" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3 col-lg-3 col-sm-6">
                                            <label>Phone Number <span class="text-danger">*</span></label>
                                            <input id="hsp-phone" type="text" formControlName="phone_numbers"
                                                class="form-control" maxlength="15" pattern="[0-9]*" required>
                                            <div class="text-danger"
                                                *ngIf="hospitalViewForm.get('phone_numbers').errors?.required && (hospitalViewForm.get('phone_numbers').dirty || hospitalViewForm.get('phone_numbers').touched)">
                                                Phone Numbers is required.
                                            </div>
                                            <div class="text-danger"
                                                *ngIf="hospitalViewForm.get('phone_numbers').errors?.pattern">
                                                Phone Number should Only in Numbers.
                                            </div>
                                            <div class="text-danger"
                                                *ngIf="hospitalViewForm.get('phone_numbers').errors?.minlength">
                                                Phone Number should minimum 10 Numbers.
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-lg-3 col-sm-6">
                                            <label>Admin Name <span class="text-danger">*</span></label>
                                            <input id="admin-name" type="text" formControlName="contact_person_name"
                                                class="form-control" maxlength="50" required>
                                            <div class="text-danger"
                                                *ngIf="hospitalViewForm.get('contact_person_name').errors?.required && (hospitalViewForm.get('contact_person_name').dirty || hospitalViewForm.get('contact_person_name').touched)">
                                                Name is required.
                                            </div>
                                            <div class="text-danger"
                                                *ngIf="hospitalViewForm.get('contact_person_name').errors?.pattern">
                                                Only alphabet and Number format are permitted
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-lg-3 col-sm-6" *ngIf="edit">
                                            <label>Hospital logo</label>
                                            <div class="change-photo-btn">
                                                <span><i class="fa fa-upload"></i> {{ 'Upload Logo'|translate
                                                    }}</span>
                                                <input type="file" class="upload" id="hospital-logo"
                                                    [disabled]="disabledUploadPhotoBtn" formControlName="logo_large"
                                                    (change)="hospitalLogoChange($event)" accept=".jpg, .png,">
                                            </div>
                                            <small class="form-text text-muted" *ngIf="edit" translate>Allowed JPG, GIF
                                                or PNG. Max size of 2MB</small>
                                        </div>
                                        <div class="col-md-3 col-lg-3 col-sm-6" *ngIf="edit || hospitalLogoURL">
                                            <label *ngIf="!edit">Hospital logo</label>
                                            <div class="profile-img" *ngIf="hospitalLogoURL!=''">
                                                <img [src]="hospitalLogoURL" style="height: 100px;width: 100px;">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div [ngClass]="{
                                            'col-md-6': edit,
                                            'col-lg-6': edit,
                                            'col-md-4': !edit,
                                            'col-lg-4': !edit,
                                            'col-sm-12': true
                                          }">
                                            <label>Login Url</label>
                                            <div class="d-flex justify-content-center align-items-center">
                                                <label class="m-0">{{urlPrefix}}</label>
                                                <input id="hsp-login-url" type="text" formControlName="custom_url"
                                                    class="form-control" [readonly]="userType!='PlatformAdmin'">
                                                <label class="ml-2 m-0"
                                                    style="min-width: fit-content; flex-grow: 1;">{{urlDomain}}</label>
                                                <div *ngIf="edit" class="d-inline-flex">
                                                    <button class="btn btn-secondary m-0 ml-5 form-control"
                                                        (click)="checkAvailability()"> Check Availability</button>
                                                    <label>{{urlAvailableStatus}}</label>
                                                </div>
                                            </div>
                                            <!-- <input id="hsp-login-url" type="text" formControlName="url_prefix"
                                                class="form-control" required readonly> -->
                                        </div>

                                        <!-- <div class="col-md-3 col-lg-3 col-sm-6">
                                            <label>Domain</label>
                                            <input id="hsp-login-url" type="text" formControlName="url_domain"
                                                class="form-control" required readonly>
                                        </div> -->
                                        <div class="col-md-3 col-lg-3 col-sm-6">

                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-9 mt-1">
                                            <label>Description <span class="text-danger">*</span></label>
                                            <div *ngIf="edit">
                                                <ckeditor [config]="ckConfig" formControlName="description" id="dist">
                                                    <ckbutton [name]="'saveButton'" [command]="'saveCmd'"
                                                        [label]="'Save Document'">
                                                    </ckbutton>
                                                </ckeditor>
                                            </div>
                                            <div *ngIf="!edit">
                                                <div class="card card-body" style="background-color: #e9ecef;
                                      opacity: 1;" [innerHTML]="hospitalViewForm.controls.description.value">
                                                </div>
                                            </div>

                                            <!--  <textarea id="dist" class="form-control desc"></textarea> -->
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3 col-lg-3 col-sm-6">
                                            <!-- <div class="col-md-3 col-lg-3 col-sm-6" formGroupName="user"> -->
                                            <!-- <input id="user-username" type="text" formControlName="username" class="form-control" hidden> -->
                                        </div>
                                        <div class="col-md-4 col-lg-4 col-sm-6">
                                        </div>

                                        <div *ngIf="edit" class="col-md-3 col-lg-3 col-sm-6">
                                            <button class="btn btn-secondary hsp-btn" (click)="cancelHspEdit()"
                                                id="hspcancel-btn">Cancel</button>
                                            <button class="btn btn-primary hsp-btn" id="hspsave-btn"
                                                [disabled]="!hospitalViewForm.valid"
                                                (click)="updateHospitalDetails()">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <h3>Hospital Address</h3>
                            <app-hospital-address></app-hospital-address>
                            <h3>Bank Accounts</h3>
                            <app-hospital-bank-accounts></app-hospital-bank-accounts>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="centered" *ngIf="isLoading">
    <app-loading-spinner></app-loading-spinner>
</div>