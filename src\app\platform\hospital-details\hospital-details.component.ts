import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { PlatformService } from './../platform.service';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { SharedService } from '../../shared/shared.service';
import { HospitalService } from 'src/app/hospital-admin/hospital-admin.service';
import * as Settings from './../../config/settings';
import { settings } from 'cluster';

@Component({
  selector: 'app-hospital-details',
  templateUrl: './hospital-details.component.html',
  styleUrls: ['./hospital-details.component.css']
})
export class HospitalDetailsComponent implements OnInit {
  hospitalViewForm: FormGroup;
  hospitalLogoURL: any;
  hospitalsList = [];
  edit = false;
  hsp_uuid = localStorage.getItem('hospital');
  selectedHospital: any;
  public ckText = '';
  public ckConfig = {
    bodyClass: 'txt-area',
  };
  public isLoading: boolean;
  hospitalLogoFileSize: boolean = false;
  disabledUploadPhotoBtn: boolean;
  profileUpload: boolean;
  userData: Object;
  doctorProfilePictureUrl: string = '';
  hospitalLogo: string = '';
  userType: string;
  urlAvailableStatus: string = '';
  urlDomain: string;
  urlPrefix: string;

  constructor(
    private platformService: PlatformService,
    private notificationService: ToastrService,
    private route: ActivatedRoute,
    private location: Location,
    private sharedService: SharedService,
    private hospitalService: HospitalService,
  ) {
    this.urlPrefix = Settings.URLPREFIX;
    if (location.path() != '') {
      const currentPath = window.location.origin;
      let path = '';
      if (currentPath.includes('app-test')) {
        path = Settings.TestURLDOMAIN;
      } else if (currentPath.includes('app-staging')) {
        path = Settings.StageURLDOMAIN;
      } else if (currentPath.includes('localhost')) {
        path = Settings.TestURLDOMAIN;
      } else {
        path = Settings.LiveURLDOMAIN;
      };
      this.urlDomain = path;
    }
  }

  ngOnInit(): void {
    this.isLoading = false;
    this.sharedService.setActiveLink('hospital details');
    this.initializeHspForm();
    this.route.params.subscribe(
      url => {
        this.hsp_uuid = url['uuid'];
        // this.renderProfileData();
        this.getHospital();
      });
    this.userType = localStorage.getItem('user_type');
  }

  editHspDetails() {
    this.hospitalViewForm.enable();
    this.edit = true;
  }

  cancelHspEdit() {
    this.hospitalViewForm.disable();
    this.edit = false;
  }

  updateHospitalDetails() {
    const request = this.hospitalViewForm.value;
    const selectedImageFile: File = this.hospitalViewForm.get('logo_large').value;
    let data = new FormData();
    data.append('uuid', request.uuid);
    data.append('name', request.name);
    data.append("url", request.url);
    data.append("contact_person_name", request.contact_person_name);
    data.append("description", request.description);
    data.append("email", request.email);
    data.append("phone_numbers", request.phone_numbers);
    data.append("logo_large", selectedImageFile);
    if (request.custom_url != '' && request.custom_url != undefined && request.custom_url != null) {
      let login_url = Settings.URLPREFIX + request.custom_url + this.urlDomain;
      data.append("login_url", login_url);
    }


    this.platformService.patchHospital(request.uuid, data).subscribe(
      data => {
        this.hospitalLogoURL = data['logo_large'].file;
        this.hospitalService.setHospitalDetails();
        this.notificationService.success('Hospital Details Updated', 'Med.Bot');
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
    this.hospitalViewForm.disable();
    this.edit = false;
  }

  getHospital() {
    this.isLoading = true;
    this.platformService.getHospitalDetail(this.hsp_uuid).subscribe(
      data => {
        this.selectedHospital = data;
        if (
          this.selectedHospital['logo_large'] != null &&
          this.selectedHospital['logo_large'] != undefined &&
          this.selectedHospital['logo_large'] != '') {
          this.hospitalLogoURL = this.selectedHospital['logo_large']['file'];
        }
        this.hospitalService.setHospitalDetails();
        this.viewHospital();
        this.isLoading = false;
      }, error => {
        console.log(error);
        this.isLoading = false
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  initializeHspForm() {
    this.hospitalViewForm = new FormGroup({
      uuid: new FormControl(''),
      name: new FormControl('', [Validators.required, Validators.pattern('^[a-zA-Z0-9 ]*$'), Validators.minLength(2)]),
      url: new FormControl('', [Validators.minLength(10),Validators.pattern('^(www\.)?([a-zA-Z0-9_-]+)+(\.[a-zA-Z]{2,})+(\/[a-zA-Z0-9#=_-]+)*\/?$')]),
      phone_numbers: new FormControl('', [Validators.required, Validators.pattern('[0-9]*'), Validators.minLength(10)]),
      email: new FormControl('', [Validators.required, Validators.email]),
      contact_person_name: new FormControl('', [Validators.required, Validators.pattern('^[a-zA-Z0-9 ]*$')]),
      description: new FormControl('', Validators.required),
      logo_large: new FormControl(''),
      url_prefix: new FormControl(Settings.URLPREFIX),
      custom_url: new FormControl(''),
      url_domain: new FormControl(this.urlDomain),
      login_url: new FormControl(''),
    });
    this.hospitalLogoURL = '';
  }

  viewHospital() {
    this.hospitalViewForm.patchValue({
      uuid: this.selectedHospital['uuid'],
      name: this.selectedHospital['name'],
      url: this.selectedHospital['url'],
      phone_numbers: this.selectedHospital['phone_numbers'],
      email: this.selectedHospital['email'],
      contact_person_name: this.selectedHospital['contact_person_name'],
      description: this.selectedHospital['description'],
    });
    if (this.selectedHospital['login_url'] != undefined && this.selectedHospital['login_url'] != null) {
      let url = this.selectedHospital['login_url'];
      url = url.replace(Settings.URLPREFIX, '');
      url = url.replace(this.urlDomain, '');
      this.hospitalViewForm.get('custom_url').setValue(url);
    }
    this.hospitalViewForm.disable();
    this.edit = false;
    if (
      this.selectedHospital['logo_large'] != null &&
      this.selectedHospital['logo_large'] != undefined &&
      this.selectedHospital['logo_large'] != '') {
      this.hospitalLogo = this.selectedHospital['logo_large']['file'];
    }
  }

  goBack() {
    this.location.back();
  }

  hospitalLogoChange(event) {
    const file = event.target.files;
    if (file.length > 0) {
      this.hospitalLogoFileSize = false;
      const hospitalLogo = file[0];
      if (
        hospitalLogo.size < 2000000 &&
        (hospitalLogo.type === 'image/jpeg' ||
          hospitalLogo.type === 'image/jpg' ||
          hospitalLogo.type === 'image/gif' ||
          hospitalLogo.type === 'image/png')
      ) {

        const reader = new FileReader();
        reader.onload = (e: any) => {
          this.hospitalLogoURL = e.target.result;
        };
        reader.readAsDataURL(hospitalLogo);

        this.hospitalViewForm.get('logo_large').setValue(hospitalLogo);
      } else {
        this.hospitalLogoFileSize = true;
        this.hospitalViewForm.get('logo_large').setValue('');
        this.notificationService.error(
          'Please select hospital logo image lessthan 2Mb and image type  JPG, GIF or PNG',
          'Med.Bot'
        );
      }
    } else {
      this.profileUpload = true;
      this.disabledUploadPhotoBtn = false;
      this.notificationService.error(
        'Please select hospital logo',
        'Med.Bot'
      );
    }
  }
  checkAvailability() {
    let url = this.hospitalViewForm.get('custom_url').value;
    if (url == '' || url == undefined || url == null) {
      this.notificationService.warning('Provide value for Login URL');
    } else {
      let login_url = this.hospitalViewForm.get('url_prefix').value +
        this.hospitalViewForm.get('custom_url').value +
        this.hospitalViewForm.get('url_domain').value;
      this.hospitalViewForm.get('login_url').setValue(login_url);
      this.platformService.checkAvailability(login_url).subscribe((result: any) => {
        console.log(result);
        this.urlAvailableStatus = result.message;
      }, (error) => {
        console.log(error);
      });
    }
  }
}
