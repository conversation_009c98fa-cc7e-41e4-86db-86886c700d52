<!--<p>emergency-contact works!</p>
<p>{{ contact_data['name'] }}</p>
<p>relationship {{ contact_data['relationship'] }}</p>
<p>phone {{ contact_data['phone_number'] }}</p>
-->
<!--Emergency Contact form-->
<div class="card">
    <div class="card-body">
        <form [formGroup]="emergencyForm">
            <h4 class="card-title"> Emergency Contact <i class="fa fa-edit" *ngIf="emergencyFormReadOnly" (click)="editContact()"></i></h4>
            <div class="row form-row">
                <div class="col-md-3">
                    <div class="form-group">
                        <input id="name-full" class="form-control" formControlName="name" type="text" name="name" placeholder="Full Name" maxlength="50" pattern="[a-zA-Z ]*" [readOnly]="emergencyFormReadOnly">
                        <div *ngIf="emergencyForm.controls.name.invalid && (emergencyForm.controls.name.dirty || emergencyForm.controls.name.touched)" class="alert alert-danger">{{alphabetsError}}</div>
                    </div>
                </div>
                <div class="col-md-3 ">
                    <div class="form-group">
                        <input id="mob-no" class="form-control" formControlName="phone_number" type="text" maxlength="10" minlength="10" name="mob_no" placeholder="Phone Number" maxlength="15" pattern="[0-9]*" [readOnly]="emergencyFormReadOnly">
                        <div *ngIf="emergencyForm.controls.phone_number.invalid && (emergencyForm.controls.phone_number.dirty || emergencyForm.controls.phone_number.touched)" class="alert alert-danger">{{numberError}}</div>
                    </div>
                </div>
                <div class="col-md-3 ">
                    <div class="form-group">
                    <ng-select id="select-Relation" [items]="relationship" [clearable]="false" [searchable]="false" bindLabel="relationship" formControlName="relationship" placeholder="relationship" [multiple]="false" [readonly]="emergencyFormReadOnly">
                    </ng-select>
                    </div>
                </div>
                <div class="col-md-3" *ngIf="!emergencyFormReadOnly">
                        <button id="save-constact-btn" class="btn btn-primary" (click)="saveContact()" [disabled]="!emergencyForm.valid" translate>Save</button>
                        <button class="btn btn-secondary cancel-btn" id="cancel-constact-btn" (click)="cancelContact()" [disabled]="emergencyForm.dirty=== true?false  :emergencyForm.valid=== true? false:true" translate>Cancel</button>
                </div>
            </div>
        </form>
    </div>
</div>
