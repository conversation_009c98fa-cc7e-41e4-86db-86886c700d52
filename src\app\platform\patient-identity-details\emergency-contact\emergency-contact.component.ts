import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common'
import { PlatformService } from '../../platform.service';
import { ToastrService } from 'ngx-toastr';
import { Router, ActivatedRoute, provideRoutes } from '@angular/router';
import { FormGroup, FormArray, FormBuilder, Validators, FormControl } from '@angular/forms';
import * as Settings from '../../../config/settings';

@Component({
  selector: 'app-emergency-contact',
  templateUrl: './emergency-contact.component.html',
  styleUrls: ['./emergency-contact.component.css']
  
})
export class EmergencyContactComponent implements OnInit {
  public postcontact = true;
  public contact_data = {};
  public formError:boolean;
  public emergencyForm: FormGroup;
  public emgDetails:any={
    name:null,
    phone_number:null,
    relationship:null,
    uuid:null
  };
  patient_uuid = '';
  emergencyFormReadOnly: boolean;
  contactData: any = [];
  specialCharacterError=Settings.specialCharacterError;
  alphabetsError=Settings.alphabetsError;
  alphanumericError=Settings.alphanumericError;
  numberError=Settings.numberError;
  relationship = [
    'Father',
    'Mother',
    'Husband',
    'Wife',
    'Sister',
    'Brother',
    'Friend',
    'Other',
  ];

  constructor(
    private location:Location,
    private platformService:PlatformService,
    private notificationService: ToastrService,
    private route: ActivatedRoute,

  ) { }

  ngOnInit(): void {

    this.route.params.subscribe((url) => {
      this.patient_uuid = url['uuid'];
      this.addEmergencyContactFormControl(null);
      this.getcontactData();      
    });

  }

  getcontactData(){
    this.platformService.getPatientContact(this.patient_uuid).subscribe(
      data =>{
//        this.doc_data = data;
       this.contact_data = data;
        console.log(this.contact_data);
        this.addEmergencyContactFormControl(this.contact_data)

      },
      error =>{
        console.log(error);
        const status = error['status'];
        if(status == 404){
          this.notificationService.error('Emergency Contact Not Found', 'Med.bot');
          }
        else{
          this.notificationService.error('Could Not Get Emergency Contact Information', 'Med.Bot');
        }

      } 
    );    
  }

  addEmergencyContactFormControl(data) {
    if (data === null) {
      this.emergencyFormReadOnly = false;
      this.postcontact = true;
      this.emergencyForm = new FormGroup({
        uuid: new FormControl(null),
        name: new FormControl('', [Validators.required,Validators.maxLength(50),Validators.pattern('[a-zA-Z ]*')]),
        phone_number: new FormControl('', [Validators.required,Validators.pattern('[0-9]*'),Validators.minLength(10),Validators.maxLength(15)]),
        relationship: new FormControl(null, Validators.required),
      });
    } else {
      this.emergencyFormReadOnly = true;
      this.postcontact = false;
      this.emergencyForm = new FormGroup({
        uuid: new FormControl(data.uuid, Validators.required),
        name: new FormControl(data.name, [Validators.required,Validators.maxLength(50),Validators.pattern('[a-zA-Z ]*')]),
        phone_number: new FormControl(data.phone_number, [Validators.required,Validators.pattern('[0-9]*'),Validators.minLength(10),Validators.maxLength(15)]),
        relationship: new FormControl(data.relationship, Validators.required),
      });
    }
  }

  saveContact() {
    this.emgDetails.name = this.emergencyForm.controls[`name`].value;
    this.emgDetails.phone_number = this.emergencyForm.controls[`phone_number`].value;
    this.emgDetails.relationship = this.emergencyForm.controls[`relationship`].value;
    this.emgDetails.uuid = this.patient_uuid;

    if (this.postcontact == true){
//        console.log('contact posted');
        this.platformService.postPatientContact(this.emgDetails).subscribe(
          (data) => {
            this.contactData = data;
            console.log(this.contactData);
            this.addEmergencyContactFormControl(this.contactData);
            if(this.emergencyForm.get('uuid').invalid){
              this.notificationService.success(
                'Emergency Contact Added Successfully',
                'Med.Bot'
              );
            }
    
          },
          (error) => {
            const status = error['status'];
            if(status == 400){
              this.notificationService.error('Emergency Contact Not Added', 'Med.Bot');
              }
            else{
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          }
        );
      }else{
      this.platformService.savePatientContact(this.patient_uuid,this.emgDetails).subscribe(
      (data) => {
        this.contactData = data;
        console.log(this.contactData);
        this.addEmergencyContactFormControl(this.contactData);
        if(this.emergencyForm.get('uuid').valid){
          this.notificationService.success(
            'Emergency Contact Updated Successfully',
            'Med.Bot'
          );
        }

      },
      (error) => {
        const status = error['status'];
        if(status == 400){
          this.notificationService.error('Emergency Contact Not Added', 'Med.Bot');
          }
        else{
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
    }
  }

  editContact() {
    this.emergencyFormReadOnly = false;
  }
  cancelContact() {
//    this.addressForm.get('country').disable();
    this.addEmergencyContactFormControl(this.contact_data);
  }


}
