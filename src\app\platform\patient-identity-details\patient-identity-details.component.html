<!--BreadCrumbs-->
<div class="breadcrumb-bar">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-12 col-12">
                <nav aria-label="breadcrumb" class="page-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);">{{'Admin'|translate}}</a></li>
                        <li #listHeader class="breadcrumb-item active" aria-current="page">{{'Patient Profile'
                            |translate}}</li>
                    </ol>
                </nav>
                <h2 #header class="breadcrumb-title">{{'Patient Profile' |translate}}</h2>
            </div>
        </div>
    </div>
</div>
<!--BreadCrumbs Ends-->
<div class="content card">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 col-lg-12 col-xl-12">
                <h5 (click)="goBack()" class="mb-4 back-head ms"><i class="fas fa-chevron-circle-left"></i>Back</h5>
                <div class="card">
                    <div class="card-body">
                        <!-- <h4 class="card-title" translate>Profile Picture</h4> -->
                        <div class="row form-row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="change-avatar">
                                        <div class="profile-img">
                                            <img [src]="doctorProfilePictureUrl" alt="User Image">
                                        </div>
                                        <h6 class=alignright>Created at {{ patient_created }}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h4 class="card-title" translate>Personal Profile <i *ngIf="disabled" (click)="editProfile()"
                                class="fa fa-edit"></i></h4>
                        <div class="form-group " *ngIf="formError">
                            <!-- <label style="color: orangered;" >Form Error</label> -->
                            <div class="card">
                                <ng-container *ngFor="let err of errorValue">
                                    <p class="text-danger">&nbsp;{{err.value}}</p>
                                </ng-container>
                            </div>
                        </div>
                        <form [formGroup]="personalProfileForm">
                            <div class="row form-row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Full Name<span class="text-danger">*</span></label>
                                        <input id="fullname" class="form-control" type="text" name="username"
                                            formControlName="username" maxlength="25" pattern="[a-zA-Z ]*" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Email<span class="text-danger">*</span></label>
                                        <input id="email" type="email" name="email" class="form-control"
                                            formControlName="email" maxlength="50" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>First Name</label>
                                        <input id="firstname" type="text" class="form-control" name="first_name"
                                            formControlName="first_name" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.first_name.invalid && (personalProfileForm.controls.first_name.dirty || personalProfileForm.controls.first_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Middle Name</label>
                                        <input id="middlename" type="text" class="form-control" name="middle_name"
                                            formControlName="middle_name" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.middle_name.invalid && (personalProfileForm.controls.middle_name.dirty || personalProfileForm.controls.middle_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Last Name</label>
                                        <input id="lastname" type="text" class="form-control" name="last_name"
                                            formControlName="last_name" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.last_name.invalid && (personalProfileForm.controls.last_name.dirty || personalProfileForm.controls.last_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Patient Unique Id</label>
                                        <input id="patientUniqueId" type="text" class="form-control"
                                            name="patientUniqueId" formControlName="patientUniqueId" maxlength="25"
                                            autocomplete="off" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Father Name</label>
                                        <input id="fathername" type="text" class="form-control" name="father_name"
                                            formControlName="father_name" maxlength="25" pattern="[a-zA-Z]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.father_name.invalid && (personalProfileForm.controls.father_name.dirty || personalProfileForm.controls.father_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Mother Name</label>
                                        <input id="mothername" type="text" class="form-control" name="mother_name"
                                            formControlName="mother_name" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="disabled">
                                        <div *ngIf="personalProfileForm.controls.mother_name.invalid && (personalProfileForm.controls.mother_name.dirty || personalProfileForm.controls.mother_name.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Husband/Wife Name</label>
                                        <input id="husbandName" type="text" class="form-control"
                                            formControlName="husbandName" name="husbandName" maxlength="15"
                                            [readonly]="disabled">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Guardian Name</label>
                                        <input id="guardianName" type="text" class="form-control"
                                            formControlName="guardianName" name="guardianName" maxlength="15"
                                            [readonly]="disabled">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Phone Number<span class="text-danger">*</span></label>
                                        <input id="phone" type="text" class="form-control" formControlName="phone"
                                            pattern="[0-9]*" name="phone" maxlength="15" readonly>
                                        <div *ngIf="personalProfileForm.controls.phone.invalid && (personalProfileForm.controls.phone.dirty || personalProfileForm.controls.phone.touched)"
                                            class="alert alert-danger">{{numberError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Gender<span class="text-danger">*</span></label>
                                        <select class="form-control" name="gender" id="gender" formControlName="gender"
                                            disabled>
                                            <option *ngFor="let data of gender" [value]="data.value ">{{data.name}}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-0 ">
                                        <label translate>Date of Birth<span class="text-danger">*</span></label>
                                        <input [maxDate]="maxDate" [minDate]="minDate" placeholder="DOB"
                                            onkeydown="return false" class="form-control"
                                            formControlName="date_of_birth" [readonly]="disabled" bsDatepicker
                                            [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }"
                                            [ngClass]="{'bs-datepicker':disabled==true}">
                                        <input class="form-control" formControlName="date_of_birth"
                                            *ngIf="disabled==true" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>ABHA ID</label>
                                        <a href="https://abha.abdm.gov.in/abha/v3/register" target="_blank" *ngIf="!disabled && createPrm()">Create ABHA</a>
                                        <input id="abha_id" type="text" class="form-control" formControlName="abha_id"
                                            pattern="[0-9]*" name="abha_id" maxlength="14" *ngIf="editPrm==true"
                                            [readonly]="disabled">
                                        <input id="abha_id" type="text" class="form-control" formControlName="abha_id"
                                            pattern="[0-9]*" name="abha_id" maxlength="14" readonly
                                            *ngIf="editPrm==false">
                                        <div *ngIf="personalProfileForm.controls.abha_id.invalid && (personalProfileForm.controls.abha_id.dirty || personalProfileForm.controls.abha_id.touched)"
                                            class="alert alert-danger">{{numberError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-0">
                                        <label translate>AADHAAR ID <span class="text-danger">*</span></label>
                                        <input id="aadhar_id" type="text" class="form-control"
                                            formControlName="aadhar_id" pattern="[0-9]*" name="aadhar_id" maxlength="12"
                                            [readonly]="disabled" *ngIf="editPrm==true">
                                            <input id="aadhar_id" type="text" class="form-control"
                                            formControlName="aadhar_id" pattern="[0-9]*" name="aadhar_id" maxlength="12"
                                            readonly *ngIf="editPrm==false">
                                        <div *ngIf="personalProfileForm.controls.aadhar_id.invalid && (personalProfileForm.controls.aadhar_id.dirty || personalProfileForm.controls.aadhar_id.touched)"
                                            class="alert alert-danger">{{numberError}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group float-right">
                                <button *ngIf="!disabled" type="button" id="save-btn" id="per-prof-btn"
                                    class="btn btn-primary" translate (click)="onSubmit()"
                                    [disabled]="!personalProfileForm.valid">Save</button>
                                <button *ngIf="!disabled" type="button" id="cancel-btn" (click)="cancelUpdate()"
                                    class="btn btn-secondary cancel-btn" translate>Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
                <!-- Address Information -->
                <div class=card>
                    <div class=card-body>
                        <h4 class="card-title" translate>Address Detail <i *ngIf="addrDisabled" (click)="editAddress()"
                                class="fa fa-edit"></i></h4>
                        <form [formGroup]="addressForm">
                            <div class="row form-row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Line 1<span class="text-danger">*</span></label>
                                        <input id="line1" class="form-control" type="text" name="line_1"
                                            formControlName="line_1" maxlength="50" [readonly]="addrDisabled">
                                        <div *ngIf="addressForm.controls.line_1.invalid && (addressForm.controls.line_1.dirty || addressForm.controls.line_1.touched)"
                                            class="alert alert-danger">{{specialCharacterError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Line 2<span class="text-danger">*</span></label>
                                        <input id="line2" type="text" name="line_2" class="form-control"
                                            formControlName="line_2" maxlength="25" [readonly]="addrDisabled">
                                        <div *ngIf="addressForm.controls.line_2.invalid && (addressForm.controls.line_2.dirty || addressForm.controls.line_2.touched)"
                                            class="alert alert-danger">{{specialCharacterError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Taluk<span class="text-danger">*</span></label>
                                        <input id="taluk" type="text" class="form-control" name="taluk"
                                            formControlName="taluk" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="addrDisabled">
                                        <div *ngIf="addressForm.controls.taluk.invalid && (addressForm.controls.taluk.dirty || addressForm.controls.taluk.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>District<span class="text-danger">*</span></label>
                                        <input id="district" type="text" class="form-control" name="district"
                                            formControlName="district" maxlength="25" pattern="[a-zA-Z ]*"
                                            autocomplete="off" [readonly]="addrDisabled">
                                        <div *ngIf="addressForm.controls.district.invalid && (addressForm.controls.district.dirty || addressForm.controls.district.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>City/Town/Village<span class="text-danger">*</span></label>
                                        <input id="citytownvillage" type="text" class="form-control"
                                            name="city_town_village" formControlName="city_town_village" maxlength="25"
                                            pattern="[a-zA-Z ]*" autocomplete="off" [readonly]="addrDisabled">
                                        <div *ngIf="addressForm.controls.city_town_village.invalid && (addressForm.controls.city_town_village.dirty || addressForm.controls.city_town_village.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>State<span class="text-danger">*</span></label>
                                        <input id="state" type="text" class="form-control" formControlName="state"
                                            pattern="[a-zA-Z ]*" name="state" maxlength="25" [readonly]="addrDisabled">
                                        <div *ngIf="addressForm.controls.state.invalid && (addressForm.controls.state.dirty || addressForm.controls.state.touched)"
                                            class="alert alert-danger">{{alphabetsError}}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Country<span class="text-danger">*</span></label>
                                        <ng-select id="country" formControlName="country" [items]="countryList"
                                            [clearable]="false" [searchable]="true" bindLabel="Name" bindValue="Name"
                                            placeholder="{{'Select Country' | translate}}" [readonly]="addrDisabled"
                                            multiple>
                                        </ng-select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label translate>Postal Code<span class="text-danger">*</span></label>
                                        <input id="postalcode" type="text" class="form-control"
                                            formControlName="postal_code" pattern="[0-9]*" name="postal_code"
                                            maxlength="10" [readonly]="addrDisabled">
                                        <div *ngIf="addressForm.controls.postal_code.invalid && (addressForm.controls.postal_code.dirty || addressForm.controls.postal_code.touched)"
                                            class="alert alert-danger">{{numberError}}</div>

                                    </div>
                                </div>
                            </div>
                            <div class="form-group float-right">
                                <!-- <button *ngIf="!addrDisabled" type="button" id="save-btn" id="per-prof-btn"
                                    class="btn btn-primary" translate (click)="onAddressSubmit()"
                                    [disabled]="addressForm.dirty=== true?false : addressForm.valid=== true?false:true">Save</button>
                                <button *ngIf="!addrDisabled" type="button" id="cancel-btn" (click)="cancelAddress()"
                                    class="btn btn-secondary cancel-btn" translate
                                    [disabled]="addressForm.dirty=== true?false  : addressForm.valid=== true?false:true">Cancel</button> -->

                                <button *ngIf="!addrDisabled" id="cancel-btn" class="btn btn-secondary cancel-btn"
                                    (click)="cancelAddress()" type="button"
                                    [disabled]="addressForm.dirty=== true?false  :addressForm.valid=== true? false:true"
                                    translate>Cancel</button>
                                <button *ngIf="!addrDisabled" id="save-btn" class="btn btn-primary"
                                    [disabled]="!addressForm.valid" (click)="onAddressSubmit()" translate>Save</button>
                            </div>
                        </form>
                    </div>
                </div>
                <!-- emergency contact information-->
                <app-emergency-contact></app-emergency-contact>
            </div>
        </div>
    </div>
</div>