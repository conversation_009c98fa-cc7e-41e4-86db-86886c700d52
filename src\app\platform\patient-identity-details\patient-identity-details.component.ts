import { Component, OnInit } from '@angular/core';
import { Location, DatePipe } from '@angular/common'
import { PlatformService } from './../platform.service';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import * as moment from 'moment';
import { ActivatedRoute } from '@angular/router';
import * as Settings from '../../config/settings';
import { DoctorService } from 'src/app/doctor/doctor.service';
@Component({
  selector: 'app-patient-identity-details',
  templateUrl: './patient-identity-details.component.html',
  styleUrls: ['./patient-identity-details.component.css'],
  providers: [DatePipe]
})
export class PatientIdentityDetailsComponent implements OnInit {
  public disabled = true;
  public postAddr = true;
  public addrDisabled = true;
  public doc_data = {};
  public doctorProfilePictureUrl: string;
  public patient_id = '';
  public user_data = {};
  public address_data = {};
  public profileUpload = true;
  public disabledUploadPhotoBtn = false;
  public personalProfileForm: FormGroup;
  public addressForm: FormGroup;
  public formError: boolean;
  public maxDate: Date;
  public user_create = {};
  public patient_created = {};
  public minDate: Date;
  public countryList: any[];
  errorValue: any = [];
  public userDetails: any = {
    username: null,
    email: null,
    phone: null,
    gender: null,
    first_name: null,
    middle_name: null,
    last_name: null,
    date_of_birth: null,
    patientUniqueId: null,
    father_name: null,
    husband_name:null,
    guardian_name:null,
    mother_name: null,
    uuid: null,
    abha_id:null,
    aadhar_id:null,
  };
  public AddressDetails: any = {
    line_1: null,
    line_2: null,
    taluk: null,
    district: null,
    city_town_village: null,
    state: null,
    country: null,
    postal_code: null,
    address_type: "Home",
    uuid: null
  }
  gender = [
    { value: '', name: 'Select' },
    { value: 'Male', name: 'Male' },
    { value: 'Female', name: 'Female' },
    { value: 'Prefer not to answer', name: 'Prefer not to answer' },
  ];
  specialCharacterError = Settings.specialCharacterError;
  alphabetsError = Settings.alphabetsError;
  alphanumericError = Settings.alphanumericError;
  numberError = Settings.numberError;
  userType: string;
  editPrm: boolean = false;

  constructor(
    private location: Location,
    private platformService: PlatformService,
    private notificationService: ToastrService,
    private route: ActivatedRoute,
    public datepipe: DatePipe,
    private doctorService: DoctorService,
  ) { }

  ngOnInit(): void {
    this.maxDate = new Date();
    this.minDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate() - 7672);
    this.minDate.setDate(this.minDate.getDate() - 36500);
    this.doctorProfilePictureUrl = '../../../../assets/img/doctors/doctor-thumb-02.png';
    this.addProfileFromControl(null);
    this.addAddressFromControl(null);
    this.doctorService.getCountryDetail().subscribe(
      (data) => {
        this.countryList = Object.values(data);
      },
      (error) => {
        console.log(error);
      }
    );
    this.route.params.subscribe(
      url => {
        this.patient_id = url['uuid'];
        this.getUserData();
        this.getAddrData();
        this.getCreatedDate();
      }
    );
    this.userType = localStorage.getItem('user_type');
  }

  getCreatedDate() {
    this.platformService.getPatientCreated(this.patient_id).subscribe(
      data => {
        if (data['terms_conditions_item_pending'] === null) {
          this.user_create = data['terms_conditions_accepted'];
          this.patient_created = this.datepipe.transform(this.user_create[0]['created_at'], 'dd-MMM-yyyy');
          console.log(this.patient_created);
        }
        else {
          this.user_create = data['terms_conditions_item_pending'];
          this.patient_created = this.datepipe.transform(this.user_create['created_at'], 'dd-MMM-yyyy');
          console.log(this.patient_created);
        }
      },
      error => {
        console.log(error);
        const status = error['status'];
        if (status == 404) {
          this.notificationService.error('Terms&Conditions Not Found', 'Med.Bot');
        }
        else {
          this.notificationService.error('Could Not Get Terms&Conditions Information', 'Med.Bot');
        }
      }
    )
  }

  getUserData() {
    this.platformService.getPatientDetails(this.patient_id).subscribe(
      data => {
        this.user_data = data;
        this.addProfileFromControl(this.user_data);
        if (this.user_data['profile_picture'] !== null) {
          this.doctorProfilePictureUrl = this.user_data['profile_picture'];
        }
      },
      error => {
        console.log(error);
        const status = error['status'];
        if (status == 404) {
          this.notificationService.error('Patient Information Not Found', 'Med.Bot');
        }
        else {
          this.notificationService.error('Could Not Get Patient Information', 'Med.Bot');
        }
      }
    );
  }


  getAddrData() {
    this.platformService.getPatientAddrDetail(this.patient_id).subscribe(
      data => {
        this.address_data = data;
        //        console.log(this.address_data['country']);
        this.addAddressFromControl(this.address_data);
      },
      error => {
        console.log(error);
        const status = error['status'];
        if (status == 404) {
          this.notificationService.error('Address Not Found', 'Med.Bot');
        }
        else {
          this.notificationService.error('Could Not Get Address Information', 'Med.Bot');
        }
      }
    );
  }

  goBack() {
    this.location.back();
  }

  editProfile() {
    this.disabled = false;
    this.personalProfileForm.get('gender').enable();
  }
  cancelUpdate() {
    this.personalProfileForm.get('gender').disable();
    this.disabled = true;
    this.addProfileFromControl(this.user_data);
  }

  editAddress() {
    this.addrDisabled = false;
    //    this.addressForm.get('country').enable();
  }

  cancelAddress() {
    //    this.addressForm.get('country').disable();
    this.addrDisabled = true;
    this.addAddressFromControl(this.address_data);
  }

  onAddressSubmit() {
    this.AddressDetails.line_1 = this.addressForm.controls[`line_1`].value;
    this.AddressDetails.line_2 = this.addressForm.controls[`line_2`].value;
    this.AddressDetails.taluk = this.addressForm.controls[`taluk`].value;
    this.AddressDetails.district = this.addressForm.controls[`district`].value;
    this.AddressDetails.city_town_village = this.addressForm.controls[`city_town_village`].value;
    this.AddressDetails.state = this.addressForm.controls[`state`].value;
    this.AddressDetails.country = this.addressForm.controls[`country`].value;
    this.AddressDetails.postal_code = this.addressForm.controls[`postal_code`].value;
    this.AddressDetails.uuid = this.patient_id;

    if (this.postAddr == true) {
      console.log(this.AddressDetails);
      this.platformService.postPatientAddrDetail(this.AddressDetails).subscribe(
        (data) => {
          this.notificationService.success('Address Added Successfully', 'Med.Bot');
          this.addrDisabled = true;
          console.log('address posted');
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.formError = true;
            if (error.error.error_details.validation_errors) {
              let messages = '';
              for (let i = 0; i < Object.keys(error.error.error_details.validation_errors).length; i++) {
                const key = Object.keys(error.error.error_details.validation_errors)[i];
                messages = messages + ' ' + key + ': ' + error.error.error_details.validation_errors[key];
              }

              this.notificationService.error(
                `${messages}`,
                'Med.Bot'
              );
            } else {
              // this.notificationService.error('Address Not Added', 'Med.Bot');
              this.notificationService.error(
                `${error.error['error_message']}`,
                'Med.Bot'
              );
            }
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    } else {
      this.platformService.patchPatientAddrDetail(this.AddressDetails).subscribe(
        (data) => {
          this.notificationService.success('Address Updated Successfully', 'Med.Bot');
          this.addrDisabled = true;
          this.address_data = data;
          this.addAddressFromControl(this.address_data);
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.formError = true
            this.notificationService.error('Address Not Updated', 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
  }

  onSubmit() {
    this.errorValue=[];
    this.userDetails.username = this.personalProfileForm.controls[`username`].value;
    this.userDetails.email = this.personalProfileForm.controls[`email`].value;
    this.userDetails.phone = this.personalProfileForm.controls[`phone`].value;
    // this.userDetails.patientUniqueId = this.personalProfileForm.controls[`patientUniqueId`].value;
    this.userDetails.first_name = this.personalProfileForm.controls[`first_name`].value;
    this.userDetails.middle_name = this.personalProfileForm.controls[`middle_name`].value;
    this.userDetails.last_name = this.personalProfileForm.controls[`last_name`].value;
    this.userDetails.father_name = this.personalProfileForm.controls[`father_name`].value;
    this.userDetails.mother_name = this.personalProfileForm.controls[`mother_name`].value;
    this.userDetails.husband_name = this.personalProfileForm.controls[`husbandName`].value;
    this.userDetails.guardian_name = this.personalProfileForm.controls[`guardianName`].value;
    this.userDetails.gender = this.personalProfileForm.controls[`gender`].value;
    this.userDetails.abha_id = this.personalProfileForm.controls[`abha_id`].value;
    this.userDetails.aadhar_id = this.personalProfileForm.controls[`aadhar_id`].value;
    const dob = this.personalProfileForm.controls[`date_of_birth`].value;
    this.userDetails.date_of_birth = moment(dob, 'DD-MM-YYYY').format('YYYY-MM-DD');
    this.userDetails.uuid = this.patient_id;

    this.platformService.updateUserDetails(this.patient_id, this.userDetails).subscribe(
      (data) => {
        this.notificationService.success('Profile Updated', 'Med.Bot');
        this.disabled = true;
        this.user_data = data;
        this.addProfileFromControl(this.user_data);
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.formError = true
          const err = error['error']['error_details']['validation_errors'];
          if (err) {
            const gender = err['gender'];
            const dob = err['date_of_birth'];
            if (gender && dob) {
              ;
              const genderError = 'Gender : ' + gender[0];
              const dobError = 'DOB : ' + dob[0];
              this.notificationService.error(
                `${genderError} ${dobError}`,
                'Med.Bot'
              );
              this.errorValue.push({ value: genderError }, { value: dobError });
            } else if (gender) {
              const genderError = 'Gender : ' + gender[0];
              this.notificationService.error(`${genderError}`, 'Med.Bot');
              this.errorValue.push({ value: genderError });
            } else if (dob) {
              const dobError = 'DOB : ' + dob[0];
              this.notificationService.error(` ${dobError}`, 'Med.Bot');
              this.errorValue.push({ value: dobError });
            } else {
              let message = Object.values(err)[0][0] ?Object.keys(err)+': '+ Object.values(err)[0][0] : 'Validation Error';
              this.notificationService.error(message, 'Med.Bot');
            }
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  addAddressFromControl(data) {
    if (data === null) {
      this.addrDisabled = false;
      this.postAddr = true;
      this.addressForm = new FormGroup({
        line_1: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        line_2: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        city_town_village: new FormControl(
          null,
          [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')],
        ),
        district: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        taluk: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        state: new FormControl(null, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        country: new FormControl(null, [Validators.required]),
        postal_code: new FormControl(null, [Validators.required, Validators.maxLength(10), Validators.pattern('[0-9 ]*')]),
      });
    } else {
      this.addrDisabled = true;
      this.postAddr = false;
      this.addressForm = new FormGroup({
        line_1: new FormControl(data.line_1, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        line_2: new FormControl(data.line_2, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z0-9,:/ ]*')]),
        city_town_village: new FormControl(
          data.city_town_village,
          [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')],
        ),
        district: new FormControl(data.district, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        taluk: new FormControl(data.taluk, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        state: new FormControl(data.state, [Validators.required, Validators.maxLength(50), Validators.pattern('[a-zA-Z ]*')]),
        country: new FormControl(data.country, [Validators.required]),
        postal_code: new FormControl(data.postal_code, [Validators.required, Validators.maxLength(10), Validators.pattern('[0-9]*')]),
      });
    }
  }

  addProfileFromControl(data) {
    if (data === null) {
      this.disabled = false;
      this.personalProfileForm = new FormGroup({
        username: new FormControl('', [
          Validators.required,
          Validators.maxLength(50),
        ]),
        email: new FormControl('', [Validators.required, Validators.email]),
        first_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        middle_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        last_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        patientUniqueId: new FormControl(''),
        father_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        mother_name: new FormControl('', [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        husbandName: new FormControl(''),
        guardianName: new FormControl(''),
        abha_id: new FormControl('', [Validators.maxLength(14), Validators.pattern('[0-9 ]*')]),
        aadhar_id: new FormControl('', [Validators.maxLength(12), Validators.pattern('[0-9 ]*')]),

        phone: new FormControl('', [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl('', [
          Validators.required,
          Validators.maxLength(10),
        ]),
        date_of_birth: new FormControl('', [
          Validators.required,
          Validators.maxLength(20),
        ]),
      });
    } else {
      this.disabled = true;
      this.personalProfileForm = new FormGroup({
        username: new FormControl(data.username, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        email: new FormControl(data.email, [
          Validators.required,
          Validators.email,
        ]),
        first_name: new FormControl(data.first_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')
        ]),
        middle_name: new FormControl(data.middle_name, [
          Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')
        ]),
        last_name: new FormControl(data.last_name, [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        patientUniqueId: new FormControl(data.patient_ha_id),
        father_name: new FormControl(data.father_name, [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        mother_name: new FormControl(data.mother_name, [Validators.maxLength(25), Validators.pattern('[a-zA-Z]*')]),
        husbandName: new FormControl(data.husband_name),
        guardianName: new FormControl(data.guardian_name),
        abha_id: new FormControl(data.abha_id, [Validators.maxLength(14), Validators.pattern('[0-9 ]*')]),
        aadhar_id:new FormControl(data.aadhar_id?data.aadhar_id:''),
        phone: new FormControl(data.phone, [
          Validators.required,
          Validators.maxLength(15),
        ]),
        gender: new FormControl(data.gender, [
          Validators.required,
          Validators.maxLength(25),
        ]),
        date_of_birth: new FormControl(
          moment(data.date_of_birth).format('DD-MM-YYYY'),
          [Validators.required, Validators.maxLength(25)]
        ),
      });
      this.personalProfileForm.get('gender').disable();
    }
  }
  createPrm(){
    if(this.userType=='Patient'|| this.userType =='HospitalAdmin'||this.userType=='Center'){
      this.editPrm =true;
      return true;
    }
    else{
      this.editPrm =false;
      return false;
    }
  }
}
