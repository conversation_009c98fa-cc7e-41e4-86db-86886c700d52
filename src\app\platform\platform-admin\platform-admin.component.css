a.link:hover {
    cursor: pointer;
}

.modalSize {
    max-width: 1200px;
    max-height: 700px;
}

.btn-primary {
    border-radius: 5px;
}

#no-data {
    pointer-events: none;
}

.banner {
    background-color: #13A89E;
}

.mt-2 {
    color: #fff;
}

.add-hsp {
    background-color: #20c0f3 !important;
    margin-top: 20px;
    margin-left: 85px;
}

h5.back-head,
.fa-edit {
    color: #20c0f3;
    cursor: pointer;
}

.text-success {
    color: #20c0f3 !important;
}

.hsptl-form {
    margin: 25px;
}

.hsp-details {
    margin-bottom: 30px;
}

.hsp-details.row label {
    margin-bottom: 0px;
}

label {
    margin-bottom: 0px;
}

input {
    margin-bottom: 10px;
}

.hsp-btn {
    float: right;
    margin: 5px;
    margin-top: 10px;
}

.cnt-btn {
    margin: 3px;
    margin-top: 25px;
}

.desc {
    height: 100px;
    resize: none;
}

.mt-4 {
    margin-left: 20px;
}

.pt-3 {
    margin-top: 0px;
}

i {
    font-size: 23px;
    margin-bottom: 2px;
}

.btn {
    margin: 5px;
}

.disabled-pagination {
    color: darkgray !important;
    pointer-events: none !important;
}

.pagination-zindex {
    position: relative;
    z-index: 10;
}