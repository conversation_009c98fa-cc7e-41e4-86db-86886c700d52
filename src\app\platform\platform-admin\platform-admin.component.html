<div class="breadcrumb-bar banner">
  <div class="container-fluid">
    <div class="row align-items-center">
      <div class="col-md-12 col-12">
        <nav aria-label="breadcrumb" class="page-breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="javascript:void(0);">{{ "Admin" | translate }}</a>
            </li>
            <li #listHeader class="breadcrumb-item active" aria-current="page">
              {{ "Dashboard" | translate }}
            </li>
          </ol>
        </nav>
        <h2 #header class="breadcrumb-title">
          {{ "Admin DashBoard" | translate }}
        </h2>
      </div>
    </div>
  </div>
</div>
<div class="container-fluid">
  <!-- <h3> Total consultation {{ adminConsult }}, Total revenue {{ adminTotalEarn }} Rs, Med.bot revenue {{ adminMedbotEarn }} Rs</h3> -->
  <div class="tab-content pt-0">
    <table class="table table-hover table-center mb-0">
      <thead>
        <tr>
          <th>Med.bot Report</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>1</td>
          <td>Total Consultation</td>
          <td>{{ adminConsult }}</td>
        </tr>
        <tr>
          <td>2</td>
          <td>Total Revenue</td>
          <td>{{ adminTotalEarn }}</td>
        </tr>
        <tr>
          <td>3</td>
          <td>Med.bot revenue</td>
          <td>{{ adminMedbotEarn }}</td>
        </tr>
      </tbody>
    </table>
    <button class="btn btn-info btn-lg h-btn mb-3" (click)="report()">
      <i class="fas fa-user-injured"></i> Reports
    </button>
    <div class="appointment-tab">
      <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
        <li class="nav-item">
          <a class="nav-link active" href="#doctor-test" data-toggle="tab">Doctor List
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#hospital-test" data-toggle="tab">Hospital List
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#patient-test" data-toggle="tab">Patient List
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#bank-test" data-toggle="tab">Bank List
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#Department-test" data-toggle="tab">Department List
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#consultationList-test" data-toggle="tab">Consultation List
          </a>
        </li>
      </ul>
      <div class="tab-content mb-4">

        <div class="tab-pane show active" id="doctor-test">
          <div class="card">
            <div class="card-body">
              <div *ngIf="!addingHospital" class="col-md-12 col-lg-12 col-xl-12">
                <div class="tab-content pt-0">
                  <div class="text-left mt-4">
                    <h4 class="text-success">Doctors List</h4>
                  </div>
                  <div class="appointment-tab">
                    <!-- Doctor Tab -->
                    <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
                      <li class="nav-item">
                        <a class="nav-link active" href="#approved-doctor" data-toggle="tab">Approved
                        </a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link" href="#pending-doctor" data-toggle="tab">Pending
                        </a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link" href="#rejected-doctor" data-toggle="tab">Rejected
                        </a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link" href="#doctor-list" data-toggle="tab">All</a>
                      </li>
                    </ul>
                    <!-- /Doctor Tab -->

                    <div class="tab-content">
                      <!-- approved Tab -->
                      <div class="tab-pane show active" id="approved-doctor">
                        <!-- <h4>Number of Approved profiles - {{approvedDoctorCount }}</h4> -->
                        <h4>Total Approved Profiles - {{ approvedDoctorCount }}</h4>
                        <div *ngIf="!aprovedDoctorLoading">
                          <div class="col-md-12 float-right">
                            <div class="float-right mt-3">
                              <nav aria-label="Page navigation example" *ngIf="this.approvedDoctorTotalPage > 1">
                                <ul class="pagination">
                                  <li class="page-item" (click)="approvedDoctorFirstPage()"
                                    [ngClass]="{ 'disabled-pagination': approvedDoctorCurrentPage === 1 }">
                                    <a class="page-link">&lt;&lt;</a>
                                  </li>
                                  <li class="page-item" (click)="approvedDoctorPreviousPage()"
                                    [ngClass]="{ 'disabled-pagination': approvedDoctorCurrentPage === 1 }">
                                    <a class="page-link">&lt;</a>
                                  </li>
                                  <li class="page-item">
                                    <a class="page-link">page &nbsp;{{ approvedDoctorCurrentPage }} &nbsp;of &nbsp; {{
                                      approvedDoctorTotalPage }}</a>
                                  </li>
                                  <li class="page-item" (click)="approvedDoctorNextPage()" [ngClass]="{
                                        'disabled-pagination': approvedDoctorCurrentPage === approvedDoctorTotalPage
                                      }">
                                    <a class="page-link">&gt;</a>
                                  </li>
                                  <li class="page-item" (click)="approvedDoctorLastPage()" [ngClass]="{
                                        'disabled-pagination': approvedDoctorCurrentPage === approvedDoctorTotalPage
                                      }">
                                    <a class="page-link">&gt;&gt;</a>
                                  </li>
                                </ul>
                              </nav>
                            </div>
                          </div>
                          <div class="card card-table mb-0">
                            <div class="card-body">
                              <div class="table-responsive" *ngIf="approvedDoctor.length > 0">
                                <table class="table table-hover table-center mb-0">
                                  <thead>
                                    <tr>
                                      <th>#</th>
                                      <th>Name</th>
                                      <th>Qualification</th>
                                      <th>Registration</th>
                                      <th>Action</th>
                                      <th>Consultation</th>
                                      <th>Bank</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr *ngFor="let data of approvedDoctor; let i = index">
                                      <th scope="row">{{ approvedDoctorSerialNumber+ i+ 1 }}</th>
                                      <td>
                                        <a class="text-primary link" (click)="onIdentity(data.uuid)">{{
                                          data.user.username
                                          }}</a>
                                      </td>
                                      <td *ngIf="data.qualifications.length > 0">
                                        <div class="text-wrap">
                                          <a *ngFor="
                                              let education of data.qualifications;
                                              let len = index
                                            " class="text-primary ">&nbsp;{{
                                            education.name }}&nbsp;{{
                                            data.qualifications.length > 1
                                            ? data.qualifications.length - 1 === len
                                            ? ""
                                            : ","
                                            : ""
                                            }}</a>
                                        </div>
                                      </td>
                                      <td *ngIf="data.qualifications.length === 0">
                                        need to add
                                      </td>
                                      <td>
                                        <ng-container *ngFor="let reg of data.registrations">
                                          <a class="text-primary " >{{ reg.council
                                            }}</a>
                                        </ng-container>
                                      </td>
                                      <td>
                                        <button class="btn-primary btn-md mr-2" (click)="onIdentity(data.uuid)">
                                          View
                                        </button>
                                      </td>
                                      <td>
                                        <button class="btn-primary btn-md mr-2"
                                          (click)="onConsultationSummary(data.uuid)">
                                          Consultation
                                        </button>
                                      </td>
                                      <td>
                                        <button class="btn-primary btn-md mr-2" (click)="BankDetail(data.uuid)">
                                          Detail
                                        </button>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                              <div class="text-center mb-2 p-2 mt-2">
                                <span class="appointmentList-no-data" *ngIf="approvedDoctor.length === 0"
                                  style="color: orangered">No approved list doctor</span>
                              </div>
                            </div>
                          </div>
                          <div class="float-right mt-3">
                            <nav aria-label="Page navigation example" *ngIf="this.approvedDoctorTotalPage > 1">
                              <ul class="pagination">
                                <li class="page-item" (click)="approvedDoctorFirstPage()"
                                  [ngClass]="{ 'disabled-pagination': approvedDoctorCurrentPage === 1 }">
                                  <a class="page-link">&lt;&lt;</a>
                                </li>
                                <li class="page-item" (click)="approvedDoctorPreviousPage()"
                                  [ngClass]="{ 'disabled-pagination': approvedDoctorCurrentPage === 1 }">
                                  <a class="page-link">&lt;</a>
                                </li>
                                <li class="page-item">
                                  <a class="page-link">page &nbsp;{{ approvedDoctorCurrentPage }} &nbsp;of &nbsp; {{
                                    approvedDoctorTotalPage }}</a>
                                </li>
                                <li class="page-item" (click)="approvedDoctorNextPage()" [ngClass]="{
                                    'disabled-pagination': approvedDoctorCurrentPage === approvedDoctorTotalPage
                                  }">
                                  <a class="page-link">&gt;</a>
                                </li>
                                <li class="page-item" (click)="approvedDoctorLastPage()" [ngClass]="{
                                    'disabled-pagination': approvedDoctorCurrentPage === approvedDoctorTotalPage
                                  }">
                                  <a class="page-link">&gt;&gt;</a>
                                </li>
                              </ul>
                            </nav>
                          </div>
                        </div>
                        <div class="centered" *ngIf="aprovedDoctorLoading">
                          <app-loading-spinner></app-loading-spinner>
                        </div>
                      </div>
                      <!-- /approved  Tab -->
                      <!-- doctor list start -->
                      <div class="tab-pane" id="doctor-list">
                        <h4>Total Doctors - {{ doctorCount }}</h4>
                        <div *ngIf="!isLoading">
                          <div class="col-md-12 float-right">
                            <div class="float-right ">
                              <nav aria-label="Page navigation example" *ngIf="this.totalPage > 1">
                                <ul class="pagination">
                                  <li class="page-item" (click)="firstPageList()"
                                    [ngClass]="{ 'disabled-pagination': currentPage === 1 }">
                                    <a class="page-link">&lt;&lt;</a>
                                  </li>
                                  <li class="page-item" (click)="previousPageList()"
                                    [ngClass]="{ 'disabled-pagination': currentPage === 1 }">
                                    <a class="page-link">&lt;</a>
                                  </li>
                                  <li class="page-item">
                                    <a class="page-link">page &nbsp;{{ currentPage }}&nbsp;of&nbsp; {{ totalPage }}</a>
                                  </li>
                                  <li class="page-item" (click)="nextPageList()" [ngClass]="{
                                        'disabled-pagination': currentPage === totalPage
                                      }">
                                    <a class="page-link">&gt;</a>
                                  </li>
                                  <li class="page-item" (click)="lastPageList()" [ngClass]="{
                                        'disabled-pagination': currentPage === totalPage
                                      }">
                                    <a class="page-link">&gt;&gt;</a>
                                  </li>
                                </ul>
                              </nav>
                            </div>
                          </div>
                          <div class="card card-table mb-0">
                            <div class="table-responsive" *ngIf="doctorList.length > 0">
                              <table class="table table-hover table-center mb-0">
                                <thead>
                                  <tr>
                                    <th>#</th>
                                    <th>Name</th>
                                    <th>Qualification</th>
                                    <th>Registration</th>
                                    <th>Action</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr *ngFor="let data of doctorList; let i = index">
                                    <th scope="row">{{ serialNumber + i + 1 }}</th>
                                    <td>
                                      <a class="text-primary link" (click)="onIdentity(data.uuid)">{{ data.user.username
                                        }}</a>
                                    </td>
                                    <td *ngIf="data.qualifications.length > 0">
                                      <div class="text-wrap">
                                        <a *ngFor="
                                              let education of data.qualifications;
                                              let len = index
                                            " class="text-primary" >&nbsp;{{
                                          education.name }}&nbsp;{{
                                          data.qualifications.length > 1
                                          ? data.qualifications.length - 1 === len
                                          ? ""
                                          : ","
                                          : ""
                                          }}</a>
                                      </div>
                                    </td>
                                    <td *ngIf="data.qualifications.length === 0">
                                      need to add
                                    </td>
                                    <td *ngIf="data.registrations.length > 0">
                                      <div class="text-wrap">
                                        <a *ngFor="let reg of data.registrations" class="text-primary "
                                          >{{ reg.council }}</a>
                                      </div>
                                    </td>
                                    <td *ngIf="data.registrations.length === 0">
                                      need to add
                                    </td>
                                    <td>
                                      <button class="btn-primary btn-md mr-2" (click)="onIdentity(data.uuid)">
                                        View
                                      </button>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                            <div class="text-center mb-2 p-2 mt-2">
                              <span class="appointmentList-no-data" *ngIf="doctorList.length === 0"
                                style="color: orangered">No Doctor Data</span>
                            </div>
                          </div>
                          <div class="float-right mt-3">
                            <nav aria-label="Page navigation example" *ngIf="this.totalPage > 1">
                              <ul class="pagination">
                                <li class="page-item" (click)="firstPageList()"
                                  [ngClass]="{ 'disabled-pagination': currentPage === 1 }">
                                  <a class="page-link">&lt;&lt;</a>
                                </li>
                                <li class="page-item" (click)="previousPageList()"
                                  [ngClass]="{ 'disabled-pagination': currentPage === 1 }">
                                  <a class="page-link">&lt;</a>
                                </li>
                                <li class="page-item">
                                  <a class="page-link">page &nbsp;{{ currentPage }}&nbsp;of&nbsp; {{ totalPage }}</a>
                                </li>
                                <li class="page-item" (click)="nextPageList()" [ngClass]="{
                                      'disabled-pagination': currentPage === totalPage
                                    }">
                                  <a class="page-link">&gt;</a>
                                </li>
                                <li class="page-item" (click)="lastPageList()" [ngClass]="{
                                      'disabled-pagination': currentPage === totalPage
                                    }">
                                  <a class="page-link">&gt;&gt;</a>
                                </li>
                              </ul>
                            </nav>
                          </div>
                        </div>
                        <div class="centered" *ngIf="isLoading">
                          <app-loading-spinner></app-loading-spinner>
                        </div>

                      </div>
                      <!-- doctor list end -->

                      <!-- pending Tab -->
                      <div class="tab-pane" id="pending-doctor">
                        <h4>Total Pending Profiles - {{ pendingDoctorCount }}</h4>
                        <div class="col-md-12 float-right">
                          <div class="float-right ">
                            <nav aria-label="Page navigation example " *ngIf="this.pendingDoctorTotalPage > 1">
                              <ul class="pagination">
                                <li class="page-item" (click)="pendingDoctorFirstPage()"
                                  [ngClass]="{ 'disabled-pagination': pendingDoctorCurrentPage === 1 }">
                                  <a class="page-link">&lt;&lt;</a>
                                </li>
                                <li class="page-item" (click)="pendingDoctorPreviousPage()"
                                  [ngClass]="{ 'disabled-pagination': pendingDoctorCurrentPage === 1 }">
                                  <a class="page-link">&lt;</a>
                                </li>
                                <li class="page-item">
                                  <a class="page-link">page &nbsp;{{ pendingDoctorCurrentPage }}&nbsp;of &nbsp;{{
                                    pendingDoctorTotalPage }}</a>
                                </li>
                                <li class="page-item" (click)="pendingDoctorNextPage()" [ngClass]="{
                                      'disabled-pagination': pendingDoctorCurrentPage === pendingDoctorTotalPage
                                    }">
                                  <a class="page-link">&gt;</a>
                                </li>
                                <li class="page-item" (click)="pendingDoctorLastPage()" [ngClass]="{
                                      'disabled-pagination': pendingDoctorCurrentPage === pendingDoctorTotalPage
                                    }">
                                  <a class="page-link">&gt;&gt;</a>
                                </li>
                              </ul>
                            </nav>
                          </div>
                        </div>
                        <div class="card card-table mb-0">
                          <div class="card-body">
                            <div class="table-responsive" *ngIf="pendingDoctor.length > 0">
                              <table class="table table-hover table-center mb-0">
                                <thead>
                                  <tr>
                                    <th>#</th>
                                    <th>Doctor Name</th>
                                    <th>Qualification</th>
                                    <th>Registration</th>
                                    <th>Action</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr *ngFor="let data of pendingDoctor; let i = index">
                                    <th scope="row">{{pendingDoctorSerialNumber+ i + 1 }}</th>
                                    <td>
                                      <a class="text-primary link" (click)="onIdentity(data.uuid)">{{ data.user.username
                                        }}</a>
                                    </td>
                                    <td *ngIf="data.qualifications.length > 0">
                                      <div class="text-wrap">
                                        <a *ngFor="
                                              let education of data.qualifications;
                                              let len = index
                                            " class="text-primary " >&nbsp;{{
                                          education.name }}&nbsp;{{
                                          data.qualifications.length > 1
                                          ? data.qualifications.length - 1 === len
                                          ? ""
                                          : ","
                                          : ""
                                          }}</a>
                                      </div>
                                    </td>
                                    <td *ngIf="data.qualifications.length === 0">
                                      need to add
                                    </td>
                                    <td>
                                      <a *ngFor="let reg of data.registrations" class="text-primary "
                                        >{{ reg.council }}</a>
                                    </td>
                                    <td>
                                      <button class="btn-primary btn-md mr-2" (click)="onIdentity(data.uuid)">
                                        View
                                      </button>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                            <div class="text-center mb-2 p-2 mt-2">
                              <span class="appointmentList-no-data" *ngIf="pendingDoctor.length === 0"
                                style="color: orangered">No Approval List Found</span>
                            </div>
                          </div>
                        </div>
                        <div class="float-right mt-3">
                          <nav aria-label="Page navigation example " *ngIf="this.pendingDoctorTotalPage > 1">
                            <ul class="pagination">
                              <li class="page-item" (click)="pendingDoctorFirstPage()"
                                [ngClass]="{ 'disabled-pagination': pendingDoctorCurrentPage === 1 }">
                                <a class="page-link">&lt;&lt;</a>
                              </li>
                              <li class="page-item" (click)="pendingDoctorPreviousPage()"
                                [ngClass]="{ 'disabled-pagination': pendingDoctorCurrentPage === 1 }">
                                <a class="page-link">&lt;</a>
                              </li>
                              <li class="page-item">
                                <a class="page-link">page &nbsp;{{ pendingDoctorCurrentPage }}&nbsp;of &nbsp;{{
                                  pendingDoctorTotalPage }}</a>
                              </li>
                              <li class="page-item" (click)="pendingDoctorNextPage()" [ngClass]="{
                                    'disabled-pagination': pendingDoctorCurrentPage === pendingDoctorTotalPage
                                  }">
                                <a class="page-link">&gt;</a>
                              </li>
                              <li class="page-item" (click)="pendingDoctorLastPage()" [ngClass]="{
                                    'disabled-pagination': pendingDoctorCurrentPage === pendingDoctorTotalPage
                                  }">
                                <a class="page-link">&gt;&gt;</a>
                              </li>
                            </ul>
                          </nav>
                        </div>
                      </div>
                      <!-- /pending  Tab -->
                      <!-- Rejected Tab -->
                      <div class="tab-pane" id="rejected-doctor">
                        <h4>Total Rejected Profiles - {{ rejectedDoctorCount }}</h4>
                        <div class="col-md-12 float-right">
                          <div class="float-right">
                            <nav aria-label="Page navigation example" *ngIf="this.rejectedDoctorTotalPage > 1">
                              <ul class="pagination">
                                <li class="page-item" (click)="rejectedDoctorFirstPage()"
                                  [ngClass]="{ 'disabled-pagination': rejectedDoctorCurrentPage === 1 }">
                                  <a class="page-link">&lt;&lt;</a>
                                </li>
                                <li class="page-item" (click)="rejectedDoctorPreviousPage()"
                                  [ngClass]="{ 'disabled-pagination': rejectedDoctorCurrentPage === 1 }">
                                  <a class="page-link">&lt;</a>
                                </li>
                                <li class="page-item">
                                  <a class="page-link">page &nbsp;{{ rejectedDoctorCurrentPage }}&nbsp;of&nbsp; {{
                                    rejectedDoctorTotalPage }}</a>
                                </li>
                                <li class="page-item" (click)="rejectedDoctorNextPage()" [ngClass]="{
                                      'disabled-pagination': rejectedDoctorCurrentPage === rejectedDoctorTotalPage
                                    }">
                                  <a class="page-link">&gt;</a>
                                </li>
                                <li class="page-item" (click)="rejectedDoctorLastPage()" [ngClass]="{
                                      'disabled-pagination': rejectedDoctorCurrentPage === rejectedDoctorTotalPage
                                    }">
                                  <a class="page-link">&gt;&gt;</a>
                                </li>
                              </ul>
                            </nav>
                          </div>

                        </div>
                        <div class="card card-table mb-0">
                          <div class="card-body">
                            <div class="table-responsive" *ngIf="rejectedDoctor.length > 0">
                              <table class="table table-hover table-center mb-0">
                                <thead>
                                  <tr>
                                    <th>#</th>
                                    <th>Doctor Name</th>
                                    <th>Qualification</th>
                                    <th>Registration</th>
                                    <th>Action</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr *ngFor="let data of rejectedDoctor; let i = index">
                                    <th scope="row">{{rejecedDoctorSerialNumber+ i + 1 }}</th>
                                    <td>
                                      <a class="text-primary link" (click)="onIdentity(data.uuid)">{{ data.user.username
                                        }}</a>
                                    </td>
                                    <td *ngIf="data.qualifications.length > 0">
                                      <div class="text-wrap">
                                        <a *ngFor="
                                              let education of data.qualifications;
                                              let len = index
                                            " class="text-primary " >&nbsp;{{
                                          education.name }}&nbsp;{{
                                          data.qualifications.length > 1
                                          ? data.qualifications.length - 1 === len
                                          ? ""
                                          : ","
                                          : ""
                                          }}</a>
                                      </div>
                                    </td>
                                    <td *ngIf="data.qualifications.length === 0">
                                      need to add
                                    </td>
                                    <td *ngIf="data.registrations.length > 0">
                                      <div class="text-wrap">
                                        <a *ngFor="let reg of data.registrations; let last = last;"
                                          class="text-primary " >{{ reg.council }}
                                          <ng-container *ngIf="!last">,&nbsp;</ng-container>
                                        </a>
                                      </div>
                                    </td>
                                    <td *ngIf="data.registrations.length === 0">
                                      need to add
                                    </td>
                                    <td>
                                      <button class="btn-primary btn-md mr-2" (click)="onIdentity(data.uuid)">
                                        View
                                      </button>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                            <div class="text-center mb-2 p-2 mt-2">
                              <span class="appointmentList-no-data" *ngIf="rejectedDoctor.length === 0"
                                style="color: orangered">No Rejected List Found</span>
                            </div>
                          </div>
                        </div>
                        <div class="float-right mt-3">
                          <nav aria-label="Page navigation example" *ngIf="this.rejectedDoctorTotalPage > 1">
                            <ul class="pagination">
                              <li class="page-item" (click)="rejectedDoctorFirstPage()"
                                [ngClass]="{ 'disabled-pagination': rejectedDoctorCurrentPage === 1 }">
                                <a class="page-link">&lt;&lt;</a>
                              </li>
                              <li class="page-item" (click)="rejectedDoctorPreviousPage()"
                                [ngClass]="{ 'disabled-pagination': rejectedDoctorCurrentPage === 1 }">
                                <a class="page-link">&lt;</a>
                              </li>
                              <li class="page-item">
                                <a class="page-link">page &nbsp;{{ rejectedDoctorCurrentPage }}&nbsp;of&nbsp; {{
                                  rejectedDoctorTotalPage }}</a>
                              </li>
                              <li class="page-item" (click)="rejectedDoctorNextPage()" [ngClass]="{
                                    'disabled-pagination': rejectedDoctorCurrentPage === rejectedDoctorTotalPage
                                  }">
                                <a class="page-link">&gt;</a>
                              </li>
                              <li class="page-item" (click)="rejectedDoctorLastPage()" [ngClass]="{
                                    'disabled-pagination': rejectedDoctorCurrentPage === rejectedDoctorTotalPage
                                  }">
                                <a class="page-link">&gt;&gt;</a>
                              </li>
                            </ul>
                          </nav>
                        </div>
                      </div>
                      <!-- /Rejected  Tab -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="tab-pane" id="hospital-test">
          <div class="card">
            <div class="card-body">
              <div *ngIf="!addingHospital" class="col-md-12 col-lg-12 col-xl-12 mb-4">
                <div class="tab-content pt-0 mt-5 ">
                  <div class="row ">
                    <div class="col-md-10">
                      <h4 class="text-success">Hospital List</h4>
                      <h4>Total Hospitals - {{ hospitalCount }}</h4>
                    </div>

                    <div class="col-md-2 float-right">
                      <button class="btn btn-primary float-right add-hsp" (click)="showHospitalForm(true)">
                        Add Hospital
                      </button>
                    </div>
                  </div>
                  <div class="col-md-12 float-right">
                    <div class="float-right">
                      <nav aria-label="Page navigation example" *ngIf="this.hospitalTotalPage > 1">
                        <ul class="pagination">
                          <li class="page-item" (click)="hospitalFirstPage()"
                            [ngClass]="{ 'disabled-pagination': hospitalCurrentPage === 1 }">
                            <a class="page-link">&lt;&lt;</a>
                          </li>
                          <li class="page-item" (click)="hospitalPreviousPage()"
                            [ngClass]="{ 'disabled-pagination': hospitalCurrentPage === 1 }">
                            <a class="page-link">&lt;</a>
                          </li>
                          <li class="page-item">
                            <a class="page-link">page &nbsp;{{ hospitalCurrentPage }}&nbsp;of&nbsp; {{ hospitalTotalPage
                              }}</a>
                          </li>
                          <li class="page-item" (click)="hospitalNextPage()" [ngClass]="{
                                'disabled-pagination': hospitalCurrentPage === hospitalTotalPage
                              }">
                            <a class="page-link">&gt;</a>
                          </li>
                          <li class="page-item" (click)="hospitalLastPage()" [ngClass]="{
                                'disabled-pagination': hospitalCurrentPage === hospitalTotalPage
                              }">
                            <a class="page-link">&gt;&gt;</a>
                          </li>
                        </ul>
                      </nav>
                    </div>
                  </div>

                  <div *ngIf="!hospitalListLoading">
                    <div class="card card-table mb-0">
                      <div class="card-body">

                        <div class="table-responsive">

                          <table class="table table-hover table-center mb-0">
                            <thead>
                              <tr>
                                <th>#</th>
                                <th>Hospital Name</th>
                                <th>Registration</th>
                                <th>Email</th>
                                <th>Hospital Url</th>
                                <th>Action</th>
                                <th>Settings</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr *ngFor="let hospital of hospitalsList; let i = index">
                                <th scope="row">{{hospitalSerialNumber+ i + 1 }}</th>
                                <td>
                                  <a class="text-primary link" (click)="viewHospital(i)">{{ hospital["name"] }}</a>
                                </td>
                                <td>
                                  <a class="text-primary " >{{
                                    hospital["med_hospital_id"]
                                    }}</a>
                                </td>
                                <td>
                                  <a class="text-primary " >{{ hospital["email"] }}</a>
                                </td>
                                <td>
                                  <a class="text-primary " >{{ hospital["url"] }}</a>
                                </td>
                                <td>
                                  <button class="btn-primary btn-md mr-2" (click)="viewHospital(i)">
                                    View
                                  </button>
                                </td>
                                <td>
                                  <button class="btn-primary btn-md mr-2" (click)="viewSettings(hospital['uuid'])">
                                    Edit
                                  </button>
                                </td>
                              </tr>
                              <tr *ngIf="hospitalsList.length == 0" id="no-data">
                                <!-- <th scope="row"></th> -->
                                <td></td>
                                <td></td>
                                <td>No Approval List Found</td>
                                <td></td>
                                <!-- <td>
                                            <button class="btn-primary btn-md mr-2" [disabled]="approvedDoctors.length == 0" (click)="onApprove()">Approve</button>
                                              <button class="btn-danger btn-md mr-2" [disabled]="approvedDoctors.length == 0" (click)="onReject()">Reject</button>
                                              <button class="btn-warning btn-md" [disabled]="approvedDoctors.length == 0" (click)="onSendBack()">Send Back</button>
                                          </td> -->
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="centered" *ngIf="hospitalListLoading">
                    <app-loading-spinner></app-loading-spinner>
                  </div>
                </div>


              </div>

              <div *ngIf="addingHospital" class="hsptl-form">
                <h5 class="back-head" (click)="showHospitalForm(false)">
                  <i class="fas fa-chevron-circle-left"></i>Back
                </h5>
                <!--<i class="fa fa-edit"></i></h4-->
                <div class="card">
                  <div class="card-body">
                    <h3>
                      Hospital Details&nbsp;<i *ngIf="!edit" (click)="editHspDetails()" class="fa fa-edit"></i>
                    </h3>
                    <div class="hsp-details">
                      <form *ngIf="!viewingHospital" [formGroup]="hospitalCreationForm" class="hsp-detail-form">
                        <div class="row">
                          <div class="col-md-3 col-lg-3 col-sm-6">
                            <small>Name <span class="text-danger">*</span></small>
                            <input id="hsp-name" formControlName="name" type="text" class="form-control" maxlength="50"
                              required />
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('name').errors?.required && (hospitalCreationForm.get('name').dirty || hospitalCreationForm.get('name').touched)">
                              Name is required.
                            </div>
                            <div class="text-danger" *ngIf="hospitalCreationForm.get('name').errors?.pattern">
                              Only alphabet and Number format are permitted
                            </div>
                          </div>
                          <div class="col-md-3 col-lg-3 col-sm-6">
                            <small>Website Url</small>
                            <input id="hsp-url" formControlName="url" type="text" class="form-control" maxlength="50"
                              placeholder="www.example.com" />
                            <div class="text-danger"
                              *ngIf="(hospitalCreationForm.get('url').dirty || hospitalCreationForm.get('url').touched)&& hospitalCreationForm.get('url').errors?.pattern">
                              Invalid URL Format
                            </div>
                            <!-- <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('url').errors?.required && (hospitalCreationForm.get('url').dirty || hospitalCreationForm.get('url').touched)">
                              url is required.
                            </div> -->
                          </div>
                          <div class="col-md-3 col-lg-3 col-sm-6">
                            <small>Email<span class="text-danger">*</span></small>
                            <input type="email" id="hsp-reg-num" formControlName="email" class="form-control"
                              maxlength="50" pattern="^\w+([\.+-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" required />
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('email').errors?.required && (hospitalCreationForm.get('email').dirty || hospitalCreationForm.get('email').touched)">
                              email is required.
                            </div>
                            <div class="text-danger" *ngIf="hospitalCreationForm.get('email').errors?.email">
                              Please enter a valid email address.
                            </div>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col-md-3 col-lg-3 col-sm-6">
                            <small>Phone Numbers<span class="text-danger">*</span></small>
                            <input id="hsp-phone" type="text" formControlName="phone_numbers" class="form-control"
                              pattern="[0-9]*" maxlength="15" required />
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('phone_numbers').errors?.required && (hospitalCreationForm.get('phone_numbers').dirty || hospitalCreationForm.get('phone_numbers').touched)">
                              Phone Numbers is required.
                            </div>
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('phone_numbers').errors?.minlength">
                              Phone Number should minimum 10 Numbers.
                            </div>
                            <div class="text-danger" *ngIf="hospitalCreationForm.get('phone_numbers').errors?.pattern">
                              Phone Number should Only in Numbers.
                            </div>
                          </div>
                          <div class="col-md-3 col-lg-3 col-sm-6">
                            <small>Admin Name<span class="text-danger">*</span></small>
                            <input id="admin-name" type="text" formControlName="contact_person_name"
                              class="form-control" maxlength="50" required />
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('contact_person_name').errors?.required && (hospitalCreationForm.get('contact_person_name').dirty || hospitalCreationForm.get('contact_person_name').touched)">
                              Admin Name is required.
                            </div>
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('contact_person_name').errors?.pattern">
                              Only alphabet and Number format are permitted
                            </div>
                          </div>
                          <div class="col-md-3 col-lg-3 col-sm-6" formGroupName="user">
                            <small>Admin Email<span class="text-danger">*</span></small>
                            <input type="email" id="admin-email" formControlName="email" class="form-control"
                              maxlength="50" />
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('user.email').errors?.required && (hospitalCreationForm.get('user.email').dirty || hospitalCreationForm.get('user.email').touched)">
                              Email is required.
                            </div>
                            <div class="text-danger" *ngIf="hospitalCreationForm.get('user.email').errors?.email">
                              Please enter a valid email address.
                            </div>
                          </div>
                        </div>
                        <div class="row" formGroupName="user">
                          <!-- <div class="col-md-3 col-lg-3 col-sm-6">
                                              <small>Admin Username</small>
                                              <input id="user-username" type="text" formControlName="email" class="form-control">
                                          </div> -->
                          <div class="col-md-3 col-lg-3 col-sm-6">
                            <small>Admin Phone<span class="text-danger">*</span></small>
                            <input id="user-phone" type="text" formControlName="phone" class="form-control"
                              maxlength="15" pattern="[0-9]*" />
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('user.phone').errors?.required && (hospitalCreationForm.get('user.phone').dirty || hospitalCreationForm.get('user.phone').touched)">
                              Phone Numbers is required.
                            </div>
                            <div class="text-danger" *ngIf="hospitalCreationForm.get('user.phone').errors?.pattern">
                              Phone Number should Only in Numbers.
                            </div>
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('user.phone').errors?.minlength">
                              Phone Number should minimum 10 Numbers.
                            </div>                            
                          </div>
                          <div class="col-md-3 col-lg-3 col-sm-6">
                            <small>Password<span class="text-danger">*</span></small>
                            <input type="password" id="pwd" formControlName="password1" class="form-control"
                              maxlength="50" minlength="8" required />
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('user.password1').errors?.required && (hospitalCreationForm.get('user.password1').dirty || hospitalCreationForm.get('user.password1').touched)">
                              Password is required.
                            </div>
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('user.password1').errors?.minlength ">
                              Password should be atleast 8 characters.
                            </div>
                          </div>
                        </div>
                        <div class="row" formGroupName="user">
                          <div class="col-md-2 col-lg-2 col-sm-6">
                            <label> Url Prefix</label>
                            <input id="hsp-login-url" type="text" formControlName="url_prefix" class="form-control"
                              readonly>
                          </div>
                          <div class="col-md-4 col-lg-4 col-sm-9">
                            <label>Login Url</label>
                            <input id="hsp-login-url" type="text" formControlName="custom_url" class="form-control">
                          </div>
                          <div class="col-md-3 col-lg-3 col-sm-6">
                            <label>Domain</label>
                            <input id="hsp-login-url" type="text" formControlName="url_domain" class="form-control"
                              readonly>
                          </div>
                          <div class="col-md-2 col-lg-2 col-sm-6" *ngIf="edit">
                            <button class="btn btn-info mt -4 form-control" (click)="checkAvailability()">Check
                              Availability</button>
                            <label>{{urlAvailableStatus}}</label>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col-9">
                            <small>Description<span class="text-danger">*</span></small>
                            <ckeditor [config]="ckConfig" formControlName="description" id="dist">
                              <ckbutton [name]="'saveButton'" [command]="'saveCmd'" [label]="'Save Document'">
                              </ckbutton>
                            </ckeditor>
                            <div class="text-danger"
                              *ngIf="hospitalCreationForm.get('description').errors?.required && (hospitalCreationForm.get('description').dirty || hospitalCreationForm.get('description').touched)">
                              Description is required.
                            </div>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col-md-3 col-lg-3 col-sm-6">
                            <!-- <div class="col-md-3 col-lg-3 col-sm-6" formGroupName="user"> -->
                            <!-- <input id="user-username" type="text" formControlName="username" class="form-control" hidden> -->
                          </div>
                          <div class="col-md-3 col-lg-3 col-sm-6"></div>
                          <div *ngIf="edit" class="col-md-3 col-lg-3 col-sm-6">
                            <button class="btn btn-secondary hsp-btn" (click)="showHospitalForm(false)"
                              id="hsp-cancel-btn">
                              Cancel
                            </button>
                            <button class="btn btn-primary hsp-btn" [disabled]="!hospitalCreationForm.valid"
                              id="save-btn" (click)="hspFormSubmit()">
                              Save
                            </button>
                          </div>
                        </div>
                      </form>

                    </div>
                  </div>
                </div>
                <div class="float-right mt-3" *ngIf="!addingHospital">
                  <nav aria-label="Page navigation example" *ngIf="this.hospitalTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="hospitalFirstPage()"
                        [ngClass]="{ 'disabled-pagination': hospitalCurrentPage === 1 }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="hospitalPreviousPage()"
                        [ngClass]="{ 'disabled-pagination': hospitalCurrentPage === 1 }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ hospitalCurrentPage }}&nbsp;of&nbsp; {{ hospitalTotalPage
                          }}</a>
                      </li>
                      <li class="page-item" (click)="hospitalNextPage()" [ngClass]="{
                            'disabled-pagination': hospitalCurrentPage === hospitalTotalPage
                          }">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="hospitalLastPage()" [ngClass]="{
                            'disabled-pagination': hospitalCurrentPage === hospitalTotalPage
                          }">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!--patient list-->
        <div class="tab-pane" id="patient-test">
          <div class="card">
            <div class="card-body">
              <app-patient-list></app-patient-list>
            </div>
          </div>
        </div>

        <!--bank account-->
        <div class="tab-pane" id="bank-test">
          <div class="card">
            <div class="card-body">
              <div *ngIf="!bankListLoading">
                <div class="col-md-12 float-right">
                  <div class="float-right mt-3">
                    <nav aria-label="Page navigation example" *ngIf="this.bankTotalPage > 1">
                      <ul class="pagination">
                        <li class="page-item" (click)="bankFirstPage()"
                          [ngClass]="{ 'disabled-pagination': bankCurrentPage === 1 }">
                          <a class="page-link">&lt;&lt;</a>
                        </li>
                        <li class="page-item" (click)="bankPreviousPage()"
                          [ngClass]="{ 'disabled-pagination': bankCurrentPage === 1 }">
                          <a class="page-link">&lt;</a>
                        </li>
                        <li class="page-item">
                          <a class="page-link">page &nbsp;{{ bankCurrentPage }}&nbsp;of&nbsp; {{ bankTotalPage }}</a>
                        </li>
                        <li class="page-item" (click)="bankNextPage()" [ngClass]="{
                    'disabled-pagination': bankCurrentPage === bankTotalPage
                  }">
                          <a class="page-link">&gt;</a>
                        </li>
                        <li class="page-item" (click)="bankLastPage()" [ngClass]="{
                    'disabled-pagination': bankCurrentPage === bankTotalPage
                  }">
                          <a class="page-link">&gt;&gt;</a>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
                <div class="col-md-10">
                  <h4 class="text-success">Doctors Bank List</h4>
                  <h4>Total Bank Accounts - {{ bankCount }}</h4>
                  <div class="card card-table mb-0">
                    <div class="card-body">
                      <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                          <thead>
                            <tr>
                              <th>#</th>
                              <th>Account Name</th>
                              <th>Account Number</th>
                              <th>Account Type</th>
                              <th>Bank Name</th>
                              <th>Branch Name</th>
                              <th>IFSC Code</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let data of bankList; let i = index">
                              <th scope="row"><a>{{ bankSerialNumber + i + 1 }}</a></th>
                              <td><a class="text-primary ">{{ data.account_name }}</a></td>
                              <td><a class="text-primary ">{{ data.account_number }}</a></td>
                              <td><a class="text-primary ">{{ data.account_type }}</a></td>
                              <td><a class="text-primary ">{{ data.bank_name }}</a></td>
                              <td><a class="text-primary ">{{ data.branch_name }}</a></td>
                              <td><a class="text-primary ">{{ data.ifsc_code }}</a></td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="centered" *ngIf="patientisLoading">
                  <app-loading-spinner></app-loading-spinner>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--end bank account-->

        <!-- depatmentList -->
        <div class="tab-pane" id="Department-test">
          <div class="card">
            <div class="card-body">
              <div *ngIf="!deptisLoading">
                <div class="col-md-10">
                  <div class="tab-content pt-0 mt-5 ">
                    <div class="row ">
                      <div class="col-md-7">
                        <h4 class="text-success">Department List</h4>
                        <h4>Total Departments - {{ deptCount }}</h4>
                      </div>
                      <div class="col-md-5 float-right">
                        <button class="btn btn-primary float-right add-hsp mr-2" (click)="addDepartment(2)">
                          Add Department</button>
                        <button class="btn btn-primary float-right add-hsp mr-2 " (click)="addDepartment(1)">
                          Add System of Medicine</button>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-12 float-right">
                    <div class="float-right">
                      <nav aria-label="Page navigation example" *ngIf="this.deptTotalPage > 1">
                        <ul class="pagination">
                          <li class="page-item" (click)="firstDeptPageList()"
                            [ngClass]="{ 'disabled-pagination': deptCurrentPage === 1 }">
                            <a class="page-link">&lt;&lt;</a>
                          </li>
                          <li class="page-item" (click)="previousDeptPageList()"
                            [ngClass]="{ 'disabled-pagination': deptCurrentPage === 1 }">
                            <a class="page-link">&lt;</a>
                          </li>
                          <li class="page-item">
                            <a class="page-link">page &nbsp;{{ deptCurrentPage }}&nbsp;of&nbsp; {{ deptTotalPage }}</a>
                          </li>
                          <li class="page-item" (click)="nextDeptPageList()" [ngClass]="{
                      'disabled-pagination': deptCurrentPage === deptTotalPage
                    }">
                            <a class="page-link">&gt;</a>
                          </li>
                          <li class="page-item" (click)="lastDeptPageList()" [ngClass]="{
                      'disabled-pagination': deptCurrentPage === deptTotalPage
                    }">
                            <a class="page-link">&gt;&gt;</a>
                          </li>
                        </ul>
                      </nav>
                    </div>
                  </div>
                  <!-- <h2 >test</h2> -->
                  <div class="card card-table mb-0">
                    <div class="card-body">
                      <div class="table-responsive" *ngIf="deptList?.length > 0">
                        <table class="table table-hover table-center mb-0">
                          <thead>
                            <tr>
                              <th>#</th>
                              <th> Department Name</th>
                              <th>Acronym</th>
                              <th>Action</th>
                              <!-- <th>Report</th> -->
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let data of deptList; let i = index">
                              <th scope="row"><a>{{ deptSerialNumber + i + 1 }}</a></th>
                              <td><a class="text-primary ">{{ data.department_value }}</a></td>
                              <td><a class="text-primary ">{{ data.department_code }}</a></td>
                              <td>
                                <button class="btn-danger btn-md mr-2"
                                  (click)="deleteDepartment(1,data)">Delete</button>
                              </td>
                              <!-- <td>
                                <button class="btn-primary btn-md mr-2"
                                  (click)="onConsultationSummary(data.department_id,'department')">
                                  Report
                                </button>
                              </td> -->
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div class="centered" *ngIf="doctorisLoading">
              <app-loading-spinner></app-loading-spinner>
              </div> -->
              </div>
            </div>
          </div>
        </div>
        <!-- end depatmentList -->

        <!-- consultationList -->
        <div class="tab-pane" id="consultationList-test">
          <div class="card">
            <div class="card-body">
              <app-consultation-list></app-consultation-list>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>

<!-- delete department popup starts -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deletModalLabel">Delete Department</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <h6>Are you sure to delete "{{deleteDepartmentName}}"?</h6>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn-primary btn-md mr-2" (click)="deleteDepartment(2)">Delete</button>
        <button class="btn-danger btn-md mr-2" data-dismiss="modal">cancel</button>
      </div>
    </div>
  </div>
</div>
<!-- delete department popup end -->

<!-- add department popup starts -->
<div class="modal fade" id="addDeptModal" tabindex="-1" role="dialog" aria-labelledby="addDeptModalLabel"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addDeptModalLabel">ADD Department</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <div class="content justify-content ">
            <form [formGroup]="createDeptPlatformAdminForm">
              <div class="form-group " *ngIf="onlySOM">
                <label class="focus-label floating  ml-2 ">System of Medicine</label>
                <input type="text" name="newSOM" class=" mr-2" required formControlName="newSOM" />
              </div>
              <div class="form-group " *ngIf="!onlySOM">
                <label class="focus-label floating  ml-2 ">System of Medicine</label>
                <ng-select formControlName="dept_name" class=" mr-2" [items]="systemOfMedicine" bindLabel="value"
                  [addTag]="true" addTagText="add new department" (change)="selectSOM($event)">
                </ng-select>
              </div>
              <div class="form-group" *ngIf="!onlySOM">
                <label class="focus-label floating  ml-2">Department</label>
                <input type="text" name="new" class="ml-2 mr-2" required formControlName="newDept" />
              </div>
              <div class="form-group" *ngIf="!onlySOM">
                <label class="focus-label  floating  ml-2">Acronym</label>
                <input type="text" name="new" class="ml-2 mr-2" required formControlName="newDepCode" />
              </div>
              <div class="form-group">
                <button id="save-hospital-admin" class="btn btn-primary btn-block btn-lg login-btn" type="submit"
                  (click)="createNewDept()" [disabled]="createDeptPlatformAdminForm.get('newSOM').hasError('required')"
                  *ngIf="onlySOM">Save</button>
                <button id="save-hospital-admin" class="btn btn-primary btn-block btn-lg login-btn" type="submit"
                  (click)="createNewDept()" [disabled]="!createDeptPlatformAdminForm.valid"
                  *ngIf="!onlySOM">Save</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- add department popup end -->