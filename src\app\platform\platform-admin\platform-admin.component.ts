import { ToastrService } from 'ngx-toastr';
import { FormGroup, FormControl, FormArray, FormBuilder, Validators } from '@angular/forms';
import { AuthService } from './../../auth/auth.service';
import { Component, OnInit } from '@angular/core';
import { PlatformService } from '../platform.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DocumentModalComponent } from 'src/app/shared/document-modal/document-modal.component';
import { Router } from '@angular/router';
import { SharedService } from '../../shared/shared.service';
import { DoctorService } from 'src/app/doctor/doctor.service';
import { HospitalService } from 'src/app/hospital-admin/hospital-admin.service';
import * as Settings from './../../config/settings';
declare var $: any;
import { Location } from '@angular/common';

@Component({
  selector: 'app-platform-admin',
  templateUrl: './platform-admin.component.html',
  styleUrls: ['./platform-admin.component.css']
})
export class PlatformAdminComponent implements OnInit {
  approvals = [];
  hospital_address_set = [];
  patientisLoading = false;
  isLoading = false;
  aprovedDoctorLoading = false;
  messages = [];
  approvedDoctors = [];
  degreeList = [];
  degreeString: any;
  adminProfile: any;
  addingHospital = false;
  hospitalsList = [];
  bankList = [];
  addressesList = [];
  selectedHospital = {};
  edit = false;
  hospitalCreationForm: FormGroup;
  hospitalViewForm: FormGroup;
  hospitalAddressForm: FormGroup;
  addressViewForm: FormGroup;
  addressArray: FormArray;
  viewingHospital = false;
  showAddHspAdd = false;
  hospitalUuid = '';
  newAddressForm = false;
  public ckText = '';
  public ckConfig = {
    bodyClass: 'txt-area',
  };
  public doctorList = [];
  public approvedDoctor = [];
  public pendingDoctor = [];
  public rejectedDoctor = [];
  currentPage: number;
  totalPage: number;
  public serialNumber = 0;
  public approvedDoctorSerialNumber = 0;
  public pendingDoctorSerialNumber = 0;
  public rejecedDoctorSerialNumber = 0;
  public hospitalSerialNumber = 0;
  public bankSerialNumber = 0;
  public apiSearchCall: boolean;
  public approvedDoctorCurrentPage: number;
  public approvedDoctorTotalPage: number;
  public approvedDoctorCount: number;
  pendingDoctorTotalPage: number;
  pendingDoctorCurrentPage: number = 1;
  pendingDoctorCount: number = 0;
  rejectedDoctorTotalPage: number;
  rejectedDoctorCurrentPage: number = 1;
  rejectedDoctorCount: number = 0;
  doctorCount: number;
  hospitalCurrentPage: number = 1;
  bankCurrentPage: number = 1;
  hospitalTotalPage: number;
  bankTotalPage: number;
  hospitalCount: number;
  bankCount: number;
  adminConsult: number;
  adminTotalEarn: number;
  adminMedbotEarn: number;
  public hospitalListLoading: boolean;
  public bankListLoading: boolean;
  deptisLoading: boolean = false;
  deptList: string[];
  deptSerialNumber: number = 0;
  deptTotalPage: any;
  deptCurrentPage: number = 1;
  deptCount: any;
  deleteDepartmentId: any;
  deleteDepartmentName: string = '';
  deleteDepartmentHspt: string = '';
  createDeptPlatformAdminForm: FormGroup;
  department: any;
  systemOfMedicine: string[];
  onlySOM: boolean = true;
  userType: string = "platformAdmin";
  urlAvailableStatus: string = '';
  urlDomain: string;

  constructor(
    private platformService: PlatformService,
    private notificationService: ToastrService,
    private authService: AuthService,
    private modalService: NgbModal,
    private router: Router,
    private formBuilder: FormBuilder,
    private sharedService: SharedService,
    private doctorService: DoctorService,
    private hospitalService: HospitalService,
    private location: Location,
  ) {
    this.createDeptPlatformAdminForm = new FormGroup({
      newSOM: new FormControl('', Validators.required),
      newDept: new FormControl('', Validators.required),
      newDepCode: new FormControl('', Validators.required),
    });
    if (this.location.path() != '') {
      const currentPath = window.location.origin;
      let path = '';
      if (currentPath.includes('app-test')) {
        path = Settings.TestURLDOMAIN;
      } else if (currentPath.includes('app-staging')) {
        path = Settings.StageURLDOMAIN;
      } else if (currentPath.includes('localhost')) {
        path = Settings.TestURLDOMAIN;
      } else {
        path = Settings.LiveURLDOMAIN;
      };
      this.urlDomain = path;
    }
  }

  ngOnInit(): void {
    this.currentPage = 1;
    this.approvedDoctorCurrentPage = 1;
    this.getApprovalLists();
    this.initializeHspForm();
    // this.addHospitalAddress();
    this.authService.getUserDetail().subscribe(
      data => {
        this.adminProfile = data;
        if (this.adminProfile['user_type'] === 'PlatformAdmin') {
          this.sharedService.setUserName(data['username']);
          localStorage.setItem('AdminName', this.adminProfile?.username);
          console.log(this.adminProfile);
        } else {
          this.router.navigate(['/login']);
        }

      }
    );
    this.getHospitals(this.hospitalCurrentPage);
    this.getDoctorList(this.currentPage);
    this.getPendingDoctorList(this.pendingDoctorCurrentPage);
    this.getRegisteredDoctorList(this.approvedDoctorCurrentPage);
    this.getRejectedDoctorList(this.rejectedDoctorCurrentPage);
    this.getAdminEarn();
    this.getDoctorsBankAccountList(this.bankCurrentPage);
    this.getDepartment(this.currentPage);
    this.getDepartmnetData();
  }

  getAdminEarn() {
    this.platformService.getAdminEarning().subscribe(
      data => {
        this.adminConsult = data['consultation count'];
        this.adminTotalEarn = data['total consultation amount'];
        this.adminMedbotEarn = data['total medbot amount'];
        // this.adminMedbotEarn = data['']
        console.log(data);
      },
      error => {
        console.log(error);
      }
    );
  }

  getDepartment(page?: number) {
    const pageNo = (page ? page : 1);
    this.platformService.getDoctorsDepartmentList(page).subscribe(
      data => {
        this.deptList = data['results'];
        this.deptTotalPage = data['total_pages'];
        this.deptCurrentPage = data['page_number'];
        this.deptCount = data['count'];
        this.deptisLoading = false;
      },
      error => {
        console.log(error);
      }
    );
  }

  // Dept list pagination
  nextDeptPageList() {
    this.deptCurrentPage = this.deptCurrentPage + 1;
    if (this.deptTotalPage >= this.deptCurrentPage) {
      this.deptSerialNumber = (this.deptCurrentPage - 1) * 10;
      this.getDepartment(this.deptCurrentPage);
    } else {
      this.deptCurrentPage = this.deptCurrentPage - 1;
    }
  }

  lastDeptPageList() {
    this.deptSerialNumber = (this.deptTotalPage - 1) * 10;
    this.getDepartment(this.deptTotalPage);
  }

  firstDeptPageList() {
    this.deptSerialNumber = 0;
    this.deptCurrentPage = 1;
    this.getDepartment(this.deptCurrentPage);
  }

  previousDeptPageList() {
    this.deptCurrentPage = this.deptCurrentPage - 1;
    if (this.deptTotalPage >= this.deptCurrentPage && this.deptCurrentPage > 0) {
      this.deptSerialNumber = (this.deptCurrentPage - 1) * 10;
      this.getDepartment(this.deptCurrentPage);
    } else {
      this.deptCurrentPage = this.deptCurrentPage + 1;
    }
  }

  onQualification(ind) {
    this.modalService.open(DocumentModalComponent, { windowClass: "modalSize" });
  }

  onIdentity(uuid) {
    this.router.navigate([`identity-details/${uuid}/view`]);
    // this.modalService.open(DocumentModalComponent,  { windowClass : "modalSize"});
  }

  onConsultationSummary(uuid) {
    this.router.navigate([`doctor-consultation-summary/${uuid}/view`]);
    console.log('onconsultationsummary', uuid);
  }

  BankDetail(uuid) {
    this.router.navigate([`doctor-bankdetail/${uuid}/view`]);
  }

  onApproveSelect(event, i) {
    if (event.target.checked) {
      this.approvedDoctors.push(this.approvals[i]['uuid']);
    }
    else {
      this.approvedDoctors = this.approvedDoctors.filter(
        (id) => id != this.approvals[i]['uuid']
      );
    }
  }

  onApprove() {
    this.isLoading = true;
    this.approvedDoctors.forEach(uuid => {
      this.platformService.acceptApproval(uuid).subscribe(() => {
        this.approvedDoctors = [];
        this.isLoading = false;
        this.getApprovalLists();
      }, (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        this.isLoading = false;
        this.approvedDoctors = [];
      });
    });
    // this.router.navigateByUrl('/approval-success');
  }

  onReject() {

  }

  onSendBack() {

  }

  getApprovalLists() {
    this.platformService.getApprovalMessages().subscribe(
      data => {
        this.approvals = [];
        this.approvals = data['results'];
        this.approvals.forEach(data => {
          this.degreeList = [];
          this.degreeString = '';
          data['qualifications'].map(qual => this.degreeList.push(qual['name']));
          for (let i = this.degreeList.length; i > 0; i--) {
            if (this.degreeString != '') {
              this.degreeString = this.degreeString + ', ' + this.degreeList[i - 1];
            }
            else {
              this.degreeString = this.degreeList[i - 1];
            }
            //this.degreeString = this.degreeString.substring(1);
          }
          data['degreeString'] = this.degreeString;
          const regNum = data['registrations'][0]['number'];
          data['registrationNumber'] = regNum;
        });
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  showHospitalForm(val) {
    this.addingHospital = val;
    this.viewingHospital = false;
    this.initializeHspForm();
    this.selectedHospital['admin'] = {};
    this.edit = true;
  }

  viewHospital(id) {
    this.selectedHospital = this.hospitalsList[id];
    this.router.navigate([`hospital-detail/${this.selectedHospital['uuid']}/view`]);
    console.log(this.selectedHospital);
    localStorage.setItem('hospital', this.selectedHospital['uuid']);
    this.hospitalViewForm = new FormGroup({
      uuid: new FormControl(this.selectedHospital['uuid']),
      name: new FormControl(this.selectedHospital['name']),
      url: new FormControl(this.selectedHospital['url']),
      phone_numbers: new FormControl(this.selectedHospital['phone_numbers']),
      email: new FormControl(this.selectedHospital['email']),
      contact_person_name: new FormControl(this.selectedHospital['contact_person_name']),
      description: new FormControl(this.selectedHospital['description']),
    });
    // this.getAddressSet(this.hospitalViewForm.get('uuid').value);
    this.addingHospital = true;
    this.edit = false;
    this.hospitalViewForm.disable();
    this.viewingHospital = true;
    this.showAddHspAdd = true;
  }

  initializeHspForm() {
    this.hospitalCreationForm = new FormGroup({
      name: new FormControl('', [Validators.required, Validators.pattern('^[a-zA-Z0-9 ]*$'), Validators.minLength(2)]),
      url: new FormControl('',[Validators.pattern('^(www\.)?([a-zA-Z0-9_-]+)+(\.[a-zA-Z]{2,})+(\/[a-zA-Z0-9#=_-]+)*\/?$')]),
      phone_numbers: new FormControl('', [Validators.required, Validators.pattern('[0-9]*'), Validators.minLength(10)]),
      email: new FormControl('', [Validators.required, Validators.email]),
      contact_person_name: new FormControl('', [Validators.required, Validators.pattern('^[a-zA-Z0-9 ]*$')]),
      description: new FormControl('', Validators.required),
      login_url: new FormControl(''),
      user: this.formBuilder.group({
        email: new FormControl('', [Validators.required, Validators.email]),
        username: new FormControl(''),
        phone: new FormControl('', [Validators.required, Validators.pattern('[0-9]*'), Validators.minLength(10)]),
        password1: new FormControl('', [Validators.required, this.noWhitespaceValidator]),
        user_type: new FormControl('HospitalAdmin'),
        url_prefix: new FormControl(Settings.URLPREFIX),
        custom_url: new FormControl(''),
        url_domain: new FormControl(this.urlDomain),
      })
    });
    this.addressViewForm = new FormGroup({
      addressArray: this.formBuilder.array([])
    });
  }

  addHospital() {
    this.initializeHspForm();
    this.addingHospital = true;
  }

  hspFormSubmit() {
    this.hospitalCreationForm.get('user').get('username').setValue(this.hospitalCreationForm.get('contact_person_name').value);
    let url = this.hospitalCreationForm.get('user.custom_url').value;
    if (url != '' && url != undefined && url != null) {
      let login_url = this.hospitalCreationForm.get('user.url_prefix').value +
        this.hospitalCreationForm.get('user.custom_url').value +
        this.hospitalCreationForm.get('user.url_domain').value;
      this.hospitalCreationForm.get('login_url').setValue(login_url);
    }
    const data = this.hospitalCreationForm.value;
    this.platformService.postHospital(data).subscribe(
      data => {
        this.edit = false;
        this.notificationService.success('Hospital Added', 'Med.Bot');
        this.getHospitals(1);
        this.addingHospital = false;
        this.hospitalCreationForm.disable();
        this.hospitalCreationForm.markAsPristine();
      },
      error => {
        console.log(error);
        const status = error['status']
        if (status == 400) {
          const url = error['error']['error_details']['validation_errors']['url'];
          const email = error['error']['error_details']['validation_errors']['email'];
          const name = error['error']['error_details']['validation_errors']['name'];
          // const url = error['error']['url'];
          // const email = error['error']['email'];
          // const name = error['error']['name'];
          const other = error['error']['error_message']
          if (url) {
            const urlError = url[0];
            this.notificationService.error(`url : ${urlError}`, 'Med.Bot');
          } else if (email) {
            const emailError = email[0];
            this.notificationService.error(`email :${emailError}`, 'Med.Bot');
          } else if (name) {
            const namelError = name[0];
            this.notificationService.error(`Name :${namelError}`, 'Med.Bot');
          } else if (other) {
            this.notificationService.error(`${error['error']['error_message']}`, 'Med.Bot');
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  updateAdminUserName(event) {
    this.hospitalCreationForm.get('user').get('username').setValue(event.target.value);
  }

  editHspDetails() {
    this.hospitalViewForm.enable();
    this.edit = true;
  }

  cancelHspEdit() {
    this.hospitalViewForm.disable();
    this.edit = false;
  }

  updateHospitalDetails() {
    const uuid = this.hospitalViewForm.get('uuid').value;
    const data = this.hospitalViewForm.value;
    this.platformService.patchHospital(uuid, data).subscribe(
      data => {
        this.getHospitals(1);
        this.notificationService.success('Saved', 'Med.Bot');
      }
    );
    this.hospitalViewForm.disable();
    this.edit = false;
  }

  getHospitals(page) {
    this.hospitalListLoading = true;
    this.hospitalTotalPage = 0;
    this.platformService.getHospitals(page).subscribe(
      data => {
        this.hospitalsList = data['results'];
        this.hospitalCount = data['count'];
        this.hospitalTotalPage = data['total_pages'];
        this.hospitalCurrentPage = data['page_number'];
        this.hospitalListLoading = false;
        console.log(this.hospitalsList);
      }, error => {
        this.hospitalListLoading = false;
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  getDoctorsBankAccountList(page) {
    this.bankListLoading = true;
    this.bankTotalPage = 0;
    this.platformService.getDoctorsBankAccountList(page).subscribe(
      data => {
        this.bankList = data['results'];
        this.bankCount = data['count'];
        this.bankTotalPage = data['total_pages'];
        this.bankListLoading = false;
        console.log("bankaccountlist", this.bankList);
      }, error => {
        this.bankListLoading = false;
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  getDoctorList(currentPage) {
    this.apiSearchCall = true;
    this.totalPage = 0;
    let searchList = [];
    searchList = this.sharedService.getSearListBypage();
    if (searchList.length > 0) {
      console.log('searchList', searchList);
      for (const data of searchList) {
        if (data.page_number == currentPage) {
          this.apiSearchCall = false;
          this.totalPage = data['total_pages'];
          this.currentPage = data['page_number'];
          this.doctorList = data['results'];
          this.isLoading = false;
        }
      }
      if (!!this.apiSearchCall) {
        this.getDoctor(currentPage);
      }
    } else {
      this.getDoctor(currentPage);
    }
  }

  getDoctor(page) {
    this.isLoading = true;
    window.scroll(0, 0);
    this.platformService.getDoctorsList(page).subscribe(
      data => {
        this.doctorList = data['results'];
        this.doctorCount = data['count'];
        this.totalPage = data['total_pages'];
        this.currentPage = data['page_number'];
        this.sharedService.searListBypage(
          this.doctorList,
          this.currentPage,
          null,
          this.totalPage
        );
        this.isLoading = false;

      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  getRegisteredDoctorList(pageNumber) {
    this.aprovedDoctorLoading = true;
    window.scroll(0, 0);
    this.approvedDoctorTotalPage = 0;
    this.platformService.getApprovedDoctor(pageNumber).subscribe(
      data => {
        console.log('approved', data);
        this.approvedDoctorCount = data['count'];
        this.approvedDoctorTotalPage = data['total_pages'];
        this.approvedDoctorCurrentPage = data['page_number'];
        this.approvedDoctor = data['results'];
        this.aprovedDoctorLoading = false;
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        this.aprovedDoctorLoading = false;
      }
    );
  }

  getPendingDoctorList(pageNumber) {
    this.isLoading = true;
    window.scroll(0, 0);
    this.pendingDoctorTotalPage = 0;
    this.platformService.getPendingDoctor(pageNumber).subscribe(
      data => {
        this.pendingDoctor = data['results'];
        this.pendingDoctorCount = data['count'];
        this.pendingDoctorTotalPage = data['total_pages'];
        this.pendingDoctorCurrentPage = data['page_number'];
        this.isLoading = false;
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  getRejectedDoctorList(pageNumber) {
    this.isLoading = true;
    window.scroll(0, 0);
    this.platformService.getRejectedDoctor(pageNumber).subscribe(
      data => {
        this.rejectedDoctor = data['results'];
        this.rejectedDoctorCount = data['count'];
        this.rejectedDoctorTotalPage = data['total_pages'];
        this.rejectedDoctorCurrentPage = data['page_number'];
        this.isLoading = false;
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  nextPageList() {
    this.currentPage = this.currentPage + 1;
    if (this.totalPage >= this.currentPage) {
      this.serialNumber = (this.currentPage - 1) * 10;
      this.getDoctorList(this.currentPage);

    } else {
      this.currentPage = this.currentPage - 1;
    }
  }

  lastPageList() {
    this.serialNumber = (this.totalPage - 1) * 10;
    this.getDoctorList(this.totalPage);
  }

  firstPageList() {
    this.serialNumber = 0;
    this.currentPage = 1;
    this.getDoctorList(this.currentPage);
  }

  previousPageList() {
    this.currentPage = this.currentPage - 1;
    if (this.totalPage >= this.currentPage && this.currentPage > 0) {
      this.serialNumber = (this.currentPage - 1) * 10;
      this.getDoctorList(this.currentPage);
    } else {
      this.currentPage = this.currentPage + 1;
    }
  }

  approvedDoctorNextPage() {
    this.approvedDoctorCurrentPage = this.approvedDoctorCurrentPage + 1;
    if (this.approvedDoctorTotalPage >= this.approvedDoctorCurrentPage) {
      this.approvedDoctorSerialNumber = (this.approvedDoctorCurrentPage - 1) * 10;
      this.getRegisteredDoctorList(this.approvedDoctorCurrentPage);

    } else {
      this.approvedDoctorCurrentPage = this.approvedDoctorCurrentPage - 1;
    }
  }

  approvedDoctorLastPage() {
    this.approvedDoctorSerialNumber = (this.approvedDoctorTotalPage - 1) * 10;
    this.getRegisteredDoctorList(this.approvedDoctorTotalPage);
  }

  approvedDoctorFirstPage() {
    this.approvedDoctorSerialNumber = 0;
    this.approvedDoctorCurrentPage = 1;
    this.getRegisteredDoctorList(this.approvedDoctorCurrentPage);
  }

  approvedDoctorPreviousPage() {
    this.approvedDoctorCurrentPage = this.approvedDoctorCurrentPage - 1;
    if (this.approvedDoctorTotalPage >= this.approvedDoctorCurrentPage && this.approvedDoctorCurrentPage > 0) {
      this.approvedDoctorSerialNumber = (this.approvedDoctorCurrentPage - 1) * 10;
      this.getRegisteredDoctorList(this.approvedDoctorCurrentPage);
    } else {
      this.approvedDoctorCurrentPage = this.approvedDoctorCurrentPage + 1;
    }
  }

  pendingDoctorNextPage() {
    this.pendingDoctorCurrentPage = this.pendingDoctorCurrentPage + 1;
    if (this.pendingDoctorTotalPage >= this.pendingDoctorCurrentPage) {
      this.pendingDoctorSerialNumber = (this.pendingDoctorCurrentPage - 1) * 10;
      this.getPendingDoctorList(this.pendingDoctorCurrentPage);

    } else {
      this.pendingDoctorCurrentPage = this.pendingDoctorCurrentPage - 1;
    }
  }

  pendingDoctorLastPage() {
    this.pendingDoctorSerialNumber = (this.pendingDoctorTotalPage - 1) * 10;
    this.getPendingDoctorList(this.pendingDoctorTotalPage);
  }

  pendingDoctorFirstPage() {
    this.pendingDoctorSerialNumber = 0;
    this.pendingDoctorCurrentPage = 1;
    this.getPendingDoctorList(this.pendingDoctorCurrentPage);
  }

  pendingDoctorPreviousPage() {
    this.pendingDoctorCurrentPage = this.pendingDoctorCurrentPage - 1;
    if (this.pendingDoctorTotalPage >= this.pendingDoctorCurrentPage && this.pendingDoctorCurrentPage > 0) {
      this.pendingDoctorSerialNumber = (this.pendingDoctorCurrentPage - 1) * 10;
      this.getPendingDoctorList(this.pendingDoctorCurrentPage);
    } else {
      this.pendingDoctorCurrentPage = this.pendingDoctorCurrentPage + 1;
    }
  }

  rejectedDoctorNextPage() {
    this.rejectedDoctorCurrentPage = this.rejectedDoctorCurrentPage + 1;
    if (this.rejectedDoctorTotalPage >= this.rejectedDoctorCurrentPage) {
      this.rejecedDoctorSerialNumber = (this.rejectedDoctorCurrentPage - 1) * 10;
      this.getRejectedDoctorList(this.rejectedDoctorCurrentPage);
    } else {
      this.rejectedDoctorCurrentPage = this.rejectedDoctorCurrentPage - 1;
    }
  }

  rejectedDoctorLastPage() {
    this.rejecedDoctorSerialNumber = (this.rejectedDoctorTotalPage - 1) * 10;
    this.getRejectedDoctorList(this.rejectedDoctorTotalPage);
  }

  rejectedDoctorFirstPage() {
    this.rejecedDoctorSerialNumber = 0;
    this.rejectedDoctorCurrentPage = 1;
    this.getRejectedDoctorList(this.rejectedDoctorCurrentPage);
  }

  rejectedDoctorPreviousPage() {
    this.rejectedDoctorCurrentPage = this.rejectedDoctorCurrentPage - 1;
    if (this.rejectedDoctorTotalPage >= this.rejectedDoctorCurrentPage && this.rejectedDoctorCurrentPage > 0) {
      this.rejecedDoctorSerialNumber = (this.rejectedDoctorCurrentPage - 1) * 10;
      this.getRejectedDoctorList(this.rejectedDoctorCurrentPage);
    } else {
      this.rejectedDoctorCurrentPage = this.rejectedDoctorCurrentPage + 1;
    }
  }

  noWhitespaceValidator(control: FormControl) {
    const isSpace = (control.value || '').match(/\s/g);
    return isSpace ? { 'whitespace': true } : null;
  }

  hospitalNextPage() {
    this.hospitalCurrentPage = this.hospitalCurrentPage + 1;
    if (this.hospitalTotalPage >= this.hospitalCurrentPage) {
      this.hospitalSerialNumber = (this.hospitalCurrentPage - 1) * 10;
      this.getHospitals(this.hospitalCurrentPage);
    } else {
      this.hospitalCurrentPage = this.hospitalCurrentPage - 1;
    }
  }

  hospitalLastPage() {
    this.hospitalSerialNumber = (this.hospitalTotalPage - 1) * 10;
    this.getHospitals(this.hospitalTotalPage);
  }

  hospitalFirstPage() {
    this.hospitalSerialNumber = 0;
    this.hospitalCurrentPage = 1;
    this.getHospitals(this.hospitalCurrentPage);
  }

  hospitalPreviousPage() {
    this.hospitalCurrentPage = this.hospitalCurrentPage - 1;
    if (this.hospitalTotalPage >= this.hospitalCurrentPage && this.hospitalCurrentPage > 0) {
      this.hospitalSerialNumber = (this.hospitalCurrentPage - 1) * 10;
      this.getHospitals(this.hospitalCurrentPage);
    } else {
      this.hospitalCurrentPage = this.hospitalCurrentPage + 1;
    }
  }

  bankNextPage() {
    this.bankCurrentPage = this.bankCurrentPage + 1;
    if (this.bankTotalPage >= this.bankCurrentPage) {
      this.bankSerialNumber = (this.bankCurrentPage - 1) * 10;
      this.getDoctorsBankAccountList(this.bankCurrentPage);
    } else {
      this.bankCurrentPage = this.bankCurrentPage - 1;
    }
  }

  bankLastPage() {
    this.bankSerialNumber = (this.bankTotalPage - 1) * 10;
    this.getDoctorsBankAccountList(this.bankTotalPage);
  }

  bankFirstPage() {
    this.bankSerialNumber = 0;
    this.bankCurrentPage = 1;
    this.getDoctorsBankAccountList(this.bankCurrentPage);
  }

  bankPreviousPage() {
    this.bankCurrentPage = this.bankCurrentPage - 1;
    if (this.bankTotalPage >= this.bankCurrentPage && this.bankCurrentPage > 0) {
      this.bankSerialNumber = (this.bankCurrentPage - 1) * 10;
      this.getDoctorsBankAccountList(this.bankCurrentPage);
    } else {
      this.bankCurrentPage = this.bankCurrentPage + 1;
    }
  }

  deleteDepartment(val: number, data?: any,) {
    if (val == 2) {
      this.platformService.deleteDeptLst(this.deleteDepartmentId, this.deleteDepartmentName).subscribe(
        (data) => {
          // console.log(data);
          this.notificationService.success(`${data['message']}`, 'Med.Bot');
          this.getDepartment(this.currentPage);
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          } else if (status == 409) {
            this.notificationService.error(`${error['error']['message']}`, 'Med.Bot');
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        })
      $('#deleteModal').modal('hide');
    }
    else {
      this.deleteDepartmentId = data.department_id;
      this.deleteDepartmentName = data.department_value;
      this.deleteDepartmentHspt = data.hospital;
      $('#deleteModal').modal('show');
    }
  }

  addDepartment(val) {
    $('#addDeptModal').modal('show');
    if (val == 1) {
      this.onlySOM = true;
    }
    else {
      this.onlySOM = false;
    }

  }

  selectSOM(data) {
    this.createDeptPlatformAdminForm.get('newSOM').setValue(data);
  }

  getDepartmnetData() {
    this.doctorService.getDepartment().subscribe(
      (data) => {
        console.log("data : ", data);
        this.department = data;
        const keysArray = Object.keys(data);
        this.systemOfMedicine = keysArray;
        // this.notificationService.success(`${data['message']}`, 'Med.Bot');
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  createNewDept() {
    const formData = new FormData;
    formData.append('medical_system_name', this.createDeptPlatformAdminForm.value.newSOM);
    if (this.onlySOM == false) {
      formData.append('department_code', this.createDeptPlatformAdminForm.value.newDepCode);
      formData.append('department_value', this.createDeptPlatformAdminForm.value.newDept);
    }
    else {
      formData.append('department_code', '');
      formData.append('department_value', '');
    }
    // formData.append('hospital_id', this.hospitalId);
    this.hospitalService
      .createHospitalDept(formData)
      .subscribe(
        (data) => {
          this.createDeptFormReset(1);
          this.notificationService.success(`${data['message']}`, 'Med.Bot');
          this.getDepartment(this.currentPage);
        },
        (err) => {
          const status = err['status'];
          if (status == 400) {
            this.notificationService.error(`${err.error['error_message']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${err.error['message']}`, 'Med.Bot');
          }
          console.log(err);
          this.createDeptFormReset();
        }
      );
  }

  createDeptFormReset(val?) {
    this.createDeptPlatformAdminForm.get('newSOM').setValue('');
    this.createDeptPlatformAdminForm.get('newDepCode').setValue('');
    this.createDeptPlatformAdminForm.get('newDept').setValue('');
    this.getDepartmnetData();
    $('#addDeptModal').modal('hide');
  }

  viewSettings(id: string) {
    this.selectedHospital = id;
    this.router.navigate([`hospital-settings/${this.selectedHospital}/view`]);
  }

  report() {
    localStorage.removeItem("report");
    localStorage.setItem("report", "platform");
    this.router.navigate([`reports`]);
  }
  checkAvailability() {
    let url = this.hospitalCreationForm.get('user.custom_url').value;
    if (url == '' || url == undefined || url == null) {
      this.notificationService.warning('Provide value for Login URL');
    } else {
      let login_url = this.hospitalCreationForm.get('user.url_prefix').value +
        this.hospitalCreationForm.get('user.custom_url').value +
        this.hospitalCreationForm.get('user.url_domain').value;
      this.hospitalCreationForm.get('login_url').setValue(login_url);
      this.platformService.checkAvailability(login_url).subscribe((result: any) => {
        console.log(result);
        this.urlAvailableStatus = 'Available';
      }, (error) => {
        console.log(error);
        this.urlAvailableStatus = 'Not Available';
      });
    }
  }
}
