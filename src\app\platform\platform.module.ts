import { HttpClient } from '@angular/common/http';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlatformAdminComponent } from './platform-admin/platform-admin.component';
import { SharedModule } from '../shared/shared.module';
import { ApprovalSuccessComponent } from './approval-success/approval-success.component';
import { DoctorIdentityDetailsComponent } from './doctor-identity-details/doctor-identity-details.component';
import { HttpLoaderFactory } from '../doctor/doctor.module';
import { RegistrationsComponent } from './doctor-identity-details/registrations/registrations.component';
import { BasicProfileComponent } from './doctor-identity-details/basic-profile/basic-profile.component';
import { QualificationsComponent } from './doctor-identity-details/qualifications/qualifications.component';
import { HospitalDetailsComponent } from './hospital-details/hospital-details.component';
import { HospitalBankAccountsComponent } from './hospital-details/hospital-bank-accounts/hospital-bank-accounts.component';
import { HospitalAddressComponent } from './hospital-details/hospital-address/hospital-address.component';
import { CKEditorModule } from 'ng2-ckeditor';
import { DatepickerModule, BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { PatientIdentityDetailsComponent } from './patient-identity-details/patient-identity-details.component';
import { EmergencyContactComponent } from './patient-identity-details/emergency-contact/emergency-contact.component';
import { ConsultationSummaryComponent } from './consultation-summary/consultation-summary.component';
import { BankDetailComponent } from './bank-detail/bank-detail.component';

@NgModule({
  declarations: [
    PlatformAdminComponent,
    ApprovalSuccessComponent,
    DoctorIdentityDetailsComponent,
    RegistrationsComponent,
    BasicProfileComponent,
    QualificationsComponent,
    HospitalDetailsComponent,
    HospitalBankAccountsComponent,
    HospitalAddressComponent,
    PatientIdentityDetailsComponent,
    EmergencyContactComponent,
    ConsultationSummaryComponent,
    BankDetailComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    FormsModule,
    NgSelectModule,
    NgMultiSelectDropDownModule,
    RouterModule,
    ReactiveFormsModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    CKEditorModule,
    BsDatepickerModule.forRoot(),
    DatepickerModule.forRoot(),
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class PlatformModule { }
