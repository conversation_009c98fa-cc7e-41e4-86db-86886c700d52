import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as Settings from './../config/settings';
import { delay } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { DataSource } from '@angular/cdk/table';
// import { settings } from 'cluster';

@Injectable({
  providedIn: 'root',
})
export class PlatformService {
  constructor(private httpClient: HttpClient) { }

  getCountryDetail() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/countries/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  // getDoctorQualification(){
  //   return this
  //   .httpClient
  //   .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/me/qualifications/`)
  //   .pipe(delay(Settings.REQUEST_DELAY_TIME));
  // }

  onApprove(approvedDoctors): Observable<any> {
    // this.httpClient.post('${Settings.API_PLATFORM_URL_PREFIX}/api/platform/')
    let str = new String();
    return this.httpClient.get('http://test.com');
  }

  getApprovalMessages() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/?approval_request_status=Pending`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }

  getDoctorDetails(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/profile/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorasDetails(doctorasst_uuid) {
    return this
      .httpClient
      .get(`${Settings.API_AUTH_URL_PREFIX}/api/auth/users/${doctorasst_uuid}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getPartnerDetails(partner_uuid) {
    return this
      .httpClient
      .get(`${Settings.API_AUTH_URL_PREFIX}/api/auth/users/${partner_uuid}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }


  getDoctorQualifications(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/qualifications/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postDoctorQualification(uuid, data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/qualifications/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateDoctorQualification(uuid, qual_uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/qualifications/${qual_uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  deleteDoctorQualification(uuid, qual_uuid) {
    return this
      .httpClient
      .delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/qualifications/${qual_uuid}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDoctorRegistrations(uuid) {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/registrations/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorHaRegistrations(doc_uuid) {
    return this.httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doc_uuid}/registrations/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postDoctorRegistrations(uuid, data, registerfile) {
    const formData = new FormData();
    formData.append('file', registerfile);
    formData.append('data', JSON.stringify(data));
    console.log(formData);
    console.log('data post');
    console.log(data);
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/registrations/`, formData)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateDoctorProfile(uuid, data) {
    return this.httpClient.patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/profile/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  updateUserDetails(uuid, data) {
    return this.httpClient.patch(`${Settings.API_AUTH_URL_PREFIX}/api/auth/users/${uuid}/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  updateDoctorRegistration(uuid, reg_uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/registrations/${reg_uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  updateHaDoctorRegistration(uuid, reg_uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/registrations/${reg_uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }


  postDoctorRegistration(doc_uuid, uuid, data) {
    if (uuid === null) {
      return this.httpClient
        .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doc_uuid}/registrations/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }
    else {
      return this.httpClient
        .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${doc_uuid}/registrations/${uuid}/`, data)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
    }

  }

  acceptApproval(uuid) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/profile/approve/`, {})
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }

  hospitalacceptApproval(doc_uuid) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospitals/${doc_uuid}/profile/approve/`, {})
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }

  assistantacceptApproval(doc_uuid) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospitals/${doc_uuid}/profile/d_a/approve/`, {})
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  assistantacceptApprovalStatus(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospitals/${uuid}/profile/d_a/approve/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  hospitaldeclineApproval(doc_uuid, data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospitals/${doc_uuid}/profile/reject/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }


  declineApproval(uuid, data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/profile/reject/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postHospital(data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchHospital(uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getHospitals(pageNumber) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/?page=${pageNumber}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getHospitalDetail(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getHospitalAddresses(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/addresses/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postHospitalAddress(uuid, data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/addresses/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchHospitalAddress(uuid, addr_uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/addresses/${addr_uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getHospitalBankAccounts(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/bank_accounts/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postHospitalBankAccounts(uuid, data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/bank_accounts/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchHospitalBankAccounts(uuid, acct_uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/bank_accounts/${acct_uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  deleteHospitalBankAccounts(uuid, acct_uuid) {
    return this
      .httpClient
      .delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/${uuid}/bank_accounts/${acct_uuid}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getApprovedDoctor(pageNumber) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/admin-search/?approval_request_status=Approved&page=${pageNumber}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }
  getPendingDoctor(pageNumber) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/admin-search/?approval_request_status=Pending&page=${pageNumber}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }
  getRejectedDoctor(pageNumber) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/admin-search/?approval_request_status=Rejected&page=${pageNumber}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }
  getDoctorsList(number) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/admin-search/?page=${number}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }
  getDegree(data) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/degrees/?system_of_medicine=${data}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getCouncils(data) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/councils/?system_of_medicine=${data}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getPatientsList(number) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/p/ps/?page=${number}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }

  //  getPatientDetails(uuid){
  //    return this
  //      .httpClient
  //      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/p/profile/${uuid}/`)
  //      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  //  }

  getPatientDetails(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_AUTH_URL_PREFIX}/api/auth/users/${uuid}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getPatientContact(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/p/patient_admin/contacts/${uuid}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }

  postPatientContact(data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/p/patient_admin/postcontact/${data.uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getPatientAddrDetail(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_AUTH_URL_PREFIX}/api/auth/admin_patients/${uuid}/addresses/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postPatientAddrDetail(data) {
    return this
      .httpClient
      .post(`${Settings.API_AUTH_URL_PREFIX}/api/auth/patients/${data.uuid}/addresses/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  patchPatientAddrDetail(data) {
    return this
      .httpClient
      .patch(`${Settings.API_AUTH_URL_PREFIX}/api/auth/admin_patients/${data.uuid}/addresses/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  savePatientContact(uuid, data) {
    return this
      .httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/p/patient_admin/contacts/${uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }

  //  getPatientKYC(uuid){
  //    return this
  //      .httpClient
  //      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/p/patient_admin/k_documents/${uuid}`)
  //  }

  getPatientCreated(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/p/profile/${uuid}/`)
  }

  getAdminEarning() {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/admin-earnings/`)
  }

  getDoctorsBankAccountList(pageNumber) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/bank_accounts/?page=${pageNumber}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }
  getDoctorsDepartmentList(pageNumber) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/departments_list/?page=${pageNumber}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  deleteDeptLst(dept_id, dept_name) {
    return this
      .httpClient
      .delete(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/l/departments_delete/${dept_id}/?department_name=${dept_name}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getConsultationSummary(uuid, query) {
    return this
      .httpClient
      // .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/appointments/?page=${pageNumber}`)
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/summary/${query}`)
  }

  getBankAccountDetail(uuid) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/${uuid}/bank_accounts/`)
  }
  getConsultation(page, param?) {
    if (param == undefined || param == null) {
      param = "";
    }
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/c/consultations/?page=${page}` + `${param}`)
  }
  getConsultationForAdmin(page, param?) {
    if (param == undefined || param == null) {
      param = "";
    }
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/c/consultations/patient_list/?page=${page}` + `${param}`)
  }

  consultationArchiveDelete(param) {
    if (param == undefined || param == null) {
      param = "";
    }
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/c/consultations/archived-delete/`, param)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  createOTP(param?: any) {
    if (param == undefined || param == null) {
      param = "";
    }
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/auth/otp/consultation_history_otp/`, param)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  otpVerification(param: any) {
    if (param == undefined || param == null) {
      param = "";
    }
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/auth/otp/consultation_verify_otp/`, param)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  checkAvailability(value:any) {
    return this.httpClient
    .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospital_logo/?check_slug=${value}`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
}
