.pub-prof {
    background-color: #F8F9FA;
}

.card.left-pane {
    margin: 25px;
    margin-right: 5px;
    background-color: #FFFFFF;
    border: solid 1px #F2f2F2;
}

.img-fluid {
    margin-top: 10px;
    margin-bottom: 10px;
    border: solid 1px #F2F2F2;
    border-radius: 50px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 40%;
    height: auto;
}

.left-pane p {
    text-align: center;
    /* margin-bottom: 3px; */
}

.doc-profile-image {
    padding-bottom: 90px;
    padding-right: 10px;
    padding-left: 10px;
}

.card.right-pane {
    margin-left: 0px;
    margin-top: 25px;
    margin-right: 10px;
}

.doc-speciality.right-pane {
    font-weight: 600;
    margin-bottom: 0px;
}

.top-para {
    margin-left: 10px;
    margin-right: 10px;
}

.btn-info.sm {
    display: inline-block;
    background-color: #20C0F3;
    margin-right: 5px;
    font-size: 10px;
    padding-top: 0px;
    padding-bottom: 0px;
    border-radius: 75px;
}

.details {
    margin: 10px;
}

.btn-info.m {
    background-color: #009745;
    margin-top: 30px;
    border-radius: 75px;
    font-size: 15px;
    padding-top: 0px;
    padding-bottom: 0px;
}

.btn-danger.m {
    background-color: #da0404;
    margin-top: 30px;
    border-radius: 75px;
    font-size: 15px;
    padding-top: 0px;
    padding-bottom: 0px;
}

.txt-area {
    border: solid 2px #F2f2F2;
    width: 100%;
    height: 110px;
    border-radius: 10px;
}

.txt-area:focus {
    outline: none !important;
    border: 1px solid #20C0F3;
    box-shadow: 0 0 5px #719ECE;
}

p.doc-location {
    color: #20C0F3;
}

.sub-head {
    font-weight: 500px;
    font-size: 30px;
}

.clinic-head {
    font-weight: 600;
    font-size: 20px;
    margin-bottom: 0px;
    ;
}

.cont-para {
    margin: 25px;
}

.cont-text {
    font-size: 15px;
    margin-bottom: 0px;
}

.ch-details {
    margin-bottom: 35px;
}

.timing {
    display: inline;
}

.timing.day {
    font-weight: bold;
    font-size: 16px;
}

.cont-text.fee {
    font-size: 17px;
}

.btn-info.appoint {
    background-color: #FFFFFF;
    color: #6FD6F7;
    border: solid 1px #6FD6F7;
    width: 55%;
    padding-top: 0px;
    padding-bottom: 0px;
    padding-left: 2px;
    padding-right: 2px;
    line-height: 17px;
}
.three_chars{
  font-family: monospace;
  width: 5ch;
  overflow: hidden;
  white-space: nowrap;
}
.fas {
  color: #20c0f3;
  cursor: pointer;
}
/* .pagebg {
    background-color: #20c0f3;;
    background-size: 1028px 720px;
    text-align: center;
  } */

  /* *{
    transition: all 0.6s;
}

html {
    height: 70%;
    background-color: #F8F9FA;

}

body{
    font-family: 'Lato', sans-serif;
    color: rgb(255, 255, 255);
    margin: 0;
}*/

#main{
    display: table;
    width: 100%;
    height: 100vh;
    text-align: center;
}

.fof{
	  display: table-cell;
	  vertical-align: middle;
}
