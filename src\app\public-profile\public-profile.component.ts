import { Component, OnInit } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import { ActivatedRoute,Router } from '@angular/router'
import * as Settings from '../config/settings';
import { ToastrService } from 'ngx-toastr';
import { PublicProfileService } from './public-profile.service';
import * as moment from 'moment';

@Component({
  selector: 'app-public-profile',
  templateUrl: './public-profile.component.html',
  styleUrls: ['./public-profile.component.css']
})


export class PublicProfileComponent implements OnInit {
  usertype: string;
  slug: string;
  errorStatus:string;
  public doctor:any;
  page: boolean;
  public practiceLocations:any;
  public doctoruuid:any;
  showProfilePic=false
  specialization:any;
  degreeList:any;
  languages:any;
  profilePicture:any;
  doctorName: string;
  slots_availability: any;
  consultinghoursgrouping=[];
  test:any;
  instant_appointment_slot_available:boolean;
  pra_length:number;
  setSlideHtml:any;
  todayAvailable:number;
  constructor(private route: ActivatedRoute, 
              private http : HttpClient,
              private router: Router,
              private notificationService: ToastrService,
              private publicprofileservice: PublicProfileService) {}
  ngOnInit(): void {
    this.usertype='none';
    this.showProfilePic= false;
    this.slug = this.route.snapshot.paramMap.get('slug')

    this.publicprofileservice.getDoctorInformations(this.slug).subscribe(
      (data) => {
        this.showProfilePic=true;
        this.doctor=data;
        this.page=true;
        this.practiceLocations=this.doctor.practicelocations;
        this.slots_availability=this.doctor.user.slot_availability;
        this.specialization=this.doctor.speciality;
        this.degreeList=this.doctor.qualifications;
        this.languages=this.doctor.languages;
        this.doctoruuid=this.doctor.uuid;
        this.doctorName=this.doctor.user.username;
        this.todayAvailable=this.doctor.user.available_today;

        if (this.slots_availability != null){
          const today=moment().format('YYYY-MM-DD');
          this.pra_length = this.doctor.practicelocations.length 
          for(let i=0; i<this.pra_length; i++){
            for(const val of this.doctor['practicelocations'][i]['consultinghoursgroups']){
              if(val['effective_upto'] >= today){
                this.consultinghoursgrouping.push({consultation_duration:`${val.consultation_duration}`,days_of_the_week:val.days_of_the_week,
                                                  doctor:val.doctor,effective_from:val.effective_from,effective_upto:val.effective_upto,
                                                  hospital:val.hospital,time_from:val.time_from,time_to:val.time_to,timezone_name:val.timezone_name,uuid:val.uuid}) 
              }
              this.doctor['practicelocations'][i]['consultinghoursgroups'] = this.consultinghoursgrouping
            }
            this.consultinghoursgrouping = [];
          }
        }

        if(this.doctor.user.profile_picture != null){
          this.profilePicture = this.doctor.user.profile_picture;        
        }else{
          this.profilePicture =  'assets/img/doctors/doctor-thumb-02.png'
         }

        if(this.doctor.instant_availability.available != true){
           this.instant_appointment_slot_available = false;
        }else{
           this.instant_appointment_slot_available = true;
         }
        //  var div = document.getElementById('divhide');
        //  div.remove();   
      },
      (error) => {
        const status = error['status'];
        this.errorStatus=error['error']['error_message']
        if(status == 404){
          this.page = false;
          console.log(this.errorStatus);
 
          }
        else{
          this.notificationService.error('Could Not Get Doctor Information', 'Med.Bot');
          console.log(error['error']);
          }    
      })

//       this.publicprofileservice.getDoctorPage(this.slug).subscribe(
//         (data) => {
//           this.setSlideHtml = data;
//           this.test = document.getElementById('wrapper').innerHTML = this.setSlideHtml;
//           console.log(this.doctoruuid);

//           this.publicprofileservice.getDoctorInformations(this.slug).subscribe(
//             (data) => {
//               this.doctor=data;
//               let doctorId = this.doctor.uuid;
//               let practiceLocationId = this.doctor.practicelocations[0].uuid;
//               const apturl =  '/patient/appointment/' + doctorId + '/' + practiceLocationId + '/' +'booking'
//               const appointment = 'true';
// //              let loginroute = this.router.navigate(['/login']);
// //              let aptroute = this.router.navigate([apturl]);
//               // console.log(document.getElementById('book-appointment'));   
// //              document.getElementById('book-appointment').onclick = this.bookAppointment;    
//               document.getElementById('book-appointment').onclick = function(){
//               localStorage.setItem('appointmenturl', apturl);
//               localStorage.setItem('appointment', appointment);
//               let usertype = localStorage.getItem('user_type');

//               if ( usertype === 'Patient'){                 
// //                  aptroute;
//                   console.log('same usertype');
//               }else{
//                   // loginroute;
//                   console.log('diff usertype');
//                 }                
//                 console.log('button work');
//             }

//             },
//             (error) => {
//               console.log('error');
//             }
//           ) 
//         },
//         (error) => {
//           console.log(this.slug);
//         }
//       )


    }

     bookAppointment() {
       const doctorId = this.doctoruuid;
       console.log(this.practiceLocations[0].uuid);
       const practiceLocationId = this.practiceLocations[0].uuid;
       const apturl =  '/patient/appointment/' + doctorId + '/' + practiceLocationId + '/' +'booking'
       const appointment = 'appoint';
       localStorage.setItem('appointmenturl', apturl);
       localStorage.setItem('appointment', appointment);
       this.usertype = localStorage.getItem('user_type');

//      console.log(`${Settings.API_DOCTOR_URL_PREFIX}`);
       if ( this.usertype === 'Patient'){
        this.router.navigate([apturl]); 
       }else{
//       localStorage.setItem('direct', "false"); 
       this.router.navigate([
       '/login']);
       }
    }
    
    consultNow() {
      console.log('consult now');
      const query ='consult_now=true'+'&name='+this.doctorName +'&fee_lte='+ this.doctor.consultationfees[0].amount;
      const page=1;
      const conurl = '/patient/search/' + page + '/' + query;
      const appointment = 'consult';
      localStorage.setItem('consulturl', conurl);
      localStorage.setItem('appointment', appointment);
      this.usertype = localStorage.getItem('user_type');      
       if ( this.usertype === 'Patient'){
        this.router.navigate([conurl]); 
       }else{
//       localStorage.setItem('direct', "false"); 
       this.router.navigate([
       '/login']);
       }
    }


}
