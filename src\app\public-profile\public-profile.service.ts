import { HttpClient, HttpHeaders} from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as Settings from './../config/settings';
import { delay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class PublicProfileService { 

    constructor(private httpClient: HttpClient) {}
    
    getDoctorInformations(slug){
        return this
        .httpClient
        .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/public_profile/${slug}`)
        .pipe(delay(Settings.REQUEST_DELAY_TIME));
      }
    // getDoctorPage(slug){
    //   const headers: HttpHeaders = new HttpHeaders({'Accept': 'text/html'});
    //   return this
    //   .httpClient
    //   .get(`${Settings.API_DOCTOR_URL_PREFIX}/${slug}`, { headers: headers, responseType: 'text' })
    //   .pipe(delay(Settings.REQUEST_DELAY_TIME))

    // }  
            
    }  
