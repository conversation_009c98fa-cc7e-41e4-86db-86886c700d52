<div class="appointment-tab appointment-tabs">
  <!-- Appointment Tab -->
  <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
    <li class="nav-item">
      <a id="dash-ba-today" class="nav-link active nm-size" href="#appointments" data-toggle="tab"
        (click)="tabSelection('ongoing')">
        <h5>Ongoing</h5>
      </a>
    </li>
   
    <li class="nav-item">
      <a id="dash-ba-today" class="nav-link nm-size" href="#appointments" data-toggle="tab"
        (click)="tabSelection('today-pending')">
        <h5>Today-Pending</h5>
      </a>
    </li>
    <li class="nav-item">
      <a id="dash-ba-upcomming" class="nav-link nm-size" href="#appointments" data-toggle="tab"
        (click)="tabSelection('upcoming')">
        <h5>Upcoming</h5>
      </a>
    </li>
    <li class="nav-item">
      <a id="dash-ba-today" class="nav-link nm-size" href="#appointments" data-toggle="tab"
        (click)="tabSelection('today-missed')">
        <h5>Today-Missed</h5>
      </a>
    </li>
    <li class="nav-item">
      <a id="dash-ba-today" class="nav-link nm-size" href="#appointments" data-toggle="tab"
        (click)="tabSelection('today-completed')">
        <h5>Today-Completed</h5>
      </a>
    </li>
    <li class="nav-item">
      <a id="dash-ba-past" class="nav-link nm-size" href="#appointments" data-toggle="tab"
        (click)="tabSelection('past-appointments')">
        <h5>Past Appointments</h5>
      </a>
    </li>
    <li class="nav-item" *ngIf="userType=='Doctor'||userType=='Patient'">
      <a id="dash-ba-past" class="nav-link nm-size" href="#appointments" data-toggle="tab"
        (click)="tabSelection('today-instant-completed')">
        <h5>Today Instant Completed</h5>
      </a>
    </li>
    <li class="nav-item">
      <a id="dash-ba-past" class="nav-link nm-size" href="#appointments" data-toggle="tab"
        (click)="tabSelection('past-instant')">
        <h5>Past-Instant</h5>
      </a>
    </li>
  </ul>
  <div class="tab-content">
    <div id="appointments" class="tab-pane active">
      <app-appointment-table [tabValue]="tabValue"></app-appointment-table>
    </div>
  </div>