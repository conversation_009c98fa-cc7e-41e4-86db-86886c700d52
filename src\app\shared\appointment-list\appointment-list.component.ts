import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-appointment-list',
  templateUrl: './appointment-list.component.html',
  styleUrls: ['./appointment-list.component.css']
})
export class AppointmentListComponent implements OnInit {
  tabValue: string = '';
  userType: string;

  constructor() { }

  ngOnInit(): void {
    this.userType = localStorage.getItem('user_type');
    this.tabSelection('ongoing');
  }

  tabSelection(val: string) {
    this.tabValue = val;
  }

}
