<div *ngIf="loading">
  <app-loading-spinner></app-loading-spinner>
</div>

<div class="card" *ngIf="!loading">
  <div class="col-md-12 mt-4" *ngIf="tabValue == 'today-pending'">
    <span *ngIf="appointments.length > 0">
      <b>Note: </b>You will be able to join the consultation 10 minutes before the appointment time</span>
  </div>
  <div class="col-md-12 mb-4">
    <div class="col-md-12 float-right mt-4">
      <div class="float-right">
        <nav aria-label="Page navigation example" *ngIf="appointmentsTotalPage > 1">
          <ul class="pagination pager_position">
            <li class="page-item" (click)="firstPage()" [ngClass]="{
              'disabled-pagination':
              appointmentsCurrentPage === 1
            }">
              <a class="page-link">&lt;&lt;</a>
            </li>
            <li class="page-item" (click)="previousPage()" [ngClass]="{
              'disabled-pagination':
              appointmentsCurrentPage === 1
            }">
              <a class="page-link">&lt;</a>
            </li>
            <li class="page-item">
              <a class="page-link">page &nbsp;{{
                appointmentsCurrentPage
                }}&nbsp;of&nbsp;
                {{ appointmentsTotalPage }}</a>
            </li>
            <li class="page-item" (click)="nextPage()" [ngClass]="{
              'disabled-pagination':
              appointmentsCurrentPage ===
              appointmentsTotalPage
            }">
              <a class="page-link">&gt;</a>
            </li>
            <li class="page-item" (click)="lastPage()" [ngClass]="{
              'disabled-pagination':
              appointmentsCurrentPage ===
              appointmentsTotalPage
            }">
              <a class="page-link">&gt;&gt;</a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
    <div class="card card-table mb-0">
      <div class="card-body">
        <div class="table-responsive" *ngIf="appointments.length > 0">
          <table class="table table-hover table-center mb-0">
            <thead>
              <tr>
                <th class="text-left">
                  <h5>Date</h5>
                </th>
                <th class="text-left"
                  *ngIf="tabValue != 'past-instant' && tabValue != 'pending-instant' && tabValue !='today-instant-completed'">
                  <h5>Time</h5>
                </th>
                <th *ngIf="tabValue == 'past-appointments' || tabValue == 'today-missed'">
                  <h5>Consultation Status</h5>
                </th>
                <th class="text-left" *ngIf="userType!='Doctor'">
                  <h5>Doctor Name</h5>
                </th>
                <th class="text-left" *ngIf="userType!='Patient'&& userType!='Partner'">
                  <h5>Patient Name</h5>
                </th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let appt of appointments, let i=index">
                <td>
                  <ng-template *ngIf="(tabValue == 'ongoing'); then ongoing else elsePart">
                  </ng-template>
                  <ng-template #ongoing>
                    {{ appt.start_datetime | date: "mediumDate" }}
                  </ng-template>
                  <ng-template #elsePart>
                    <ng-container
                      *ngIf="tabValue != 'past-instant' && tabValue != 'pending-instant' && tabValue !='today-instant-completed'">
                      {{ appt.start_datetime | date: "mediumDate" }}
                    </ng-container>
                    <ng-container
                      *ngIf="tabValue == 'past-instant' || tabValue == 'pending-instant' || tabValue == 'today-instant-completed'">
                      {{ appt.request_datetime | date: "mediumDate" }}
                      <span class="d-block text-info">{{ appt.request_datetime | date: "hh:mm a" }}</span>
                    </ng-container>
                  </ng-template>
                </td>

                <td
                  *ngIf="(tabValue != 'past-instant' && tabValue != 'pending-instant' && tabValue != 'today-instant-completed') || tabValue == 'ongoing'">
                  <ng-template *ngIf="(tabValue == 'ongoing'); then truePart else elsePart">
                  </ng-template>
                  <ng-template #truePart>
                    <ng-container *ngIf="appt['is_instant_consult'] == false">
                      {{ appt.start_datetime | date: "hh:mm a" }} to {{ appt.end_datetime | date: "hh:mm a" }}
                    </ng-container>
                    <ng-container *ngIf="appt['is_instant_consult'] == true">
                      {{ appt.start_datetime | date: "hh:mm a" }}
                    </ng-container>
                  </ng-template>
                  <ng-template #elsePart>
                    <ng-container
                      *ngIf="(tabValue != 'past-instant' && tabValue != 'pending-instant' && tabValue != 'today-instant-completed')">
                      {{ appt.start_datetime | date: "hh:mm a" }} to {{ appt.end_datetime | date: "hh:mm a" }}
                    </ng-container>
                  </ng-template>
                </td>

                <td *ngIf="tabValue == 'past-appointments' || tabValue == 'today-missed'">{{appt["fulfilment_status"]}}
                </td>

                <td *ngIf="userType=='DoctorAssistant'">
                  <ng-template *ngIf="tabValue == 'ongoing'; then truePart else elsePart">
                  </ng-template>
                  <ng-template #truePart>
                    <h2 class="table-avatar" *ngIf="appt && appt['doctor_json']">
                      <img class="avatar-img rounded-circle avatar avatar-sm mr-2"
                        [src]="appt['doctor_json']['user']['profile_picture']=== null ? defalutPicture : appt['doctor_json']['user']['profile_picture']" />
                      {{ appt["doctor_json"]['user']["username"] }}
                    </h2>
                  </ng-template>
                  <ng-template #elsePart>
                    <h2 class="table-avatar" *ngIf="appt && appt['doctor_user_json']">
                      <img class="avatar-img rounded-circle avatar avatar-sm mr-2"
                        [src]="appt['doctor_user_json']['profile_picture'] === null ? defalutPicture : appt['doctor_user_json']['profile_picture']" />
                      {{ appt["doctor_user_json"]["username"] }}
                    </h2>
                  </ng-template>
                </td>

                <td>
                  <ng-template *ngIf="(userType == 'Patient'||userType == 'Partner'); then patientPart else doctorPart">
                  </ng-template>
                  <ng-template #patientPart>
                    <ng-template *ngIf="tabValue == 'ongoing'; then truePart else elsePart">
                    </ng-template>
                    <ng-template #truePart>
                      <h2 class="table-avatar" *ngIf="appt && appt['doctor_json']">
                        <img class="avatar-img rounded-circle avatar avatar-sm mr-2"
                          [src]="appt['doctor_json']['user']['profile_picture']=== null ? defalutPicture : appt['doctor_json']['user']['profile_picture']" />
                        {{ appt["doctor_json"]['user']["username"] }}
                      </h2>
                    </ng-template>
                    <ng-template #elsePart>
                      <h2 class="table-avatar" *ngIf="appt && appt['doctor_user_json']">
                        <img class="avatar-img rounded-circle avatar avatar-sm mr-2"
                          [src]="appt['doctor_user_json']['profile_picture'] === null ? defalutPicture : appt['doctor_user_json']['profile_picture']" />
                        {{ appt["doctor_user_json"]["username"] }}
                      </h2>
                    </ng-template>
                  </ng-template>
                  <ng-template #doctorPart>
                    <ng-template *ngIf="tabValue == 'ongoing'; then truePart else elsePart">
                    </ng-template>
                    <ng-template #truePart>
                      <ng-container *ngIf="appt && appt['patient_json']">
                        <h2 class="table-avatar">
                          <img class="avatar-img rounded-circle avatar avatar-sm mr-2"
                            [src]="appt['patient_json']['profile_picture']===null ? defalutPicture : appt['patient_json']['profile_picture']" />
                          {{ appt["patient_json"]["username"] }}
                        </h2>
                        <a href="javascript:void(0);" class="btn btn-sm bg-info-light sm-size ml-2"
                          (click)="viewPatient(appt['patient_json'],appt['consultation_uuid'],appt)"
                          *ngIf="btnPermission('view-patient', appt['fulfilment_status'])">
                          <i class="far fa-eye"></i> View Patient
                        </a>
                      </ng-container>
                    </ng-template>
                    <ng-template #elsePart>
                      <ng-container *ngIf="appt && appt['patient_user_json']">
                        <h2 class="table-avatar">
                          <img class="avatar-img rounded-circle avatar avatar-sm mr-2"
                            [src]="appt['patient_user_json']['profile_picture']===null ? defalutPicture : appt['patient_user_json']['profile_picture']" />
                          {{ appt["patient_user_json"]["username"] }}
                        </h2>
                        <a href="javascript:void(0);" class="btn btn-sm bg-info-light sm-size ml-2"
                          (click)="viewPatient(appt['patient_user_json'],appt['consultation_uuid'])"
                          *ngIf="btnPermission('view-patient', appt['fulfilment_status'])">
                          <i class="far fa-eye"></i> View Patient
                        </a>
                      </ng-container>
                    </ng-template>
                  </ng-template>
                </td>

                <td class="text-left en-size">
                  <div class="table-action">
                    <button class="btn app-btn btn-sm sm-size ml-2"
                      [ngClass]="(appt['disable'] && appt['disable'] == true) ? 'bg-primary-light text-dark' : 'bg-primary text-white'"
                      [disabled]="appt['disable']" *ngIf="btnPermission('consult', appt['fulfilment_status'])"
                      (click)="onConsult((tabValue == 'ongoing') ? appt.uuid : appt.consultation_uuid, appt.doctor, appt.consultation_type,appt)">
                      <i class="fas fa-user-md"></i> Consult
                    </button>
                    <a href="javascript:void(0);" class="btn app-btn btn-sm bg-success text-white sm-size ml-2"
                      *ngIf="btnPermission('consultation-details', appt['fulfilment_status'])"
                      (click)="viewConsultHistory(appt['patient'],appt['fulfilment_status'],appt['doctor'],(tabValue == 'ongoing') ? appt.uuid : appt.consultation_uuid)">
                      <i class="fa fa-notes-medical"></i> Consultation Details
                    </a>
                    <button id="upcomming_apt_cancel{{i}}" class="btn app-btn btn-sm bg-danger-light sm-size ml-2"
                      data-toggle="modal" *ngIf="btnPermission('cancel', appt['fulfilment_status'])"
                      (click)="cancelAppointment(appt['uuid'], 'upcomming',appt['cancelButtonStatus'])">
                      <i class="fas fa-times"></i> Cancel
                    </button>
                    <button class="btn app-btn btn-sm bg-secondary text-white sm-size ml-2"
                      *ngIf="btnPermission('upload', appt['fulfilment_status'])" data-toggle="modal"
                      data-target="#upload-report2" (click)="getReportId(appt,'upload')">
                      <i class="fa fa-upload"></i> Uploads
                    </button>
                    <a href="javascript:void(0);" class="btn app-btn btn-sm bg-secondary text-white sm-size ml-2"
                      *ngIf="btnPermission('view-reports', appt['fulfilment_status'], (appt['medical_report']?true:false))"
                      (click)="getReports((tabValue == 'ongoing') ? appt.uuid : appt.consultation_uuid)">
                      <i class="fas fa-file-prescription"></i> View Reports
                    </a>
                    <button class="btn app-btn bg-info btn-sm sm-size ml-2"
                      [ngClass]="(appt['reSchedule'] && appt['reSchedule'] == true) ? 'light-yellow-bg text-white' : 'dark-gray-bg  text-white'"
                      [disabled]="appt['reSchedule']"
                      *ngIf="btnPermission('re-schedule', appt['fulfilment_status'], appt['reSchedule'])"
                      (click)="reSchedule('initiate', appt)">
                      <i class="fas fa-user-md"></i> Reschedule
                    </button>
                    <button class="btn app-btn btn-sm bg-warning text-dark sm-size ml-2"
                      *ngIf="btnPermission('share', appt['fulfilment_status'])" data-target="#upload-report2"
                      (click)="shareReport((tabValue == 'ongoing') ? appt.uuid : appt.consultation_uuid, appt.doctor_uuid)">
                      <i class="fa fa-share-square"></i> {{appt['is_shared']?'Shared History':'Share History' }}
                    </button>
                    <button class="btn app-btn btn-sm bg-secondary text-white sm-size ml-2"
                      *ngIf="btnPermission('patient-history', appt['fulfilment_status'],appt['is_shared'])"
                       (click)="getReportId(appt,'history')">
                      <i class="fa fa-history"></i> Patient History
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="text-center mb-2 p-2 mt-2">
          <p id="no-data" *ngIf="appointments.length == 0">
            No Appointments Available
          </p>
        </div>
      </div>
    </div>
    <div class="float-right mt-3" *ngIf="appointmentsTotalPage > 1">
      <nav aria-label="Page navigation example" *ngIf="appointmentsTotalPage > 1">
        <ul class="pagination">
          <li class="page-item" (click)="firstPage()" [ngClass]="{
        'disabled-pagination':
        appointmentsCurrentPage === 1
      }">
            <a class="page-link">&lt;&lt;</a>
          </li>
          <li class="page-item" (click)="previousPage()" [ngClass]="{
        'disabled-pagination':
        appointmentsCurrentPage === 1
      }">
            <a class="page-link">&lt;</a>
          </li>
          <li class="page-item">
            <a class="page-link">page &nbsp;{{
              appointmentsCurrentPage
              }}&nbsp;of&nbsp;
              {{ appointmentsTotalPage }}</a>
          </li>
          <li class="page-item" (click)="nextPage()" [ngClass]="{
        'disabled-pagination':
        appointmentsCurrentPage ===
        appointmentsTotalPage
      }">
            <a class="page-link">&gt;</a>
          </li>
          <li class="page-item" (click)="lastPage()" [ngClass]="{
        'disabled-pagination':
        appointmentsCurrentPage ===
        appointmentsTotalPage
      }">
            <a class="page-link">&gt;&gt;</a>
          </li>
        </ul>
      </nav>
    </div>
  </div>

  <!--Patient Modal starts here -->
  <div class="modal" id="patientModal1" *ngIf="patientDetail">
    <div class="modal-dialog">
      <div class="modal-content">
        <!-- Modal Header -->
        <div class="modal-header">
          <h4 class="modal-title">{{ patientDetail["username"] }}</h4>
          <button type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <!-- Modal body -->
        <div class="modal-body">
          <img class="avatar-img modal-img rounded-circle" *ngIf="!patientDetail['profile_picture']"
            src="../../../assets/img/doctors/doctor-thumb-02.png" />
          <img class="avatar-img modal-img rounded-circle" *ngIf="patientDetail['profile_picture']"
            [src]="patientDetail['profile_picture']" />
          <p>
            Email:
            <a href="javascript:void(0);">{{ patientDetail["email"] }}</a>
          </p>
          <p>Contact No: {{ patientDetail["phone"] }}</p>
          <p>Age : {{ patientDetail["age"] }}</p>
          <p>Gender : {{ patientDetail["gender"] }}</p>
          <p *ngIf="btnPermission('pre-consulting-notes')">Pre Consultation Notes : {{ patientNotes }}&nbsp;
            <i (click)="showNotes(patientNotes)" class="fa fa-edit" *ngIf="btnPermission('edit-notes')"></i>
          </p>
          <textarea *ngIf="notes" [(ngModel)]="updatedNotes" placeholder="Notes for Doctor"></textarea>
        </div>
        <!-- Modal footer -->
        <div class="modal-footer text-center">
          <button type="button" class="btn btn-danger" data-dismiss="modal">
            Close
          </button>
          <button *ngIf="notes" type="button" class="btn btn-primary" data-dismiss="modal" (click)="saveNotes()">
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
  <!--Patient Modal ends here -->

  <!-- upload modal starts here -->
  <div class="modal fade" id="upload-report2">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Report</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-4">
              <ng-select id="reportType" [items]="reportTypes" [formControl]="selectedDiagnosticReportName"
                [clearable]="false" [searchable]="false" bindLabel="testType" bindValue="id" placeholder="Report Type"
                (change)="getReportType($event)">
              </ng-select>
            </div>
            <div class="col-md-4 mb-1">
              <input type="text" [maxDate]="maxDate" [minDate]="minDate" placeholder="Report Generated On"
                onkeydown="return false" class="form-control" [(ngModel)]="reportDate" bsDatepicker [bsConfig]="{
              showWeekNumbers: false,
              isAnimated: true,
              dateInputFormat: 'DD-MM-YYYY'
            }" />
            </div>
            <div class="col-md-4 mb-1">
              <div class="change-photo-btn" style="
              padding-left: 5px !important;
              margin-left: 0px;
              width: 138px;
              font-family: sans-serif;
            ">
                <span><i class="fa fa-upload ic">&nbsp;Choose File</i></span>
                <input type="file" class="upload" id="medical-report" (change)="medicalReports($event)"
                  accept=".jpg, .jpeg,.pdf" />
              </div>
              <small class="form-text text-muted" translate>Max size of 2MB</small>
              <small *ngIf="reportName">&nbsp;{{ reportName }}</small>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="col-md-12 text-center">
            <button class="btn btn-primary" (click)="saveMedicalReport()"
              [disabled]="!selectedDiagnosticReportName || !reportFile">
              Save
            </button>
            <button type="button" class="btn btn-secondary ml-2" data-dismiss="modal">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- upload modal ends here -->

  <!-- cancel confirmation modal -->
  <div class="modal fade" id="cancelConfirmModal1">
    <div class="modal-dialog modal-confirm">
      <div class="modal-content">
        <div class="modal-header">
          <div class="icon-box">
            <i class="material-icons">&#xE5CD;</i>
          </div>
          <h4 class="modal-title model-header-alinement">Are you sure?</h4>
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
            &times;
          </button>
        </div>
        <div class="modal-body">
          <p>Do you really want to cancel this appointment ?</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-info" data-dismiss="modal" id="cancel-stop">
            No
          </button>
          <button type="button" class="btn btn-danger" (click)="confirmCancel()" id="proceed-cancel">
            Yes
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- cancel confirmation modal -->

  <!--Reschedule Modal starts here -->
  <div class="modal" id="reschedule" >
    <div class="modal-dialog">
      <div class="modal-content">
        <!-- Modal Header -->
        <div class="modal-header">
          <h4 class="modal-title">Reschedule</h4>
          <button type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <!-- Modal body -->
        <div class="modal-body">
          <p>
            Are you sure want to reschedule this appointment?
          </p>
          <p>
            Doctor:{{ (reScheduleData["doctor_user_json"]!=undefined?reScheduleData["doctor_user_json"]["username"]:'') }}
          </p>
          <p *ngIf="userType!='pattient' && patient_user_json">
            Patient:{{ (reScheduleData["patient_user_json"]!=undefined?reScheduleData["patient_user_json"]["username"]:'') }}
          </p>
          <p>
            Date:{{ reScheduleData.start_datetime | date: "mediumDate" }}
          </p>
          <p>
            Time:{{ reScheduleData.start_datetime | date: "hh:mm a" }} to {{ reScheduleData.end_datetime | date: "hh:mm
            a" }}
          </p>
          <!-- Modal footer -->
          <div class="modal-footer text-center">
            <button type="button" class="btn btn-danger" data-dismiss="modal">
              Cancel
            </button>
            <button type="button" class="btn btn-primary" data-dismiss="modal" (click)="reSchedule('confirm')">
              Confirm
            </button>
          </div>
        </div>
      </div>
    </div>
    <!--Reschedule Modal ends here -->
  </div>

  <!-- onconsult close previous consultation popup starts -->
  <div class="modal" tabindex="-1" role="dialog" id="consultation-complete1" *ngIf="userType=='Doctor'">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Pending Consultation</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <p>Please complete/end the pending consultation to proceed to new consultation</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="endConsultation()">End
            Consultation</button>
          <button type="button" class="btn btn-primary" (click)="docConsult(consultationUuid)">Resume
            Consultation</button>
        </div>
      </div>
    </div>
  </div>
  <!-- onconsult close previous consultation popup starts -->

  <div class="modal fade bd-example-modal-lg" id="reports" tabindex="-1" role="dialog"
    aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="col-md-12 mt-2">
          <div class="card">
            <table class="table table-hover table-responsive table-center mb-0">
              <thead>
                <tr>
                  <th>No</th>
                  <th>Report Type</th>
                  <th>File Name</th>
                  <th>Report Generated On</th>
                  <th>Uploaded On</th>
                  <th>Action</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let file of reportFiles; let i = index">
                  <td>{{ i + 1 }}</td>
                  <td>{{ file.medical_report_type}}</td>
                  <td>{{ file.file_name }}</td>
                  <td>{{ file.report_generated_on | date:'mediumDate'}}</td>
                  <td>{{ file.created_at | date:'mediumDate'}}</td>
                  <td>
                    <button class="btn btn-primary btn-sm btn-msg" (click)="openFile(file['file'])" data-toggle="modal">
                      View Report
                    </button>
                  </td>
                  <td></td>
                  <td></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>