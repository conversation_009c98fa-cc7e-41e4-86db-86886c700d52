import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import * as moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { DoctorService } from 'src/app/doctor/doctor.service';
import { PatientService } from 'src/app/patient/patient.service';
import * as Settings from '../../config/settings';
import { NavigationEnd, Router } from '@angular/router';
import { DoctorAssistantService } from 'src/app/doctor-assistant/doctor-assistant-service';
import { FormControl } from '@angular/forms';
import { TeleConsultService } from 'src/app/tele-consult/tele-consult.service';

declare var $;

@Component({
  selector: 'app-appointment-table',
  templateUrl: './appointment-table.component.html',
  styleUrls: ['./appointment-table.component.css']
})
export class AppointmentTableComponent implements OnInit {
  @Input() tabValue: string;
  userType: string = '';
  current_user_uuid: string = '';
  doctorAppointments: any;
  appointmentsTotalPage: number;
  appointmentsCurrentPage: number = 1;
  appointments: any = '';
  isLoading: boolean;
  serialNumber: number;
  defalutPicture = "assets/img/doctors/doctor-thumb-02.png";
  patientDetail: any = '';
  patientNotes: string;
  reportFiles: unknown[];
  cancelBtnDisabled: boolean;
  availableAppointmenList: any[];
  notes: string = '';
  updatedNotes: string = '';
  consultMsgId: string = '';
  public minTime = Settings.consultaion_left_margin;
  public loading = false;
  public currentDate = moment();
  consultationUuid: any;
  reportPatientUuid: any;
  reportTypes = [
    { id: 'Pathology Test', testType: 'Pathology Test' },
    { id: 'Prescription', testType: 'Prescription' },
    { id: 'X-Ray', testType: 'X-Ray' },
    { id: 'Ultra Sound Scan', testType: 'Ultra Sound Scan' },
    { id: 'CT Scan', testType: 'CT Scan' },
    { id: 'MRI Scan', testType: 'MRI Scan' },
    { id: 'Prescription', testType: 'Prescription' },
    { id: 'Other', testType: 'Other' },
  ];
  public selectedDiagnosticReportName = new FormControl(null);
  maxDate: Date;
  minDate: any;
  reportFile: any;
  reportName: any;
  showUploading: boolean;
  reportDate: null;
  cancelAppointmentId: any;
  reScheduleData: any = {};
  isRescheduleDisabled: boolean = false;
  reScheduleHours = Settings.RESCHEDULE_DISABLE_TIME;
  public available_now = JSON.parse(localStorage.getItem('available_now'));
  viewConsultUuid: any;
  reSchedulePopup: boolean = false;

  constructor(private doctorService: DoctorService, private notificationService: ToastrService,
    private patientService: PatientService, private router: Router, private doctorAssistantService: DoctorAssistantService,
    private teleConsultService: TeleConsultService
  ) {
  }

  ngOnInit(): void {
    this.loading = true;
    this.userType = localStorage.getItem('user_type');
    this.current_user_uuid = localStorage.getItem("current_user_uuid");
    this.setSearchWithParams(this.tabValue, 1);
    setInterval(() => {
      if (this.tabValue === 'today-pending') {
        // this.appointmentButtonEnableAndDisable();
        this.setSearchWithParams(this.tabValue, 1);
      }
    }, 60000);
    if (this.userType == 'Doctor') {
      this.doctorService.getDoctorProfile().subscribe(
        (data) => {
          this.available_now = data['instant_appointment_slot_available'];
          // localStorage.setItem('available_now', data['instant_appointment_slot_available']);
        });
    }
    localStorage.removeItem('share_consult_id');
    localStorage.removeItem('share_doc_id');
    localStorage.removeItem('share_pat_id');
    localStorage.removeItem("patient_uuid");
    localStorage.removeItem("reSchedule");
    localStorage.removeItem("parentApptSlot");
  }

  setSearchWithParams(tab: string, page: number) {
    let todayDate = new Date();
    let tomorrowDate = moment(todayDate).add(+1, 'days').format('YYYY-MM-DD');
    let today = moment(todayDate).format('YYYY-MM-DD');
    let yesterdayDate = moment(todayDate).add(-1, 'days').format('YYYY-MM-DD');
    let upComing = `?start_datetime=${tomorrowDate}&status=Booked&fulfilment_status=Not%20Started&page=${page}&sort_by=Descending`;
    let todayPending = `?start_datetime=${today}&end_datetime=${today}&status=Booked&fulfilment_status=Not%20Started&page=${page}&sort_by=Descending`;

    let uuid = '';
    if (this.userType == 'Doctor') {
      uuid = 'doctor_uuid=' + this.current_user_uuid;
    } else if (this.userType == 'Patient') {
      uuid = 'patient_uuid=' + this.current_user_uuid;
    } else {
      uuid = 'book_user_uuid=' + this.current_user_uuid;
    }

    let todayStarted = uuid + `&fulfilment_status=Started&page=${page}&sort_by=Descending`;

    let todayMissed = `?start_datetime_missed=${today}&end_datetime_missed=${today}&fulfilment_status=Both Missed%2BPatient Missed%2BDoctor Missed&page=${page}&sort_by=Descending`;
    let todayCompleted = `?start_datetime=${today}&end_datetime=${today}&fulfilment_status=Suspended%2BCompleted&page=${page}&sort_by=Descending`;
    let todayAll = `?start_datetime=${today}&end_datetime=${today}&page=${page}&sort_by=Descending`;
    const formateDay = moment(todayDate).add(-1, 'days').format('YYYY-MM-DD');
    let pastAppointment = '?end_datetime=' + formateDay + `&page=${page}&sort_by=Descending`;

    let todayInstantCompleted = `start_datetime=${today}&end_datetime=${today}&page=${page}&status=Booked&sort_by=Descending`;
    let pastInstant = `end_datetime=` + formateDay + `&page=${page}&status=Booked&sort_by=Descending`;

    if (this.userType == 'Patient') {
      todayInstantCompleted = `start_datetime=${today}&end_datetime=${today}&page=${page}&status=Booked&sort_by=Descending&patient_id=${this.current_user_uuid}`;
      pastInstant = `end_datetime=` + formateDay + `&page=${page}&status=Booked&sort_by=Descending&patient_id=${this.current_user_uuid}`;
    }


    let pendingInstant = `end_datetime=${today}&fulfilment_status=Started&page=${page}&status=Booked&sort_by=Descending`;
    let searchParam = '';
    switch (tab) {
      case 'upcoming':
        searchParam = upComing;
        break;
      case 'today-pending':
        searchParam = todayPending;
        break;
      case 'ongoing':
        searchParam = todayStarted;
        break;
      case 'today-missed':
        searchParam = todayMissed;
        break;
      case 'today-completed':
        searchParam = todayCompleted;
        break;
      case 'today':
        searchParam = todayAll;
        break;
      case 'past-appointments':
        searchParam = pastAppointment;
        break;
      case 'today-instant-completed':
        searchParam = todayInstantCompleted;
        break;
      case 'past-instant':
        searchParam = pastInstant;
        break;
      case 'pending-instant':
        searchParam = pendingInstant;
        break;
    }
    this.appointment(searchParam, tab, page);
  }

  appointment(searchParams: string, tab: string, page: number) {
    switch (this.userType) {
      case 'HospitalAdmin':
      case 'DoctorAssistant':
      case 'Partner':
        if (tab == 'past-instant' || tab == 'today-instant-completed') {
          this.doctorPastInstantAppointment(searchParams);
        } else if (tab == 'ongoing') {
          this.doctorOngoingConsultation(searchParams);
        } else {
          this.hospitalAdminAppointment(searchParams);
        }
        break;
      case 'Doctor':
        if (tab == 'past-instant' || tab == 'today-instant-completed') {
          this.doctorPastInstantAppointment(searchParams);
        } else if (tab == 'ongoing') {
          this.doctorOngoingConsultation(searchParams);
        } else {
          this.doctorService
            .getDoctorAppointments(searchParams)
            .subscribe((data) => {
              this.setAppointmentValues(data);
            }, error => {
              console.log(error);
            });
        }
        break;
      case 'Patient':
        if (tab == 'past-instant' || tab == 'today-instant-completed') {
          this.doctorPastInstantAppointment(searchParams);
        } else if (tab == 'ongoing') {
          this.doctorOngoingConsultation(searchParams);
        } else {
          this.getPatientAppointmentList(searchParams);
          break;
        }
    }
  }

  hospitalAdminAppointment(searchParam: string) {
    this.patientService.getPatientAppointmentHa(this.current_user_uuid, searchParam).subscribe(
      (data) => {
        this.setAppointmentValues(data);
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        this.loading = false;
      });
  }

  doctorPastInstantAppointment(searchParams: string) {
    this.doctorService.getPastInstantAppointment(searchParams).subscribe((data) => {
      this.setAppointmentValues(data);
    }, error => {
      console.log(error);
      this.loading = false;
    })
  }

  doctorOngoingConsultation(searchParams: string) {
    this.doctorService.getOngoingConsultation(searchParams).subscribe((data) => {
      this.setAppointmentValues(data);
    }, error => {
      console.log(error);
      this.loading = false;
    })
  }

  getPatientAppointmentList(searchParams) {
    this.patientService.getPatientAppointment(searchParams).subscribe(
      (data) => {
        this.setAppointmentValues(data);
        this.isLoading = false;
      },
      (error) => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        this.isLoading = false;
      }
    );
  }

  getAllAppointment(page) {
    this.patientService.getAllPatientAppointment(page).subscribe(
      (data) => {
        this.setAppointmentValues(data);
      }, error => {
        console.log(error);
        this.isLoading = false;
        const status = error['status'];
      });
  }

  setAppointmentValues(data: any) {
    this.appointmentsTotalPage = data['total_pages'];
    this.appointmentsCurrentPage = data['page_number'];
    this.appointments = [];
    if (this.tabValue == 'today-pending') {
      this.todayPendingData(data['results']);
    } else if (this.tabValue == 'today-completed' && this.userType == 'Patient') {
      this.todayCompleted(data['results']);
    } else {
      this.appointments = data['results'];
    }
    this.loading = false;
  }

  todayPendingData(result: any) {
    const todayDateAndTime = moment(new Date()).format('YYYY-MM-DD');
    let index = 0;
    this.cancelBtnDisabled = true;
    for (const value of result) {
      let consultStatus = false;
      let reScheduleStatus = false;
      const doctorObj = value.doctor_user_json;
      const patientObj = value.patient_user_json;
      const startDate = moment(value.start_datetime).format('YYYY-MM-DD');
      const day = moment(value.start_datetime);
      const endTime = moment(value.end_datetime)
      let minutes = moment().diff(day, 'minutes');
      let maxTime = endTime.diff(day, 'minutes');
      // let hour =  moment(value.start_datetime).format('HH:mm');
      // let hours = moment().format('h:mm A');
      // let scheduledHour = moment(value.start_datetime).format('h:mm A');;
      // let maxRescheduleTime = scheduledHour - hours;
      let currentTime = moment();
      let scheduledTime = moment(value.start_datetime);
      let diffMinutes = scheduledTime.diff(currentTime, 'minutes');
      let maxRescheduleTime = Math.floor(diffMinutes / 60);
      let doctorUuid = this.userType == 'Patient' ? value.doctor_uuid : value.doctor;
      let shareStatus: boolean = value.is_shared;


      if (startDate >= todayDateAndTime && (value.status === 'Booked' && value.fulfilment_status == 'Not Started')) {
        if (minutes > Settings.consultaion_cancel_margin) {
          this.cancelBtnDisabled = true;
        } else if (day > moment()) {
          this.cancelBtnDisabled = false;
        }

        if (minutes > this.minTime && maxTime > minutes) {
          consultStatus = false;
        } else {
          consultStatus = true;
        }

        if (maxRescheduleTime < this.reScheduleHours) {
          reScheduleStatus = true;
        } else {
          reScheduleStatus = false;
        }

        // tslint:disable-next-line: max-line-length
        this.appointments.push({
          consultation_uuid: `${value.consultation_uuid}`, start_datetime: `${value.start_datetime}`, end_datetime: `${value.end_datetime}`, consultation_type: `${value.consultation_type}`,
          doctor_uuid: doctorUuid, doctor_user_json: doctorObj, patient_user_uuid: `${value.patient}`, patient_user_json: patientObj,
          disable: consultStatus, status: `${value.status}`, cancelButtonStatus: this.cancelBtnDisabled, reSchedule: reScheduleStatus, uuid: `${value.consultation_uuid}`,
          is_shared: shareStatus, medical_report: value.medical_report
        });
      }
      index = index + 1;
      console.log('maxRescheduleTime', maxRescheduleTime, 'this.reScheduleHours', this.reScheduleHours, 'reScheduleStatus', reScheduleStatus, this.appointments);
    }
  }

  appointmentButtonEnableAndDisable() {
    const currentDate = moment();
    const result = this.appointments;
    const todayDateAndTime = moment(currentDate).format('YYYY-MM-DD');
    let index = 0;
    this.appointments = [];
    for (const value of result) {
      this.cancelBtnDisabled = true;
      const doctorObj = value.doctor_user_json;
      const patientObj = value.patient_user_json;
      const startDate = moment(currentDate).format('YYYY-MM-DD');
      const day = moment(value.start_datetime);
      const endTime = moment(value.end_datetime)
      let minutes = currentDate.diff(day, 'minutes');
      let maxTime = endTime.diff(day, 'minutes');
      if (startDate >= todayDateAndTime && value.status === 'Booked') {
        if (minutes > Settings.consultaion_cancel_margin) {
          this.cancelBtnDisabled = true;
        } else
          if (day > this.currentDate) {
            this.cancelBtnDisabled = false;
          }
        if (minutes > this.minTime && maxTime > minutes) {
          // tslint:disable-next-line: max-line-length
          this.appointments.push({
            consultation_uuid: `${value.consultation_uuid}`, start_datetime: `${value.start_datetime}`, end_datetime: `${value.end_datetime}`, consultation_type: `${value.consultation_type}`,
            doctor: `${value.doctor}`, doctor_user_json: doctorObj, patient_user_uuid: `${value.patient}`, patient_user_json: patientObj,
            disable: false, status: `${value.status}`, cancelButtonStatus: this.cancelBtnDisabled, uuid: `${value.consultation_uuid}`, medical_report: value.medical_report
          });
        } else {
          // tslint:disable-next-line: max-line-length
          this.appointments.push({
            consultation_uuid: `${value.consultation_uuid}`, start_datetime: `${value.start_datetime}`, end_datetime: `${value.end_datetime}`, consultation_type: `${value.consultation_type}`,
            doctor: `${value.doctor}`, doctor_user_json: doctorObj, patient_user_uuid: `${value.patient}`, patient_user_json: patientObj,
            disable: true, status: `${value.status}`, cancelButtonStatus: this.cancelBtnDisabled, uuid: `${value.consultation_uuid}`, medical_report: value.medical_report
          });
        }
      }
      index = index + 1;
    }
  }
  todayCompleted(result: any) {
    for (const value of result) {
      if (!value.is_instant_consult) {
        this.appointments.push(value);
      }
    }
  }
  firstPage() {
    this.appointmentsCurrentPage = 1;
    this.setSearchWithParams(this.tabValue, this.appointmentsCurrentPage);
  }

  previousPage() {
    this.appointmentsCurrentPage = this.appointmentsCurrentPage - 1;
    if (this.appointmentsTotalPage >= this.appointmentsCurrentPage && this.appointmentsCurrentPage > 0) {
      this.serialNumber = (this.appointmentsCurrentPage - 1) * 10;
      this.setSearchWithParams(this.tabValue, this.appointmentsCurrentPage);
    } else {
      this.appointmentsCurrentPage = this.appointmentsCurrentPage + 1;
    }
  }

  nextPage() {
    this.appointmentsCurrentPage = this.appointmentsCurrentPage + 1;
    if (this.appointmentsTotalPage >= this.appointmentsCurrentPage) {
      this.setSearchWithParams(this.tabValue, this.appointmentsCurrentPage);
    } else {
      this.appointmentsCurrentPage = this.appointmentsCurrentPage - 1;
    }
  }

  lastPage() {
    this.serialNumber = (this.appointmentsTotalPage - 1) * 10;
    this.setSearchWithParams(this.tabValue, this.appointmentsTotalPage);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['tabValue']) {
      this.setSearchWithParams(this.tabValue, 1);
    }
  }

  btnPermission(btnName: string, consultationStatus: string, contorlStatus: boolean) {
    let permission = false;
    switch (btnName) {
      case 'consult':
        if ((this.userType == 'Doctor' || this.userType == 'DoctorAssistant' || this.userType == 'HospitalAdmin' || this.userType == 'Patient' || this.userType == 'Partner') && (this.tabValue == 'ongoing')) {
          permission = true;
        } else if ((this.userType == 'Doctor' || this.userType == 'DoctorAssistant' || this.userType == 'HospitalAdmin' || this.userType == 'Patient' || this.userType == 'Partner') && (this.tabValue == 'today-pending')) {
          permission = true;
        } else if (this.userType == 'Doctor' && this.tabValue == 'pending-instant') {
          permission = true;
        }
        break;
      case 'consultation-details':
        if ((this.tabValue == 'today-completed' || this.tabValue == 'past-appointments' || this.tabValue == 'past-instant' || this.tabValue == 'today-instant-completed') && (consultationStatus == 'Completed')) {
          permission = true;
        }
        break;
      case 'view-reports':
        if (contorlStatus == true) {
          permission = true;
        }
        break;
      case 'cancel':
        if ((this.userType == 'Doctor' || this.userType == 'DoctorAssistant' || this.userType == 'HospitalAdmin' || this.userType == 'Patient') && (this.tabValue == 'upcoming' || this.tabValue == 'today-pending')) {
          permission = true;
        }
        break;
      case 'upload':
        if ((this.userType == 'DoctorAssistant' || this.userType == 'HospitalAdmin' || this.userType == 'Patient' || this.userType == 'Partner') && (this.tabValue == 'upcoming' || this.tabValue == 'today-pending')) {
          permission = true;
        }
        break;
      case 'view-patient':
        if (this.userType == 'Doctor' || this.userType == 'DoctorAssistant' || this.userType == 'HospitalAdmin' || this.userType == 'HospitalAdmin') {
          permission = true;
        }
        break;
      case 'pre-consulting-notes':
        if (this.tabValue == 'upcoming' || this.tabValue == 'today-pending' || this.tabValue == 'ongoing') {
          permission = true;
        }
        break;
      case 'edit-notes':
        if (this.userType == 'DoctorAssistant' || this.userType == 'HospitalAdmin' && (this.tabValue == 'today-pending')) {
          permission = true;
        }
        break;
      case 're-schedule':
        if ((this.userType == 'DoctorAssistant' || this.userType == 'HospitalAdmin' || this.userType == 'Patient' || this.userType == 'Partner') && (this.tabValue == 'upcoming')) {
          permission = true;
        } else if ((this.userType == 'DoctorAssistant' || this.userType == 'HospitalAdmin' || this.userType == 'Patient' || this.userType == 'Partner') && (this.tabValue == 'today-pending') && (contorlStatus == false)) {
          permission = true;
        }
        break;
      case 'share':
        if (this.userType == 'Patient' && (this.tabValue == 'today-pending' || this.tabValue == 'upcoming' || this.tabValue == 'today-completed')) {
          permission = true;
        }
        break;
      case 'patient-history':
        if (this.userType == 'Doctor' && (this.tabValue == 'today-pending' || this.tabValue == 'upcoming' || this.tabValue == 'past-appointments') && (contorlStatus == true)) {
          permission = true;
        }
        break;
    }
    return permission;
  }

  viewPatient(userData, consultationid, result) {
    this.patientDetail = userData;
    this.patientNotes = 'Nil';
    let counsutationId = consultationid;
    if (consultationid == '' || consultationid == null || consultationid == undefined) {
      counsutationId=result.uuid;
    }

    this.doctorService.getPatientNotes(counsutationId).subscribe((data) => {
      const result = data['results']
      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          if (result[i].consultation == counsutationId) {
            this.patientNotes = data['results'][i].text;
            this.consultMsgId = data['results'][i].uuid;
            this.viewConsultUuid = counsutationId;
          }
        }
      }
      $('#patientModal1').modal('show');
    }, error => {
      $('#patientModal1').modal('show');
      console.log(error);
      const status = error['status'];
      if (status == 400) {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
      else {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
    })
    this.notes = '';
  }

  onConsult(apptId, doctor_user_uuid, consultation_type, apptData?: any) {
    if (this.userType == 'Patient' && apptData.doctor_uuid != undefined) {
      localStorage.setItem('doctor_id', apptData.doctor_uuid);
    } else if (this.userType == 'Doctor' && apptData.patient != undefined) {
      localStorage.setItem('consult_patient_id', apptData.patient);
    } else if ((this.userType == 'Partner' || this.userType == 'DoctorAssistant') && apptData.doctor_uuid != undefined) {
      localStorage.setItem('doctor_id', apptData.doctor_uuid);
    } else {
      localStorage.setItem('doctor_id', doctor_user_uuid);
    }
    localStorage.setItem('consultation_type', consultation_type);
    switch (this.userType) {
      case 'Patient':
      case 'Partner':
        this.patientConsult(apptId);
        break;
      case 'DoctorAssistant':
        this.doctorAssistantConsult(apptId);
        break;
      case 'Doctor':
        this.docConsult(apptId);
        break;
    }
  }

  docConsult(consId) {
    let consultation_uuid = consId;
    this.doctorService
      .joinConsultation(consultation_uuid)
      .subscribe((data) => {
        this.router.navigate(['/doctor/consultation'], {
          queryParams: { consultationId: consultation_uuid },
        });
        this.router.events.subscribe((val) => {
          const nvigationEnd = val instanceof NavigationEnd;
          if (!!nvigationEnd) {
            location.reload();
          }

        });
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          const errorData = error['error']['error_details']['consultations'];
          console.log(errorData);
          if (errorData.length > 0) {
            this.consultationUuid = errorData[0]['uuid'];
            $('#consultation-complete1').modal({ backdrop: 'static', keyboard: false });
            $('#consultation-complete1').modal('show');
          }
          const errMessage = error['error']['error_message']
          this.notificationService.error(errMessage, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
      );
  }

  patientConsult(consId) {
    let consultation_uuid = consId;
    this.patientService.joinConsultation(consultation_uuid).subscribe((data) => {
      this.router.navigate(['/patient/consultation'], {
        queryParams: { consultationId: consultation_uuid },
      });
      this.router.events.subscribe((val) => {
        const nvigationEnd = val instanceof NavigationEnd;
        if (!!nvigationEnd) {
          location.reload();
        }
      });
    });
  }

  doctorAssistantConsult(consId) {
    let consultation_uuid = consId;
    this.patientService.joinConsultation(consultation_uuid).subscribe((data) => {
      // this.router.navigate(['hapatient-consultation'], {
      this.router.navigate(['daconsulting-video'], {
        queryParams: { consultationId: consultation_uuid },
      });
      this.router.events.subscribe((val) => {
        const nvigationEnd = val instanceof NavigationEnd;
        if (!!nvigationEnd) {
          location.reload();
        }
      });
    });
  }

  getReports(id: any) {
    this.doctorService.getMedicalReportsById(id).subscribe(data => {
      this.reportFiles = Object.values(data['results']);
      if (this.reportFiles.length > 0) {
        $('#reports').modal('show');
      } else {
        this.notificationService.warning('Patient did not upload any report');
      }
    }, error => {
      console.log(error
      )
    })
  }

  viewConsultHistory(patient_uuid, status, doctor_uuid, consultation_uuid) {
    if (status == 'Completed' || status == 'Suspended') {
      if (this.userType == 'Patient') {
        this.router.navigate(['/consultation-history/', this.current_user_uuid, consultation_uuid]);
      }
      else {
        this.router.navigate(['/consultation-history/', patient_uuid, doctor_uuid, consultation_uuid]);
      }
    }
    else {
      this.notificationService.warning('No consultation data', 'Med.Bot')
    }
  }

  showNotes(notes) {
    this.notes = notes;
    this.updatedNotes = notes;
  }

  saveNotes() {
    const data = { text: this.updatedNotes };
    const uuid = this.viewConsultUuid;
    this.doctorAssistantService.saveNotes(data, uuid, this.consultMsgId).subscribe(
      (data) => {
        this.notificationService.success('Notes added Successfully');
        $('#patientModal1').modal('hide');
        this.router.navigate(['/hadashboard']);
      },
      (error) => {
        $('#patientModal1').modal('hide');
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(error.error.detail, 'Med.Bot');
        }
        else {
          this.notificationService.error(error.error.detail, 'Med.Bot');
          console.error('Error:', error);
        }
      }
    );
    this.notes = '';
  }

  getReportId(appt: object, type: string) {
    localStorage.setItem('share_consult_id', appt['consultation_uuid']);
    localStorage.setItem('share_doc_id', appt['doctor_uuid']);
    localStorage.setItem('share_pat_id', (this.userType == 'Patient') ? appt['patient_user_uuid'] : appt['patient_user_json']['uuid']);
    this.consultationUuid = appt['consultation_uuid'];
    this.reportPatientUuid = (this.userType == 'Patient') ? appt['patient_user_uuid'] : appt['patient_user_json']['uuid'];
    if (type == 'history') {
      this.router.navigate(['/share-history/', this.reportPatientUuid, this.consultationUuid]);
    }
  }

  getReportType(event, appointmentId = null) {
    this.selectedDiagnosticReportName.setValue(event.testType);
  }

  medicalReports(event) {
    this.reportFile = event.target.files[0];
    this.reportName = event.target.files[0]?.name;
  }

  saveMedicalReport() {
    this.showUploading = true;
    const file = this.reportFile;

    const errorMsg = this.fileValidation(file);
    if (errorMsg) {
      this.notificationService.warning(errorMsg);
      return false;
    }

    const type = this.selectedDiagnosticReportName.value;
    const data = { 'medical_report_type': type, 'appointment': this.consultationUuid, 'consultation': this.consultationUuid, 'report_generated_on': this.reportDate };
    if (this.userType == 'Patient') {
      this.patientService.postMedicalReport(file, data).subscribe(
        data => {
          this.showUploading = false;
          if (this.reportFiles != undefined) {
            this.reportFiles.unshift(data);
          }
          $('#upload-report2').modal('hide');
          this.selectedDiagnosticReportName.setValue(null);
          this.reportDate = null;
          this.reportFile = null;
          this.reportName = null;
          this.notificationService.success('Report updated', 'Med.Bot');
        }, error => {
          console.log(error);
          const status = error['status'];
          if (status == 413) {
            this.notificationService.error('File size large,', 'Med.Bot');
          } else {
            this.notificationService.error('Internal server error', 'Med.Bot');
          }
          // this.showUploading = false;
          // $('#upload-report2').modal('hide');
        }
      );
    } else {
      this.patientService.postMedicalReportHospital(file, data, this.reportPatientUuid).subscribe(
        data => {
          this.showUploading = false;
          if (this.reportFiles != undefined) {
            this.reportFiles.unshift(data);
          }
          $('#upload-report2').modal('hide');
          this.selectedDiagnosticReportName.setValue(null);
          this.reportDate = null;
          this.reportFile = null;
          this.reportName = null;
          this.notificationService.success('Report updated', 'Med.Bot');
        }, error => {
          this.notificationService.error('Internal server error', 'Med.Bot');
          console.log(error);
          this.showUploading = false;
          $('#upload-report2').modal('hide');
        }
      );
    }
  }

  cancelAppointment(id, index, cancelButtonStatus) {
    console.log(id, cancelButtonStatus);

    if (!cancelButtonStatus) {
      this.appointmentSlotId = id;
      this.cancelAppointmentId = '#cancelAppointmentId' + index;
      $(`${this.cancelAppointmentId}`).prop('disabled', true);
      $('#cancelConfirmModal1').modal('show');
    } else {
      this.notificationService.error('You can cancel before 10 minutes of your consultation time only ');
    }
  }

  confirmCancel() {
    $('#cancelConfirmModal1').modal('hide');
    this.notificationService.warning('Please wait appointment  cancel is processing', 'Med.Bot');
    const status = { status: 'Cancelled' };
    this.patientService
      .cancelAppointment(this.appointmentSlotId, status)
      .subscribe(
        (data) => {
          this.removeAppointmentData(this.appointmentSlotId);
          $('#cancelConfirmModal1').modal('hide');
          this.setSearchWithParams(this.tabValue, 1);
          this.notificationService.success('Appointment cancelled ', 'Med.Bot');
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            const errMessage = error['error']['error_message']
            if (errMessage == 'The appointment has been already cancelled') {
              this.notificationService.error(`${errMessage}`, 'Med.Bot');
              this.removeAppointmentData(this.appointmentSlotId);
            } else if (errMessage) {
              this.notificationService.error(`${errMessage}`, 'Med.Bot');
              $(`${this.cancelAppointmentId}`).prop('disabled', false);
            }
            else {
              this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
            }
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
  }

  appointmentSlotId(appointmentSlotId: any) {
    throw new Error('Method not implemented.');
  }

  removeAppointmentData(id) {
    this.appointments = this.appointments.filter(
      (obj) => obj.doctor_appointment_uuid !== id
    );
    this.notificationService.success('Appointment cancelled ', 'Med.Bot');
  }

  reSchedule(process: string, apptData?: any) {
    if (process == 'initiate') {
      this.reScheduleData = apptData ? apptData : {};
      this.checkRescheduleData();
      $('#reschedule').modal('show');
    }
    else if (process == 'confirm') {
      console.log(this.reScheduleData);
      let patient = this.reScheduleData['patient_user_uuid'];
      let consultid = this.reScheduleData['consultation_uuid'];
      let doctor = (this.userType == "Patient" || this.userType == "DoctorAssistant") ? this.reScheduleData['doctor_uuid'] : this.reScheduleData["doctor"];
      // let location = this.reScheduleData['practice_location'];
      let parentApptSlot = this.reScheduleData['uuid'];
      localStorage.setItem("patient_uuid", patient);
      localStorage.setItem("reSchedule", '1');
      localStorage.setItem("parentApptSlot", parentApptSlot);
      this.router.navigate([
        '/patient/appointment/',
        doctor,
        consultid
      ]);
    }
  }

  endConsultation() {
    this.teleConsultService.completeConsultation(this.consultationUuid).subscribe(data => {
      this.available_now = false;
      $('#consultation-complete1').model('hide');
    },
      error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          const errMessage = error['error']['error_message']
          this.notificationService.error(errMessage, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }

  shareReport(consltId: string, docId: string) {
    localStorage.setItem('share_doc_id', docId);
    const patientId = localStorage.getItem('current_user_uuid');
    this.router.navigate(['/share-history/', patientId, consltId]);
  }

  fileValidation(file: any) {
    const fileNameLength = file.name;
    let errorMsg = '';

    if (fileNameLength.length >= 51) {
      errorMsg = "File name: Ensure this field has no more than 50 characters.";
      this.notificationService.warning();
    }

    if (file.size > 2000000 && (file.type != 'image/jpeg' || file.type != 'image/jpg' || file.type != 'image/png' || file.type != 'application/pdf')) {
      errorMsg = "File Size Large or Check File Format";
    }

    return errorMsg;
  }

  openFile(file) {
    window.open(file);
  }

  checkRescheduleData() {
    if (Object.keys(this.reScheduleData).length > 0) {
      this.reSchedulePopup = true;
    }
    else {
      this.reSchedulePopup = false;
    }
  }
}
