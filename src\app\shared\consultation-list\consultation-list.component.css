.flexContainer{
    display: flex;
    justify-content: flex-end;
}
.disabled-pagination{
    color: darkgray !important;
    pointer-events: none !important;
  }
a.link:hover {
    cursor: pointer;
}
.float-right {
  margin-left: 80px;
}
.float-left {
  margin-right: 80px;
}
.card {
  margin-bottom: 5px !important;
}

.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  transition: opacity 500ms;
  visibility: visible;
  opacity: 1;
}


.archivePopup {
  margin: 70px auto;
  padding: 20px;
  background: #fff;
  border-radius: 5px;
  min-width: 25%;
  max-width: fit-content;
  position: relative;
  transition: all 5s ease-in-out;
}

.archivePopup h2 {
  margin-top: 0;
  color: #333;
  font-family: Tahoma, Arial, sans-serif;
}
.archivePopup .close {
  position: absolute;
  top: 20px;
  right: 30px;
  transition: all 200ms;
  font-size: 30px;
  font-weight: bold;
  text-decoration: none;
  color: #333;
}
.archivePopup .close:hover {
  color: #d80606;
  cursor: pointer;
}
.archivePopup .content {
  max-height: 30%;
  overflow: auto;
}
.input-field {
  width: 30px; 
  height: 30px; 
  margin: 5px; 
  text-align: center;
  font-size: 16px; 
  border: 1px solid #ccc; 
  border-radius: 5px; 
}

.input-field:focus {
  outline: none;
}

.input-field:focus  {
  outline: none;
  border-color: blue; 
}

.input-field[type="text"] {
  max-length: 1;
}
.input-field.invalid {
  border-color: red; 
}

.countdown {
  font-size: 16px;
  font-weight: bold;
  color: rgb(47, 0, 255);
}

.resend-otp{
  color: #0000ff !important;
  cursor: pointer;
  font-size: 0.75rem;
}

.page-size{
  .form-control{
    min-height: 38px !important;
  }
}