<div class="tab-content">
  <h4 class="dashboard-title mb-2">Consultation</h4>


  <div class="appointment-tab">
    <!-- Consultation Tab -->
    <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded">
      <li class="nav-item" >
        <a class="nav-link nm-size active" href="#completed-consultation" data-toggle="tab">
          <h5>Completed</h5>
        </a>
      </li>
      <li class="nav-item"  *ngIf="userType=='PlatformAdmin'||userType == 'HospitalAdmin'">
        <a class="nav-link nm-size" a href="#archived-consultation" data-toggle="tab">
          <h5>Archived</h5>
        </a>
      </li>
      <li class="nav-item" >
        <a class="nav-link nm-size" a href="#deleted-consultation" data-toggle="tab" *ngIf="userType=='PlatformAdmin'||userType == 'HospitalAdmin'">
          <h5>Deleted</h5>
        </a>
      </li>
    </ul>
    <!-- /Consultation Tab -->

    <div class="tab-content">
      <!-- Completed Consultation Tab -->
      <div class="tab-pane show active" id="completed-consultation">
        <div class="card">
          <div class="mx-5 my-2">
            <div class="search">
              <form [formGroup]="completeFormData">
                <div class="row">
                  <div class="col-md-3 col-lg-2 col-sm-6">
                    <label>From Date</label>
                    <input placeholder="From Date" onkeydown="return false" class="form-control"
                      formControlName="fromDate" bsDatepicker
                      [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6">
                    <label>To Date</label>
                    <input placeholder="To Date" onkeydown="return false" class="form-control" formControlName="toDate"
                      bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6" *ngIf="userType=='PlatformAdmin'">
                    <label>Doctor Type</label>
                    <select class="form-control input-field-border select" name="doctorType" id="doctorType"
                      formControlName="doctorType" (change)="setDoctorType($event.target.value)">
                      <option value="0" disabled>Select Doctor Type</option>
                      <option value="1" translate>Individual</option>
                      <option value="2" translate>Hospital-Based</option>
                    </select>
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6"
                    *ngIf="userType=='PlatformAdmin' && completeFormData.value.doctorType=='2'">
                    <label>Hospitals</label>
                    <select class="form-control select" name="hospital" id="hospital" formControlName="hospital"
                      (change)="getDoctorPatient($event.target.value)">
                      <option>Select Hospital</option>
                      <option *ngFor="let item of hospitalList" [value]="item.uuid"> {{item.name}}</option>
                    </select>
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6"
                    *ngIf="(userType=='PlatformAdmin'||userType=='HospitalAdmin') && completeFormData.value.doctorType!='0'">
                    <label>Doctors</label>
                    <select class="form-control select" name="doctor" id="doctor" formControlName="doctor">
                      <option value="0">Select Doctor</option>
                      <option *ngFor="let item of doctorList" [value]="item.uuid"> {{item.username}}</option>
                    </select>
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6"
                    *ngIf="(userType=='PlatformAdmin'||userType=='HospitalAdmin') && completeFormData.value.doctorType!='0'">
                    <label>Patients</label>
                    <select class="form-control select" name="patient" id="patient" formControlName="patient">
                      <option value="0">Select Patient</option>
                      <option *ngFor="let item of patientList" [value]="item.uuid"> {{item.username}}</option>
                    </select>
                  </div>
                </div>
              </form>
              <div class="row mt-2">
                <div class="col-md-6 col-lg-7 col-sm-6">
                </div>
                <div class="col-md-4 col-lg-5 col-sm-6 text-reset flexContainer">
                  <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-2"
                    (click)="searchConsultation(1,1)">Search Consultation</button>
                  <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-2"
                    (click)="archiveConsultation(1,0)" *ngIf="(SDate && EDate) && userType=='PlatformAdmin'||userType == 'HospitalAdmin'">Archive Consultation</button>
                  <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-2" (click)="formReset(1)">Clear
                    Filter</button>
                </div>
              </div>
            </div>
            <div class="row" *ngIf="userType=='PlatformAdmin'||userType == 'HospitalAdmin'">
              <div class="col-md-12">
                <p>
                  Configure the search criteria as desired, then click "Search Consultation." The consultation list
                  will populate on the table. You can select multiple entries and click "Archive Consultation"
                  <br /><br />
                  <b>Note:</b> Archiving is only possible for consulting date older than 3 years.
                </p>
              </div>
            </div>
            <div *ngIf="!consultationsLoading">
              <div class="d-flex justify-content-between col-md-12 mb-2" *ngIf="consultationCount > 10">
                <div class="col-md-7 d-flex page-size align-items-center">
                  <label class="mr-2"> No Of Records</label>
                  <select class="form-control col-md-2 input-field-border select" name="recordsPerPage"
                    id="recordsPerPage" [(ngModel)]="recordsPerPage" (change)="tabSelection(1,recordsPerPage)">
                    <option value="10" translate>10</option>
                    <option value="25" translate>25</option>
                    <option value="50" translate>50</option>
                    <option value="100" translate>100</option>
                  </select>
                </div>
                <div class="col-md-5">
                  <nav aria-label="Page navigation example" *ngIf="consultationsTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="consultationsFirstPageList(1)" [ngClass]="{
                      'disabled-pagination': consultationsCurrentPage === 1
                    }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="consultationsPreviousPageList(1)" [ngClass]="{
                      'disabled-pagination': consultationsCurrentPage === 1
                    }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ consultationsCurrentPage }}&nbsp;of &nbsp;{{
                          consultationsTotalPage
                          }}</a>
                      </li>
                      <li class="page-item" (click)="consultationsNextPageList(1)" [ngClass]="{
                      'disabled-pagination':
                        consultationsCurrentPage === consultationsTotalPage
                    }">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="consultationsLastPageList(1)" [ngClass]="{
                      'disabled-pagination':
                        consultationsCurrentPage === consultationsTotalPage
                    }">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
            </div>
            <div *ngIf="!consultationsLoading">
              <app-consultation-table [consultations]="completedConsultations" [userType]="userType"
                [selectedTab]="selectedTab" (selectedPatientId)="selectedPatientList($event)"></app-consultation-table>
            </div>
          </div>
        </div>
      </div>
      <!-- /Completed Consultation Tab -->

      <!-- Archived Consultation Tab -->
      <div class="tab-pane show " id="archived-consultation">
        <div class="card">
          <div class="mx-5 my-2">
            <div class="search">
              <form [formGroup]="archivedFormData">
                <div class="row">
                  <div class="col-md-3 col-lg-2 col-sm-6">
                    <label>From Date</label>
                    <input placeholder="From Date" onkeydown="return false" class="form-control"
                      formControlName="fromDate" bsDatepicker
                      [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6">
                    <label>To Date</label>
                    <input placeholder="To Date" onkeydown="return false" class="form-control" formControlName="toDate"
                      bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6"*ngIf="userType=='PlatformAdmin'">
                    <label>Doctor Type</label>
                    <select class="form-control input-field-border select" name="doctorType" id="doctorType"
                      formControlName="doctorType" (change)="setDoctorType($event.target.value)">
                      <option value="0" disabled>Select Doctor Type</option>
                      <option value="1" translate>Individual</option>
                      <option value="2" translate>Hospital-Based</option>
                    </select>
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6"
                    *ngIf="userType=='PlatformAdmin' && archivedFormData.value.doctorType=='2'">
                    <label>Hospitals</label>
                    <select class="form-control select" name="hospital" id="hospital"
                      (change)="getDoctorPatient($event.target.value)" formControlName="hospital">
                      <option>Select Hospital</option>
                      <option *ngFor="let item of hospitalList" [value]="item.uuid"> {{item.name}}</option>
                    </select>
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6"
                    *ngIf="(userType=='PlatformAdmin'||userType=='HospitalAdmin') && archivedFormData.value.doctorType!='0' && doctorList.length>0">
                    <label>Doctors</label>
                    <select class="form-control select" name="doctor" id="doctor" formControlName="doctor">
                      <option value="0">Select Doctor</option>
                      <option *ngFor="let item of doctorList" [value]="item.uuid"> {{item.username}}</option>
                    </select>
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6"
                    *ngIf="(userType=='PlatformAdmin'||userType=='HospitalAdmin') && archivedFormData.value.doctorType!='0'">
                    <label>Patients</label>
                    <select class="form-control select" name="patient" id="patient" formControlName="patient">
                      <option value="0">Select Patient</option>
                      <option *ngFor="let assist of patientList" value={{assist.uuid}}> {{assist.username}}</option>
                    </select>
                  </div>
                </div>
              </form>
              <div class="row mt-2">
                <div class="col-md-8 col-lg-7 col-sm-6">
                </div>
                <div class="col-md-4 col-lg-5 col-sm-6 text-reset flexContainer">
                  <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-2"
                    (click)="searchConsultation(2,1)">Search Consultation</button>
                  <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-2"
                    *ngIf="(userType=='PlatformAdmin'||userType=='HospitalAdmin')&& SDate && EDate"
                    (click)="deleteConfirm()">Delete
                    Consultation</button>
                  <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-2" (click)="formReset(2)">Clear
                    Filter</button>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12">
                <p>
                  Configure the search criteria as desired, then click "Search Consultation." The consultation list
                  will populate on the table. You can select multiple entries and click "Delete Consultation"
                  <br /><br />
                  <b>Note:</b> Deleting is only possible for consulting date older than 7 years.
                </p>
              </div>
            </div>
            <div *ngIf="!consultationsLoading">
              <div class="d-flex justify-content-between col-md-12 mb-2" *ngIf="consultationCount > 10">
                <div class="col-md-9 d-flex page-size align-items-center">
                  <label class="mr-2"> No Of Records</label>
                  <select class="form-control input-field-border select" name="recordsPerPage" id="recordsPerPage"
                    [(ngModel)]="recordsPerPage" (change)="tabSelection(2,recordsPerPage)">
                    <option value="10" translate>10</option>
                    <option value="25" translate>25</option>
                    <option value="50" translate>50</option>
                    <option value="100" translate>100</option>
                  </select>
                </div>
                <div class="col-md-4 col-lg-4 col-sm-6 text-reset flexContainer">
                  <nav aria-label="Page navigation example" *ngIf="consultationsTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="consultationsFirstPageList(2)" [ngClass]="{
                        'disabled-pagination': consultationsCurrentPage === 1
                      }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="consultationsPreviousPageList(2)" [ngClass]="{
                        'disabled-pagination': consultationsCurrentPage === 1
                      }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ consultationsCurrentPage }}&nbsp;of &nbsp;{{
                          consultationsTotalPage
                          }}</a>
                      </li>
                      <li class="page-item" (click)="consultationsNextPageList(2)" [ngClass]="{
                        'disabled-pagination':
                          consultationsCurrentPage === consultationsTotalPage
                      }">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="consultationsLastPageList(2)" [ngClass]="{
                        'disabled-pagination':
                          consultationsCurrentPage === consultationsTotalPage
                      }">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
              <div>
                <app-consultation-table [consultations]="archivedConsultations" [userType]="userType"
                  [selectedTab]="selectedTab"
                  (selectedPatientId)="selectedPatientList($event)"></app-consultation-table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- /Archived Consultation Tab -->

      <!-- Deleted Consultation Tab -->
      <div class="tab-pane show" id="deleted-consultation">
        <div class="card">
          <div class="mx-5 my-2">
            <div class="search">
              <form [formGroup]="deletedFormData">
                <div class="row">
                  <div class="col-md-3 col-lg-2 col-sm-6">
                    <label>From Date</label>
                    <input placeholder="From Date" onkeydown="return false" class="form-control"
                      formControlName="fromDate" bsDatepicker
                      [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6">
                    <label>To Date</label>
                    <input placeholder="To Date" onkeydown="return false" class="form-control" formControlName="toDate"
                      bsDatepicker [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }">
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6" *ngIf="userType=='PlatformAdmin'">
                    <label>Doctor Type</label>
                    <select class="form-control input-field-border select" name="doctorType" id="doctorType"
                      formControlName="doctorType" (change)="setDoctorType($event.target.value)">
                      <option value="0" disabled>Select Doctor Type</option>
                      <option value="1" translate>Individual</option>
                      <option value="2" translate>Hospital-Based</option>
                    </select>
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6"
                    *ngIf="userType=='PlatformAdmin' && deletedFormData.value.doctorType=='2'">
                    <label>Hospitals</label>
                    <select class="form-control select" name="hospital" id="hospital"
                      (change)="getDoctorPatient($event.target.value)" formControlName="hospital">
                      <option>Select Hospital</option>
                      <option *ngFor="let item of hospitalList" [value]="item.uuid"> {{item.name}}</option>
                    </select>
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6"
                    *ngIf="(userType=='PlatformAdmin'||userType=='HospitalAdmin' )&& deletedFormData.value.doctorType!='0'">
                    <label>Doctors</label>
                    <select class="form-control select" name="doctor" id="doctor" formControlName="doctor">
                      <option value="0">Select Doctor</option>
                      <option *ngFor="let item of doctorList" [value]="item.uuid"> {{item.username}}</option>
                    </select>
                  </div>
                  <div class="col-md-3 col-lg-2 col-sm-6"
                    *ngIf="(userType=='PlatformAdmin'||userType=='HospitalAdmin' ) && deletedFormData.value.doctorType!='0'">
                    <label>Patients</label>
                    <select class="form-control select" name="patient" id="patient" formControlName="patient">
                      <option value="0">Select Patient</option>
                      <option *ngFor="let item of patientList" [value]="item.uuid"> {{item.username}}</option>
                    </select>
                  </div>
                </div>
              </form>
              <div class="row mt-2">
                <div class="col-md-8 col-lg-7 col-sm-6">
                </div>
                <div class="col-md-4 col-lg-5 col-sm-6 text-reset flexContainer">
                  <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-2"
                    (click)="searchConsultation(3,1)">Search Consultation</button>
                  <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-2" (click)="formReset(3)">Clear
                    Filter</button>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12">
                <p>
                  Configure the search criteria as desired, then click "Search Consultation." The consultation list
                  will populate on the table.
                </p>
              </div>
            </div>
            <div *ngIf="!consultationsLoading">
              <div class="d-flex justify-content-between col-md-12 mb-2" *ngIf="consultationCount > 10">
                <div class="col-md-9 d-flex page-size align-items-center">
                  <label class="mr-2"> No Of Records</label>
                  <select class="form-control input-field-border select" name="recordsPerPage" id="recordsPerPage"
                    [(ngModel)]="recordsPerPage" (change)="tabSelection(3,recordsPerPage)">
                    <option value="10" translate>10</option>
                    <option value="25" translate>25</option>
                    <option value="50" translate>50</option>
                    <option value="100" translate>100</option>
                  </select>
                </div>
                <div class="float-right mt-3 col-md-3">
                  <nav aria-label="Page navigation example" *ngIf="consultationsTotalPage > 1">
                    <ul class="pagination">
                      <li class="page-item" (click)="consultationsFirstPageList(3)" [ngClass]="{
                        'disabled-pagination': consultationsCurrentPage === 1
                      }">
                        <a class="page-link">&lt;&lt;</a>
                      </li>
                      <li class="page-item" (click)="consultationsPreviousPageList(3)" [ngClass]="{
                        'disabled-pagination': consultationsCurrentPage === 1
                      }">
                        <a class="page-link">&lt;</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link">page &nbsp;{{ consultationsCurrentPage }}&nbsp;of &nbsp;{{
                          consultationsTotalPage
                          }}</a>
                      </li>
                      <li class="page-item" (click)="consultationsNextPageList(3)" [ngClass]="{
                        'disabled-pagination':
                          consultationsCurrentPage === consultationsTotalPage
                      }">
                        <a class="page-link">&gt;</a>
                      </li>
                      <li class="page-item" (click)="consultationsLastPageList(3)" [ngClass]="{
                        'disabled-pagination':
                          consultationsCurrentPage === consultationsTotalPage
                      }">
                        <a class="page-link">&gt;&gt;</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
              <div>
                <app-consultation-table [consultations]="deletedConsultations" [userType]="userType"
                  [selectedTab]="selectedTab"
                  (selectedPatientId)="selectedPatientList($event)"></app-consultation-table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- /Deleted Consultation Tab -->
    </div>

  </div>

</div>


<div *ngIf="consultationsLoading">
  <app-loading-spinner></app-loading-spinner>
</div>

<!-- archive confirmation popup starts -->
<div class="overlay" *ngIf="archivePopup">
  <div class="archivePopup">
    <h4 class="mb-3">Archive Confirmation </h4>
    <a class="close" (click)="archivePopup = false">&times;</a>
    <div class="mb-3">
      <h5> Are you sure to Archive the selected patient consulting data?</h5>
      <h6><b>Note:</b> Archiving is only possible for consulting date older than 3 years.</h6>
    </div>
    <div class="d-flex align-items-center justify-content-end">
      <button class="btn btn-info mb-2 mr-2" (click)="archiveConsultation(1,1)">Confirm</button>
      <button class="btn btn-danger mb-2 mr-2" (click)="archivePopup = false">Cancel</button>
    </div>

  </div>
</div>
<!-- archive confirmationpopup ends-->

<!-- delete confirmation popup starts -->
<div class="overlay" *ngIf="deletePopup">
  <div class="archivePopup">
    <h4 class="mb-3">Delete Confirmation </h4>
    <a class="close" (click)="deletePopup = false">&times;</a>
    <div class="mb-3">
      <h5> Are you sure to Delete the selected patient consulting data?</h5>
      <h6><b>Note:</b> Deleting is only possible for consulting date older than 7 years.</h6>
    </div>
    <div class="d-flex align-items-center justify-content-end">
      <button class="btn btn-info mb-2 mr-2" (click)="createOTP()">Confirm</button>
      <button class="btn btn-danger mb-2 mr-2" (click)="deletePopup = false">Cancel</button>
    </div>
  </div>
</div>
<!-- delete confirmationpopup ends-->

<!-- otp confirmation popup starts -->
<div class="overlay" *ngIf="otpPopup">
  <div class="archivePopup">
    <h4 class="mb-3">OTP Verification </h4>
    <a class="close" (click)="otpPopup = false">&times;</a>
    <div [formGroup]="otpForm" class="mb-3">
      <input #otp1 id="otp1" formControlName="otp1" (input)="pass(otp1,otp2)" class="input-field" maxlength="1">
      <input #otp2 id="otp2" formControlName="otp2" (input)="pass(otp2,otp3)" class="input-field" maxlength="1">
      <input #otp3 id="otp3" formControlName="otp3" (input)="pass(otp2,otp4)" class="input-field" maxlength="1">
      <input #otp4 id="otp4" formControlName="otp4" class="input-field" maxlength="1">
      <label class="countdown ml-2" *ngIf="!resendOtp">{{seconds}}</label>
      <a class="ml-2 resend-otp" *ngIf="resendOtp" (click)="createOTP()"><u>Resend OTP</u></a>
    </div>
    <div class="d-flex align-items-center justify-content-end">
      <button class="btn btn-info mb-2 mr-2" [disabled]="otpForm.invalid" (click)="otpVerification()">Verify</button>
      <button class="btn btn-danger mb-2 mr-2" (click)="otpPopup = false">Cancel</button>
    </div>
  </div>
</div>
<!-- otp confirmationpopup ends-->