import { Component, OnInit, Input, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
import { Router } from '@angular/router';
import { PatientService } from 'src/app/patient/patient.service';
import { DatePipe } from '@angular/common';
import { AuthService } from 'src/app/auth/auth.service';
import { ToastrService } from 'ngx-toastr';
import { PlatformService } from 'src/app/platform/platform.service';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import * as moment from 'moment';
import { SharedService } from '../shared.service';

@Component({
  selector: 'app-consultation-list',
  templateUrl: './consultation-list.component.html',
  styleUrls: ['./consultation-list.component.css'],
  providers: [DatePipe]
})

export class ConsultationListComponent implements OnInit {
  completedConsultations: any = [];
  archivedConsultations: any = [];
  deletedConsultations: any = [];
  consultationCount: any;
  public consultationsCurrentPage: number;
  public consultationsTotalPage: number;
  consultationsLoading: boolean;
  current_user_uuid: any;
  userType: string;
  patientType: string = "0";
  userProfile: any = {};
  patientUuid: string;
  fromDate: Date;
  toDate: Date;
  doctorType: number = 0;
  public maxDate: Date;
  public minDate: Date;
  completeFormData: FormGroup;
  archivedFormData: FormGroup;
  deletedFormData: FormGroup;
  recordsPerPage: number = 10;
  selectedTab: number;
  archivePopup: boolean = false;
  deletePopup: boolean = false;
  selectedPatientId: any = "";
  patients: string[] = [];
  otpPopup: boolean = false;
  otpValue: string = '';
  seconds = 90;
  resendOtp: boolean = false;
  otpForm: FormGroup;
  SDate: boolean = false;
  EDate: boolean = false;
  hospitalList: any = [];
  doctorList: any = [];
  patientList: any = [];
  currentHsptId: string = '';

  constructor(
    private router: Router,
    private patientService: PatientService,
    private userService: AuthService,
    private notificationService: ToastrService,
    private platformService: PlatformService,
    private fb: FormBuilder,
    private sharedService: SharedService
  ) {

    this.completeFormData = this.fb.group({
      fromDate: new FormControl('', Validators.required),
      toDate: new FormControl('', Validators.required),
      doctorType: new FormControl('0'),
      hospital: new FormControl(''),
      doctor: new FormControl(''),
      patient: new FormControl(''),
    });
    this.deletedFormData = this.fb.group({
      fromDate: new FormControl('', Validators.required),
      toDate: new FormControl('', Validators.required),
      doctorType: new FormControl('0'),
      hospital: new FormControl(''),
      doctor: new FormControl(''),
      patient: new FormControl(''),
    });
    this.archivedFormData = this.fb.group({
      fromDate: new FormControl('', Validators.required),
      toDate: new FormControl('', Validators.required),
      doctorType: new FormControl('0'),
      hospital: new FormControl(''),
      doctor: new FormControl(''),
      patient: new FormControl(''),
    });
    this.otpForm = this.fb.group({
      otp1: new FormControl('', Validators.required),
      otp2: new FormControl('', Validators.required),
      otp3: new FormControl('', Validators.required),
      otp4: new FormControl('', Validators.required),
    });
  }

  ngOnInit(): void {
    this.userType = localStorage.getItem('user_type');
    this.current_user_uuid = localStorage.getItem("current_user_uuid");
    this.maxDate = new Date();
    this.minDate = new Date();
    if (this.userType === 'Patient') {
      this.userService.getUserDetail().subscribe(
        (data) => {
          this.userProfile = data;
          this.patientUuid = data['uuid'];
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
    else if (this.userType == 'HospitalAdmin') {
      this.currentHsptId = localStorage.getItem("hstId");
      this.getDoctorPatient(this.currentHsptId);
      this.completeFormData.get('doctorType').setValue('2');
      this.archivedFormData.get('doctorType').setValue('2');
      this.deletedFormData.get('doctorType').setValue('2');
    }
    if( this.userType === 'Patient' || this.userType === 'DoctorAssistant' || this.userType == 'Partner'){
    this.tabSelection(1);
    }
    
  }

  getconsultationsList(tab: number, page: number, param?: string) {
    this.consultationsLoading = true;
    this.consultationsTotalPage = 0;
    this.selectedTab = tab;
    if (this.userType == 'PlatformAdmin'||this.userType === 'HospitalAdmin') {
      this.platformService.getConsultationForAdmin(page, param).subscribe(
        data => {
          this.setConsultationData(tab, data);
        },
        error => {
          this.consultationsLoading = false;
        }
      );

    } else if ( this.userType === 'Doctor' || this.userType === 'DoctorAssistant' || this.userType == 'Partner') {
      // this.patientService.getConsultationHistoryForBookUser(this.current_user_uuid, page, param).subscribe(
      this.platformService.getConsultation(page, param).subscribe(
        (data) => {
          this.setConsultationData(tab, data);
        }, error => {
          this.consultationsLoading = false;
        });
    } else if (this.userType === 'Patient') {
      this.userService.getUserDetail().subscribe(
        (data) => {
          this.userProfile = data;
          this.patientUuid = data['uuid'];
          this.patientService.getConsultationHistory(this.patientUuid, page, param).subscribe(
            (data) => {
              this.setConsultationData(tab, data);
            }, error => {
              this.consultationsLoading = false;
            });
        },
        (error) => {
          console.log(error);
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
  }

  consultationsNextPageList(tab: number) {
    this.consultationsCurrentPage = this.consultationsCurrentPage + 1;
    if (this.consultationsTotalPage >= this.consultationsCurrentPage) {
      this.getconsultationsList(tab, this.consultationsCurrentPage, this.formQueryParam(tab));
    } else {
      this.consultationsCurrentPage = this.consultationsCurrentPage - 1;
    }
  }

  consultationsLastPageList(tab: number) {
    this.getconsultationsList(tab, this.consultationsTotalPage, this.formQueryParam(tab));
  }

  consultationsFirstPageList(tab: number) {
    this.consultationsCurrentPage = 1;
    this.getconsultationsList(tab, this.consultationsCurrentPage, this.formQueryParam(tab));
  }

  consultationsPreviousPageList(tab: number) {
    this.consultationsCurrentPage = this.consultationsCurrentPage - 1;
    if (this.consultationsTotalPage >= this.consultationsCurrentPage && this.consultationsCurrentPage > 0) {
      this.getconsultationsList(tab, this.consultationsCurrentPage, this.formQueryParam(tab));
    } else {
      this.consultationsCurrentPage = this.consultationsCurrentPage + 1;
    }
  }

  tabSelection(id: any, pageSize?: number) {
    this.consultationsLoading = true;
    if (pageSize == undefined) {
      this.recordsPerPage = 10;
    } else {
      this.recordsPerPage = pageSize;
    }
    this.searchConsultation(id, 0);
    this.dateEnable(0, 0);
  }

  setDoctorType(event: any) {
    this.doctorType = event;
    switch (event) {
      case '1':
        this.getDoctorPatient(this.currentHsptId);
        this.completeFormData.get('hospital').setValue('');
        break;
      case '2':
        this.doctorList = [];
        this.patientList = [];
        this.getHospitalList();
        break;
    }
  }
  getDoctorPatient(id?: string) {
    if (id == undefined || id == null) {
      this.getDoctorList();
      this.getPatientList();
    }
    else {
      this.getDoctorList(id);
      this.getPatientList(id);
    }
  }
  getDoctorList(id?: string) {
    this.doctorList = [];
    this.sharedService.getDoctorList(id).subscribe((data: any) => {
      data.forEach(element => {
        this.doctorList.push(element);
      });
    });
  }
  getPatientList(id?: string) {
    this.patientList = [];
    this.sharedService.getPatientList(id).subscribe((data: any) => {
      data.forEach(element => {
        this.patientList.push(element);
      });
    });
  }
  getHospitalList() {
    this.sharedService.getHospitalList().subscribe((data: any) => {
      this.hospitalList = [];
      data.forEach(element => {
        this.hospitalList.push(element);
      });
    });

  }

  formQueryParam(tab: number): string {
    let param: string = '';
    let pageSize: number = this.recordsPerPage;
    // if (this.userType === 'HospitalAdmin' || this.userType === 'Doctor' || this.userType === 'DoctorAssistant' || this.userType == 'Partner') {
    //   param += '&book_user_uuid=' + this.current_user_uuid;
    // }
    if (this.recordsPerPage == undefined) {
      param += '&page_size=10'
    }
    else {
      param += '&page_size=' + pageSize;
    }
    switch (tab) {
      case 1:
        param += '&fulfilment_status=' + 'Completed%2BSuspended';
        if (this.completeFormData.value.fromDate) {
          param += '&from_date=' + this.formatDate(this.completeFormData.value.fromDate);
          this.dateEnable(1, 1);
        }
        if (this.completeFormData.value.toDate) {
          param += '&to_date=' + this.formatDate(this.completeFormData.value.toDate);
          this.dateEnable(2, 1);
        }
        if (this.completeFormData.value.hospital) {
          param += '&hospital_uuid=' + this.completeFormData.value.hospital;
        }
        if (this.completeFormData.value.doctor) {
          param += '&doctor_uuid=' + this.completeFormData.value.doctor;
        }
        if (this.completeFormData.value.patient) {
          param += '&patient_uuid=' + this.completeFormData.value.patient;
        }
        break;
      case 2:
        param += '&archived=true';
        if (this.archivedFormData.value.fromDate) {
          param += '&from_date=' + this.formatDate(this.archivedFormData.value.fromDate);
          this.dateEnable(1, 1);
        }
        if (this.archivedFormData.value.toDate) {
          param += '&to_date=' + this.formatDate(this.archivedFormData.value.toDate);
          this.dateEnable(2, 1);
        }
        if (this.archivedFormData.value.hospital) {
          param += '&hospital_uuid=' + this.archivedFormData.value.hospital;
        }
        if (this.archivedFormData.value.doctor) {
          param += '&doctor_uuid=' + this.archivedFormData.value.doctor;
        }
        if (this.archivedFormData.value.patient) {
          param += '&patient_uuid=' + this.archivedFormData.value.patient;
        }
        break;
      case 3:
        param += '&is_deleted=true';
        if (this.deletedFormData.value.fromDate) {
          param += '&from_date=' + this.formatDate(this.deletedFormData.value.fromDate);
          this.dateEnable(1, 1);
        }
        if (this.deletedFormData.value.toDate) {
          param += '&to_date=' + this.formatDate(this.deletedFormData.value.toDate);
          this.dateEnable(2, 1);
        }
        if (this.deletedFormData.value.hospital) {
          param += '&hospital_uuid=' + this.deletedFormData.value.hospital;
        }
        if (this.deletedFormData.value.doctor) {
          param += '&doctor_uuid=' + this.deletedFormData.value.doctor;
        }
        if (this.deletedFormData.value.patient) {
          param += '&patient_uuid=' + this.deletedFormData.value.patient;
        }
        break;
    }
    return param;
  }

  searchConsultation(tab: number, val: number) {
    this.selectedPatientId = "";
    if (val == 0) {
      const param = this.formQueryParam(tab);
      this.getconsultationsList(tab, 1, param);
    }
    else {
      if (this.fm(tab).fromDate == null || this.fm(tab).toDate == null ||
        this.fm(tab).fromDate == undefined || this.fm(tab).toDate == undefined ||
        this.fm(tab).fromDate == "" || this.fm(tab).toDate == ""
      ) {
        this.notificationService.error(`Please select From Date and To Date`, 'Med.Bot');
        return;
      }
      else {
        const param = this.formQueryParam(tab);
        this.getconsultationsList(tab, 1, param);
      }
    }
  }

  archiveConsultation(tab: number, confirm: number) {
    if (confirm == 1) {
      const formData = new FormData();
      formData.append('start_date', this.formatDate(this.completeFormData.value.fromDate));
      formData.append('end_date', this.formatDate(this.completeFormData.value.toDate));
      formData.append('patient_uuid', this.selectedPatientId);
      formData.append('action', 'archive');
      this.platformService.consultationArchiveDelete(formData).subscribe(
        data => {
          this.notificationService.success(`${data['message']}`, 'Med.Bot');
          this.tabSelection(tab);
          this.archivePopup = false;
          this.tabSelection(1);
        },
        error => {
          this.notificationService.error(error.error.error, 'Med.Bot');
          this.archivePopup = false;
        }
      );
    }
    else {
      if (this.selectedPatientId == "") {
        this.notificationService.error(`Please select any patient`, 'Med.Bot');
      } else {
        this.archivePopup = true;
      }
    }
  }
  deleteConsultation(tab: number, confirm: number) {
    if (confirm == 1) {
      const formData = new FormData();
      formData.append('start_date', this.formatDate(this.archivedFormData.value.fromDate));
      formData.append('end_date', this.formatDate(this.archivedFormData.value.toDate));
      formData.append('patient_uuid', this.selectedPatientId);
      formData.append('action', 'delete');
      this.platformService.consultationArchiveDelete(formData).subscribe(
        data => {
          this.tabSelection(tab);
          this.notificationService.success(`${data['message']}`, 'Med.Bot');
          this.deletePopup = false;
          this.tabSelection(2);
        },
        error => {
          this.notificationService.error(`${error['message']}`, 'Med.Bot');
          this.deletePopup = false;
        }
      );
    }
    else {
      this.deletePopup = true;
    }
  }

  formatDate(val) {
    return moment(val).format('YYYY-MM-DD');
  }
  setConsultationData(tab: number, data: any) {
    this.consultationsTotalPage = 0;
    this.consultationsCurrentPage = 0;
    switch (tab) {
      case 1:
        this.completedConsultations = data['results'];
        break;
      case 2:
        this.archivedConsultations = data['results'];
        break;
      case 3:
        this.deletedConsultations = data['results'];
        break;
    }
    this.consultationsTotalPage = data['total_pages'];
    this.consultationsCurrentPage = data['page_number'];
    this.consultationsLoading = false;
    this.consultationCount = data['count'];

  }

  selectedPatientList(patientId: string) {
    this.selectedPatientId = patientId;
  }

  createOTP() {
    this.deletePopup = false;
    this.formReset(4);
    const formData = new FormData();
    formData.append('start_date', this.formatDate(this.archivedFormData.value.fromDate));
    formData.append('end_date', this.formatDate(this.archivedFormData.value.toDate));
    this.platformService.createOTP(formData).subscribe(
      data => {
        // this.notificationService.success(`${data['message']}`, 'Med.Bot');
        this.otpPopup = true;
        this.countdown();
      },
      error => {
        console.log(error);
        this.notificationService.error(error.error.error, 'Med.Bot');
      }
    );
  }
  otpVerification() {
    const formData = new FormData();
    this.otpValue = this.otpForm.value.otp1 + this.otpForm.value.otp2 + this.otpForm.value.otp3 + this.otpForm.value.otp4;
    formData.append('password', this.otpValue)
    this.platformService.otpVerification(formData).subscribe(
      data => {
        this.notificationService.success(`${data['message']}`, 'Med.Bot');
        this.deleteConsultation(2, 1);
        this.otpPopup = false;
      },
      error => {
        console.log(error);
      }
    );
  }
  countdown() {
    this.resendOtp = false;
    const countdownInterval = setInterval(() => {
      if (this.seconds >= 1) {
        this.seconds--;
      } else {
        clearInterval(countdownInterval);
        this.resendOtp = true;
        this.seconds = 90;
      }
    }, 1000);
  }
  pass(c: any, n: any) {
    var length = c.value.length;
    var maxlength = c.getAttribute("maxlength");
    if (length == maxlength && n != "") {
      n.focus();
    }
  }
  deleteConfirm() {
    if (this.selectedPatientId == "") {
      this.notificationService.error(`Please select any patient`, 'Med.Bot');
    }
    else {
      this.deletePopup = true;
    }
  }
  formReset(val: number) {
    switch (val) {
      case 1:
        this.completeFormData.reset();
        this.completeFormData.get('doctorType').setValue('0');
        this.tabSelection(val);
        break;
      case 2:
        this.archivedFormData.reset();
        this.archivedFormData.get('doctorType').setValue('0');
        this.tabSelection(val);
        break;
      case 3:
        this.deletedFormData.reset();
        this.deletedFormData.get('doctorType').setValue('0');
        this.tabSelection(val);
        break;
      case 4:
        this.otpForm.reset();
        break;
    }

  }
  dateEnable(value: number, result: number) {
    switch (value) {
      case 0:
        this.SDate = (result == 1 ? true : false);
        this.EDate = (result == 1 ? true : false);
      case 1:
        this.SDate = (result == 1 ? true : false);
        break;
      case 2:
        this.EDate = (result == 1 ? true : false);
        break;
    }
  }
  fm(val: number) {
    switch (val) {
      case 1:
        return this.completeFormData.value;
      case 2:
        return this.archivedFormData.value;
      case 3:
        return this.deletedFormData.value;
      default:
        break;
    }
  }
}
