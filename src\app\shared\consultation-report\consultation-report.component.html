<div class="container-fluid">
    <div class="row">
        <div class="col-md-12 col-lg-12 col-xl-12">
            <div class="m-4">
                <div class="">
                    <div class="">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-4 dashboard-font-size">Consultation Report</h4>
                                <div class="appointment-tab">
                                    <form class="earning-report-form" [formGroup]="completeFormData">
                                        <div class="mb-5">
                                            <div class="row">
                                                <div class="col-md-3" *ngIf="userType=='PlatformAdmin'">
                                                    <label>Doctor Type</label>
                                                    <select class="form-control input-field-border select"
                                                        name="doctorType" id="doctorType" formControlName="doctorType"
                                                        (change)="setDoctorType($event.target.value)">
                                                        <option value="0" disabled>Select Doctor Type</option>
                                                        <option value="1" translate>Individual</option>
                                                        <option value="2" translate>Hospital-Based</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-3"
                                                    *ngIf="userType=='PlatformAdmin' && completeFormData.value.doctorType=='2'">
                                                    <label>Hospitals</label>
                                                    <select class="form-control select" name="hospital" id="hospital"
                                                        formControlName="hospital"
                                                        (change)="setHospital($event.target.value)">
                                                        <option value="0">Select Hospital</option>
                                                        <option *ngFor="let item of hospitalList" [value]="item.uuid">
                                                            {{item.name}}</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-3"
                                                    *ngIf="(userType=='PlatformAdmin') && completeFormData.value.doctorType=='1'">
                                                    <label>Doctors</label>
                                                    <select class="form-control select" name="doctor" id="doctor"
                                                        formControlName="doctor">
                                                        <option value="0">Select Doctor</option>
                                                        <option *ngFor="let item of doctorList" [value]="item.uuid">
                                                            {{item.username}}</option>
                                                    </select>
                                                </div>

                                                <div class="col-md-3" *ngIf="showFilterFields('consultationBased')">
                                                    <label>Consultation Based</label>
                                                    <select class="form-control input-field-border select"
                                                        name="consultationBased" id="consultationBased"
                                                        formControlName="consultationBased"
                                                        (change)="setConsultBased($event.target.value)">
                                                        <option value="0" disabled>Select Consultation Based</option>
                                                        <option value="1" translate *ngIf="userType!='DoctorAssistant'">
                                                            Centre</option>
                                                        <option value="2" translate>Doctor</option>
                                                        <option value="3" translate>Patient</option>
                                                    </select>
                                                </div>

                                                <div class="col-md-3" *ngIf="showFilterFields('centre')">
                                                    <label>Centres</label>
                                                    <select class="form-control select" name="centre" id="centre"
                                                        formControlName="centre">
                                                        <option value="0">Select Centre</option>
                                                        <option *ngFor="let item of centreList" [value]="item.uuid">
                                                            {{item.username}}</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-3" *ngIf="showFilterFields('doctor')">
                                                    <label>Doctors</label>
                                                    <select class="form-control select" name="doctor" id="doctor"
                                                        formControlName="doctor"
                                                        (change)="setSelectedDoctor($event.target.value)">
                                                        <option value="0">Select Doctor</option>
                                                        <option *ngFor="let item of doctorList" [value]="item.uuid">
                                                            {{item.username}}</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-3" *ngIf="showFilterFields('patient')">
                                                    <label>Patients</label>
                                                    <select class="form-control select" name="patient" id="patient"
                                                        formControlName="patient"
                                                        (change)="setSelectedPatient($event.target.value)">
                                                        <option value="0">Select Patient</option>
                                                        <option *ngFor="let item of patientList" [value]="item.uuid">
                                                            {{item.username}}</option>
                                                    </select>
                                                </div>

                                                <div class="col-md-3">
                                                    <label>From Date</label>
                                                    <input type="text" id="from_date" placeholder="From Date"
                                                        onkeydown="return false"
                                                        class="form-control input-field-border mb-2" [minDate]=""
                                                        [maxDate]="" bsDatepicker
                                                        [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }"
                                                        formControlName="fromDate">
                                                </div>
                                                <div class="col-md-3">
                                                    <label>To Date</label>
                                                    <input type="text" id="to_date" placeholder="To Date"
                                                        onkeydown="return false"
                                                        class="form-control input-field-border mb-2" [minDate]=""
                                                        [maxDate]="" bsDatepicker
                                                        [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }"
                                                        formControlName="toDate">
                                                </div>
                                            </div>
                                            <button class="btn btn-primary btn-sm float-right ml-2"
                                                (click)="getConsultationReportList(1)">Submit</button>
                                            <button class="btn btn-primary btn-sm float-right"
                                                (click)="getConsultationAsExcel()">Export Excel</button>
                                        </div>
                                    </form>

                                    <div class="tab-content">
                                        <!-- Upcoming Appointment Tab -->
                                        <div class="tab-pane show active" id="admin-user">
                                            <div class="col-md-12 float-right mt-2 tab_pager_position">
                                                <div class="tab_pager_position float-right">
                                                    <nav aria-label="Page navigation example"
                                                        *ngIf="this.consultationReportTotalPage > 1">
                                                        <ul class="pagination pager_position">
                                                            <li class="page-item"
                                                                (click)="consultationReportFirstPageList()" [ngClass]="{
                                  'disabled-pagination':
                                    consultationReportCurrentPage === 1
                                }">
                                                                <a class="page-link">&lt;&lt;</a>
                                                            </li>
                                                            <li class="page-item"
                                                                (click)="consultationReportPreviousPageList()"
                                                                [ngClass]="{
                                  'disabled-pagination':
                                    consultationReportCurrentPage === 1
                                }">
                                                                <a class="page-link">&lt;</a>
                                                            </li>
                                                            <li class="page-item">
                                                                <a class="page-link">page &nbsp;
                                                                    {{consultationReportCurrentPage}}&nbsp;of&nbsp; {{
                                                                    consultationReportTotalPage }}</a>
                                                            </li>
                                                            <li class="page-item"
                                                                (click)="consultationReportNextPageList()" [ngClass]="{
                                  'disabled-pagination':
                                    consultationReportCurrentPage ===
                                    consultationReportTotalPage
                                }">
                                                                <a class="page-link">&gt;</a>
                                                            </li>
                                                            <li class="page-item"
                                                                (click)="consultationReportLastPageList()" [ngClass]="{
                                  'disabled-pagination':
                                    consultationReportCurrentPage ===
                                    consultationReportTotalPage
                                }">
                                                                <a class="page-link">&gt;&gt;</a>
                                                            </li>
                                                        </ul>
                                                    </nav>
                                                </div>
                                            </div>
                                            <div *ngIf="consultationReportLoading">
                                                <app-loading-spinner></app-loading-spinner>
                                            </div>
                                            <div class="card card-table mb-0"
                                                *ngIf="!consultationReportLoading && currentMonthEarning.length>0">
                                                <div class="card-body">
                                                    <div class="table-responsive">
                                                        <table class="table table-hover table-center mb-0">
                                                            <thead>
                                                                <tr>
                                                                    <th>Sl.No</th>
                                                                    <th>Patient Name</th>
                                                                    <th
                                                                        *ngIf="userType!='DoctorAssistant' && userType!='Doctor'">
                                                                        Hospital Name</th>
                                                                    <th
                                                                        *ngIf="consultType!=1 && userType!='DoctorAssistant'&& userType!='Doctor'">
                                                                        Centre Name</th>
                                                                    <th *ngIf="consultType!=2 || userType!='Doctor'">
                                                                        Doctor Name</th>
                                                                    <th>No of Consultation</th>
                                                                    <th>Earnings</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr
                                                                    *ngFor="let data of currentMonthEarning;let i=index">
                                                                    <td>{{consultationReportSerialNumber+i+1}}</td>
                                                                    <td>{{data.customer_name}} </td>
                                                                    <td
                                                                        *ngIf="userType!='DoctorAssistant'&& userType!='Doctor'">
                                                                        {{data.hospital_name}} </td>
                                                                    <th
                                                                        *ngIf="consultType!=1 && userType!='DoctorAssistant'&& userType!='Doctor'">
                                                                        {{data.centre_name?data.centre_name:''}}</th>
                                                                    <th *ngIf="consultType!=2 || userType!='Doctor'">
                                                                        {{data.doctor_username}}</th>
                                                                    <td>{{data.consultation_count}}</td>
                                                                    <td>{{data.total_amount }}</td>
                                                                </tr>
                                                                <!-- <tr *ngIf="currentMonthEarning.length >0">
                                    <td colspan="6"></td>
                                    <th>Total</th>
                                    <th>{{totalAmount}}</th>
                                  </tr> -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <div class="text-center mb-2 p-2 mt-2">
                                                        <span class="appointmentList-no-data"
                                                            *ngIf="currentMonthEarning.length ===0">No Earning
                                                            Data</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="float-right mt-2">
                                                <div class="float-right">
                                                    <nav aria-label="Page navigation example"
                                                        *ngIf="this.consultationReportTotalPage > 1">
                                                        <ul class="pagination">
                                                            <li class="page-item"
                                                                (click)="consultationReportFirstPageList()" [ngClass]="{
                                  'disabled-pagination':
                                    consultationReportCurrentPage === 1
                                }">
                                                                <a class="page-link">&lt;&lt;</a>
                                                            </li>
                                                            <li class="page-item"
                                                                (click)="consultationReportPreviousPageList()"
                                                                [ngClass]="{
                                  'disabled-pagination':
                                    consultationReportCurrentPage === 1
                                }">
                                                                <a class="page-link">&lt;</a>
                                                            </li>
                                                            <li class="page-item">
                                                                <a class="page-link">page &nbsp;{{
                                                                    consultationReportCurrentPage
                                                                    }}&nbsp;of&nbsp; {{ consultationReportTotalPage
                                                                    }}</a>
                                                            </li>
                                                            <li class="page-item"
                                                                (click)="consultationReportNextPageList()" [ngClass]="{
                                  'disabled-pagination':
                                    consultationReportCurrentPage ===
                                    consultationReportTotalPage
                                }">
                                                                <a class="page-link">&gt;</a>
                                                            </li>
                                                            <li class="page-item"
                                                                (click)="consultationReportLastPageList()" [ngClass]="{
                                  'disabled-pagination':
                                    consultationReportCurrentPage ===
                                    consultationReportTotalPage
                                }">
                                                                <a class="page-link">&gt;&gt;</a>
                                                            </li>
                                                        </ul>
                                                    </nav>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- /Upcoming Appointment Tab -->

                                        <!-- Today Appointment Tab -->
                                        <div class="tab-pane" id="doctor-assistant">
                                            <div class="card card-table mb-0">
                                                <div class="card-body">
                                                    <div class="table-responsive">
                                                        <table class="table table-hover table-center mb-0">
                                                            <thead>
                                                                <tr>
                                                                    <th>Name</th>
                                                                    <th>Email</th>
                                                                    <th>Phone</th>
                                                                    <th>Action</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <!-- <tr *ngFor="let data of assistantList;let i=index">
                                  <td>{{data.username}}    </td>
                                  <td>{{data.email}}</td>
                                  <td>{{data.phone}}</td>
                                  <td><button class="btn btn-primary" disabled>view</button></td>
                                </tr> -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <div class="text-center mb-2 p-2 mt-2">
                                                        <span class="appointmentList-no-data"
                                                            style="color: orangered;">No Assistant Data</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- /Today Appointment Tab -->

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>