import { Component, OnInit } from '@angular/core';
import { DoctorService } from '../../doctor/doctor.service';
import { AuthService } from '../../auth/auth.service';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { SharedService } from 'src/app/shared/shared.service';
import * as moment from 'moment';


@Component({
  selector: 'app-consultation-report',
  templateUrl: './consultation-report.component.html',
  styleUrls: ['./consultation-report.component.css']
})
export class ConsultationReportComponent implements OnInit {
  public doctorId: string;
  public currentMonthEarning: any = [];
  consultationReportCurrentPage: any;
  public consultationReportSerialNumber: number = 0;
  consultationReportTotalPage: any;
  consultationReportLoading: boolean;
  public totalAmount: number;
  public doctorTotalAmount: number;
  public plateformTotalAmount: number;
  public filter: string;
  public fromDate = '';
  public toDate = '';
  hospital_uuid: any;
  uuid: any;
  report: any;
  public fulfilment_status = null;
  public payment_mode = null;
  public appointment_type = null;
  userType: string;
  completeFormData: FormGroup;
  hospitalList: any = [];
  doctorList: any = [];
  doctorType: number = 0;
  patientList: any[];
  selectedHospitalId: any;
  centerList: any[];
  consultType: any;
  selectedPatient: string;
  selectedDoctor: string;
  indiviualDoc: boolean = false;
  searchedQuery: any;

  constructor(private doctorService: DoctorService, private userService: AuthService, private notificationService: ToastrService,
    private location: Location, private activatedRoute: ActivatedRoute, private sharedService: SharedService, private fb: FormBuilder) {
    this.completeFormData = this.fb.group({
      doctorType: new FormControl('0'),
      hospital: new FormControl(''),
      doctor: new FormControl(''),
      centre: new FormControl(''),
      patient: new FormControl(''),
      consultationBased: new FormControl('0'),
      fromDate: new FormControl(''),
      toDate: new FormControl(''),
    });
  }

  ngOnInit(): void {
    this.sharedService.setActiveLink('hadoctor-reports');
    this.userType = localStorage.getItem('user_type');

    this.report = localStorage.getItem('report');
    if (this.userType == 'HospitalAdmin') {
      this.hospital_uuid = localStorage.getItem('hstId');
    } else if (this.userType == 'Partner') {
      this.uuid = localStorage.getItem("current_user_uuid");
      this.hospital_uuid = localStorage.getItem('hstId');
    } else if (this.userType == 'Patient') {
    } else if (this.userType == 'Doctor') {
      this.uuid = localStorage.getItem("current_user_uuid");
      this.indiviualDoc = this.isIndividualDoctor();
    } else if (this.userType == 'PlatformAdmin') {
    } else if (this.userType == 'DoctorAssistant') {
      this.uuid = localStorage.getItem("current_user_uuid");
      this.hospital_uuid = localStorage.getItem('hstId');
    }

    this.consultationReportLoading = true;
    this.consultationReportSerialNumber = 0;

    this.userService.getUserDetail().subscribe(
      (data) => {
        this.doctorId = data['uuid'];
        if (this.userType != 'PlatformAdmin') {
          this.getConsultationReportList(1);
        } else {
          this.consultationReportLoading = false;
          this.consultationReportTotalPage = 0;
          this.currentMonthEarning = [];
        }
      },
      (error) => {
        console.log(error);
      }
    );
    console.log('init successful');
  }

  setDoctorType(event: any) {
    this.doctorType = event;
    this.consultType = event == 1 ? 2 : 0;
    switch (event) {
      case '1':
        this.hospitalList = [];
        if (this.userType == 'PlatformAdmin') {
          this.getDoctorPatient();
        }
        else {
          this.getDoctorPatient(this.hospital_uuid);
        }
        break;
      case '2':
        this.doctorList = [];
        this.getHospitalList();
        break;
    }
    this.completeFormData.get('consultationBased').setValue('0');
    this.completeFormData.get('centre').setValue('0');
    this.completeFormData.get('doctor').setValue('0');
    this.completeFormData.get('hospital').setValue('0');
    this.completeFormData.get('patient').setValue('0');
    this.showFilterFields('consultationBased');
    this.showFilterFields('centre');
    this.showFilterFields('doctor');
    this.showFilterFields('patient');
  }

  getDoctorPatient(id?: string) {
    if (id == undefined || id == null) {
      this.getDoctorList();
    }
    else {
      this.getDoctorList(id);
    }
  }

  getDoctorList(id?: string) {
    this.doctorList = [];
    this.sharedService.getDoctorList(id).subscribe((data: any) => {
      data.forEach(element => {
        this.doctorList.push(element);
      });
    });
  }

  getHospitalList() {
    this.sharedService.getHospitalList().subscribe((data: any) => {
      this.hospitalList = [];
      data.forEach(element => {
        this.hospitalList.push(element);
      });
    });
  }

  showFilterFields(val: string) {
    let result = false;
    switch (val) {
      case 'consultationBased':
        if (this.userType == 'PlatformAdmin' && this.completeFormData.value.doctorType == '2' && this.completeFormData.value.hospital != '0') {
          result = true;
        } else if ((this.userType == 'PlatformAdmin' && this.completeFormData.value.doctorType == '1') || (this.indiviualDoc == true)) {
          result = false;
        } else if (this.userType != 'PlatformAdmin') {
          result = true;
        }
        break;
      case 'centre':
        if (this.completeFormData.value.consultationBased == '1') {
          result = true;
        }
        break;
      case 'doctor':
        if (this.completeFormData.value.consultationBased == '2') {
          result = true;
        }
        break;
      case 'patient':
        if (this.completeFormData.value.consultationBased == '3') {
          result = true;
        }
        break;
    }
    return result;
  }

  setHospital(val: any) {
    this.hospital_uuid = val;
    if (this.completeFormData.value.hospital != '0') {
      this.completeFormData.get('consultationBased').setValue('0');
      this.showFilterFields('consultationBased');
    }
    this.completeFormData.get('centre').setValue('0');
    this.completeFormData.get('doctor').setValue('0');
    this.completeFormData.get('patient').setValue('0');
    this.showFilterFields('centre');
    this.showFilterFields('doctor');
    this.showFilterFields('patient');
    this.getHospitalBasedList(this.hospital_uuid);
  }

  getConsultationReportList(page) {
    this.totalAmount = 0;
    this.doctorTotalAmount = 0;
    this.plateformTotalAmount = 0;
    this.consultationReportLoading = true;
    this.consultationReportTotalPage = 0;
    this.currentMonthEarning = [];

    var query = '?page=' + page;
    if (this.completeFormData.value.fromDate != '' && this.completeFormData.value.toDate != '') {
      let fromDate = moment(this.completeFormData.value.fromDate).format('YYYY-MM-DD');
      let toDate = moment(this.completeFormData.value.toDate).format('YYYY-MM-DD');
      query += '&from_date=' + fromDate + '&to_date=' + toDate;
    }

    let uniqueID = '';
    if (this.userType == 'HospitalAdmin') {
      uniqueID = this.hospital_uuid;
      // this.getEarningDataHa(uniqueID, query);
      if (uniqueID != undefined && uniqueID != null) {
        query += this.setQueryParam(uniqueID);
        this.getConsultationReport(query);
      } else {
        this.notificationService.warning('Please select one of the hospital from the list', 'Med.Bot');
      }
    } else if (this.userType == 'Partner' || this.userType == 'DoctorAssistant') {
      query += '&doctor_assistant_uuid=' + this.uuid;
      if (this.consultType == 2 && this.selectedDoctor != undefined && this.selectedDoctor != null) {
        query += '&doctor_uuid=' + this.selectedDoctor + '&hospital_uuid=' + this.hospital_uuid;
      }
      else if (this.consultType == 3 && this.selectedPatient != undefined && this.selectedPatient != null) {
        query += '&patient_uuid=' + this.selectedPatient + '&hospital_uuid=' + this.hospital_uuid;
      } else if (this.consultType != 0 && this.consultType != undefined && this.consultType != null) {
        let val = this.consultType == 2 ? 'Doctor' : 'Patient' + '&hospital_uuid=' + this.hospital_uuid;
        this.notificationService.warning('Please select one of the ' + val + ' from the list', 'Med.Bot');
      }
      // this.getEarningDataHa(uniqueID, query);

      this.getConsultationReport(query);
    } else if (this.userType == 'PlatformAdmin') {
      if (this.completeFormData.value.doctorType == '1' && this.completeFormData.value.doctor != undefined && this.completeFormData.value.doctor != null) {
        uniqueID = this.completeFormData.value.doctor;
        query += '&doctor_uuid=' + this.completeFormData.value.doctor;
        this.getConsultationReport(query);
      } else if (this.completeFormData.value.doctorType == '2') {
        let hospital_uuid = this.completeFormData.value.hospital;
        query += this.setQueryParam(hospital_uuid);
        this.getConsultationReport(query);
      }
    } else if (this.userType == 'Doctor') {
      uniqueID = this.uuid;
      if (uniqueID != undefined && uniqueID != null) {
        query += '&doctor_uuid=' + this.uuid;
      }
      this.getConsultationReport(query);
    }
  }

  getConsultationReport(query) {
    this.sharedService.getConsultationReport(query).subscribe((data: any) => {
      console.log(data);
      this.setConsultationData(data);
      this.consultationReportLoading = false;
      this.searchedQuery = query;

    },
      (error) => {
        this.consultationReportLoading = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }
  setQueryParam(hsptId: string) {
    var queryParam = '';
    if (this.completeFormData.value.doctor != '' && this.completeFormData.value.doctor != 0) {
      queryParam = '&doctor_uuid=' + this.completeFormData.value.doctor + '&hospital_uuid=' + hsptId;
    } if (this.completeFormData.value.centre != '' && this.completeFormData.value.centre != 0) {
      queryParam = '&centre_uuid=' + this.completeFormData.value.centre + '&hospital_uuid=' + hsptId;
    } if (this.completeFormData.value.patient != '' && this.completeFormData.value.patient != 0) {
      queryParam = '&hospital_uuid=' + hsptId + '&patient_uuid=' + this.completeFormData.value.patient;
    } if (queryParam == '' && hsptId != '0') {
      queryParam = '&hospital_uuid=' + hsptId;
    }
    return queryParam;

  }
  setConsultationData(data: any) {
    this.currentMonthEarning = data['results'];
    this.consultationReportTotalPage = data['total_pages'];
    this.consultationReportCurrentPage = data['page_number'];
    this.consultationReportLoading = false;
    for (let data of this.currentMonthEarning) {
      this.totalAmount = this.totalAmount + parseInt(data.gross_amount);
      this.doctorTotalAmount = this.doctorTotalAmount + parseInt(data.net_amount);
      this.plateformTotalAmount = this.plateformTotalAmount + parseInt(data.platform_service_fee);

    }
  }

  getEarningDataDoctor(uniqueID: string, query: string) {
    this.doctorService.getEarningData(query, uniqueID).subscribe(
      (data) => {
        this.currentMonthEarning = data['results'];
        this.consultationReportTotalPage = data['total_pages'];
        this.consultationReportCurrentPage = data['page_number'];
        this.consultationReportLoading = false;
        for (let data of this.currentMonthEarning) {
          this.totalAmount = this.totalAmount + parseInt(data.gross_amount);
          this.doctorTotalAmount = this.doctorTotalAmount + parseInt(data.net_amount);
          this.plateformTotalAmount = this.plateformTotalAmount + parseInt(data.platform_service_fee);
        }
      },
      (error) => {
        this.consultationReportLoading = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }

  getEarningDataHa(uniqueID: string, query: string) {
    this.doctorService.getEarningDataHa(uniqueID, query).subscribe((data) => {
      this.currentMonthEarning = data['results'];
      this.consultationReportTotalPage = data['total_pages'];
      this.consultationReportCurrentPage = data['page_number'];
      this.consultationReportLoading = false;
      for (let data of this.currentMonthEarning) {
        this.totalAmount = this.totalAmount + parseInt(data.gross_amount);
        this.doctorTotalAmount = this.doctorTotalAmount + parseInt(data.net_amount);
        this.plateformTotalAmount = this.plateformTotalAmount + parseInt(data.platform_service_fee);
      }
    }, error => {
      this.consultationReportLoading = false;
      const status = error['status'];
      if (status == 400) {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
      else {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
    });
  }

  consultationReportNextPageList() {
    this.consultationReportCurrentPage = this.consultationReportCurrentPage + 1;
    if (this.consultationReportTotalPage >= this.consultationReportCurrentPage) {
      this.getConsultationReportList(this.consultationReportCurrentPage);
      this.consultationReportSerialNumber = (this.consultationReportCurrentPage - 1) * 10;
    } else {
      this.consultationReportCurrentPage = this.consultationReportCurrentPage - 1;
    }
  }

  consultationReportLastPageList() {
    this.consultationReportSerialNumber = (this.consultationReportTotalPage - 1) * 10;
    this.getConsultationReportList(this.consultationReportTotalPage);

  }

  consultationReportFirstPageList() {
    this.consultationReportCurrentPage = 1;
    this.consultationReportSerialNumber = 0;
    this.getConsultationReportList(this.consultationReportCurrentPage);
  }

  consultationReportPreviousPageList() {
    this.consultationReportCurrentPage = this.consultationReportCurrentPage - 1;
    if (this.consultationReportTotalPage >= this.consultationReportCurrentPage && this.consultationReportCurrentPage > 0) {
      this.getConsultationReportList(this.consultationReportCurrentPage);
      this.consultationReportSerialNumber = (this.consultationReportCurrentPage - 1) * 10;
    } else {
      this.consultationReportCurrentPage = this.consultationReportCurrentPage + 1;
    }
  }
  setConsultBased(value: any) {
    let hsptId = this.hospital_uuid;
    if (hsptId == null || hsptId == undefined) {
      hsptId = '';
    }
    this.consultType = value;
    switch (value) {
      case '1':
        // this.getCenterList();
        break;
      case '2':
        this.getDoctorList(hsptId);
        break;
      case '3':
        this.getPatientList(hsptId);
        break;

    }

  }
  getPatientList(id?: string) {
    this.patientList = [];
    this.sharedService.getPatientList(id).subscribe((data: any) => {
      data.forEach(element => {
        this.patientList.push(element);
      });
    });
  }
  getHospitalBasedList(hospitalId: string) {
    this.centerList = [];
    this.getDoctorList(hospitalId);
    this.getPatientList(hospitalId);
    // this.getCenterList();
  }
  getCsvReport() {
    this.doctorService.getEarningCsvDataHa(this.doctorId).subscribe((data) => {

    }, error => { })
  }

  back() {
    this.location.back();
  }

  ngOnDestroy() {
    localStorage.removeItem('report');
  }

  setSelectedDoctor(uuid: string) {
    this.selectedDoctor = uuid;
  }
  setSelectedPatient(uuid: string) {
    this.selectedPatient = uuid;
  }
  isIndividualDoctor() {
    var status1 = localStorage.getItem('practice');
    var status2 = localStorage.getItem('bank');
    var status3 = localStorage.getItem('profile_approved_status');
    if (status1 == 'true' && status2 == 'true' && status3 == 'Approved') {
      return true;
    }
    else {
      return false;
    }
  }
  getConsultationAsExcel() {
    if (this.currentMonthEarning.length == 0) {
      this.notificationService.warning('No Record found to Export!', 'Med.Bot')
    } else {
      let exportQuery = this.searchedQuery + '&export_to_csv=True'
      this.sharedService.getConsultationExcelReport(exportQuery).subscribe(
        (result: any) => {
          const date = moment(new Date()).format('YYYY-MM-DD');
          const blob = new Blob([result.body], { type: 'text/csv' });
          const link = document.createElement('a');
          const url = URL.createObjectURL(blob);
          link.href = url;
          link.download='consultation_report_'+date+'.csv'; 
          link.click();
          // Clean up
          URL.revokeObjectURL(url);
        },
        (error) => {
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
          else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
        );
    }
  }
}