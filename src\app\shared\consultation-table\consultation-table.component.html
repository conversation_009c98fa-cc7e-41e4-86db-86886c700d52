<div class="col-md-12">
  <div class="card card-table mb-0">
    <div class="card-body">
      <div class="table-responsive" *ngIf="consultations.length > 0">
        <table class="table table-hover table-center mb-0">
          <thead>
            <tr>
              <th *ngIf="checkPermission('checkBox')">
                <input type="checkbox" (click)="selectAll($event)">
              </th>
              <th *ngIf="checkPermission('doctorName')">Doctor Name</th>
              <th *ngIf="checkPermission('patienName')">Patient Name</th>
              <th *ngIf="checkPermission('consultedDate')">Consulted Date</th>
              <th *ngIf="checkPermission('consultedTime')">Consulted time</th>
              <th *ngIf="checkPermission('patientType')">Patient Type</th>
              <th *ngIf="checkPermission('IP_OP_ID')">IP/OP ID</th>
              <th>View</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let consult of consultations; let i = index">
              <td *ngIf="checkPermission('checkBox')">
                <input type="checkbox" [checked]="allSelected" (click)="selectedUserId($event,consult.patient_json.uuid)">
              </td>
              <td *ngIf="checkPermission('doctorName')">
                <h2 class="table-avatar">
                  <a class="avatar avatar-sm mr-2">
                    <img class="avatar-img rounded-circle"
                      [src]="consultations[i].doctor_json.user.profile_picture?consultations[i].doctor_json.user.profile_picture :'../assets/img/doctors/doctor-thumb-02.png'"
                      alt="User Image" />
                  </a>
                  <a>{{ consultations[i].doctor_json.user.username }}</a>
                </h2>
              </td>
              <td *ngIf="checkPermission('patienName')">
                <h2 class="table-avatar">
                  <a class="avatar avatar-sm mr-2">
                    <img class="avatar-img rounded-circle"
                      [src]="consultations[i].patient_json.profile_picture?consultations[i].patient_json.profile_picture :'../assets/img/doctors/doctor-thumb-02.png'"
                      alt="User Image" />
                  </a>
                  <a>{{ consultations[i].patient_json.username }}</a>
                </h2>
              </td>
              <td *ngIf="checkPermission('consultedDate')">
                {{
                consult.scheduled_start_datetime | date: "mediumDate"
                }}
              </td>
              <td *ngIf="checkPermission('consultedTime')">
                {{ consult.scheduled_start_datetime | date: "hh:mm a" }}
              </td>
              <td *ngIf="checkPermission('patientType')">
                {{ consult.patient_type }}
              </td>
              <td *ngIf="checkPermission('IP_OP_ID')">
                {{ consult.patient_unique_id }}
              </td>
              <!-- <td>14 Nov 2019</td> -->
              <button class="btn btn-info ml-2 mt-3" (click)="
                      viewPrescription(
                        consult.patient_uuid,
                        consult.doctor_uuid,
                        consult.uuid
                      )
                    ">
                Details
              </button>
              <button class="btn btn-info ml-2 mt-3" (click)="
                      bookAppointment(
                        consult.doctor_uuid,
                        consult.doctor_json.practicelocations[0].uuid,
                        consult.uuid,
                        consult.patient_uuid
                      )
                      " *ngIf="
                          consult.fulfilment_status === 'Suspended' &&
                          consult.c_appointment_count_allowed_during_suspension >
                            0
                        ">
                Follow
              </button>
              <button *ngIf="
                      consult.fulfilment_status === 'Suspended' && userType=='patient' && 
                      consult.c_appointment_count_allowed_during_suspension >
                        0
                    " type="button" class="btn btn-info ml-2 mt-3" data-toggle="modal" data-target="#upload-report1"
                (click)="getReportId(consult.uuid,consult.patient_user_uuid)">
                Upload
              </button>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="text-center mb-2 p-2 mt-2" *ngIf="consultations.length === 0">
        <span class="appointmentList-no-data">Consultation data not found
        </span>
      </div>
    </div>
  </div>
  <!-- upload modal starts here -->
  <div class="modal fade" id="upload-report1" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Report</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-4">
              <ng-select id="reportType" [items]="reportTypes" [formControl]="selectedDiagnosticReportName"
                [clearable]="false" [searchable]="false" bindLabel="testType" bindValue="id" placeholder="Report Type"
                (change)="getReportType($event)">
              </ng-select>
            </div>
            <div class="col-md-4 mb-1">
              <input type="text" [maxDate]="maxDate" [minDate]="minDate" placeholder="Report Generated On"
                onkeydown="return false" class="form-control" [(ngModel)]="reportDate" bsDatepicker [bsConfig]="{
                  showWeekNumbers: false,
                  isAnimated: true,
                  dateInputFormat: 'DD-MM-YYYY'
                }" />
            </div>
            <div class="col-md-4 mb-1">
              <div class="change-photo-btn" style="
                  padding-left: 5px !important;
                  margin-left: 0px;
                  width: 138px;
                  font-family: sans-serif;
                ">
                <span><i class="fa fa-upload ic">&nbsp;Choose File</i></span>
                <input type="file" class="upload" id="medical-report" (change)="medicalReports($event)"
                  accept=".jpg, .jpeg,.pdf" />
              </div>
              <small *ngIf="reportName">&nbsp;{{ reportName }}</small>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="col-md-12 text-center">
            <button class="btn btn-primary" (click)="saveMedicalReport()"
              [disabled]="!selectedDiagnosticReportName || !reportFile">
              Save
            </button>
            <button type="button" class="btn btn-secondary ml-2" data-dismiss="modal">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- upload modal ends here -->

</div>