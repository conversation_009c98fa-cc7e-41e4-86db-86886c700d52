import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { PatientService } from 'src/app/patient/patient.service';
declare var $: any;


@Component({
  selector: 'app-consultation-table',
  templateUrl: './consultation-table.component.html',
  styleUrls: ['./consultation-table.component.css']
})
export class ConsultationTableComponent implements OnInit {
  @Input() consultations: any;
  @Input() userType: string;
  @Input() selectedTab: number;
  @Output() selectedPatientId: EventEmitter<string> = new EventEmitter();
  patients: string[] = [];
  allSelected: boolean = false;
  consultationUuid: any;
  reportPatientUuid: any;
  reportTypes = [
    { id: 'Pathology Test', testType: 'Pathology Test' },
    { id: 'Prescription', testType: 'Prescription' },
    { id: 'X-Ray', testType: 'X-Ray' },
    { id: 'Ultra Sound Scan', testType: 'Ultra Sound Scan' },
    { id: 'CT Scan', testType: 'CT Scan' },
    { id: 'MRI Scan', testType: 'MRI Scan' },
    { id: 'Prescription', testType: 'Prescription' },
    { id: 'Other', testType: 'Other' },
  ];
  public selectedDiagnosticReportName = new FormControl(null);
  reportDate: any = null;
  maxDate: Date;
  minDate: any;
  reportFile: any;
  reportName: any;
  showUploading: boolean;
  reportFiles: any = [];

  constructor(private router: Router,
    private notificationService: ToastrService,
    private patientService: PatientService) { }

  ngOnInit(): void {
    console.log(this.selectedTab);
  }
  viewPrescription(patient_uuid, doctor_uuid, consult_id) {
    this.router.navigate(['/haconsultation-history/', patient_uuid, doctor_uuid, consult_id]);
  }
  bookAppointment(doctorid, practiceLocationId, parentConsultationUuid, patient_uuid) {
    localStorage.setItem("patient_uuid", patient_uuid);
    this.router.navigate([
      '/patient/appointment/',
      doctorid,
      practiceLocationId, parentConsultationUuid
    ]);
  }
  getReportId(consultId, patientId) {
    this.consultationUuid = consultId;
    this.reportPatientUuid = patientId;
  }
  selectedUserId(event: any, patientId: string) {
    let existsIndex = -1;
    let selectedPatientId: string;
    if (this.patients == undefined) {
      this.patients.push(patientId);
      return;
    }

    this.patients.forEach((id, index) => {
      if (id === patientId) {
        existsIndex = index;
      }
    });
    if (existsIndex !== -1 && event.target.checked == false) {
      this.patients.splice(existsIndex, 1);
    } else if (existsIndex !== -1 && event.target.checked == true) {
      this.notificationService.success(`Patient already selected `, 'Med.Bot');
      event.target.checked = false;
    } else {
      this.patients.push(patientId);
    }
    this.patients.forEach(id => {
      if (selectedPatientId == undefined) {
        selectedPatientId = id;
      } else {
        selectedPatientId += ',' + id;
      }
    });
    this.selectedPatientId.emit(selectedPatientId);
  }
  selectAll(event: any) {
    let selectedPatientId: string = '';
    this.allSelected = event.target.checked;
    if (this.allSelected == true) {
      for (let i = 0; i < this.consultations.length; i++) {
        if (selectedPatientId == undefined || selectedPatientId == '') {
          selectedPatientId = this.consultations[i].patient_json.uuid;
        }
        else {
          selectedPatientId += ',' + this.consultations[i].patient_json.uuid;
        }
      }
    } else {
      selectedPatientId = '';
      this.patients = [];
    }
    this.selectedPatientId.emit(selectedPatientId);
  }
  getReportType(event, appointmentId = null) {
    this.selectedDiagnosticReportName.setValue(event.testType);
  }
  medicalReports(event) {
    this.reportFile = event.target.files[0];
    this.reportName = event.target.files[0]?.name;
  }
  saveMedicalReport() {
    this.showUploading = true;
    const file = this.reportFile;
    const type = this.selectedDiagnosticReportName.value;
    const data = { 'medical_report_type': type, 'appointment': this.consultationUuid, 'consultation': this.consultationUuid, 'report_generated_on': this.reportDate };
    this.patientService.postMedicalReport(file, data).subscribe(
      data => {
        this.showUploading = false;
        this.reportFiles.unshift(data);
        $('#upload-report1').modal('hide');
        this.selectedDiagnosticReportName.setValue(null);
        this.reportDate = null;
        this.reportFile = null;
        this.reportName = null;
        this.notificationService.success('Report updated', 'Med.Bot')

      }, error => {
        this.notificationService.error('Internal server error', 'Med.Bot')
        console.log(error);
        this.showUploading = false;
      }
    );
  }
  checkPermission(val: string) {
    switch (val) {
      case 'checkBox':
        if ((this.userType == 'PlatformAdmin' || this.userType == 'HospitalAdmin') && this.selectedTab != 3) {
          return true;
        }
        break;
      case 'doctorName':
        if (this.userType == 'Patient') {
          return true;
        }
        break;
      case 'patienName':
        if (this.userType != 'Patient') {
          return true;
        }
        break;
      case 'consultedDate':
      case 'consultedTime':
      case 'patientType':
      case 'IP_OP_ID':
        if (this.userType != 'PlatformAdmin' && this.userType != 'HospitalAdmin') {
          return true;
        }
        break;



    }

  }
}
