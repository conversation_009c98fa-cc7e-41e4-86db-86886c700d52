<div class="container-fluid">
  <div class="row">
    <div class="col-md-12 col-lg-12 col-xl-12">
      <div class="m-4">
        <div class="">
          <div class="">
            <div class="row">
              <div class="col-md-12">
                <h4 class="mb-4 dashboard-font-size">Earning Report</h4>
                <div class="appointment-tab">
                  <form class="earning-report-form" [formGroup]="completeFormData">
                    <div class="mb-5">
                      <div class="row">
                        <div class="col-md-3" *ngIf="userType=='PlatformAdmin'">
                          <label>Doctor Type</label>
                          <select class="form-control input-field-border select" name="doctorType" id="doctorType"
                            formControlName="doctorType" (change)="setDoctorType($event.target.value)">
                            <option value="0" disabled>Select Doctor Type</option>
                            <option value="1" translate>Individual</option>
                            <option value="2" translate>Hospital-Based</option>
                          </select>
                        </div>
                        <div class="col-md-3"
                          *ngIf="userType=='PlatformAdmin' && completeFormData.value.doctorType=='2'">
                          <label>Hospitals</label>
                          <select class="form-control select" name="hospital" id="hospital" formControlName="hospital">
                            <option value="0">Select Hospital</option>
                            <option *ngFor="let item of hospitalList" [value]="item.uuid"> {{item.name}}</option>
                          </select>
                        </div>
                        <div class="col-md-3"
                          *ngIf="(userType=='PlatformAdmin') && completeFormData.value.doctorType=='1'">
                          <label>Doctors</label>
                          <select class="form-control select" name="doctor" id="doctor" formControlName="doctor">
                            <option value="0">Select Doctor</option>
                            <option *ngFor="let item of doctorList" [value]="item.uuid"> {{item.username}}</option>
                          </select>
                        </div>
                        <div class="col-md-3">
                          <label>From Date</label>
                          <input type="text" id="from_date" placeholder="From Date" onkeydown="return false"
                            class="form-control input-field-border mb-2" [minDate]="" [maxDate]="" bsDatepicker
                            [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }"
                            formControlName="fromDate">
                        </div>
                        <div class="col-md-3">
                          <label>To Date</label>
                          <input type="text" id="to_date" placeholder="To Date" onkeydown="return false"
                            class="form-control input-field-border mb-2" [minDate]="" [maxDate]="" bsDatepicker
                            [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'YYYY-MM-DD' }"
                            formControlName="toDate">
                        </div>
                        <div class="col-md-3">
                          <label>Consultation Status</label>
                          <select name="fulfilment" id="fulfilment_status" class="form-control input-field-border mb-2"
                            formControlName="fulfilmentStatus">
                            <option value="all">All</option>
                            <option value="Started">Started</option>
                            <option value="Not Started">Not Started</option>
                            <option value="Completed">Completed</option>
                            <option value="Suspended">Suspended</option>
                            <option value="Cancelled">Cancelled</option>
                            <option value="Doctor Missed">Doctor Missed</option>
                            <option value="Patient Missed">Patient Missed</option>
                            <option value="Both Missed">Both Missed</option>
                          </select>
                        </div>
                        <div class="col-md-3">
                          <label>Payment Type</label>
                          <select name="payment" id="payment_mode" class="form-control input-field-border mb-2"
                            formControlName="paymentType">
                            <option value="all">All</option>
                            <option value="Online">Online</option>
                            <option value="Offline">Offline</option>
                          </select>
                        </div>
                        <div class="col-md-3">
                          <label>Appointment Type</label>
                          <select name="appointment" id="appointment_type" class="form-control input-field-border mb-2"
                            formControlName="appointmentType">
                            <option value="all">All Appointment</option>
                            <option value="regular_appointment">Doctor Appointment</option>
                            <option value="instant_appointment">Doctor Instant Appointment</option>
                          </select>
                        </div>

                      </div>
                      <button class="btn btn-primary btn-sm float-right"
                        (click)="getEarningReportList(1)">Submit</button>
                    </div>
                  </form>

                  <div class="tab-content">
                    <!-- Upcoming Appointment Tab -->
                    <div class="tab-pane show active" id="admin-user">
                      <div class="col-md-12 float-right mt-2 tab_pager_position">
                        <div class="tab_pager_position float-right">
                          <nav aria-label="Page navigation example" *ngIf="this.eraningReportTotalPage > 1">
                            <ul class="pagination pager_position">
                              <li class="page-item" (click)="eraningReportFirstPageList()" [ngClass]="{
                                'disabled-pagination':
                                  eraningReportCurrentPage === 1
                              }">
                                <a class="page-link">&lt;&lt;</a>
                              </li>
                              <li class="page-item" (click)="eraningReportPreviousPageList()" [ngClass]="{
                                'disabled-pagination':
                                  eraningReportCurrentPage === 1
                              }">
                                <a class="page-link">&lt;</a>
                              </li>
                              <li class="page-item">
                                <a class="page-link">page &nbsp;
                                  {{eraningReportCurrentPage}}&nbsp;of&nbsp; {{ eraningReportTotalPage }}</a>
                              </li>
                              <li class="page-item" (click)="eraningReportNextPageList()" [ngClass]="{
                                'disabled-pagination':
                                  eraningReportCurrentPage ===
                                  eraningReportTotalPage
                              }">
                                <a class="page-link">&gt;</a>
                              </li>
                              <li class="page-item" (click)="eraningReportLastPageList()" [ngClass]="{
                                'disabled-pagination':
                                  eraningReportCurrentPage ===
                                  eraningReportTotalPage
                              }">
                                <a class="page-link">&gt;&gt;</a>
                              </li>
                            </ul>
                          </nav>
                        </div>
                      </div>
                      <div *ngIf="eraningReportLoading">
                        <app-loading-spinner></app-loading-spinner>
                      </div>
                      <div class="card card-table mb-0" *ngIf="!eraningReportLoading">
                        <div class="card-body">
                          <div class="table-responsive">
                            <table class="table table-hover table-center mb-0">
                              <thead>
                                <tr>
                                  <th>Sl.No</th>
                                  <th>Patient Name</th>
                                  <th>Consultation Date</th>
                                  <th>Consultation Time</th>
                                  <th>Earnings</th>
                                  <th>Payment</th>
                                  <th>Consultation Status</th>
                                  <th>Department</th>
                                  <!-- <th>Platform Share</th> -->
                                  <!-- <th>Total</th> -->
                                </tr>
                              </thead>
                              <tbody>
                                <tr *ngFor="let data of currentMonthEarning;let i=index">
                                  <td>{{eraningReportSerialNumber+i+1}}</td>
                                  <td>{{data.customer_name}} </td>
                                  <td>&nbsp;&nbsp;{{data.start_datetime | date:'dd-MM-yyyy'}}</td>
                                  <td>&nbsp;{{data.start_datetime | date:'hh:mm'}}-&nbsp;{{data.end_datetime |
                                    date:'hh:mm a'}}
                                  </td>
                                  <td>{{data.net_amount }}</td>
                                  <td>{{data.payment_mode}}</td>
                                  <td>{{data.consultation_status}}</td>
                                  <td>{{data.department}}</td>
                                  <!-- <td>{{data.platform_service_fee}}</td> -->
                                  <!-- <td>{{data.gross_amount}}</td> -->
                                </tr>
                                <!-- <tr *ngIf="currentMonthEarning.length >0">
                                  <td colspan="6"></td>
                                  <th>Total</th>
                                  <th>{{totalAmount}}</th>
                                </tr> -->
                              </tbody>
                            </table>
                          </div>
                          <div class="text-center mb-2 p-2 mt-2">
                            <span class="appointmentList-no-data" *ngIf="currentMonthEarning.length ===0">No Earning
                              Data</span>
                          </div>
                        </div>
                      </div>
                      <div class="float-right mt-2">
                        <div class="float-right">
                          <nav aria-label="Page navigation example" *ngIf="this.eraningReportTotalPage > 1">
                            <ul class="pagination">
                              <li class="page-item" (click)="eraningReportFirstPageList()" [ngClass]="{
                                'disabled-pagination':
                                  eraningReportCurrentPage === 1
                              }">
                                <a class="page-link">&lt;&lt;</a>
                              </li>
                              <li class="page-item" (click)="eraningReportPreviousPageList()" [ngClass]="{
                                'disabled-pagination':
                                  eraningReportCurrentPage === 1
                              }">
                                <a class="page-link">&lt;</a>
                              </li>
                              <li class="page-item">
                                <a class="page-link">page &nbsp;{{
                                  eraningReportCurrentPage
                                  }}&nbsp;of&nbsp; {{ eraningReportTotalPage }}</a>
                              </li>
                              <li class="page-item" (click)="eraningReportNextPageList()" [ngClass]="{
                                'disabled-pagination':
                                  eraningReportCurrentPage ===
                                  eraningReportTotalPage
                              }">
                                <a class="page-link">&gt;</a>
                              </li>
                              <li class="page-item" (click)="eraningReportLastPageList()" [ngClass]="{
                                'disabled-pagination':
                                  eraningReportCurrentPage ===
                                  eraningReportTotalPage
                              }">
                                <a class="page-link">&gt;&gt;</a>
                              </li>
                            </ul>
                          </nav>
                        </div>
                      </div>
                    </div>
                    <!-- /Upcoming Appointment Tab -->

                    <!-- Today Appointment Tab -->
                    <div class="tab-pane" id="doctor-assistant">
                      <div class="card card-table mb-0">
                        <div class="card-body">
                          <div class="table-responsive">
                            <table class="table table-hover table-center mb-0">
                              <thead>
                                <tr>
                                  <th>Name</th>
                                  <th>Email</th>
                                  <th>Phone</th>
                                  <th>Action</th>
                                </tr>
                              </thead>
                              <tbody>
                                <!-- <tr *ngFor="let data of assistantList;let i=index">
                                <td>{{data.username}}    </td>
                                <td>{{data.email}}</td>
                                <td>{{data.phone}}</td>
                                <td><button class="btn btn-primary" disabled>view</button></td>
                              </tr> -->
                              </tbody>
                            </table>
                          </div>
                          <div class="text-center mb-2 p-2 mt-2">
                            <span class="appointmentList-no-data" style="color: orangered;">No Assistant Data</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- /Today Appointment Tab -->

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>