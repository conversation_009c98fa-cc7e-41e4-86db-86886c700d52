import { Component, OnInit } from '@angular/core';
import { DoctorService } from '../../doctor/doctor.service';
import { AuthService } from '../../auth/auth.service';
import { ToastrService } from 'ngx-toastr';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { SharedService } from 'src/app/shared/shared.service';
import * as moment from 'moment';


@Component({
  selector: 'app-haconsultation-summary',
  templateUrl: './haconsultation-summary.component.html',
  styleUrls: ['./haconsultation-summary.component.css']
})
export class HaconsultationSummaryComponent implements OnInit {
  public doctorId: string;
  public currentMonthEarning: any = [];
  eraningReportCurrentPage: any;
  public eraningReportSerialNumber: number = 0;
  eraningReportTotalPage: any;
  eraningReportLoading: boolean;
  public totalAmount: number;
  public doctorTotalAmount: number;
  public plateformTotalAmount: number;
  // from_date="2021-07-01";
  // to_date="2021-07-30";
  public filter: string;
  public fromDate = '';
  public toDate = '';
  hospital_uuid: any;
  uuid: any;
  report: any;
  public fulfilment_status = null;
  public payment_mode = null;
  public appointment_type = null;
  userType: string;
  completeFormData: FormGroup;
  hospitalList: any = [];
  doctorList: any = [];
  doctorType: number = 0;

  constructor(private doctorService: DoctorService, private userService: AuthService, private notificationService: ToastrService,
    private location: Location, private activatedRoute: ActivatedRoute, private sharedService: SharedService, private fb: FormBuilder) {
    this.completeFormData = this.fb.group({
      doctorType: new FormControl('0'),
      hospital: new FormControl(''),
      doctor: new FormControl(''),
      fromDate: new FormControl(''),
      toDate: new FormControl(''),
      fulfilmentStatus: new FormControl(''),
      paymentType: new FormControl(''),
      appointmentType: new FormControl(''),
    });
  }

  ngOnInit(): void {
    this.sharedService.setActiveLink('hadoctor-reports');
    this.userType = localStorage.getItem('user_type');
    // this.activatedRoute.params.subscribe(
    //   url => {
    //     console.log("url", url);
    //     this.uuid = url['uuid'];
    //     console.log(this.uuid);
    //     this.report = localStorage.getItem('report');
    //     this.hospital_uuid = localStorage.getItem('hstId');
    //     this.getEarningReportList(1);
    //   }
    // );

    this.report = localStorage.getItem('report');
    if (this.userType == 'HospitalAdmin') {
      this.hospital_uuid = localStorage.getItem('hstId');
    } else if (this.userType == 'Partner') {
      this.uuid = localStorage.getItem("current_user_uuid");
      this.hospital_uuid = localStorage.getItem('hstId');
    } else if (this.userType == 'Patient') {
    } else if (this.userType == 'Doctor') {
      this.uuid = localStorage.getItem("current_user_uuid");
    } else if (this.userType == 'PlatformAdmin') {
    } else if (this.userType == 'DoctorAssistant') {
      this.uuid = localStorage.getItem("current_user_uuid");
      this.hospital_uuid = localStorage.getItem('hstId');
    }

    // this.getEarningReportList(1);

    this.eraningReportLoading = true;
    this.eraningReportSerialNumber = 0;

    this.userService.getUserDetail().subscribe(
      (data) => {
        this.doctorId = data['uuid'];
        if (this.userType != 'PlatformAdmin') {
          this.getEarningReportList(1);
        } else {
          this.eraningReportLoading = false;
          this.eraningReportTotalPage = 0;
          this.currentMonthEarning = [];
        }
      },
      (error) => {
        console.log(error);
      }
    );
    console.log('init successful');
  }

  setDoctorType(event: any) {
    this.doctorType = event;
    switch (event) {
      case '1':
        this.hospitalList = [];
        this.getDoctorPatient(this.hospital_uuid);
        this.completeFormData.get('doctor').setValue('0');
        break;
      case '2':
        this.doctorList = [];
        this.getHospitalList();
        this.completeFormData.get('hospital').setValue('0');
        break;
    }
  }

  getDoctorPatient(id?: string) {
    if (id == undefined || id == null) {
      this.getDoctorList();
    }
    else {
      this.getDoctorList(id);
    }
  }

  getDoctorList(id?: string) {
    this.doctorList = [];
    this.sharedService.getDoctorList(id).subscribe((data: any) => {
      data.forEach(element => {
        this.doctorList.push(element);
      });
    });
  }

  getHospitalList() {
    this.sharedService.getHospitalList().subscribe((data: any) => {
      this.hospitalList = [];
      data.forEach(element => {
        this.hospitalList.push(element);
      });
    });
  }

  getEarningReportList(page) {
    this.totalAmount = 0;
    this.doctorTotalAmount = 0;
    this.plateformTotalAmount = 0;
    this.eraningReportLoading = true;
    this.eraningReportTotalPage = 0;
    this.currentMonthEarning = [];

    var query = '?page=' + page;
    if (this.completeFormData.value.fulfilmentStatus != '' && this.completeFormData.value.fulfilmentStatus != '0') {
      query += '&fulfilment_status=' + this.completeFormData.value.fulfilmentStatus;
    }
    if (this.completeFormData.value.paymentType != '' && this.completeFormData.value.paymentType != '0') {
      query += '&payment_mode=' + this.completeFormData.value.paymentType;
    }
    if (this.completeFormData.value.appointmentType != '' && this.completeFormData.value.appointmentType != '0') {
      query += '&appointment_type=' + this.completeFormData.value.appointmentType;
    }
    if (this.completeFormData.value.fromDate != '' && this.completeFormData.value.toDate != '') {
      let fromDate=moment(this.completeFormData.value.fromDate).format('YYYY-MM-DD');
      let toDate =moment(this.completeFormData.value.toDate).format('YYYY-MM-DD');
      query += '&from_date=' + fromDate + '&to_date=' + toDate;
    }

    let uniqueID = '';
    if (this.userType == 'HospitalAdmin') {
      uniqueID = this.hospital_uuid;
      this.getEarningDataHa(uniqueID, query);
    } else if (this.userType == 'Partner' || this.userType == 'DoctorAssistant') {
      uniqueID = this.uuid;
      this.getEarningDataHa(uniqueID, query);
    } else if (this.userType == 'PlatformAdmin') {
      if (this.completeFormData.value.doctorType == '1') {
        uniqueID = this.completeFormData.value.doctor;
        this.getEarningDataDoctor(uniqueID, query);
      } else if (this.completeFormData.value.doctorType == '2') {
        uniqueID = this.completeFormData.value.hospital;
        this.getEarningDataHa(uniqueID, query);
      }
    } else if (this.userType == 'Doctor') {
      uniqueID = this.uuid;
      this.getEarningDataDoctor(uniqueID, query);
    }
  }

  getEarningDataDoctor(uniqueID: string, query: string) {
    this.doctorService.getEarningData(query, uniqueID).subscribe(
      (data) => {
        this.currentMonthEarning = data['results'];
        this.eraningReportTotalPage = data['total_pages'];
        this.eraningReportCurrentPage = data['page_number'];
        this.eraningReportLoading = false;
        for (let data of this.currentMonthEarning) {
          this.totalAmount = this.totalAmount + parseInt(data.gross_amount);
          this.doctorTotalAmount = this.doctorTotalAmount + parseInt(data.net_amount);
          this.plateformTotalAmount = this.plateformTotalAmount + parseInt(data.platform_service_fee);
        }
      },
      (error) => {
        this.eraningReportLoading = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      });
  }

  getEarningDataHa(uniqueID: string, query: string) {
    this.doctorService.getEarningDataHa(uniqueID, query).subscribe((data) => {
      this.currentMonthEarning = data['results'];
      this.eraningReportTotalPage = data['total_pages'];
      this.eraningReportCurrentPage = data['page_number'];
      this.eraningReportLoading = false;
      for (let data of this.currentMonthEarning) {
        this.totalAmount = this.totalAmount + parseInt(data.gross_amount);
        this.doctorTotalAmount = this.doctorTotalAmount + parseInt(data.net_amount);
        this.plateformTotalAmount = this.plateformTotalAmount + parseInt(data.platform_service_fee);
      }
    }, error => {
      this.eraningReportLoading = false;
      const status = error['status'];
      if (status == 400) {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
      else {
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
    });
  }

  eraningReportNextPageList() {
    this.eraningReportCurrentPage = this.eraningReportCurrentPage + 1;
    if (this.eraningReportTotalPage >= this.eraningReportCurrentPage) {
      this.getEarningReportList(this.eraningReportCurrentPage);
      this.eraningReportSerialNumber = (this.eraningReportCurrentPage - 1) * 10;
    } else {
      this.eraningReportCurrentPage = this.eraningReportCurrentPage - 1;
    }
  }

  eraningReportLastPageList() {
    this.eraningReportSerialNumber = (this.eraningReportTotalPage - 1) * 10;
    this.getEarningReportList(this.eraningReportTotalPage);

  }

  eraningReportFirstPageList() {
    this.eraningReportCurrentPage = 1;
    this.eraningReportSerialNumber = 0;
    this.getEarningReportList(this.eraningReportCurrentPage);
  }

  eraningReportPreviousPageList() {
    this.eraningReportCurrentPage = this.eraningReportCurrentPage - 1;
    if (this.eraningReportTotalPage >= this.eraningReportCurrentPage && this.eraningReportCurrentPage > 0) {
      this.getEarningReportList(this.eraningReportCurrentPage);
      this.eraningReportSerialNumber = (this.eraningReportCurrentPage - 1) * 10;
    } else {
      this.eraningReportCurrentPage = this.eraningReportCurrentPage + 1;
    }
  }

  getCsvReport() {
    this.doctorService.getEarningCsvDataHa(this.doctorId).subscribe((data) => {

    }, error => { })
  }

  back() {
    this.location.back();
  }

  ngOnDestroy() {
    localStorage.removeItem('report');
  }
}
