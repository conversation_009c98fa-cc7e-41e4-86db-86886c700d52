<!--patient list-->
<div>
  <div *ngIf="(userType != 'PlatformAdmin' && patientCount > 0)" class="container-fluid">
    <div class="row">
      <div class="col-md-3 col-lg-3 col-sm-6">
        <label>Patient Name</label>
        <input type="text" id="name" style="cursor:pointer" class="form-control form-control-height"
          [(ngModel)]="searchPatientName">
      </div>
      <div class="col-md-3 col-lg-3 col-sm-6">
        <label>Patient Hospital Id</label>
        <input type="text" id="name" style="cursor:pointer" class="form-control form-control-height"
          [(ngModel)]="searchPatientId">
      </div>
      <div class="col-md-3 col-lg-3 col-sm-6">
        <label>Patient Type </label>
        <select class="form-control input-field-border select" name="patientType" id="patientType"
          [(ngModel)]="patientType">
          <option value="0" disabled>Select Patient Type</option>
          <option value="inpatient" translate>In Patient</option>
          <option value="outpatient" translate>Out Patient</option>
        </select>
      </div>
      <div class="col-md-3 col-lg-3 col-sm-6" *ngIf="patientType!='0'">
        <label>IP/OP ID</label>
        <input type="text" id="name" style="cursor:pointer" class="form-control form-control-height"
          [(ngModel)]="searchPatientTypeId">
      </div>
    </div>
    <div class="text-right col-md-3 mt-2">
      <button type="button" id="search-Btn" class="btn btn-info mb-2 mr-0" (click)="searchPatient()">Search
        Patient</button>
    </div>
  </div>
  <div *ngIf="!patientisLoading">
    <div class="col-md-12">
      <div class=" mt-3">
        <nav aria-label="Page navigation example" *ngIf="this.patientTotalPage > 1">
          <ul class="pagination">
            <li class="page-item" (click)="firstPatientPageList()" [ngClass]="{ 'disabled-pagination': patientCurrentPage === 1 }">
              <a class="page-link">&lt;&lt;</a>
            </li>
            <li class="page-item" (click)="previousPatientPageList()" [ngClass]="{ 'disabled-pagination': patientCurrentPage === 1 }">
              <a class="page-link">&lt;</a>
            </li>
            <li class="page-item">
              <a class="page-link">page &nbsp;{{ patientCurrentPage }}&nbsp;of&nbsp; {{ patientTotalPage }}</a>
            </li>
            <li class="page-item" (click)="nextPatientPageList()" [ngClass]="{'disabled-pagination': patientCurrentPage === patientTotalPage}">
              <a class="page-link">&gt;</a>
            </li>
            <li class="page-item" (click)="lastPatientPageList()" [ngClass]="{'disabled-pagination': patientCurrentPage === patientTotalPage}">
              <a class="page-link">&gt;&gt;</a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
    <div class="col-md-12">
      <h4 class="text-success">Patients List</h4>
      <h4>Total Patients - {{ patientCount }}</h4>
      <div class="card card-table mb-0">
        <div class="card-body">
          <div class="table-responsive" *ngIf="patientList.length > 0">
            <table class="table table-hover table-center mb-0">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Patient ID</th>
                  <th>Patient Name</th>
                  <th>Phone</th>
                  <th>Father Name</th>
                  <th>Mother Name</th>
                  <th>Gender</th>
                  <th>DOB</th>
                  <th>Age</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody
                *ngIf="this.userType === 'HospitalAdmin' || this.userType === 'Doctor' || this.userType === 'DoctorAssistant' || this.userType == 'Partner'">
                <tr *ngFor="let data of patientList; let i = index">
                  <th scope="row"><a>{{ patientSerialNumber + i + 1 }}</a></th>
                  <td><a class="text-primary">{{ data.patient_ha_id }}</a></td>
                  <td *ngIf="data.first_name!='' || data.middle_name!='' || data.last_name !=''"><a class="text-primary link" (click)="onPatientIdentity(data.uuid)" >
                    {{ data.first_name }}{{data.middle_name }}{{data.last_name}}</a></td>
                  <td *ngIf="data.first_name=='' && data.middle_name=='' && data.last_name ==''"><a class="text-primary ">{{ data.username}}</a></td>
                  <td>{{data.phone}}</td>
                  <td>{{data.father_name}}</td>
                  <td>{{data.mother_name}}</td>
                  <td>{{data.gender}}</td>
                  <td>{{data.date_of_birth}}</td>
                  <td>{{data.age}}</td>
                  <td>
                    <button class="btn-primary btn-md mr-2" (click)="onPatientIdentity(data.uuid)">View</button>
                  </td>
                  <td>
                    <button class="btn-primary btn-md mr-2" (click)="Detail(data.uuid)">Details</button>
                  </td>
                </tr>
              </tbody>
              <tbody *ngIf="this.userType == 'PlatformAdmin'">
                <tr *ngFor="let data of patientList; let i = index">
                  <th scope="row"><a>{{ patientSerialNumber + i + 1 }}</a></th>
                  <td><a class="text-primary link">{{ data.patient_ha_id }}</a></td>
                  <td><a class="text-primary link">{{ data.user.username }}</a></td>
                  <td>{{data.user.phone}}</td>
                  <td>{{data.user.father_name}}</td>
                  <td>{{data.user.mother_name}}</td>
                  <td>{{data.user.gender}}</td>
                  <td>{{data.user.date_of_birth}}</td>
                  <td>{{data.user.age}}</td>
                  <td>
                    <button class="btn-primary btn-md mr-2" (click)="onPatientIdentity(data.uuid)">View</button>
                  </td>
                  <td>
                    <button class="btn-primary btn-md mr-2" (click)="Detail(data.uuid)">Details</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="centered" *ngIf="patientisLoading">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>
</div>