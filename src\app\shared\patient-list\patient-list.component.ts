import { Component, OnInit, Input, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { HospitalService } from 'src/app/hospital-admin/hospital-admin.service';
import { SharedService } from '../shared.service';
import { Router } from '@angular/router';
import { PlatformService } from 'src/app/platform/platform.service';
import { Subscription } from 'rxjs';
import { HospitalModel } from 'src/app/hospital-admin/models/hospital.model';

@Component({
  selector: 'app-patient-list',
  templateUrl: './patient-list.component.html',
  styleUrls: ['./patient-list.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PatientListComponent implements OnInit {
  searchPatientName = null;
  patientType: string = "0";
  searchPatientTypeId: string;
  searchPatientId: string = '';
  userType: string;
  hospitalId: string;
  patientList: any = [];
  patientSerialNumber = 0;
  patientTotalPage: number;
  patientCurrentPage: number = 1;
  patientCount: number;
  patientisLoading: boolean;
  private subscriptions: Subscription;
  hospital: HospitalModel;

  constructor(
    private hospitalService: HospitalService,
    private notificationService: ToastrService,
    private sharedService: SharedService,
    private router: Router,
    private changesDetector: ChangeDetectorRef,
    private platformService: PlatformService
  ) { }

  ngOnInit(): void {
    this.userType = localStorage.getItem('user_type');
    if (this.userType == 'PlatformAdmin') {
      this.getPatientDetailsForPA(this.patientCurrentPage);
    } else if (this.userType === 'HospitalAdmin' || this.userType === 'Doctor' || this.userType === 'DoctorAssistant' || this.userType == 'Partner') {
      this.subscriptions = this.hospitalService.currentHospitalDetails.pipe()
        .subscribe(value => {
          if (value && value.hospitalId != '') {
            this.hospital = Object.assign({}, value);
            this.hospitalId = this.hospital.hospitalId;
          } else {
            this.hospital = this.hospitalService.getHospitalDetails();
            this.hospitalId = this.hospital.hospitalId;
          }
          if (this.hospitalId) {
            this.getPatientDetails(this.hospitalId, this.patientCurrentPage);
          }
        });
    }
  }

  getPatientDetailsForPA(page) {
    this.patientisLoading = true;
    let psearchList = [];
    psearchList = this.sharedService.getpatientSearListBypage();
    if (psearchList.length > 0) {
      for (const data of psearchList) {
        if (data.page_number == this.patientCurrentPage) {
          this.patientList = data['results'];
          this.patientTotalPage = data['total_pages'];
          this.patientCurrentPage = data['page_number'];
          this.patientCount = data['count'];
          this.patientisLoading = false;
          this.changesDetector.detectChanges();
        }
      }
      if (!!this.patientisLoading) {
        this.getPatient(this.patientCurrentPage);
      }
    } else {
      this.getPatient(this.patientCurrentPage);
    }
  }

  getPatient(page) {
    this.patientisLoading = true;
    window.scroll(0, 0);
    this.platformService.getPatientsList(page).subscribe(
      data => {
        this.patientList = data['results'];
        this.patientTotalPage = data['total_pages'];
        this.patientCurrentPage = data['page_number'];
        this.patientCount = data['count'];
        this.sharedService.patientsearListBypage(
          this.patientList,
          this.patientCurrentPage,
          this.patientCount,
          this.patientTotalPage
        );
        this.patientisLoading = false;
        this.changesDetector.detectChanges();
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  getPatientDetails(hospitalId, page) {
    this.patientisLoading = true;
    this.hospitalService.getPatientsList(hospitalId, page).subscribe(
      (data) => {
        this.patientList = data['results'];
        this.patientTotalPage = data['total_pages'];
        this.patientCurrentPage = data['page_number'];
        this.patientCount = data['count'];
        this.patientisLoading = false;
        this.changesDetector.detectChanges();
      },
      (err) => {
        console.log(err);
        this.notificationService.error('Internal server error', 'Med.Bot');
        this.patientisLoading = false;
      }
    );
  }

  searchPatient() {
    var query = '?page=1&user_type=patient';
    // if (this.searchPatientName != null) {
    //   query += '&username=' + this.searchPatientName + '&patienttype=' + this.patientType + '&patienttypeid' + this.searchPatientTypeId;
    // }
    if (this.searchPatientName != null) {
      query += '&username=' + this.searchPatientName;
    }
    if (this.searchPatientId != null && this.searchPatientId != '') {
      query += '&patient_ha_id=' + this.searchPatientId;
    }
    if (this.patientType != null && this.patientType != '0') {
      query += '&patient_type=' + this.patientType;
    }
    if (this.searchPatientTypeId != null && this.searchPatientTypeId != '') {
      query += '&patient_unique_id=' + this.searchPatientTypeId;
    }
    this.patientisLoading = true;
    this.hospitalService.getDoctorsSearchList(this.hospitalId, query).subscribe(
      (data) => {
        if (data['count'] == 0) {
          this.notificationService.error('No patient information available', 'Med.Bot');
          this.searchPatientName = '';
          this.searchPatientTypeId = '';
          this.patientType = "0";
        }
        else {
          this.patientList = data['results'];
          this.patientTotalPage = data['total_pages'];
          this.patientCurrentPage = data['page_number'];
          this.patientCount = data['count'];
          this.patientisLoading = false;
          this.changesDetector.detectChanges();
        }
      },
      (err) => {
        console.log(err);
        this.notificationService.error(err.error.error_message, 'Med.Bot');
        this.patientisLoading = false;
      }
    );
  }

  onPatientIdentity(uuid) {
    this.router.navigate([`patient-identity-details/${uuid}/view`]);
  }

  // patient list pagination
  nextPatientPageList() {
    this.patientCurrentPage = this.patientCurrentPage + 1;
    if (this.patientTotalPage >= this.patientCurrentPage) {
      this.patientSerialNumber = (this.patientCurrentPage - 1) * 10;
      this.callPatientAPIBasedRole();
    } else {
      this.patientCurrentPage = this.patientCurrentPage - 1;
    }
  }

  lastPatientPageList() {
    this.patientSerialNumber = (this.patientTotalPage - 1) * 10;
    this.callPatientAPIBasedRole();
  }

  firstPatientPageList() {
    this.patientSerialNumber = 0;
    this.patientCurrentPage = 1;
    this.callPatientAPIBasedRole();
  }

  previousPatientPageList() {
    this.patientCurrentPage = this.patientCurrentPage - 1;
    if (this.patientTotalPage >= this.patientCurrentPage && this.patientCurrentPage > 0) {
      this.patientSerialNumber = (this.patientCurrentPage - 1) * 10;
      this.callPatientAPIBasedRole();
    } else {
      this.patientCurrentPage = this.patientCurrentPage + 1;
    }
  }

  callPatientAPIBasedRole() {
    if (this.userType == 'PlatformAdmin') {
      this.getPatientDetailsForPA(this.patientCurrentPage);
    } else if (this.userType === 'HospitalAdmin' || this.userType === 'Doctor' || this.userType === 'DoctorAssistant' || this.userType == 'Partner') {
      this.getPatientDetails(this.hospitalId, this.patientCurrentPage);
    }
  }
  Detail(patient_uuid){
    this.router.navigate(['/consultation-history/', patient_uuid]);
  }
  ngOnDestroy() {
    if (this.subscriptions) {
      this.subscriptions.unsubscribe();
    }
  }
}
