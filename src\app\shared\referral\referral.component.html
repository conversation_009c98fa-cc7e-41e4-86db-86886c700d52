<div class="container-fluid">
    <div class="row">
        <div class="col-md-12 col-lg-12 col-xl-12">
            <h5 class="m-4 back-head ms" (click)="goBack()">
                <i class="fas fa-chevron-circle-left"></i>Back
            </h5>
            <div class="hsptl-form">
                <div class="card">
                    <div class="card-body">
                        <h3>
                            &nbsp;
                        </h3>
                        <div class="hsp-details">
                            <h4 class="text-success">Referral List</h4>
                            <h4>Total Referral - {{ referralCount }}</h4>
                            <div class="card card-table mb-0">
                                <div class="card-body">
                                    <!-- <div class="table-responsive" *ngIf="referralList.length > 0"> -->
                                    <div class="table-responsive">
                                        <table class="table table-hover table-center mb-0">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <!-- <th>Patient ID</th> -->
                                                    <th>Patient Name</th>
                                                    <th>Patient Phone</th>
                                                    <th>Referred to</th>
                                                    <th>Referred for</th>
                                                    <th>Referred by</th>
                                                    <th *ngIf="userType=='DoctorAssistant'||userType=='HospitalAdmin'" >Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr *ngFor="let data of referralList; let i = index">
                                                    <th scope="row"><a>{{ serialNumber + i + 1 }}</a></th>
                                                    <td>{{data.patient_id?data.patient_id.name:''}}</td>
                                                    <td>{{data.patient_id?data.patient_id.ph_no:''}}</td>
                                                    <td>{{data.referred_to_doctor.name}}</td>
                                                    <td>{{data.reason_for_referral}}</td>
                                                    <td>{{data.referring_doctor.name}}</td>
                                                    <td *ngIf="data.patient_id && data.patient_id.is_exist == true && (userType=='DoctorAssistant'||userType=='HospitalAdmin')"
                                                        (click)="bookAppointment(data.patient_id.uuid)">
                                                        <button class="btn-primary btn-md mr-2">Book Appointment</button>
                                                    </td>
                                                    <td *ngIf="data.patient_id && data.patient_id.is_exist == false && (userType=='DoctorAssistant'||userType=='HospitalAdmin')"
                                                        (click)="addPatient(data.patient_id)">
                                                        <button class="btn-primary btn-md mr-2">Add Patient</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>