import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { SharedService } from '../shared.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-referral',
  templateUrl: './referral.component.html',
  styleUrls: ['./referral.component.css']
})
export class ReferralComponent implements OnInit {
  referralCount: number = 0;
  referralList: any;
  bookAppt: boolean = false;
  hospitalId: string;
  doctorId: string;
  userType: string;
  currentUserId: string;
  serialNumber: number = 0;
  isPublicDoctor = true;
  idType: string;

  constructor(private location: Location,
    private router: Router,
    private sharedService: SharedService,
    private notificationService: ToastrService,
  ) {
    this.userType = localStorage.getItem('user_type');
    if (this.userType == 'Doctor') {
      if (this.checkDoctorIsPublic()) {
        this.doctorId = localStorage.getItem('Doctor');
        this.idType = 'doctor';
      } else {
        this.hospitalId = localStorage.getItem('hospital_id');
        this.idType = 'hospital';
      }
    } else {
      this.hospitalId = localStorage.getItem('hstId');
      this.idType = 'hospital';
    }
    this.currentUserId = localStorage.getItem('current_user_uuid');
  }

  ngOnInit(): void {
    this.sharedService.setActiveLink('referral');
    this.getReferral();
  }

  addPatient(patient: any) {
    localStorage.setItem('user', JSON.stringify(patient));
    this.router.navigate(['/add-patient', this.hospitalId]);
  }

  bookAppointment(id) {
    const query = 'consult_now=false'
    const page = 1;
    const patientId = id;
    this.router.navigate(['/hadoctor-bookappointment/', page, query, patientId]);
  }

  goBack() {
    this.location.back();
  }

  getReferral() {
    var uuid = (this.idType == 'doctor') ? this.doctorId : this.hospitalId;
    this.sharedService.getReferral(uuid, this.idType).subscribe(data => {
      this.referralList = data;
      if (this.referralList.length > 0) {
        this.referralCount = this.referralList.length;
      }
    },
      (err) => {
        console.log(err);
        this.notificationService.error(err.error.error_message, 'Med.Bot');
      });
  }

  checkDoctorIsPublic() {
    const hospital_id = localStorage.getItem('hospital_id');
    if (hospital_id != null) {
      this.isPublicDoctor = false;
    } else {
      this.isPublicDoctor = true;
    }
    return this.isPublicDoctor;
  }

}
