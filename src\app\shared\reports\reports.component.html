<div class="container-fluid">
    <div class="row">
        <div class="col-md-12 col-lg-12 col-xl-12">
            <h5 class="m-4 back-head ms"><i class="fas fa-chevron-circle-left" style=" color: #20C0F3;"
                    (click)="back()"></i>Back</h5>

            <div class="card mx-5">
                <div class="tab-content pt-0">
                    <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded my-2 mx-1">
                        <li class="nav-item"  *ngIf="userType=='HospitalAdmin'|| userType=='PlatformAdmin'">
                            <a class="nav-link " [ngClass]="{ 'active': activeLink === 'ER' }" href="#earning-report" data-toggle="tab">Earning Report
                            </a>
                        </li>
                        <li class="nav-item" >
                            <a class="nav-link" [ngClass]="{ 'active': activeLink === 'CR' }" href="#consultation-report" data-toggle="tab">Consultation Report
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content">
                        <!-- Earning Report start -->
                        <div class="tab-pane " [ngClass]="{ 'show active': activeLink === 'ER' }" id="earning-report" 
                        *ngIf="userType=='HospitalAdmin'|| userType=='PlatformAdmin'">
                            <app-haconsultation-summary></app-haconsultation-summary>
                        </div>
                        

                        <!-- Consultation Report Start -->
                        <div class="tab-pane" [ngClass]="{ 'active': activeLink === 'CR' }" id="consultation-report" >
                            <app-consultation-report></app-consultation-report>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>