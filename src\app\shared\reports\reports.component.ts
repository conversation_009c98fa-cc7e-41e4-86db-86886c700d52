import { Component, OnInit } from '@angular/core';
import { SharedService } from 'src/app/shared/shared.service';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { Location } from '@angular/common';
import { HospitalModel } from 'src/app/hospital-admin/models/hospital.model';
import { HospitalService } from 'src/app/hospital-admin/hospital-admin.service';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.css']
})
export class ReportsComponent implements OnInit {

  hospitalId: string;
  hospital: HospitalModel;
  private subscriptions: Subscription;
  userType: string;
  activeLink: string;

  constructor(private sharedService: SharedService, private notificationService: ToastrService, private hospitalService: HospitalService, private location: Location) { }

  ngOnInit(): void {
    this.userType = localStorage.getItem('user_type');
    this.sharedService.setActiveLink('hadoctor-reports');
    this.subscriptions = this.hospitalService.currentHospitalDetails.pipe()
      .subscribe(value => {
        if (value && value.hospitalId != '') {
          this.hospital = Object.assign({}, value);
          this.hospitalId = this.hospital.hospitalId;
        } else {
          this.hospital = this.hospitalService.getHospitalDetails();
          this.hospitalId = this.hospital.hospitalId;
        }
        if (this.hospitalId) {

        }
      });
      this.setActiveLink();
  }

  back() {
    this.location.back();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
  setActiveLink() {
    if (this.userType=='HospitalAdmin'|| this.userType=='PlatformAdmin') {
      this.activeLink = 'ER';
    } else if (this.userType=='DoctorAssistant'|| this.userType=='Patient') {
      this.activeLink = 'CR';
    } 
  }

}
