
.modal-confirm {
    color: #636363;
    width: 400px;
    margin: 30px auto;
  }
  
  .modal-confirm .modal-content {
    padding: 20px;
    border-radius: 5px;
    border: none;
    text-align: center;
    font-size: 14px;
  }
  
  .modal-confirm .modal-header {
    border-bottom: none;
    position: relative;
  }
  
  .modal-confirm h4 {
    text-align: center;
    font-size: 26px;
    margin: 30px 0 -10px;
  }
  
  .modal-confirm .close {
    position: absolute;
    top: -5px;
    right: -2px;
  }
  
  .modal-confirm .modal-body {
    color: #999;
  }
  
  .modal-confirm .modal-footer {
    border: none;
    text-align: center;
    border-radius: 5px;
    font-size: 13px;
    padding: 10px 15px 25px;
    margin: 0px 34px 0px;
  }
  
  .modal-confirm .modal-footer a {
    color: #999;
  }
  
  .modal-confirm .icon-box {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    border-radius: 50%;
    z-index: 9;
    text-align: center;
    border: 3px solid #f15e5e;
  }
  
  .modal-confirm .icon-box i {
    color: #f15e5e;
    font-size: 46px;
    display: inline-block;
    margin-top: 13px;
  }
  
  .modal-confirm .btn {
    color: #fff;
    border-radius: 4px;
    background: #60c7c1;
    text-decoration: none;
    transition: all 0.4s;
    line-height: normal;
    min-width: 120px;
    border: none;
    min-height: 40px;
    border-radius: 3px;
    margin: 0 5px;
    outline: none !important;
  }
  
  .modal-confirm .btn-info {
    background: #c1c1c1;
  }
  
  .modal-confirm .btn-info:hover,
  .modal-confirm .btn-info:focus {
    background: #a8a8a8;
  }
  
  .modal-confirm .btn-danger {
    background: #f15e5e;
  }
  
  .modal-confirm .btn-danger:hover,
  .modal-confirm .btn-danger:focus {
    background: #ee3535;
  }
  
  .modal-dialogBox {
    overflow-y: initial !important;
    min-width: 1000px;
  }
  .light-blue-bg {
    background-color: #a890cee8;
  }
  .dark-gray-bg {
    background-color: #cc47099d;
  }
  
  .input-field {
    width: 30px; 
    height: 30px; 
    margin: 5px; 
    text-align: center;
    font-size: 16px; 
    border: 1px solid #ccc; 
    border-radius: 5px; 
  }
  
  .input-field:focus {
    outline: none;
  }
  
  .input-field:focus  {
    outline: none;
    border-color: blue; 
  }
  
  .input-field[type="text"] {
    max-length: 1;
  }
  .input-field.invalid {
    border-color: red; 
  }
  
  .countdown {
    font-size: 16px;
    font-weight: bold;
    color: rgb(47, 0, 255);
  }
  
  .resend-otp{
    color: #0000ff !important;
    cursor: pointer;
    font-size: 0.75rem;
  }
  