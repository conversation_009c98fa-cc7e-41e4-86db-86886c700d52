<div *ngIf="loading">
    <app-haloading-spinner></app-haloading-spinner>
</div>
<div *ngIf="!loading">
    <div class="content">
        <div class="container-fluid">
            <h2 class="mx-4" *ngIf="userType=='Patient'"><i class="icon-size fas fa-chevron-circle-left back" *ngIf="showBackBtn"
                    (click)="back()" ></i>Share Consultation History</h2>
                    <h2 class="mx-4" *ngIf="userType=='Doctor'"><i class="icon-size fas fa-chevron-circle-left back" *ngIf="showBackBtn"
                        (click)="back()" ></i>Patient Consultation History</h2>
            <div class="row">
                <div class="col-md-4  float-right">
                    <ng-multiselect-dropdown  id="one" name="history" [settings]="dropdownSettings"
                        [data]="appointmentsArray" [(ngModel)]="selectedItem" (onSelect)="onItemSelect($event)" (onDeSelect)="onItemDeSelect($event)"
                        (onSelectAll)="onSelectAll($event)" (onDeSelectAll)="onDeSelectAll($event)">
                    </ng-multiselect-dropdown>
                    
                </div>
                <div class="col-md-4  float-right" *ngIf="userType=='Patient'">
                    <button class="btn btn-primary btn-sm mr-2" (click)="confirmShare()">Share</button>
                </div>
            </div>
                <!-- static data -->

                <div>
                    <div *ngFor="let data of consultationDataList; let indx = index">
                        <div class="row mt-4">
                            <div class="col-md-12 float-right" *ngIf="indx == 0 && showPagination">
                                <div class="float-right">
                                    <nav aria-label="Page navigation example" *ngIf="showPagination">
                                        <ul class="pagination mt-2">
                                            <li class="page-item" (click)="firstPageList()"
                                                [ngClass]="{'disabled-pagination': consultationsCurrentPage === 1}">
                                                <a class="page-link">&lt;&lt;</a>
                                            </li>
                                            <li class="page-item" (click)="previousPageList()"
                                                [ngClass]="{'disabled-pagination': consultationsCurrentPage === 1 }">
                                                <a class="page-link">&lt;</a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link">page &nbsp;{{ consultationsCurrentPage }}&nbsp;of
                                                    &nbsp;{{ consultationsTotalPage }}</a>
                                            </li>
                                            <li class="page-item" (click)="nextPageList()"
                                                [ngClass]="{'disabled-pagination': consultationsCurrentPage === consultationsTotalPage }">
                                                <a class="page-link">&gt;</a>
                                            </li>
                                            <li class="page-item" (click)="lastPageList()"
                                                [ngClass]="{'disabled-pagination': consultationsCurrentPage === consultationsTotalPage}">
                                                <a class="page-link">&gt;&gt;</a>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                            <div [ngClass]="{'col-md-6':showBackBtn,'col-xl-8 col-lg-8 col-md-5':!showBackBtn}">
                                <h4 class="ml-5">
                                    Date: {{ data.scheduled_start_datetime | date: "medium" }}
                                </h4>
                            </div>
                            <div class="col-md-12" *ngIf="videoAndData || onlyData"
                                [ngStyle]="{ width: onlyData ? '1500px' : '800px' }"
                                [ngStyle]="{ 'margin-left': onlyData ? '10%' : '' }" style="height: 750px">
                                <div class="row">
                                    <div class="card-body d-flex flex-column">
                                        <div>
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <!-- <div class="card"> -->
                                                    <div class="card-body">
                                                        <div class="profile-box">
                                                            <div class="row">
                                                                <div class="col-md-12">
                                                                    <div
                                                                        class="card border-0 bg-light schedule-widget mb-0">
                                                                        <div class="schedule-header">
                                                                            <div class="schedule-nav">
                                                                                <ul class="nav nav-tabs nav-justified">
                                                                                    <li class="nav-item"
                                                                                        *ngIf="!!showAllTabes">
                                                                                        <a class="nav-link active  pt-4 pb-2 btb"
                                                                                            data-toggle="tab"
                                                                                            href="#slot_medical{{ indx }}">
                                                                                            <h6 class="tab-title">
                                                                                                <i
                                                                                                    class="icon-size fas fa-book-medical"></i><br />Medical
                                                                                                History
                                                                                            </h6>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="nav-item"
                                                                                        *ngIf="!!showAllTabes">
                                                                                        <a class="nav-link pt-4 pb-4 btb"
                                                                                            data-toggle="tab"
                                                                                            href="#slot_vitals{{ indx }}">
                                                                                            <h6 class="tab-title">
                                                                                                <i
                                                                                                    class="icon-size fa fa-heartbeat"></i><br />Vitals
                                                                                            </h6>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="nav-item"
                                                                                        *ngIf="!!showAllTabes">
                                                                                        <a class="nav-link pt-4 pb-2 btb"
                                                                                            data-toggle="tab"
                                                                                            href="#slot_physicalexam{{ indx }}">
                                                                                            <h6 class="tab-title">
                                                                                                <i
                                                                                                    class="icon-size fas fa-user-md"></i><br />Physical
                                                                                                Exam
                                                                                            </h6>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="nav-item"
                                                                                        *ngIf="!!showAllTabes">
                                                                                        <a class="nav-link pt-4 pb-4 btb"
                                                                                            data-toggle="tab"
                                                                                            href="#slot_systemicexam{{ indx }}">
                                                                                            <h6 class="tab-title">
                                                                                                <i
                                                                                                    class="icon-size fas fa-assistive-listening-systems"></i><br />Systemic
                                                                                                Exam
                                                                                            </h6>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="nav-item"
                                                                                        *ngIf="!!showAllTabes">
                                                                                        <a class="nav-link pt-3 pb-3 btb"
                                                                                            data-toggle="tab"
                                                                                            href="#slot_diagnosis{{ indx }}">
                                                                                            <h6 class="tab-title">
                                                                                                <i
                                                                                                    class="icon-size fas fa-diagnoses"></i><br />Diagnosis
                                                                                            </h6>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="nav-item">
                                                                                        <a class="nav-link pt-3 pb-3 btb"
                                                                                            (click)="deActiveTab()"
                                                                                            data-toggle="tab"
                                                                                            href="#slot_investigation{{ indx }}">
                                                                                            <h6 class="tab-title">
                                                                                                <i
                                                                                                    class="icon-size fas fa-comment-medical"></i><br />Investigation
                                                                                            </h6>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="nav-item">
                                                                                        <a class="nav-link pt-3 pb-3 btb"
                                                                                            data-toggle="tab" [ngClass]="{
                                                  'active-prescription':
                                                    showAllTabes == false &&
                                                    !prescriptionTabDeactive
                                                }" href="#slot_prescription{{ indx }}">
                                                                                            <h6 class="tab-title">
                                                                                                <i
                                                                                                    class="icon-size fas fa-pills"></i><br />Prescription
                                                                                            </h6>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li class="nav-item"
                                                                                        *ngIf="!!showAllTabes">
                                                                                        <a class="nav-link pt-4 pb-4 btb"
                                                                                            data-toggle="tab"
                                                                                            href="#slot_report{{ indx }}">
                                                                                            <h6 class="tab-title">
                                                                                                <i
                                                                                                    class="icon-size fas fa-file-medical"></i><br />Reports
                                                                                            </h6>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li (click)="getRecording(data)"
                                                                                        class="nav-item"
                                                                                        *ngIf="!!showAllTabes">
                                                                                        <a class="nav-link pt-4 pb-4 btb"
                                                                                            data-toggle="tab"
                                                                                            href="#recording{{ indx }}">
                                                                                            <h6 class="tab-title"><i
                                                                                                    class="icon-size fas fa-video"></i><br />Recording
                                                                                            </h6>
                                                                                        </a>
                                                                                    </li>
    
                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                        <div class="tab-content schedule-cont">
                                                                            <div id="slot_medical{{ indx }}"
                                                                                class="tab-pane fade show active">
                                                                                <div class="card border-info pb-2"
                                                                                    style="height: 410px; overflow: scroll">
                                                                                    <!-- <h5 class="text-danger text-center mt-4 pl-2">Medical History</h5> -->
    
                                                                                    <table class="mt-3"
                                                                                        style="width: 100%;">
                                                                                        <tr>
                                                                                            <td class="p-2">
                                                                                                Chief Complaint
                                                                                                <textarea
                                                                                                    name="chief_complaint"
                                                                                                    class="form-control mb-3"
                                                                                                    id="notes" cols="5"
                                                                                                    rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory
                                                        .chief_complaint
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td class="pl-2">
                                                                                                History Of Present Illness
                                                                                                <textarea
                                                                                                    name="history_of_present_illness"
                                                                                                    class="form-control mb-3"
                                                                                                    id="notes" cols="5"
                                                                                                    rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory
                                                        .history_of_present_illness
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Immunization History
                                                                                                <textarea
                                                                                                    name="immunizationHistory"
                                                                                                    class="form-control mb-3"
                                                                                                    id="immunHis{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory
                                                        .immunization_history
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Past Medical History
                                                                                                <textarea
                                                                                                    name="personalHistory"
                                                                                                    class="form-control mb-3"
                                                                                                    id="persHis{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory
                                                        .past_medical_history
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr class="pt-2">
                                                                                            <td class="pl-2 mt-2">
                                                                                                Appetite
                                                                                                <textarea name="appetite"
                                                                                                    class="form-control mb-3"
                                                                                                    id="appetite{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory.appetite
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Diet
                                                                                                <textarea name="diet"
                                                                                                    class="form-control mb-3"
                                                                                                    id="diet{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory.diet
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
    
                                                                                        <tr class="ml-2 pt-2">
                                                                                            <td class="pl-2">
                                                                                                Thirst / Water Intake
                                                                                                <textarea name="thirst"
                                                                                                    class="form-control mb-3"
                                                                                                    id="thirst{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory.thirst
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Sleep
                                                                                                <textarea name="sleep"
                                                                                                    class="form-control mb-3"
                                                                                                    id="sleep{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory.sleep
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td class="pl-2">
                                                                                                Social History/ Habits/
                                                                                                Addictions
                                                                                                <textarea name="habits"
                                                                                                    class="form-control mb-3"
                                                                                                    id="habits{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory
                                                        .social_history
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Smoking
                                                                                                <textarea name="smoking"
                                                                                                    class="form-control mb-3"
                                                                                                    id="smoking{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory.smoking
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Alcohol
                                                                                                <textarea name="alcohol"
                                                                                                    class="form-control mb-3"
                                                                                                    id="alcohol{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory.alcohol
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Drugs
                                                                                                <textarea name="drugs"
                                                                                                    class="form-control mb-3"
                                                                                                    id="drugs{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory.drugs
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td class="pl-2">
                                                                                                Sexual History
                                                                                                <textarea
                                                                                                    name="sexual_history"
                                                                                                    class="form-control mb-3"
                                                                                                    id="sh{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory
                                                        .sexual_history
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Other Observation/ Notes
                                                                                                <textarea
                                                                                                    name="other_observations"
                                                                                                    class="form-control mb-3"
                                                                                                    id="othob{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.medicalhistory
                                                        .other_observations
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <div
                                                                                            class="d-flex justify-content-center text-success">
                                                                                            <tr class="mt-2">
                                                                                                <h5>Female patient</h5>
                                                                                                <input type="radio"
                                                                                                    class="ml-3" [checked]="
                                                      data.medicalhistory.female ==
                                                      true
                                                        ? true
                                                        : false
                                                    " disabled />
                                                                                                <label class="ml-1"
                                                                                                    for="gender">Yes</label>
                                                                                                <input type="radio"
                                                                                                    class="ml-3" [checked]="
                                                      data.medicalhistory.female ==
                                                        false ||
                                                      data.medicalhistory.female ==
                                                        null
                                                    " disabled />
                                                                                                <label class="ml-1"
                                                                                                    for="gender">No</label>
                                                                                            </tr>
                                                                                        </div>
                                                                                        <tr
                                                                                            *ngIf="data.medicalhistory.female">
                                                                                            <td class="pl-2">
                                                                                                Gynaecological History
                                                                                                <textarea
                                                                                                    name="gynaecological_history"
                                                                                                    class="form-control mb-3"
                                                                                                    id="gynHis{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 80%"
                                                                                                    disabled>{{
                                                      data.medicalhistory
                                                        .gynaecological_history
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Age of Menarche
                                                                                                <input type="number"
                                                                                                    name="age_of_menarche"
                                                                                                    class="form-control mb-3"
                                                                                                    id="aom{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 90%"
                                                                                                    [value]="
                                                      data.medicalhistory
                                                        .age_of_menarche
                                                    " disabled />
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr
                                                                                            *ngIf="data.medicalhistory.female">
                                                                                            <td class="pl-2">
                                                                                                Menstrual History
                                                                                                <textarea
                                                                                                    name="menstrual_history"
                                                                                                    class="form-control mb-3"
                                                                                                    id="menstrual_history{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 80%"
                                                                                                    disabled>{{
                                                      data.medicalhistory
                                                        .menstrual_history
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Last Menstrual Period
                                                                                                <textarea
                                                                                                    name="last_menstrual_period"
                                                                                                    class="form-control mb-3"
                                                                                                    id="lmp{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 90%"
                                                                                                    disabled>{{
                                                      data.medicalhistory
                                                        .last_menstrual_period |date:'dd-MM-yyyy'
                                                    }}</textarea>
                                                                                                <!-- <input id="mensHist" formControlName="last_menstrual_period" type="text" style="caret-color: transparent;width:90%" class="form-control" name="last_menstrual_period" cols="5" rows="1" #dp="bsDatepicker" placeholder="Select Date" autocomplete="off" bsDatepicker
                                                                                                [bsConfig]="{ showWeekNumbers:false,isAnimated: true }" (ngModelChange)="consultationDataDirectSave('medical_history','last_menstrual_period', $event)" required> -->
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr
                                                                                            *ngIf="data.medicalhistory.female">
                                                                                            <td class="pl-2">
                                                                                                Number Of Pregnancy
                                                                                                <input
                                                                                                    onkeydown="return false"
                                                                                                    name="number_of_pregnancy"
                                                                                                    class="form-control mb-3"
                                                                                                    min="0" max="20"
                                                                                                    id="noFp{{ indx }}"
                                                                                                    type="number" cols="5"
                                                                                                    rows="1"
                                                                                                    style="width: 80%"
                                                                                                    [value]="
                                                      data.medicalhistory
                                                        .number_of_pregnancy
                                                    " disabled />
                                                                                            </td>
                                                                                            <td>
                                                                                                Gravida
                                                                                                <input type="number"
                                                                                                    onkeydown="return false"
                                                                                                    name="gravida"
                                                                                                    class="form-control mb-3"
                                                                                                    id="gravida{{ indx }}"
                                                                                                    [value]="
                                                      data.medicalhistory.gravida
                                                    " cols="5" rows="1" min="0" style="width: 90%" disabled />
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr
                                                                                            *ngIf="data.medicalhistory.female">
                                                                                            <td class="pl-2">
                                                                                                Para
                                                                                                <input type="number"
                                                                                                    name="para"
                                                                                                    onkeydown="return false"
                                                                                                    [value]="
                                                      data.medicalhistory.para
                                                    " class="form-control mb-3" id="para{{ indx }}" cols="5" rows="1"
                                                                                                    min="0"
                                                                                                    style="width: 80%"
                                                                                                    disabled />
                                                                                            </td>
                                                                                            <td>
                                                                                                Abortions
                                                                                                <input type="number"
                                                                                                    name="abortions"
                                                                                                    onkeydown="return false"
                                                                                                    [value]="
                                                      data.medicalhistory.abortions
                                                    " class="form-control mb-3" id="abor{{ indx }}" min="0" cols="5"
                                                                                                    rows="1"
                                                                                                    style="width: 90%"
                                                                                                    disabled />
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </div>
                                                                                <!-- <div class="card border-info pb-2" style="height: 410px; overflow: scroll;">
                                                                                    <div *ngFor="let dataObj of data.medicalhistory | keyvalue; let i = index">
                                                                                        <div class="col-md-6" *ngIf="dataObj.key != 'consultation' && dataObj.key != 'uuid' && dataObj.key != 'female' ">
                                                                                            <label>{{underScoreToSpaceCaps(dataObj.key)}}</label>
                            <textarea [name]="dataObj.key" class="form-control mb-3" [id]="dataObj.key" cols="5" rows="1" style="width: 85%;" disabled>{{dataObj.value}}</textarea>
                                                                                </div>
                                                                            </div>
                                                                        </div> -->
                                                                            </div>
                                                                            <div id="slot_vitals{{ indx }}"
                                                                                class="tab-pane fade "
                                                                                *ngIf="!!showAllTabes">
                                                                                <div class="card border-info pb-2"
                                                                                    style="height: 410px">
                                                                                    <table class="mt-4"
                                                                                        style="width: 100%;">
                                                                                        <tr class="ml-2 mt-3">
                                                                                            <td class="pl-2 mt-2">
                                                                                                <div class="mt-4 pl-2"
                                                                                                    style="
                                                  margin-left: 10px;
                                                  margin-right: 20px;
                                                ">
                                                                                                    <h6 class="ml-3">
                                                                                                        <i
                                                                                                            class="icon-size fas fa-file-medical-alt"></i>
                                                                                                        Blood Pressure
                                                                                                        Systolic
                                                                                                    </h6>
                                                                                                    <input
                                                                                                        class="form-control ml-3"
                                                                                                        pattern="^[1-9]"
                                                                                                        type="text" [value]="
                                                      data.vitalsigns
                                                        .blood_pressure_systolic
                                                    " disabled />
                                                                                                </div>
                                                                                            </td>
    
                                                                                            <td class="pl-2 mt-2">
                                                                                                <div class="mt-4 pl-2"
                                                                                                    style="
                                                  margin-left: 10px;
                                                  margin-right: 20px;
                                                ">
                                                                                                    <h6 class="ml-3">
                                                                                                        <i
                                                                                                            class="icon-size fas fa-file-medical-alt"></i>
                                                                                                        Blood Pressure
                                                                                                        Diastolic
                                                                                                    </h6>
                                                                                                    <input
                                                                                                        class="form-control ml-3"
                                                                                                        pattern="^[1-9]"
                                                                                                        type="text" [value]="
                                                      data.vitalsigns
                                                        .blood_pressure_diastolic
                                                    " disabled />
                                                                                                </div>
                                                                                            </td>
                                                                                            <td class="pl-2 mt-2">
                                                                                                <div class="mt-4 pl-2"
                                                                                                    style="
                                                  margin-left: 10px;
                                                  margin-right: 20px;
                                                ">
                                                                                                    <h6 class="ml-3">
                                                                                                        <i class="icon-size fa fa-thermometer-empty"
                                                                                                            aria-hidden="true"></i>
                                                                                                        Temperature
                                                                                                        (Farenheit)
                                                                                                    </h6>
                                                                                                    <input
                                                                                                        class="form-control ml-3"
                                                                                                        pattern="^[1-9]"
                                                                                                        type="text" [value]="
                                                      data.vitalsigns.temperature
                                                    " disabled />
                                                                                                </div>
                                                                                            </td>
                                                                                            <td class="pl-2 mt-2">
                                                                                                <div class="mt-4 pl-2"
                                                                                                    style="
                                                  margin-left: 0px;
                                                  margin-right: 20px;
                                                ">
                                                                                                    <h6 class="ml-3">
                                                                                                        <i class="icon-size fa fa-heartbeat"
                                                                                                            aria-hidden="true"></i>
                                                                                                        Pulse Rate
                                                                                                    </h6>
                                                                                                    <input
                                                                                                        class="form-control ml-3"
                                                                                                        pattern="^[1-9]"
                                                                                                        type="text" [value]="
                                                      data.vitalsigns.pulse_rate
                                                    " disabled />
                                                                                                </div>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <div class="mt-4 mb-4"></div>
                                                                                        </tr>
    
                                                                                        <tr>
                                                                                            <td
                                                                                                class="ml-2 justify-content-center">
                                                                                                <div class="mt-4 pl-2"
                                                                                                    style="
                                                      margin-left: 30px;
                                                      margin-right: 6px;
                                                    ">
                                                                                                    <h6 class="ml-3">
                                                                                                        <i
                                                                                                            class="icon-size fas fa-stethoscope"></i>
                                                                                                        Auscultation
                                                                                                    </h6>
                                                                                                    <input type="text"
                                                                                                        name="auscultation"
                                                                                                        class="form-control"
                                                                                                        id="auscultation{{ indx }}"
                                                                                                        [value]="
                                                        data.vitalsigns.auscultation
                                                      " cols="5" rows="1" style="width: 100%" disabled />
                                                                                                </div>
                                                                                            </td>
                                                                                            <td>
                                                                                                <div class="mt-4 pl-4"
                                                                                                    style="
                                                      margin-left: 20px;
                                                      margin-right: 6px;
                                                    ">
                                                                                                    <h6 class="ml-3">
                                                                                                        <i
                                                                                                            class="icon-size fas fa-wave-square"></i>
                                                                                                        ECG
                                                                                                    </h6>
                                                                                                    <input name="ecg"
                                                                                                        type="text"
                                                                                                        class="form-control"
                                                                                                        id="ecg{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 100%"
                                                                                                        [value]="data.vitalsigns.ecg"
                                                                                                        disabled />
                                                                                                </div>
                                                                                            </td>
                                                                                            <td class="pl-2 mt-2">
                                                                                                <div class="mt-4 pl-4"
                                                                                                    style="
                                                      margin-left: 3px;
                                                      margin-right: 20px;
                                                    ">
                                                                                                    <h6 class="ml-3">
                                                                                                        <i
                                                                                                            class="icon-size fas fa-file-medical-alt"></i>
                                                                                                        SPO2
                                                                                                    </h6>
                                                                                                    <input
                                                                                                        class="form-control ml-3"
                                                                                                        pattern="^[1-9]"
                                                                                                        id="spo2{{ indx }}"
                                                                                                        type="text" min="1"
                                                                                                        [value]="data.vitalsigns.spo2"
                                                                                                        disabled />
                                                                                                </div>
                                                                                            </td>
                                                                                            <td
                                                                                                class="ml-4 justify-content-center">
                                                                                                <div class="mt-4 pl-4 mr-2">
                                                                                                    <h6 class="ml-3">
                                                                                                        <i
                                                                                                            class="icon-size far fa-sticky-note"></i>
                                                                                                        Additional Notes
                                                                                                    </h6>
                                                                                                    <textarea name="notes"
                                                                                                        class="form-control mr-3"
                                                                                                        id="notes{{ indx }}"
                                                                                                        [value]="
                                                        data.vitalsigns
                                                          .additional_notes
                                                      " cols="20" rows="1" disabled></textarea>
                                                                                                </div>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </div>
                                                                                <!-- <div class="card border-info pb-2" style="height: 410px;overflow: scroll;">
                                                                                    <div *ngFor="let dataObj of data.vitalsigns | keyvalue; let i = index">
                                                                                        <div class="col-md-6" *ngIf="dataObj.key != 'consultation' && dataObj.key != 'uuid' && dataObj.key != 'female' ">
                                                                                            <label>{{underScoreToSpaceCaps(dataObj.key)}}</label>
                                                                                            <textarea [name]="dataObj.key" class="form-control mb-3" [id]="dataObj.key" cols="5" rows="1" style="width: 85%;" disabled>{{dataObj.value}}</textarea>
                                                                                        </div>
                                                                                    </div>
                                                                                </div> -->
                                                                            </div>
    
                                                                            <div id="slot_physicalexam{{ indx }}"
                                                                                class="tab-pane fade">
                                                                                <div class="card border-info pb-2"
                                                                                    style="height: 410px; overflow: scroll">
                                                                                    <table class="pt-2 pb-2 mt-4"
                                                                                        style="width: 100%;">
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Weight (kg)
                                                                                                <input type="number"
                                                                                                    id="weight{{ indx }}"
                                                                                                    min="1" [value]="
                                                      data.physicalexamination
                                                        .weight
                                                    " style="width: 22%" disabled />
                                                                                            </td>
                                                                                            <td>
                                                                                                Height (cm)
                                                                                                <input type="number"
                                                                                                    id="height{{ indx }}"
                                                                                                    min="1" [value]="
                                                      data.physicalexamination
                                                        .height
                                                    " style="width: 22%" disabled />
                                                                                            </td>
                                                                                            <td>
                                                                                                BMI
                                                                                                <input type="number"
                                                                                                    id="bmi{{ indx }}"
                                                                                                    min="1" [value]="
                                                      data.physicalexamination.bmi
                                                    " style="width: 22%" disabled />
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                    <table class="mt-3"
                                                                                        style="width: 100%;">
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Nutrition
                                                                                                <textarea name="nutrition"
                                                                                                    class="form-control mb-3"
                                                                                                    id="nutrition{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.physicalexamination
                                                        .nutrition
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td class="mt-2">
                                                                                                Nail Changes
                                                                                                <textarea
                                                                                                    name="nail_changes"
                                                                                                    class="form-control mb-3"
                                                                                                    id="nail_changes{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.physicalexamination
                                                        .nail_changes
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
    
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Clubbing Of Fingers
                                                                                                <textarea
                                                                                                    name="clubbingOfFingers"
                                                                                                    class="form-control mb-3"
                                                                                                    id="cof{{ indx }}}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.physicalexamination
                                                        .clubbing_of_fingers
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Cyanosis
                                                                                                <textarea name="cyanosis"
                                                                                                    class="form-control mb-3"
                                                                                                    id="cyn{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.physicalexamination
                                                        .cyanosis
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Icterus/Jaundice
                                                                                                <textarea
                                                                                                    name="icterusJaundice"
                                                                                                    class="form-control mb-3"
                                                                                                    id="ictJau{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.physicalexamination
                                                        .icterus_jaundice
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Pallor
                                                                                                <textarea name="pallor"
                                                                                                    class="form-control mb-3"
                                                                                                    id="pallor{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.physicalexamination
                                                        .pallor
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Lymph Nodes
                                                                                                <textarea name="lymphNodes"
                                                                                                    class="form-control mb-3"
                                                                                                    id="lymphNodes{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.physicalexamination
                                                        .lymph_nodes
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Oedema
                                                                                                <textarea name="oedema"
                                                                                                    class="form-control mb-3"
                                                                                                    id="oedema{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.physicalexamination
                                                        .oedema
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Sclera
                                                                                                <textarea name="sclera"
                                                                                                    class="form-control mb-3"
                                                                                                    id="sclera" cols="5"
                                                                                                    rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.physicalexamination
                                                        .sclera
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </div>
                                                                            </div>
                                                                            <div id="slot_systemicexam{{ indx }}"
                                                                                class="tab-pane fade">
                                                                                <div class="card border-info pb-2"
                                                                                    style="height: 410px; overflow: scroll">
                                                                                    <table class="mt-4"
                                                                                        style="width: 100%;">
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Respiratory System
                                                                                                <textarea
                                                                                                    name="respiratory_system"
                                                                                                    class="form-control mb-3"
                                                                                                    id="resp_sys{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination
                                                        .respiratory_system
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Gastro Intestinal/Abdomen
                                                                                                <textarea
                                                                                                    name="gastro_intestinal_system"
                                                                                                    class="form-control mb-3"
                                                                                                    id="gasInt{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination
                                                        .gastro_intestinal_system
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr class="pt-2">
                                                                                            <td class="pl-2 mt-2">
                                                                                                Cardio Vascular System
                                                                                                <textarea
                                                                                                    name="cardio_vascular_system"
                                                                                                    class="form-control mb-3"
                                                                                                    id="cardiVas{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination
                                                        .cardio_vascular_system
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Genito Urinary System
                                                                                                <textarea
                                                                                                    name="genitourinary_system"
                                                                                                    class="form-control mb-3"
                                                                                                    id="gns{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination
                                                        .genitourinary_system
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
    
                                                                                        <tr class="ml-2 pt-2">
                                                                                            <td class="pl-2">
                                                                                                Musculoskeletal System
                                                                                                <textarea
                                                                                                    name="musculoskeletal_system"
                                                                                                    class="form-control mb-3"
                                                                                                    id="mus{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination
                                                        .musculoskeletal_system
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Central Nervous System
                                                                                                <textarea
                                                                                                    name="central_nervous_system"
                                                                                                    class="form-control mb-3"
                                                                                                    id="cns{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination
                                                        .central_nervous_system
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td class="pl-2">
                                                                                                Eye
                                                                                                <textarea name="eye"
                                                                                                    class="form-control mb-3"
                                                                                                    id="eye{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination.eye
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Ear
                                                                                                <textarea name="ear"
                                                                                                    class="form-control mb-3"
                                                                                                    id="ear{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination.ear
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Nose
                                                                                                <textarea name="nose"
                                                                                                    class="form-control mb-3"
                                                                                                    id="nose{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination.nose
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Mouth
                                                                                                <textarea name="mouth"
                                                                                                    class="form-control mb-3"
                                                                                                    id="mouth{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination.mouth
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td class="pl-2">
                                                                                                Throat
                                                                                                <textarea name="throat"
                                                                                                    class="form-control mb-3"
                                                                                                    id="throat{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination
                                                        .throat
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Neck
                                                                                                <textarea name="neck"
                                                                                                    class="form-control mb-3"
                                                                                                    id="neck{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination.neck
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td class="pl-2">
                                                                                                Skin
                                                                                                <textarea name="skin"
                                                                                                    class="form-control mb-3"
                                                                                                    id="skin{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination.skin
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Psychiatric History
                                                                                                <textarea
                                                                                                    name="psychiatric_history"
                                                                                                    class="form-control mb-3"
                                                                                                    id="psych{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 85%"
                                                                                                    disabled>{{
                                                      data.systemicexamination
                                                        .psychiatric_history
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </div>
                                                                            </div>
                                                                            <div id="slot_diagnosis{{ indx }}"
                                                                                class="tab-pane fade">
                                                                                <div class="card border-info pb-2"
                                                                                    style="height: 410px; overflow: scroll">
                                                                                    <table class="mt-4"
                                                                                        style="width: 100%;">
                                                                                        <tr class="ml-2">
                                                                                            <td class="pl-2">
                                                                                                Primary
                                                                                                <textarea name="primary"
                                                                                                    class="form-control mb-3"
                                                                                                    id="primary{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 80%"
                                                                                                    disabled>{{
                                                      data.diagnosis
                                                        .primary_diagnosis
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Secondary
                                                                                                <textarea name="secondary"
                                                                                                    class="form-control mb-3"
                                                                                                    id="secondary{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 90%"
                                                                                                    disabled>{{
                                                      data.diagnosis
                                                        .secondary_diagnosis
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr class="pt-2">
                                                                                            <td class="pl-2 mt-2">
                                                                                                Differential Diagnosis
                                                                                                <textarea
                                                                                                    name="differential_diagnosis"
                                                                                                    class="form-control mb-3"
                                                                                                    id="diffDia{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 80%"
                                                                                                    disabled>{{
                                                      data.diagnosis
                                                        .differential_diagnosis
                                                    }}</textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                Final Diagnosis
                                                                                                <textarea
                                                                                                    name="finalDiagnosis"
                                                                                                    class="form-control mb-3"
                                                                                                    id="finalDia{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 90%"
                                                                                                    disabled>{{
                                                      data.diagnosis.final_diagnosis
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
    
                                                                                        <tr class="ml-2 pt-2">
                                                                                            <td class="pl-2">
                                                                                                ICD 10 Codes
                                                                                                <textarea name="ICD10Codes"
                                                                                                    name="finalDiagnosis"
                                                                                                    class="form-control mb-3"
                                                                                                    id="icd{{ indx }}"
                                                                                                    cols="5" rows="1"
                                                                                                    style="width: 80%"
                                                                                                    disabled>{{
                                                      data.diagnosis.icd_10_codes
                                                    }}</textarea>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </div>
                                                                            </div>
    
                                                                            <div id="slot_investigation{{ indx }}"
                                                                                class="tab-pane fade">
                                                                                <div class="col-xs-12 text-success">
                                                                                    <div class="col-md-12 float-right mb-3">
                                                                                        <button
                                                                                            class="btn btn-primary float-right"
                                                                                            (click)="downloadInvestigation(data.uuid)">
                                                                                            Download
                                                                                        </button>
                                                                                    </div>
    
                                                                                    <div class="card border-info pb-2"
                                                                                        style="
                                                height: 410px;
                                                overflow: scroll;
                                              ">
                                                                                        <!-- <form [formGroup]="investigationForm"> -->
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                HAEMATOLOGY
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="haematologyItems" bindLabel="investigation_name" formControlName="haematology" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('HAEMATOLOGY',$event)" (focusout)="processInvestigation('HAEMATOLOGY')">
                                                                                                    </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="haematology"
                                                                                                        class="form-control mb-3"
                                                                                                        id="haem{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "HAEMATOLOGY"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                BIOCHEMISTRY AND
                                                                                                IMMUNOASSAYS
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="biochemistryItems" bindLabel="investigation_name" formControlName="biochemistryAndImmunoassay" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('BIOCHEMISTRY AND IMMUNOASSAYS',$event)"
                                                                                                        (focusout)="processInvestigation('BIOCHEMISTRY AND IMMUNOASSAYS')">
                                                                                                    </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="BIOCHEMISTRY AND IMMUNOASSAYS"
                                                                                                        class="form-control mb-3"
                                                                                                        id="bai{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "BIOCHEMISTRY AND IMMUNOASSAYS"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                MICROBIOLOGY
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="microbiologyItems" bindLabel="investigation_name" formControlName="microbiology" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('MICROBIOLOGY',$event)" (focusout)="processInvestigation('MICROBIOLOGY')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="microbiology"
                                                                                                        class="form-control mb-3"
                                                                                                        id="mic{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "MICROBIOLOGY"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                CLINICAL PATHOLOGY
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="clinicalPathologyItems" bindLabel="investigation_name" formControlName="clinicalPathology" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('CLINICAL PATHOLOGY',$event)"
                                                                                                    (focusout)="processInvestigation('CLINICAL PATHOLOGY')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="clinical_pathology"
                                                                                                        class="form-control mb-3"
                                                                                                        id="clinPath{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "CLINICAL PATHOLOGY"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                PATHOLOGY
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="pathologyItems" bindLabel="investigation_name" formControlName="pathology" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('PATHOLOGY',$event)" (focusout)="processInvestigation('PATHOLOGY')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="pathology"
                                                                                                        class="form-control mb-3"
                                                                                                        id="path{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "PATHOLOGY"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                SEROLOGY
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="serologyItems" bindLabel="investigation_name" formControlName="serology" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('SEROLOGY',$event)" (focusout)="processInvestigation('SEROLOGY')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="serology"
                                                                                                        class="form-control mb-3"
                                                                                                        id="ser{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "SEROLOGY"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                MALARIA
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="malariaItems" bindLabel="investigation_name" formControlName="malaria" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('MALARIA',$event)" (focusout)="processInvestigation('MALARIA')">
                                                                                                </ng-select> -->
                                                                                                    <textarea name="malaria"
                                                                                                        class="form-control mb-3"
                                                                                                        id="mal{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "MALARIA"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                FILARIASIS
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="filariasisItems" bindLabel="investigation_name" formControlName="filariasis" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('FILARIASIS',$event)" (focusout)="processInvestigation('FILARIASIS')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="filariasis"
                                                                                                        class="form-control mb-3"
                                                                                                        id="fil{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "FILARIASIS"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                DENGUE
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="dengueItems" bindLabel="investigation_name" formControlName="dengue" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('DENGUE',$event)" (focusout)="processInvestigation('DENGUE')">
                                                                                                </ng-select> -->
                                                                                                    <textarea name="dengue"
                                                                                                        class="form-control mb-3"
                                                                                                        id="den{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "DENGUE"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                JAPANESE ENCEPHALITIS
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="japaneseEncephalitisItems" bindLabel="investigation_name" formControlName="japaneseEncephalitis" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('JAPANESE ENCEPHALITIS',$event)"
                                                                                                    (focusout)="processInvestigation('JAPANESE ENCEPHALITIS')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="japanese"
                                                                                                        class="form-control mb-3"
                                                                                                        id="japEnce{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "JAPANESE ENCEPHALITIS"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                CHIKUNGUNYA
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="chikungunyaItems" bindLabel="investigation_name" formControlName="chikungunya" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('CHIKUNGUNYA',$event)" (focusout)="processInvestigation('CHIKUNGUNYA')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="chikungunya"
                                                                                                        class="form-control mb-3"
                                                                                                        id="chick{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "CHIKUNGUNYA"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                SCRUB TYPHUS
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="scrubTyphusItems" bindLabel="investigation_name" formControlName="scrubTyphus" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('SCRUB TYPHUS',$event)" (focusout)="processInvestigation('SCRUB TYPHUS')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="scrub_typhus"
                                                                                                        class="form-control mb-3"
                                                                                                        id="scrub_typus{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "SCRUB TYPHUS"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                LEPTOSPIROSIS
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="leptospirosisItems" bindLabel="investigation_name" formControlName="leptospirosis" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('LEPTOSPIROSIS',$event)" (focusout)="processInvestigation('LEPTOSPIROSIS')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="leptospirosis"
                                                                                                        class="form-control mb-3"
                                                                                                        id="lepto{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "LEPTOSPIROSIS"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                BRUCELLOSIS
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="brucellosisItems" bindLabel="investigation_name" formControlName="brucellosis" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('BRUCELLOSIS',$event)" (focusout)="processInvestigation('BRUCELLOSIS')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="brucellosis"
                                                                                                        class="form-control mb-3"
                                                                                                        id="bruce{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "BRUCELLOSIS"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                TUBERCULOSIS
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="tuberculosisItems" bindLabel="investigation_name" formControlName="tuberculosis" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('TUBERCULOSIS',$event)" (focusout)="processInvestigation('TUBERCULOSIS')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="tuberculosis"
                                                                                                        class="form-control mb-3"
                                                                                                        id="tuber{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "TUBERCULOSIS"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                HIV
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="hivItems" bindLabel="investigation_name" formControlName="hiv" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('HIV',$event)" (focusout)="processInvestigation('HIV')">
                                                                                                </ng-select> -->
                                                                                                    <textarea name="hiv"
                                                                                                        class="form-control mb-3"
                                                                                                        id="hiv{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "HIV"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                HEPATITIS B
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="hepatitisBItems" bindLabel="investigation_name" formControlName="hepatitisB" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('HEPATITIS B',$event)" (focusout)="processInvestigation('HEPATITIS B')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="hepatitisb"
                                                                                                        class="form-control mb-3"
                                                                                                        id="hepB{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "HEPATITIS B"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                HEPATITIS C
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="hepatitisCItems" bindLabel="investigation_name" formControlName="hepatitisC" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('HEPATITIS C',$event)" (focusout)="processInvestigation('HEPATITIS C')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="hepatitisC"
                                                                                                        class="form-control mb-3"
                                                                                                        id="hepC{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "HEPATITIS C"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                HEPATITIS A
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="hepatitisAItems" bindLabel="investigation_name" formControlName="hepatitisA" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('HEPATITIS A',$event)" (focusout)="processInvestigation('HEPATITIS A')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="hepatitisA"
                                                                                                        class="form-control mb-3"
                                                                                                        id="hepA{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "HEPATITIS A"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                HEPATITIS E
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="hepatitisEItems" bindLabel="investigation_name" formControlName="hepatitisE" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('HEPATITIS E',$event)" (focusout)="processInvestigation('HEPATITIS E')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="hepatitisE"
                                                                                                        class="form-control mb-3"
                                                                                                        id="hepE{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "HEPATITIS E"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                HBC (CORE ANTIBODIES)
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="hbcItems" bindLabel="investigation_name" formControlName="hbc" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('HBC (CORE ANTIBODIES)',$event)" (focusout)="processInvestigation('HBC (CORE ANTIBODIES)')">
                                                                                                </ng-select> -->
                                                                                                    <textarea name="hbc"
                                                                                                        class="form-control mb-3"
                                                                                                        id="hbc{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "HBC (CORE ANTIBODIES)"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                OTHER DIAGNOSTIC TESTS
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="otherDiagnosticTestsItems" bindLabel="investigation_name" formControlName="otherDiagnosticTest" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('OTHER DIAGNOSTIC TESTS',$event)"
                                                                                                    (focusout)="processInvestigation('OTHER DIAGNOSTIC TESTS')">
                                                                                                </ng-select> -->
                                                                                                    <textarea name="obt"
                                                                                                        class="form-control mb-3"
                                                                                                        id="odt{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "OTHER DIAGNOSTIC TESTS"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-6 invest-col">
                                                                                                RADIOLOGY & OTHER DIAGNOSTIC
                                                                                                TESTS
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                    <!-- <ng-select [items]="radiologyAndOtherDiagnosticTestsItems" bindLabel="investigation_name" formControlName="radiologyAndOtherDiagnostics" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('RADIOLOGY & OTHER DIAGNOSTIC TESTS',$event)"
                                                                                                    (focusout)="processInvestigation('RADIOLOGY & OTHER DIAGNOSTIC TESTS')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="radiology"
                                                                                                        class="form-control mb-3"
                                                                                                        id="radio{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        processInvestigationData(
                                                          data,
                                                          "RADIOLOGY & OTHER DIAGNOSTIC TESTS"
                                                        )
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                <div class="text-center mb-2"
                                                                                                    style="width: 75%">
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row mt-4">
                                                                                            <div
                                                                                                class="col-md-12 invest-col">
                                                                                                special_instructions
                                                                                                <div
                                                                                                    class="text-center mb-2">
                                                                                                    <!-- <ng-select [items]="radiologyAndOtherDiagnosticTestsItems" bindLabel="investigation_name" formControlName="radiologyAndOtherDiagnostics" bindValue="uuid" placeholder="Select item" appendTo="body" multiple="true" [addTag]="true" (change)="updateInvestigation('RADIOLOGY & OTHER DIAGNOSTIC TESTS',$event)"
                                                                                                    (focusout)="processInvestigation('RADIOLOGY & OTHER DIAGNOSTIC TESTS')">
                                                                                                </ng-select> -->
                                                                                                    <textarea
                                                                                                        name="special_instructions"
                                                                                                        class="form-control mb-3"
                                                                                                        id="radio{{ indx }}"
                                                                                                        cols="5" rows="1"
                                                                                                        style="width: 80%"
                                                                                                        disabled>{{
                                                        data['investigation']['special_instructions']
                                                      }}</textarea>
                                                                                                </div>
                                                                                            </div>
    
                                                                                        </div>
                                                                                        <!-- </form> -->
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div id="slot_report{{ indx }}"
                                                                                class="tab-pane fade">
                                                                                <app-hamedical-report
                                                                                    [patientUuid]="patientUuid"
                                                                                    [doctorUuid]="doctorUuid"
                                                                                    [consultationId]="consultationId"
                                                                                    [history]="medicValue">
                                                                                </app-hamedical-report>
                                                                            </div>
                                                                            <div id="slot_prescription{{ indx }}"
                                                                                class="tab-pane fade" [ngClass]="{
                                            'show active': !showAllTabes
                                          }">
                                                                                <div class="col-md-12 float-right mb-3">
                                                                                    <button
                                                                                        class="btn btn-primary float-right"
                                                                                        (click)="downloadPrescription(data.uuid)">
                                                                                        Download
                                                                                    </button>
                                                                                </div>
                                                                                <table class="table table-responsive" *ngIf="
                                              data.prescription.prescription !==
                                              null
                                            " style="overflow-y: auto" [ngStyle]="
                                              userType == 'Patient'
                                                ? { height: '340px' }
                                                : { height: '350px' }
                                            ">
                                                                                    <thead>
                                                                                        <tr class="text-center">
                                                                                            <th>Sl.no</th>
                                                                                            <th>
                                                                                                Form<span
                                                                                                    class="text-danger">*</span>
                                                                                            </th>
                                                                                            <th>
                                                                                                Medicine<span
                                                                                                    class="text-danger">*</span>
                                                                                            </th>
                                                                                            <th>
                                                                                                Strength<span
                                                                                                    class="text-danger">*</span>
                                                                                            </th>
                                                                                            <th>
                                                                                                Dose<span
                                                                                                    class="text-danger">*</span>
                                                                                            </th>
                                                                                            <th>Morning</th>
                                                                                            <th>Afternoon</th>
                                                                                            <th>Evening</th>
                                                                                            <th>Night</th>
                                                                                            <th>
                                                                                                Instruction<span
                                                                                                    class="text-danger">*</span>
                                                                                            </th>
                                                                                            <th>
                                                                                                Duration<span
                                                                                                    class="text-danger">*</span>
                                                                                            </th>
                                                                                        </tr>
                                                                                    </thead>
                                                                                    <tbody>
                                                                                        <tr *ngFor="
                                                  let prescription of data
                                                    .prescription.prescription
                                                    .drugs_prescribed;
                                                  let x = index
                                                ">
                                                                                            <td>{{ x + 1 }}</td>
                                                                                            <td>
                                                                                                <input type="text" value="{{
                                                      prescription?.medicine_type
                                                    }}" class="form-control" name="medicine_type" disabled />
                                                                                            </td>
                                                                                            <td>
                                                                                                <input type="text"
                                                                                                    class="form-control"
                                                                                                    name="medicine" value="{{
                                                      prescription?.brand_name
                                                    }}" disabled />
                                                                                            </td>
    
                                                                                            <td>
                                                                                                <input class="form-control"
                                                                                                    value="{{
                                                      prescription?.strength
                                                    }}" disabled />
                                                                                            </td>
    
                                                                                            <td>
                                                                                                <input type="text"
                                                                                                    class="form-control"
                                                                                                    name="dosage" value="{{
                                                      prescription
                                                        ?.administration_instructions
                                                        ?.dosage
                                                    }}" disabled />
                                                                                            </td>
                                                                                            <td>
                                                                                                <input class="form-control"
                                                                                                    value="{{
                                                      prescription
                                                        ?.administration_instructions
                                                        ?.morning
                                                    }}" disabled [ngStyle]="
                                                      videoAndData
                                                        ? { width: '70px' }
                                                        : { width: '70px' }
                                                    " />
                                                                                            </td>
                                                                                            <td>
                                                                                                <input class="form-control"
                                                                                                    value="{{
                                                      prescription
                                                        ?.administration_instructions
                                                        ?.afternoon
                                                    }}" disabled [ngStyle]="
                                                      videoAndData
                                                        ? { width: '70px' }
                                                        : { width: '70px' }
                                                    " />
                                                                                            </td>
                                                                                            <td>
                                                                                                <input class="form-control"
                                                                                                    value="{{
                                                      prescription
                                                        ?.administration_instructions
                                                        ?.evening
                                                    }}" disabled [ngStyle]="
                                                      videoAndData
                                                        ? { width: '70px' }
                                                        : { width: '70px' }
                                                    " />
                                                                                            </td>
                                                                                            <td>
                                                                                                <input class="form-control"
                                                                                                    value="{{
                                                      prescription
                                                        ?.administration_instructions
                                                        ?.night
                                                    }}" disabled [ngStyle]="
                                                      videoAndData
                                                        ? { width: '70px' }
                                                        : { width: '70px' }
                                                    " />
                                                                                            </td>
    
                                                                                            <td>
                                                                                                <textarea type="text"
                                                                                                    name="notes" disabled
                                                                                                    value="{{
                                                      prescription
                                                        ?.administration_instructions
                                                        ?.notes
                                                    }}" class="form-control" style="
                                                      height: 20px;
                                                      width: 200px;
                                                    " maxlength="100"></textarea>
                                                                                            </td>
                                                                                            <td>
                                                                                                <input
                                                                                                    class="days form-control"
                                                                                                    type="text" disabled
                                                                                                    name="days" value="{{
                                                                                          prescription
                                                                                            ?.administration_instructions
                                                                                            ?.duration_days
                                                                                        }}" min="1" />
                                                                                            </td>
                                                                                        </tr>
                                                                                    </tbody>
                                                                                </table>
                                                                                <div class="col-md-12 text-center">
                                                                                    <h6 *ngIf="
                                                data.prescription.prescription ===
                                                null
                                              ">
                                                                                        No data
                                                                                    </h6>
                                                                                </div>
                                                                            </div>
                                                                            <div id="recording{{ indx }}"
                                                                                class="tab-pane fade">
    
                                                                                <div class="schedule-header">
                                                                                    <div class="row">
                                                                                        <h5>Consultation Video</h5>
                                                                                        <div class="col-md-12">
                                                                                            <div class="day-slot">
                                                                                                <ul>
    
                                                                                                    <li *ngFor="let record of recordingData"
                                                                                                        style="cursor: pointer;">
                                                                                                        <a
                                                                                                            (click)="openVideo(record.file)">
                                                                                                            <span
                                                                                                                id="day-days">
                                                                                                                <i
                                                                                                                    class="  fa fa-video"></i></span>
                                                                                                            <span
                                                                                                                id="day-mediumDate"
                                                                                                                class="slot-date">{{record.name}}
                                                                                                            </span>
                                                                                                            <span></span>
                                                                                                        </a>
                                                                                                    </li>
                                                                                                    <li
                                                                                                        *ngIf="recordingData.length===0 ">
                                                                                                        <p>No Video Data</p>
                                                                                                    </li>
    
    
                                                                                                </ul>
                                                                                            </div>
                                                                                        </div>
                                                                                        <h5>Screenshot Image</h5>
                                                                                        <div class="col-md-12">
                                                                                            <div class="day-slot">
                                                                                                <ul>
    
                                                                                                    <li *ngFor="let img of screenshot"
                                                                                                        style="cursor: pointer;">
                                                                                                        <a
                                                                                                            (click)="openVideo(img.file)">
                                                                                                            <span
                                                                                                                id="day-days">
                                                                                                                <i
                                                                                                                    class="fa fa-image"></i></span>
                                                                                                            <span
                                                                                                                id="day-mediumDate"
                                                                                                                class="slot-date">{{img.file_name}}
                                                                                                            </span>
                                                                                                            <span></span>
                                                                                                        </a>
                                                                                                    </li>
                                                                                                    <li
                                                                                                        *ngIf=" screenshot.length===0  ">
                                                                                                        <p>No image Data</p>
                                                                                                    </li>
                                                                                                </ul>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr />
                    </div>
                </div>
        </div>
    </div>
</div>
<!-- Sharing consultation confirmation modal starts-->
<div class="modal" id="shareConfirmModal">
    <div class="modal-dialog modal-confirm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title model-header-alinement">Sharing Medical history</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                    &times;
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure want to share the selected medical history with relevant Doctor?</p>
                <p>once confirmed the otp will sent to your new mobile number</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-info" data-dismiss="modal" id="cancel-stop">
                    No
                </button>
                <button type="button" class="btn btn-danger" id="proceed-cancel" (click)="createOTP()">
                    Confirm
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Sharing consultation  confirmation modal ends -->

<!-- otp confirmation modal starts-->
<div class="modal" id="otpConfirmModal">
    <div class="modal-dialog modal-confirm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title model-header-alinement">OTP Confirmation</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                    &times;
                </button>
            </div>
            <div class="modal-body">
                <div [formGroup]="otpForm" class="mb-3">
                    <input #otp1 id="otp1" formControlName="otp1" (input)="pass(otp1,otp2)" class="input-field"
                        maxlength="1">
                    <input #otp2 id="otp2" formControlName="otp2" (input)="pass(otp2,otp3)" class="input-field"
                        maxlength="1">
                    <input #otp3 id="otp3" formControlName="otp3" (input)="pass(otp2,otp4)" class="input-field"
                        maxlength="1">
                    <input #otp4 id="otp4" formControlName="otp4" class="input-field" maxlength="1">
                    <label class="countdown ml-2" *ngIf="!resendOtp">{{seconds}}</label>
                    <a class="ml-2 resend-otp" *ngIf="resendOtp" (click)="createOTP()"><u>Resend OTP</u></a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-info" data-dismiss="modal" id="cancel-stop">
                    No
                </button>
                <button type="button" class="btn btn-primary" id="proceed-cancel" (click)="otpVerification()" [disabled]="otpForm.invalid">
                    Verify
                </button>
            </div>
        </div>
    </div>
</div>
<!-- otp confirmation modal ends -->