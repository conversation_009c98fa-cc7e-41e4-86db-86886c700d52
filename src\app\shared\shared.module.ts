import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppointmentComponent } from './appointment/appointment.component';
import { LoadingSpinnerComponent } from './loading-spinner/loading-spinner.component';
import { DocumentModalComponent } from './document-modal/document-modal.component';
import { RemarksModalComponent } from './remarks-modal/remarks-modal.component';
import { ShortenMessageComponent } from './shorten-message/shorten-message.component';
import { ApprovalDeclineModalComponent } from './approval-decline-modal/approval-decline-modal.component';
import { PatientListComponent } from './patient-list/patient-list.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConsultationHistoryComponent } from './consultation-history/consultation-history.component';
import { ConsultationListComponent } from './consultation-list/consultation-list.component';
import { DatepickerModule, BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { ConsultationTableComponent } from './consultation-table/consultation-table.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { AppointmentListComponent } from './appointment-list/appointment-list.component';
import { AppointmentTableComponent } from './appointment-table/appointment-table.component';
import { ReferralComponent } from './referral/referral.component';
import { ReportsComponent } from './reports/reports.component';
import { HaconsultationSummaryComponent } from './haconsultation-summary/haconsultation-summary.component';
import { ConsultationReportComponent } from './consultation-report/consultation-report.component';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ShareHistoryComponent } from './share-history/share-history.component';

@NgModule({
    imports: [CommonModule, FormsModule, ReactiveFormsModule, BsDatepickerModule.forRoot(),
        DatepickerModule.forRoot(), NgSelectModule,NgMultiSelectDropDownModule],
    declarations: [
        AppointmentComponent,
        LoadingSpinnerComponent,
        DocumentModalComponent,
        RemarksModalComponent,
        ShortenMessageComponent,
        ApprovalDeclineModalComponent,
        PatientListComponent,
        ConsultationHistoryComponent,
        ConsultationListComponent,
        ConsultationTableComponent,
        AppointmentListComponent,
        AppointmentTableComponent,
        ReferralComponent,
        ReportsComponent,
        HaconsultationSummaryComponent,
        ConsultationReportComponent,
        ShareHistoryComponent,
    ],
    exports: [
        LoadingSpinnerComponent,
        RemarksModalComponent,
        PatientListComponent,
        ConsultationHistoryComponent,
        ConsultationListComponent,
        AppointmentListComponent,
        ConsultationTableComponent,
        BsDatepickerModule,
        DatepickerModule,
        NgSelectModule,
        ReferralComponent,
        ReportsComponent,
        HaconsultationSummaryComponent,
        ConsultationReportComponent,
        ShareHistoryComponent,
        
    ],
})
export class SharedModule { }
