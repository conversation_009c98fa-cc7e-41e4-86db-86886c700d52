import { delay } from 'rxjs/operators';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as Settings from './../config/settings';
import { Observable, Observer, BehaviorSubject } from 'rxjs';
import { webSocket } from "rxjs/webSocket";
import { Socket } from '../shared/interfaces';

declare var io: {
  connect(url: string): Socket;
};
@Injectable({
  providedIn: 'root'
})
export class SharedService {
  public picture = '../../../../assets/img/doctors/doctor-thumb-02.png';
  public activeTab = '';
  public userName = ' ';
  public searchList: any = [];
  public patientSearchList: any = [];
  appointmentList: any = [];
  observer: Observer<number>;
  webSocketAvailable: BehaviorSubject<any> = new BehaviorSubject({});
  msg: any;
  individualLogin: BehaviorSubject<any> = new BehaviorSubject(0);

  constructor(private httpClient: HttpClient) { }
  getMessages(): Observable<object> {
    const tokenObj = localStorage.getItem('currentUser');
    const accessToken = JSON.parse(tokenObj)['access'];
    const ws = webSocket(`${Settings.WEBSOCKET_BASE_URL}/ws?username=${accessToken}`);
    ws.subscribe(
      msg => {
        // console.log('shared',msg);
        this.webSocketAvailable.next(msg);
      }
    )
    return this.createObservable();
  }

  createObservable(): Observable<object> {
    return new Observable<any>(observer => {
      this.observer = observer;
      // console.log('observer',observer);
    });
  }
  private handleError(error) {
    console.error('server error:', error);
    if (error.error instanceof Error) {
      let errMessage = error.error.message;
      return Observable.throw(errMessage);
    }
    return Observable.throw(error || 'Socket.io server error');
  }

  post(data): Observable<any> {
    return this
      .httpClient
      .post(
        `${Settings.API_AUTH_URL_PREFIX}/api/auth/validate-otp/`,
        data
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }

  get(uidb64 = null, token = null, otp_type = null): Observable<any> {

    let data = {};
    if (!!uidb64) { data['uidb64'] = uidb64; }
    if (!!token) { data['token'] = token; }
    if (!!otp_type) { data['otp_type'] = otp_type; }
    //console.log(page);
    const params = new HttpParams({ fromObject: data });

    return this
      .httpClient
      .get(
        `${Settings.API_AUTH_URL_PREFIX}/api/auth/validate-otp-token-link/`,
        { 'params': params }
      )
      .pipe(delay(Settings.REQUEST_DELAY_TIME))
      ;
  }

  sendEmailOtp(data) {
    return this.httpClient.post(`${Settings.API_AUTH_URL_PREFIX}/api/auth/signup/email_otp_resend/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  sendPhoneOtp(data) {
    return this.httpClient.post(`${Settings.API_AUTH_URL_PREFIX}/api/auth/signup/phone_otp_resend/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  initiateBooking(uuid, data, hsptl_uuid?) {
    let hospital_uuid = ''
    if (hsptl_uuid && hsptl_uuid != undefined) {
      hospital_uuid = hsptl_uuid + '/';
    }
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/appointment_slots/${uuid}/booking/initiate/${hospital_uuid}`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  initiateBookingForPatient(patient_uuid, uuid, transaction_type, consultation_type, current_user_uuid, hospital_uuid, doctor_uuid, data) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/user/${patient_uuid}/appointment_slots/booking/${transaction_type}/${consultation_type}/${current_user_uuid}/${hospital_uuid}/initiate/${doctor_uuid}/${uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }




  completeBooking(uuid, data) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/appointment_slots/${uuid}/booking/complete/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  completeBookingForPatient(patient_uuid, current_user_uuid, transaction_type, consultation_type, uuid, data) {
    return this.httpClient
      // .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/user/${patient_uuid}/appointment_slots/${current_user_uuid}/booking/${transaction_type}/${consultation_type}/complete/${uuid}/${patient_type}/`, data)
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/user/${patient_uuid}/appointment_slots/${current_user_uuid}/booking/${transaction_type}/${consultation_type}/complete/${uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  instantRequest(data) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/instant_requests/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  instantRequestForPatient(patient_uuid, doctor_uuid, data) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital_instant_requests/${doctor_uuid}/${patient_uuid}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getConsultNowStatus(id) {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/instant_requests/${id}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }

  updateConsultNowStatus(id, data) {
    return this.httpClient
      .patch(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/instant_requests/${id}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  consultNowinitiateBooking(uuid, data) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/instant_requests/${uuid}/booking/initiate/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  consultNowinitiateBookingForPatient(uuid, patient_uuid, transaction_type, data) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital_instant_requests/${uuid}/booking/${patient_uuid}/initiate/${transaction_type}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  consultNowZeroBooking(uuid, data) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/instant_requests/${uuid}/booking_zero_fees/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  completeConsultNow(uuid, data) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/instant_requests/${uuid}/booking/complete/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  completeConsultNowForPatient(uuid, current_user_uuid, patient_uuid, transaction_type, data) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital_instant_requests/${current_user_uuid}/${uuid}/booking/${patient_uuid}/complete/${transaction_type}/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  setActiveLink(data) {
    this.activeTab = data;
  }

  getActiveLink() {
    return this.activeTab;
  }

  setPicture(data) {
    this.picture = data;
  }

  public getDrPicture(): string {
    return this.picture;
  }
  setUserName(data) {
    this.userName = data;
  }

  public getUserName(): string {
    return this.userName;
  }
  followUpAppointment(slotId, parentConsultationUuid, formData) {
    const data = (formData && formData != undefined) ? formData : {};
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/appointment_slots/${slotId}/booking/p_c/${parentConsultationUuid}/fu/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  followUpAppointmentForPatient(slotId, parentConsultationUuid, patient_uuid, formData) {
    const data = (formData && formData != undefined) ? formData : {};
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/hospital_appointment_slots/${slotId}/booking/${patient_uuid}/${parentConsultationUuid}/fu/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  followUpInstantAppointment(parentConsultationUuid, requestUuid, formData) {
    const data = (formData && formData != undefined) ? formData : {};
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/instant_requests/${requestUuid}/booking/p_c/${parentConsultationUuid}/fu/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  searListBypage(data, page, query, totalPage) {
    this.searchList.push({ results: data, page_number: page, query_string: query, total_pages: totalPage });

  }
  patientsearListBypage(data, page, count, totalPage) {
    this.patientSearchList.push({ results: data, page_number: page, count: count, total_pages: totalPage });

  }

  getSearListBypage() {
    return this.searchList;
  }

  getpatientSearListBypage() {
    return this.patientSearchList;
  }

  removeSearchList() {
    this.searchList = [];
  }
  searchAppointmentByPage(data, page, query, totalPage) {
    this.appointmentList.push({ results: data, page_number: page, query_string: query, total_pages: totalPage });

  }
  getsearchAppointmentByPage() {
    return this.appointmentList;
  }
  removeAppointmentByPage() {
    this.appointmentList = []
  }

  createWebsocketStream() {
    const tokenObj = localStorage.getItem('currentUser');
    // console.log(JSON.parse(tokenObj)['access']);
    const accessToken = JSON.parse(tokenObj)['access'];
    const ws_url = `${Settings.WEBSOCKET_BASE_URL}/ws?` + "username=" + accessToken;
    let ws = new WebSocket(ws_url);
    ws.onopen = function (e) {
      // if(e['data']['message_type']="Instant Appointment Available"){
      //   // this.doctorInstantAppointment.emit(true);
      // }
    };
    return
  }

  freeBooking(uuid, data) {
    return this.httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/appointment_slots/${uuid}/booking_zero_fees/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorList(hspId) {
    let param = hspId ? ('?hospital_id=' + hspId) : '';
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/doctors/list/${param}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDoctorListByDept(som:string,dept:any,hspId?:any) {
    let param='';
    if(som&&som!=undefined&&som!=null&&dept&&dept!=undefined&&dept!=null){
      param='?system_of_medicine_queue='+som +'&specialty_queue='+dept;
    }
    if(hspId&&hspId!=undefined&&hspId!=null){
      param=param+'&hospital_uuid='+ hspId;
    }
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/doctors/search/${param}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getPatientList(hspId) {
    let param = hspId ? ('?hospital_id=' + hspId) : '';
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/patients/list/${param}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getHospitalList() {
    return this.httpClient.get(`${Settings.API_DOCTOR_URL_PREFIX}/api/h/hospitals/list/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  reScheduledAppointmentForPatient(slotId, parentConsultationUuid, value) {
    const data = (value && value != undefined) ? value : {};
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/doctor/appointment_slots/${slotId}/booking/da_p/${parentConsultationUuid}/re/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  setReferral(data: any) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/c/consultations/patient_referrals/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getReferral(uuid: string, idType: string) {
    let data = {};
    if (!!uuid) { (idType == 'doctor') ? data['doctor_id'] = uuid : data['hospital_uuid'] = uuid; }
    const params = new HttpParams({ fromObject: data });
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/c/consultations/patient_referrals/`, { 'params': params })
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsultationReport(data: any) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/c/consultations/consultation_report/${data}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  shareConsultationHistory(data) {
    return this
      .httpClient
      .post(`${Settings.API_DOCTOR_URL_PREFIX}/api/c/consultations/consultation_share/`, data)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getsharedConsultationHistory(consltId: string, doctorId: string, patientId: string) {
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/c/consultations/consultation_share/?current_consultation_uuid=${consltId}&doctor_uuid=${doctorId}&patient_uuid=${patientId}`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getPatientConsultationHistory(patient_id: string, consltId: string) {
    return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/c/consultations/data/?patient_uuid=${patient_id}&current_consultation_uuid=${consltId}&fulfilment_status=Suspended%2BCompleted`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsultationExcelReport(data: any) {
    let headers = new HttpHeaders();
    headers = headers.set('content-type', 'application/json');
    return this
      .httpClient
      .get(`${Settings.API_DOCTOR_URL_PREFIX}/api/c/consultations/consultation_report/${data}`, { headers: headers, observe: 'response', responseType: 'text' as 'json' })
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  get individualLogin$() {
    return this.individualLogin.asObservable();
  }

  updateIndividualLogin(newData: any) {
    this.individualLogin.next(newData);
  }

  downloadPdf(result: Blob,data: string,id:string) {
    const downloadUrl = window.URL.createObjectURL(result);
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = data+'-'+id;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(downloadUrl);
}

}
