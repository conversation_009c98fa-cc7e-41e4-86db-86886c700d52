/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .example {
    background: blue;
  }
  .icon-size {
    font-size: 14px !important;
  }
  .h2, h2 {
    font-size: 1 rem !important;
  }
  .h6,
  h6 {
    font-size: 12px !important;
  }
  .consult_data_height_doctor {
    height: 480px;
  }
  .consult_data_height_patient {
    height: 485px;
  }
  .videoAndData {
    height: 280px;
  }
  .videoAndData_content {
    height: 300px;
    overflow-y: auto;
  }
  .videoAndData_pane {
    height: 280px;
    overflow-y: auto;
  }
  .onlyData_content {
    height: 280px;
  }
  .onlyData_pane {
    height: 250px;
    overflow-y: auto;
  }
  .prescrptionData_pane_Patient {
    height: 220px;
    overflow-y: auto;
  }
  .prescrptionData_pane_Doctor {
    height: 220px;
    overflow-y: auto;
  }
  table {
    font-size: 12px !important;
  }
  .investigation_videoAndData {
    font-size: 12px;
    font-weight: 600;
  }
  .investigation_onlyData {
    font-size: 12px;
    font-weight: 600;
  }
  h4 {
    font-size: 1rem;
  }
  .onlyData_Btn_list {
    margin-top: 2rem !important;
  }
  .btn-lg {
    font-size: small;
  }
  .remove-td {
    width: 350px !important;
  }
  .add-width {
    width: 200px !important;
  }
  .dose-width {
    width: 150px !important;
  }
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .example {
    background: orange;
  }
  .h6,
  h6 {
    font-size: 14px !important;
  }
  .consult_data_height_doctor {
    height: 650px;
  }
  .consult_data_height_patient {
    height: 685px;
  }
  h4 {
    font-size: 1.5rem;
  }
  .videoAndData_content {
    height: 360px;
    overflow-y: auto;
  }
  .videoAndData_pane {
    height: 330px;
  }
  .onlyData_content {
    height: 380px;
  }
  .onlyData_pane {
    height: 350px;
    color: #000000;
  }
  .btn-lg {
    font-size: large;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .example {
    background: pink;
  }
  .consult {
    top: 50%;
    left: 50%;
  }
  p {
    font-family: Lato;
  }
  .hide {
    display: none;
  }
  .h6,
  h6 {
    font-size: 14px !important;
  }

  /* Styles for tab labels */

  .mat-tab-label {
    min-width: 25px !important;
    padding: 5px;
    background-color: transparent;
    color: blue;
    font-weight: 400;
  }

  /* Styles for the active tab label */

  .mat-tab-label.mat-tab-label-active {
    min-width: 25px !important;
    padding: 5px;
    background-color: transparent;
    color: blue;
    font-weight: 600;
  }

  /* Styles for the ink bar */

  .mat-ink-bar {
    background-color: green;
  }

  .resize {
    resize: both;
    overflow: auto;
  }

  .pointer {
    cursor: pointer;
  }

  .med {
    margin: 5px;
  }

  .chk-bx {
    text-align: center;
    margin-left: 20px;
    vertical-align: bottom;
  }

  .days {
    /* width: 60px; */
  }

  .row.pres {
    margin-bottom: 5px;
  }

  .prescrip {
    color: #20c0f3 !important;
  }

  .add-pres {
    cursor: pointer;
    margin: 5px;
  }

  .sv-presc {
    float: right;
    margin: 10px;
    margin-right: 65px;
  }

  .mt3 {
    margin-left: 5px;
  }

  .fa-trash-alt {
    color: red !important;
    cursor: pointer;
  }

  #del0 {
    margin-top: 48px !important;
  }

  .btb {
    height: 80px;
    padding: 11px !important;
  }

  .invest-col {
    padding-left: 45px;
  }

  .active-prescription {
    background: #ff4877 !important;
    border: 1px solid #ff4877 !important;
  }

  #suspend-reason {
    width: 100%;
    height: 100px;
  }

  .vidOnly {
    position: relative;
    left: 10%;
    height: 650px !important;
    width: 50%;
    overflow: hidden;
  }

  .join-btn {
    margin-top: 300px !important;
  }

  /* .nav-link.c-tab {
    min-height: 80px !important;
} */

  .nav-link h6 {
    margin-top: 13px;
    margin-bottom: 6px;
  }

  .btb {
    height: 100px;
    padding: 12px !important;
    vertical-align: center;
  }

  .card.left-pane {
    background-color: #ffffff;
    border: solid 1px #f2f2f2;
  }

  .left-pane p {
    text-align: center;
    /* margin-bottom: 3px; */
  }

  .doc-profile-image {
    padding-bottom: 90px;
    padding-right: 10px;
    padding-left: 10px;
  }

  .fas {
    cursor: pointer;
  }

  p.doc-location {
    color: #20c0f3;
  }

  .icon-size {
    font-size: 30px !important;
  }


  .dose-width {
    width: 150px !important;
  }
  #suspendModal {
    z-index: 999999 !important;
  }

  .no-spin::-webkit-inner-spin-button,
  .no-spin::-webkit-outer-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
  }

  .no-spin {
    -moz-appearance: textfield !important;
  }
  /* new css */
  .consult_data_height_doctor {
    height: 650px;
  }
  .consult_data_height_patient {
    height: 685px;
  }
  h4 {
    font-size: 1.5rem;
  }
  .videoAndData_content{
    height: 430px ;
    overflow-y: auto;
  }
  .videoAndData_pane{
    height: 390px ;
  }
  .btn-lg {
    font-size: large;
  }
}

