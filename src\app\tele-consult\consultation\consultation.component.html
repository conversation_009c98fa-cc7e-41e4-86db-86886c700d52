<div *ngIf="loading">
    <app-loading-spinner></app-loading-spinner>
</div>
<div *ngIf="!loading" class="">
    <div [class]="{'content':userType=='Doctor'}">
        <div class="container-fluid">
            <div class="row col-md-8" *ngIf="userType=='Doctor'">
                <h3 class="mb-4 ms text-info"><i class="fas fa-chevron-circle-left" id="openDoctorDashboard"
                        (click)="openDoctorDashboard()"></i> Consultation</h3>
            </div>
            <div *ngIf="showHeaderLinks">
                <div *ngIf="!detailView" class="row col-md-12 text-center mb-4" style="margin-left: 5%;">
                    <a href="javascript:void(0);" class="active d-inline ml-4 mr-4 text-primary" id="dataAndVideo"
                        (click)=dataAndVideo()>
                        <h4 class="text-primary" *ngIf="videoAndData"><u>Video and Consultation Data</u></h4>
                        <h4 class="text-primary" *ngIf="!videoAndData">Video and Consultation Data</h4>
                    </a>
                    <a href="javascript:void(0);" class="d-inline ml-4 mr-4 text-primary" id="videoOnly"
                        (click)=videoOnly()>
                        <h4 class="text-primary" *ngIf="onlyVideo"><u> Video Only</u></h4>
                        <h4 class="text-primary" *ngIf="!onlyVideo">Video Only</h4>
                    </a>
                    <a href="javascript:void(0);" class="d-inline ml-4 text-primary" id="consultation-data-only"
                        (click)=dataOnly()>
                        <h4 class="text-primary" *ngIf="onlyData"><u> Consultation Data Only </u></h4>
                        <h4 class="text-primary" *ngIf="!onlyData">Consultation Data Only</h4>
                    </a>
                </div>
            </div>
            <div *ngIf="detailView &&userType=='Doctor'" class="row col-md-12 text-center mb-4"
                style="margin-left: 30%;">
                <a href="javascript:void(0);" class="d-inline ml-4 text-primary" (click)=dataOnly()>
                    <h4 class="text-primary"><u> Consultation Data </u></h4>
                    <!-- <h4 class="text-primary" *ngIf="!onlyData">Consultation Data </h4> -->
                </a>
            </div>
            <div *ngIf="!detailView &&userType=='Doctor'" class="row col-md-12 text-center mb-4"
                style="margin-left: 30%;">
                <a class="btn btn-primary btn-lg ml-5 " style="color:white" (click)="viewPrescription()">
                    Refresh
                </a>
            </div>
            <hr *ngIf="userType=='Doctor'">
            <div class="row" *ngIf="videoAndData&&userType=='Doctor'">
                <!-- <div class="col-md-4 offset-md-4 text-center form-group">
                    <h6>Video size</h6>
                    <button class="btn btn-info btn-sm mx-2" (click)="changeVideoSize('4')">2:2</button>
                    <button class="btn btn-info btn-sm mx-2" (click)="changeVideoSize('3')">2:3</button>
                    <button class="btn btn-info btn-sm mx-2" (click)="changeVideoSize('2')">1:3</button>
                    <button class="btn btn-info btn-sm mx-2" (click)="changeVideoSize('1')">1:4</button>
                </div> -->
            </div>

            <form [formGroup]="consultForm" (ngSubmit)="onFormSubmit()">
                <div class="row mt-4">

                    <div class="column card resize" *ngIf="!detailView" [ngClass]="{'col-md-5':(videoAndData&&videoSize==0),'col-md-4':(videoAndData&&videoSize==3),
                    'col-md-3':(videoAndData&&videoSize==2),'col-md-2':(videoAndData&&videoSize==1), 'col-md-0':onlyData,
                     'vidOnly':onlyVideo}"
                        [ngStyle]="{'width':onlyVideo ? '75%' : '750px','height':onlyData? '0px': '670px','background-color':onlyData?'white': '#99bee8', 'display':onlyData?'none': 'block' }">
                        <!-- <div class="column card bg-secondary resize" *ngIf="videoAndData || onlyVideo" style="width: 800px; height: 700px;" > -->
                        <button class="btn btn-secondary btn-lg" [ngClass]="{'join-btn':onlyVideo}" (click)=joinVideo()
                            *ngIf="!joinedVideo"
                            [ngStyle]=" videoSize==0 ?{'margin': '36%'}: videoSize==3? {'margin': '36%'}:videoSize==2? {'margin': '30%'}:videoSize==1? {'margin': '20%'}:''">Join
                            Video</button>
                        <app-consulting-video [consultationId]="consultationId" [participantName]="name"
                            *ngIf="joinedVideo" [closeVideoSession]="closeVideoSession"
                            (showJoinButton)="showJoinVideoBtn($event)"></app-consulting-video>

                    </div>
                    <!-- <div class="col-md-3 col-lg-3  col-xl-3" *ngIf="userType=='Doctor'&&!onlyVideo&&!onlyData">
                      <div class=" left-pane" >
                        <div class="card body">
                          <div class="doc-profile-data" >
                            <div class="spinner-border" role="status" *ngIf="!showProfilePic" style="margin: 10px 0px 0px 140px;">
                              <span class="sr-only">Loading...</span>
                            </div>
                              <img [src]="profilePicture" class="img-fluid" alt="User Image" *ngIf="showProfilePic"  style="cursor: pointer;">
                              <p class="doc-name first-name"  style="cursor: pointer;">{{personalInfo['username']}}</p>
                              <p class="doc-name" >Age {{personalInfo['age']}}</p>
                              <p class="doc-name" >Gender{{personalInfo['gender']}}</p>

                           </div>
                          </div>
                      </div>

                  </div> -->


                    <div [ngClass]="{'col-md-12':patientVideoAndDataShow,'col-md-12':onlyData,'col-md-7':userType=='Doctor'}"
                        *ngIf="(videoAndData||patientVideoAndDataShow) || onlyData">
                        <div
                            [ngClass]="{'consult_data_height_doctor':userType=='Doctor','consult_video_data_height_patient': (userType=='Patient'&&patientVideoAndDataShow),'consult_data_only_height_patient': (userType=='Patient'&&!patientVideoAndDataShow)}">
                            <div class="d-flex flex-column">
                                <div class="col-sm-12 col-md-12 col-lg-12">
                                    <!-- <div class="card"> -->
                                    <div>
                                        <div class="profile-box">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="card border-0 bg-light schedule-widget mb-0">
                                                        <div class="schedule-header">
                                                            <div class="schedule-nav">
                                                                <ul class="nav nav-tabs nav-justified">
                                                                    <li class="nav-item" *ngIf="!!showAllTabes">
                                                                        <a class="nav-link c-tab active  pt-4 pb-2 btb"
                                                                            data-toggle="tab" href="#slot_medical">
                                                                            <h6 class="tab-title">
                                                                                <i
                                                                                    class="icon-size fas fa-book-medical"></i><br />
                                                                                Medical History
                                                                            </h6>
                                                                        </a>
                                                                    </li>
                                                                    <li class="nav-item" *ngIf="!!showAllTabes">
                                                                        <a class="nav-link c-tab  pt-4 pb-4 btb"
                                                                            data-toggle="tab" href="#slot_vitals">
                                                                            <h6 class="tab-title"><i
                                                                                    class=" icon-size fa fa-heartbeat "></i><br />Vitals
                                                                            </h6>
                                                                        </a>
                                                                    </li>
                                                                    <li class="nav-item" *ngIf="!!showAllTabes">
                                                                        <a class="nav-link c-tab pt-4 pb-2 btb"
                                                                            data-toggle="tab" href="#slot_physicalexam">
                                                                            <h6 class="tab-title"><i
                                                                                    class=" icon-size fas fa-user-md"></i><br />
                                                                                Physical Exam</h6>
                                                                        </a>
                                                                    </li>
                                                                    <li class="nav-item" *ngIf="!!showAllTabes">
                                                                        <a class="nav-link c-tab pt-4 pb-4 btb"
                                                                            data-toggle="tab" href="#slot_systemicexam">
                                                                            <h6 class="tab-title"><i
                                                                                    class=" icon-size fas fa-assistive-listening-systems"></i><br />Systemic
                                                                                Exam</h6>
                                                                        </a>
                                                                    </li>
                                                                    <li class="nav-item" *ngIf="!!showAllTabes">
                                                                        <a class="nav-link c-tab pt-3 pb-3 btb"
                                                                            data-toggle="tab" href="#slot_diagnosis">
                                                                            <h6 class="tab-title"><i
                                                                                    class=" icon-size fas fa-diagnoses"></i><br />
                                                                                Diagnosis</h6>
                                                                        </a>
                                                                    </li>
                                                                    <li class="nav-item">
                                                                        <a class="nav-link pt-3 pb-3 btb"
                                                                            data-toggle="tab"
                                                                            href="#slot_investigation">
                                                                            <h6 class="tab-title"><i
                                                                                    class="icon-size fas fa-comment-medical"></i><br />Investigation
                                                                            </h6>
                                                                        </a>
                                                                    </li>
                                                                    <li class="nav-item">
                                                                        <a class="nav-link pt-3 pb-3 btb"
                                                                            data-toggle="tab"
                                                                            href="#slot_prescription_data">
                                                                            <h6 class="tab-title">
                                                                                <i
                                                                                    class="icon-size fas fa-pills"></i><br />Prescription
                                                                            </h6>
                                                                        </a>
                                                                    </li>

                                                                    <li class="nav-item">
                                                                        <a class="nav-link c-tab  pt-4 pb-4 btb"
                                                                            data-toggle="tab" href="#slot_report">
                                                                            <h6 class="tab-title">
                                                                                <i
                                                                                    class="icon-size fas fa-file-medical"></i><br />Reports
                                                                            </h6>
                                                                        </a>
                                                                    </li>
                                                                    <li class="nav-item" *ngIf="userType=='Doctor'">
                                                                        <a class="nav-link c-tab  pt-4 pb-4 btb"
                                                                            data-toggle="tab" href="#private_notes">
                                                                            <h6 class="tab-title">
                                                                                <i
                                                                                    class="icon-size fas fa-file-medical"></i><br />Private
                                                                                Notes
                                                                            </h6>
                                                                        </a>
                                                                    </li>
                                                                    <li (click)="getRecording()" class="nav-item"
                                                                        *ngIf="!!showAllTabes">
                                                                        <a class="nav-link c-tab pt-4 pb-4 btb"
                                                                            data-toggle="tab" href="#recording">
                                                                            <h6 class="tab-title"><i
                                                                                    class="icon-size fas fa-video"></i><br />Recording
                                                                            </h6>
                                                                        </a>
                                                                    </li>
                                                                    <li class="nav-item"
                                                                        *ngIf="!!showAllTabes">
                                                                        <a class="nav-link pt-4 pb-4 btb"
                                                                            data-toggle="tab" href="#slot_referal">
                                                                            <h6 class="tab-title"><i
                                                                                    class="icon-size fas fa-file-alt"></i><br />Refer
                                                                            </h6>
                                                                        </a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="tab-content schedule-cont"
                                                            [ngClass]="{'videoAndData_content':videoAndData, 'onlyData_content':(!videoAndData&&userType=='Doctor'),'onlyData_content_patient':(!videoAndData&&userType=='Patient')}">

                                                            <div id="slot_medical" class="tab-pane fade show active">
                                                                <div class="card border-info pb-2"
                                                                    style="overflow: scroll;"
                                                                    [ngClass]="{'videoAndData_pane':videoAndData, 'onlyData_pane':(!videoAndData&&userType=='Doctor'),'onlyData_pane_patient':(!videoAndData&&userType=='Patient')}">
                                                                    <!-- <h5 class="text-danger text-center mt-4 pl-2">Medical History</h5> -->


                                                                    <table class="mt-3" style="width: 100%;">
                                                                        <tr>
                                                                            <td class="pl-2">
                                                                                Chief Complaint
                                                                                <textarea
                                                                                    formControlName="chief_complaint"
                                                                                    name="chief_complaint"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','chief_complaint', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                History Of Present Illness
                                                                                <textarea
                                                                                    formControlName="history_of_present_illness"
                                                                                    name="history_of_present_illness"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','history_of_present_illness', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>

                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Immunization History
                                                                                <textarea id="immunization_history"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','immunization_history', $event)"
                                                                                    formControlName="immunization_history"
                                                                                    name="immunizationHistory"
                                                                                    class="form-control mb-3"
                                                                                    id="immunHis" cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Past Medical History
                                                                                <textarea id="past_medical_history"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','past_medical_history', $event)"
                                                                                    formControlName="past_medical_history"
                                                                                    name="personalHistory"
                                                                                    class="form-control mb-3"
                                                                                    id="persHis" cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr class="pt-2">
                                                                            <td class="pl-2 mt-2">
                                                                                Appetite
                                                                                <textarea
                                                                                    (focusout)="consultationDataDirectSave('medical_history','appetite', $event)"
                                                                                    formControlName="appetite"
                                                                                    name="appetite"
                                                                                    class="form-control mb-3"
                                                                                    id="appetite" cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Diet
                                                                                <textarea
                                                                                    (focusout)="consultationDataDirectSave('medical_history','diet', $event)"
                                                                                    formControlName="diet" name="diet"
                                                                                    class="form-control mb-3" id="diet"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>

                                                                        <tr class="ml-2 pt-2">
                                                                            <td class="pl-2">
                                                                                Thirst / Water Intake
                                                                                <textarea
                                                                                    (focusout)="consultationDataDirectSave('medical_history','thirst', $event)"
                                                                                    formControlName="thirst"
                                                                                    name="thirst"
                                                                                    class="form-control mb-3"
                                                                                    id="thirst" cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Sleep
                                                                                <textarea formControlName="sleep"
                                                                                    name="sleep"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','sleep', $event)"
                                                                                    class="form-control mb-3" id="sleep"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="pl-2">
                                                                                Social History/ Habits/ Addictions
                                                                                <textarea
                                                                                    formControlName="social_history"
                                                                                    name="habits"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','social_history', $event)"
                                                                                    class="form-control mb-3"
                                                                                    id="habits" cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Smoking
                                                                                <textarea formControlName="smoking"
                                                                                    name="smoking"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','smoking', $event)"
                                                                                    class="form-control mb-3"
                                                                                    id="smoking" cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Alcohol
                                                                                <textarea formControlName="alcohol"
                                                                                    name="alcohol"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','alcohol', $event)"
                                                                                    class="form-control mb-3"
                                                                                    id="alcohol" cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Drugs
                                                                                <textarea formControlName="drugs"
                                                                                    name="drugs"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','drugs', $event)"
                                                                                    class="form-control mb-3" id="drugs"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="pl-2">
                                                                                Sexual History
                                                                                <textarea
                                                                                    formControlName="sexual_history"
                                                                                    name="sexual_history"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','sexual_history', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Other Observation/ Notes
                                                                                <textarea
                                                                                    formControlName="other_observations"
                                                                                    name="other_observations"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','other_observations', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>

                                                                        <div
                                                                            class="d-flex justify-content-center text-success">
                                                                            <tr class="mt-2" *ngIf="userType=='Doctor'">
                                                                                <h5>Female patient </h5>
                                                                                <input type="radio" class="ml-3"
                                                                                    (click)="loadFemData(true)"
                                                                                    id="female-yes"
                                                                                    [checked]="female==true?true:false">
                                                                                <label class="ml-1"
                                                                                    for="gender">Yes</label>
                                                                                <input type="radio" class="ml-3"
                                                                                    (click)="loadFemData(false)"
                                                                                    id="female-no"
                                                                                    [checked]="female==false?true:false">
                                                                                <label class="ml-1"
                                                                                    for="gender">No</label>
                                                                            </tr>

                                                                        </div> -->
                                                                        <tr *ngIf="female">
                                                                            <td class="pl-2">
                                                                                Gynaecological History
                                                                                <textarea
                                                                                    formControlName="gynaecological_history"
                                                                                    name="gynaecological_history"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','gynaecological_history', $event)"
                                                                                    class="form-control mb-3"
                                                                                    id="gynHis" cols="5" rows="1"
                                                                                    style="width: 80%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Age of Menarche
                                                                                <input type="text"
                                                                                    onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                    formControlName="age_of_menarche"
                                                                                    name="age_of_menarche"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','age_of_menarche', $event)"
                                                                                    class="form-control mb-3" id="aom"
                                                                                    style="width: 90%;" maxlength="3">
                                                                            </td>
                                                                        </tr>
                                                                        <tr *ngIf="female">
                                                                            <td class="pl-2">
                                                                                Menstrual History
                                                                                <textarea
                                                                                    formControlName="menstrual_history"
                                                                                    name="menstrual_history"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','menstrual_history', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 80%;"></textarea>

                                                                            </td>
                                                                            <td>
                                                                                Last Menstrual Period
                                                                                <!-- <textarea formControlName="last_menstrual_period" name="last_menstrual_period" (select)="consultationDataDirectSave('medical_history','last_menstrual_period', $event)" class="form-control mb-3" (ngModelChange)="consultationDataDirectSave('medical_history','last_menstrual_period', $event)" id="notes" cols="5" rows="1" style="width: 90%;"></textarea> -->
                                                                                <input id="mensHist"
                                                                                    formControlName="last_menstrual_period"
                                                                                    type="text" onkeydown="return false"
                                                                                    style="caret-color: transparent;width:90%"
                                                                                    class="form-control"
                                                                                    name="last_menstrual_period"
                                                                                    cols="5" rows="1" #dp="bsDatepicker"
                                                                                    placeholder="Select Date"
                                                                                    autocomplete="off" bsDatepicker
                                                                                    [maxDate]="maxDate"
                                                                                    [bsConfig]="{ showWeekNumbers:false,isAnimated: true,dateInputFormat:'DD-MM-YYYY' }"
                                                                                    (ngModelChange)="consultationDataDirectSave('medical_history','last_menstrual_period', $event)"
                                                                                    required>
                                                                            </td>
                                                                        </tr>
                                                                        <tr *ngIf="female">
                                                                            <td class="pl-2">
                                                                                Number Of Pregnancy
                                                                                <input
                                                                                    formControlName="number_of_pregnancy"
                                                                                    onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                    minlength="0" maxlength="1"
                                                                                    name="number_of_pregnancy"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','number_of_pregnancy', $event)"
                                                                                    class="form-control mb-3" id="noFp"
                                                                                    type="text" style="width: 80%;">
                                                                            </td>
                                                                            <td>
                                                                                Gravida
                                                                                <input type="text"
                                                                                    formControlName="gravida"
                                                                                    onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                    name="gravida"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','gravida', $event)"
                                                                                    class="form-control mb-3"
                                                                                    id="gravida" minlength="0"
                                                                                    maxlength="1" style="width: 90%;">
                                                                            </td>
                                                                        </tr>
                                                                        <tr *ngIf="female">
                                                                            <td class="pl-2">
                                                                                Para
                                                                                <input formControlName="para"
                                                                                    type="text" name="para"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','para', $event)"
                                                                                    class="form-control mb-3" id="para"
                                                                                    onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                    minlength="0" maxlength="1"
                                                                                    style="width: 80%;">
                                                                            </td>
                                                                            <td>
                                                                                Abortions
                                                                                <input formControlName="abortions"
                                                                                    type="text" name="abortions"
                                                                                    (focusout)="consultationDataDirectSave('medical_history','abortions', $event)"
                                                                                    class="form-control mb-3"
                                                                                    id="abortions"
                                                                                    onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                    minlength="0" maxlength="1"
                                                                                    style="width: 90%;">
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                            <div id="slot_vitals" class="tab-pane fade "
                                                                *ngIf="!!showAllTabes">
                                                                <div class="card border-info pb-2"
                                                                    [ngClass]="{'videoAndData_pane':videoAndData, 'onlyData_pane':!videoAndData}">
                                                                    <table class="mt-4"
                                                                        *ngIf="videoAndData ||patientVideoAndData"
                                                                        style="width: 100%;">
                                                                        <tr>
                                                                            <td
                                                                                class="pl-2 mt-2 justify-content-center">
                                                                                <div
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fas fa-file-medical-alt"
                                                                                            aria-hidden="true"></i>
                                                                                        Blood Pressure Systolic(mmHg)
                                                                                    </h6>
                                                                                    <input type="text"
                                                                                        class="form-control mr-3 ml-3 "
                                                                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','blood_pressure_systolic',$event)"
                                                                                        pattern="^[1-9]"
                                                                                        formControlName="blood_pressure_systolic"
                                                                                        maxlength="3">
                                                                                </div>
                                                                            </td>

                                                                            <td
                                                                                class="pl-2 mt-2 justify-content-center">
                                                                                <div
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fas fa-file-medical-alt"
                                                                                            aria-hidden="true"></i>
                                                                                        Blood Pressure Diastolic (mmHg)
                                                                                    </h6>
                                                                                    <input type="text"
                                                                                        class="form-control mr-3 ml-3"
                                                                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','blood_pressure_diastolic',$event)"
                                                                                        pattern="^[1-9]"
                                                                                        formControlName="blood_pressure_diastolic"
                                                                                        maxlength="3">
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>

                                                                            <td
                                                                                class="pl-2 mt-2 justify-content-center">
                                                                                <div
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fa fa-thermometer-empty"
                                                                                            aria-hidden="true"></i>
                                                                                        Temperature (Fahrenheit) </h6>
                                                                                    <input
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','temperature',$event)"
                                                                                        class=" ml-3 form-control mr-3"
                                                                                        formControlName="temperature"
                                                                                        type="text" maxlength="5">
                                                                                </div>
                                                                            </td>
                                                                            <td
                                                                                class="pl-2 mt-2 justify-content-center">
                                                                                <div
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fa fa-heartbeat"
                                                                                            aria-hidden="true"></i>
                                                                                        Pulse Rate (BPM)</h6>
                                                                                    <input
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','pulse_rate',$event)"
                                                                                        class="form-control mr-3 ml-3"
                                                                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                        formControlName="pulse_rate"
                                                                                        type="text" maxlength="3">
                                                                                </div>
                                                                            </td>
                                                                        </tr>

                                                                        <tr>
                                                                            <td
                                                                                class=" pl-2 mt-2 justify-content-center">
                                                                                <div
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fas fa-stethoscope"></i>
                                                                                        Auscultation </h6>

                                                                                    <!-- <input formControlName="auscultation" onkeypress="return event.charCode >= 48 && event.charCode <= 57" type="text" class="form-control mr-3 ml-3" maxlength="2" name="auscultation" (focusout)="consultationDataDirectSave('vital_signs','auscultation',$event)"
                                                                                    id="auscultation"> -->

                                                                                    <input
                                                                                        formControlName="auscultation"
                                                                                        type="text"
                                                                                        class="form-control mr-3 ml-3"
                                                                                        maxlength="50"
                                                                                        name="auscultation"
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','auscultation',$event)"
                                                                                        id="auscultation">
                                                                                </div>
                                                                            </td>
                                                                            <td
                                                                                class=" pl-2 ml-2 justify-content-center">
                                                                                <div
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fas fa-wave-square"></i>
                                                                                        ECG </h6>
                                                                                    <input formControlName="ecg"
                                                                                        name="ecg" type="text"
                                                                                        class="form-control mr-3 ml-3"
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','ecg',$event)"
                                                                                        id="ecg">
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>

                                                                            <td class="mt-1">
                                                                                <div
                                                                                    style="margin-left: 20px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fas fa-file-medical-alt"></i>
                                                                                        SPO2 %</h6>
                                                                                    <input
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','spo2',$event)"
                                                                                        pattern="^[1-9]"
                                                                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                        formControlName="spo2"
                                                                                        type="text"
                                                                                        class="form-control mr-3 ml-3"
                                                                                        maxlength="3">
                                                                                </div>
                                                                            </td>
                                                                            <td
                                                                                class="ml-4 mt-1 justify-content-center">
                                                                                <div
                                                                                    style="margin-left: 20px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size far fa-sticky-note"></i>
                                                                                        Additional Notes </h6>
                                                                                    <textarea
                                                                                        formControlName="additional_notes"
                                                                                        name="notes"
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','additional_notes',$event)"
                                                                                        class="form-control mr-3 ml-3"
                                                                                        id="notes" cols="20"
                                                                                        rows="1"></textarea>
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                    <table class="mt-4 mb-4" style="width: 100%;"
                                                                        *ngIf="!videoAndData && !patientVideoAndData ">
                                                                        <tr>
                                                                            <td
                                                                                class="pl-2 mt-2 justify-content-center">
                                                                                <div class="mt-4 pl-2"
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fas fa-file-medical-alt"></i>
                                                                                        Blood Pressure Systolic(mmHg)
                                                                                    </h6>
                                                                                    <input id="blood_pressure_systolic"
                                                                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','blood_pressure_systolic',$event)"
                                                                                        class=" form-control ml-3"
                                                                                        pattern="^[a-zA-Z1-9]"
                                                                                        formControlName="blood_pressure_systolic"
                                                                                        type="text" maxlength="3">

                                                                                </div>
                                                                            </td>

                                                                            <td
                                                                                class="pl-2 mt-2 justify-content-center">
                                                                                <div class="mt-4 pl-2"
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fas fa-file-medical-alt"></i>
                                                                                        Blood Pressure Diastolic (mmHg)
                                                                                    </h6>
                                                                                    <input id="blood_pressure_diastolic"
                                                                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','blood_pressure_diastolic',$event)"
                                                                                        class="form-control ml-3"
                                                                                        pattern="^[a-zA-Z0-9]"
                                                                                        formControlName="blood_pressure_diastolic"
                                                                                        type="text" maxlength="3">

                                                                                </div>
                                                                            </td>
                                                                            <td
                                                                                class="pl-2 mt-2 justify-content-center">
                                                                                <div class="mt-4 pl-2"
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class=" icon-size fa fa-thermometer-empty"
                                                                                            aria-hidden="true"></i>
                                                                                        Temperature (Fahrenheit) </h6>
                                                                                    <input
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','temperature',$event)"
                                                                                        class=" form-control ml-3"
                                                                                        formControlName="temperature"
                                                                                        type="text" maxlength="5"
                                                                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57">

                                                                                </div>
                                                                            </td>
                                                                            <td
                                                                                class="pl-2 mt-2 justify-content-center">
                                                                                <div class="mt-4 pl-2"
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fa fa-heartbeat"
                                                                                            aria-hidden="true"></i>
                                                                                        Pulse Rate(BPM) </h6>
                                                                                    <input
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','pulse_rate',$event)"
                                                                                        class=" form-control ml-3"
                                                                                        formControlName="pulse_rate"
                                                                                        type="number" maxlength="4"
                                                                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57">

                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="ml-2 justify-content-center">
                                                                                <div class="mt-4 pl-4"
                                                                                    style="margin-left: 10px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fas fa-stethoscope"></i>
                                                                                        Auscultation </h6>
                                                                                    <input
                                                                                        formControlName="auscultation"
                                                                                        type="text"
                                                                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                        maxlength="4"
                                                                                        name="auscultation"
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','auscultation',$event)"
                                                                                        class="form-control" id="notes"
                                                                                        cols="5" rows="1"
                                                                                        style="width: 100%;">
                                                                                </div>
                                                                            </td>
                                                                            <td>
                                                                                <div class="mt-4 pl-4"
                                                                                    style="margin-left: 20px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class=" icon-size fas fa-wave-square"></i>
                                                                                        ECG </h6>
                                                                                    <input formControlName="ecg"
                                                                                        name="ecg" type="text"
                                                                                        pattern="^[a-zA-Z0-9]"
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','ecg',$event)"
                                                                                        class="form-control" id="notes"
                                                                                        cols="5" rows="1"
                                                                                        style="width: 100%;">
                                                                                </div>
                                                                            </td>

                                                                            <td class=" mt-2">
                                                                                <div class="mt-4 pl-4"
                                                                                    style="margin-left: 20px;margin-right: 20px;">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size fas fa-file-medical-alt"></i>
                                                                                        SPO2 %</h6>
                                                                                    <input
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','spo2',$event)"
                                                                                        class="form-control ml-3"
                                                                                        pattern="^[1-9]"
                                                                                        formControlName="spo2"
                                                                                        type="text"
                                                                                        onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                                                                        maxlength="3">
                                                                                </div>
                                                                            </td>
                                                                            <td class=" justify-content-center">
                                                                                <div class="mt-4 pl-4 mr-2">
                                                                                    <h6 class="ml-3"><i
                                                                                            class="icon-size far fa-sticky-note"></i>
                                                                                        Additional Notes </h6>
                                                                                    <textarea
                                                                                        formControlName="additional_notes"
                                                                                        name="notes"
                                                                                        (focusout)="consultationDataDirectSave('vital_signs','additional_notes',$event)"
                                                                                        class="form-control mr-3"
                                                                                        id="notes" cols="20"
                                                                                        rows="1"></textarea>
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                            </div>

                                                            <div id="slot_physicalexam" class="tab-pane fade">
                                                                <div class="card border-info pb-2"
                                                                    [ngClass]="{'videoAndData_pane':videoAndData, 'onlyData_pane':!videoAndData}"
                                                                    style="overflow: scroll;">
                                                                    <table class="pt-2 pb-2 mt-4" style="width: 100%;">
                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Weight (kg)
                                                                                <input formControlName="weight"
                                                                                    id="weight" type="text"
                                                                                    maxlength="5"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','weight', $event)"
                                                                                    style="width:30%"
                                                                                    onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode==46">
                                                                            </td>
                                                                            <td>
                                                                                Height (cm)
                                                                                <input formControlName="height"
                                                                                    id="height" type="text"
                                                                                    maxlength="6"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','height', $event)"
                                                                                    style="width:30%"
                                                                                    onkeypress="return (event.charCode >= 48 && event.charCode <= 57)  ||event.charCode==46">
                                                                            </td>
                                                                            <td>
                                                                                BMI
                                                                                <input formControlName="bmi" type="text"
                                                                                    maxlength="5"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','bmi', $event)"
                                                                                    style="width:30%">
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                    <table class="mt-3" style="width: 100%;">
                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Nutrition
                                                                                <textarea formControlName="nutrition"
                                                                                    name="nutrition"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','nutrition', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td class="mt-2">
                                                                                Nail Changes
                                                                                <textarea formControlName="nail_changes"
                                                                                    name="nail_changes"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','nail_changes', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>

                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Clubbing of Fingers
                                                                                <textarea
                                                                                    formControlName="clubbing_of_fingers"
                                                                                    name="clubbingOfFingers"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','clubbing_of_fingers', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Cyanosis
                                                                                <textarea formControlName="cyanosis"
                                                                                    name="cyanosis"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','cyanosis', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Icterus/Jaundice
                                                                                <textarea
                                                                                    formControlName="icterus_jaundice"
                                                                                    name="icterusJaundice"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','icterus_jaundice', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Pallor
                                                                                <textarea formControlName="pallor"
                                                                                    name="pallor"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','pallor', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Lymph Nodes
                                                                                <textarea formControlName="lymph_nodes"
                                                                                    name="lymphNodes"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','lymph_nodes', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Oedema
                                                                                <textarea formControlName="oedema"
                                                                                    name="oedema"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','oedema', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Sclera
                                                                                <textarea formControlName="sclera"
                                                                                    name="sclera"
                                                                                    (focusout)="consultationDataDirectSave('physical_examination','sclera', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <!-- <td>
                                                                          Other Observations
                                                                          <textarea formControlName="otherObservations" name="otherObservations" (focusout)="consultationDataDirectSave('physical_examination','other_observations', $event)" class="form-control mb-3" id="notes" cols="5" rows="1" style="width: 85%;"></textarea>                                                                                    </td>
                                                                  </tr> -->
                                                                    </table>
                                                                </div>
                                                            </div>

                                                            <div id="slot_systemicexam" class="tab-pane fade">
                                                                <div class="card border-info pb-2"
                                                                    [ngClass]="{'videoAndData_pane':videoAndData, 'onlyData_pane':!videoAndData}"
                                                                    style="overflow: scroll;">
                                                                    <table class="mt-4" style="width: 100%;">
                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Respiratory System
                                                                                <textarea id="respiratory_system"
                                                                                    formControlName="respiratory_system"
                                                                                    name="respiratory_system"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','respiratory_system', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                GastroIntestinal/Abdomen
                                                                                <textarea id="gastro_intestinal_system"
                                                                                    formControlName="gastro_intestinal_system"
                                                                                    name="gastro_intestinal_system"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','gastro_intestinal_system', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr class="pt-2">
                                                                            <td class="pl-2 mt-2">
                                                                                CardioVascular System
                                                                                <textarea
                                                                                    formControlName="cardio_vascular_system"
                                                                                    name="cardio_vascular_system"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','cardio_vascular_system', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Genito Urinary System
                                                                                <textarea
                                                                                    formControlName="genitourinary_system"
                                                                                    name="genitourinary_system"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','genitourinary_system', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>

                                                                        <tr class="ml-2 pt-2">
                                                                            <td class="pl-2">
                                                                                Musculoskeletal System
                                                                                <textarea
                                                                                    formControlName="musculoskeletal_system"
                                                                                    name="musculoskeletal_system"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','musculoskeletal_system', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Central Nervous System
                                                                                <textarea
                                                                                    formControlName="central_nervous_system"
                                                                                    name="central_nervous_system"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','central_nervous_system', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="pl-2">
                                                                                Eye
                                                                                <textarea formControlName="eye"
                                                                                    name="eye"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','eye', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Ear
                                                                                <textarea formControlName="ear"
                                                                                    name="ear"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','ear', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Nose
                                                                                <textarea formControlName="nose"
                                                                                    name="nose"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','nose', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Mouth
                                                                                <textarea formControlName="mouth"
                                                                                    name="mouth"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','mouth', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="pl-2">
                                                                                Throat
                                                                                <textarea formControlName="throat"
                                                                                    name="throat"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','throat', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Neck
                                                                                <textarea formControlName="neck"
                                                                                    name="neck"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','neck', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="pl-2">
                                                                                Skin
                                                                                <textarea formControlName="skin"
                                                                                    name="skin"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','skin', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Psychiatric History
                                                                                <textarea
                                                                                    formControlName="psychiatric_history"
                                                                                    name="psychiatric_history"
                                                                                    (focusout)="consultationDataDirectSave('systemic_examination','psychiatric_history', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                            <div id="slot_diagnosis" class="tab-pane fade">
                                                                <div class="card border-info pb-2"
                                                                    [ngClass]="{'videoAndData_pane':videoAndData, 'onlyData_pane':!videoAndData}"
                                                                    style=" overflow: scroll;">
                                                                    <table class="mt-4" style="width: 100%;">
                                                                        <tr class="ml-2">
                                                                            <td class="pl-2">
                                                                                Primary
                                                                                <textarea id="primary_diagnosis"
                                                                                    formControlName="primary_diagnosis"
                                                                                    name="primary"
                                                                                    (focusout)="consultationDataDirectSave('diagnosis','primary_diagnosis', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 80%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Secondary
                                                                                <textarea id="secondary_diagnosis"
                                                                                    formControlName="secondary_diagnosis"
                                                                                    name="secondary"
                                                                                    (focusout)="consultationDataDirectSave('diagnosis','secondary_diagnosis', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 90%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                        <tr class="pt-2">
                                                                            <td class="pl-2 mt-2">
                                                                                Differential Diagnosis
                                                                                <textarea id="differential_diagnosis"
                                                                                    formControlName="differential_diagnosis"
                                                                                    name="differential_diagnosis"
                                                                                    (focusout)="consultationDataDirectSave('diagnosis','differential_diagnosis', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 80%;"></textarea>
                                                                            </td>
                                                                            <td>
                                                                                Final Diagnosis
                                                                                <textarea
                                                                                    formControlName="final_diagnosis"
                                                                                    name="finalDiagnosis"
                                                                                    (focusout)="consultationDataDirectSave('diagnosis','final_diagnosis', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 90%;"></textarea>
                                                                            </td>
                                                                        </tr>

                                                                        <tr class="ml-2 pt-2">
                                                                            <td class="pl-2">
                                                                                ICD 10 Codes
                                                                                <textarea formControlName="icd_10_codes"
                                                                                    name="ICD10Codes"
                                                                                    name="finalDiagnosis"
                                                                                    (focusout)="consultationDataDirectSave('diagnosis','icd_10_codes', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="5" rows="1"
                                                                                    style="width: 80%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                            </div>

                                                            <div id="slot_investigation" class="tab-pane fade">
                                                                <div class="col-xs-12 text-success">
                                                                    <div class="col-md-12 float-right mb-3"
                                                                        *ngIf="userType=='Patient'">
                                                                        <button
                                                                            class="btn btn-primary btn-sm float-right"
                                                                            id="download-investigation"
                                                                            (click)="downloadInvestigation()">Download</button>

                                                                    </div>

                                                                    <div class="card border-info pb-2"
                                                                        [ngClass]="{'videoAndData_pane':(videoAndData&& userType=='Doctor'), 'onlyData_pane':(!videoAndData&& userType=='Doctor'),'onlyData_pane_patient':(!videoAndData&&userType=='Patient')}"
                                                                        style="overflow-y: auto">

                                                                        <form [formGroup]="investigationForm"
                                                                            [ngClass]="{'investigation_videoAndData':videoAndData,'investigation_onlyData':!videoAndData}">
                                                                            <!-- <table class="mt-4"> -->
                                                                            <!-- <tr class="ml-2 mt-4">
                                                                          <td class="pl-2"> -->
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    HAEMATOLOGY
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select class="haematology"
                                                                                            [items]="haematologyItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="haematology"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            [closeOnSelect]="false"
                                                                                            (change)="updateInvestigation('HAEMATOLOGY',$event)"
                                                                                            (close)="processInvestigation('HAEMATOLOGY')"
                                                                                            (remove)="processInvestigation('HAEMATOLOGY')"
                                                                                            [clearable]="false">
                                                                                            <!--(focusout)="onKey('investigation','tests_prescribed', $event)"-->
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    BIOCHEMISTRY AND IMMUNOASSAYS
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            class="investigation_name"
                                                                                            [items]="biochemistryItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="biochemistryAndImmunoassay"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('BIOCHEMISTRY AND IMMUNOASSAYS',$event)"
                                                                                            (remove)="processInvestigation('BIOCHEMISTRY AND IMMUNOASSAYS')"
                                                                                            (close)="processInvestigation('BIOCHEMISTRY AND IMMUNOASSAYS')"
                                                                                            foc>
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                            </div>
                                                                            <!-- </tr> -->
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    MICROBIOLOGY
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select class="microbiology"
                                                                                            [items]="microbiologyItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="microbiology"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('MICROBIOLOGY',$event)"
                                                                                            (remove)="processInvestigation('MICROBIOLOGY')"
                                                                                            (close)="processInvestigation('MICROBIOLOGY')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    CLINICAL PATHOLOGY
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            class="clinicalPathology"
                                                                                            [items]="clinicalPathologyItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="clinicalPathology"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('CLINICAL PATHOLOGY',$event)"
                                                                                            (remove)="processInvestigation('CLINICAL PATHOLOGY')"
                                                                                            (close)="processInvestigation('CLINICAL PATHOLOGY')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <!-- </td> -->
                                                                            <!-- </tr> -->
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    PATHOLOGY
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select class="pathology"
                                                                                            [items]="pathologyItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="pathology"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('PATHOLOGY',$event)"
                                                                                            (remove)="processInvestigation('PATHOLOGY')"
                                                                                            (close)="processInvestigation('PATHOLOGY')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    SEROLOGY
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select class="serology"
                                                                                            [items]="serologyItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="serology"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('SEROLOGY',$event)"
                                                                                            (remove)="processInvestigation('SEROLOGY')"
                                                                                            (close)="processInvestigation('SEROLOGY')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    MALARIA
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select class="malaria"
                                                                                            [items]="malariaItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="malaria"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('MALARIA',$event)"
                                                                                            (remove)="processInvestigation('MALARIA')"
                                                                                            (close)="processInvestigation('MALARIA')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    FILARIASIS
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select class="filariasis"
                                                                                            [items]="filariasisItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="filariasis"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('FILARIASIS',$event)"
                                                                                            (remove)="processInvestigation('FILARIASIS')"
                                                                                            (close)="processInvestigation('FILARIASIS')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    DENGUE
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select id="dengue"
                                                                                            [items]="dengueItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="dengue"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('DENGUE',$event)"
                                                                                            (remove)="processInvestigation('DENGUE')"
                                                                                            (close)="processInvestigation('DENGUE')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    JAPANESE ENCEPHALITIS
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="japaneseEncephalitisItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="japaneseEncephalitis"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('JAPANESE ENCEPHALITIS',$event)"
                                                                                            (remove)="processInvestigation('JAPANESE ENCEPHALITIS')"
                                                                                            (close)="processInvestigation('JAPANESE ENCEPHALITIS')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    CHIKUNGUNYA
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="chikungunyaItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="chikungunya"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('CHIKUNGUNYA',$event)"
                                                                                            (remove)="processInvestigation('CHIKUNGUNYA')"
                                                                                            (close)="processInvestigation('CHIKUNGUNYA')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    SCRUB TYPHUS
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="scrubTyphusItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="scrubTyphus"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('SCRUB TYPHUS',$event)"
                                                                                            (remove)="processInvestigation('SCRUB TYPHUS')"
                                                                                            (close)="processInvestigation('SCRUB TYPHUS')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    LEPTOSPIROSIS
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="leptospirosisItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="leptospirosis"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('LEPTOSPIROSIS',$event)"
                                                                                            (remove)="processInvestigation('LEPTOSPIROSIS')"
                                                                                            (close)="processInvestigation('LEPTOSPIROSIS')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    BRUCELLOSIS
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="brucellosisItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="brucellosis"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('BRUCELLOSIS',$event)"
                                                                                            (remove)="processInvestigation('BRUCELLOSIS')"
                                                                                            (close)="processInvestigation('BRUCELLOSIS')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    TUBERCULOSIS
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="tuberculosisItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="tuberculosis"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('TUBERCULOSIS',$event)"
                                                                                            (remove)="processInvestigation('TUBERCULOSIS')"
                                                                                            (close)="processInvestigation('TUBERCULOSIS')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    HIV
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select [items]="hivItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="hiv"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('HIV',$event)"
                                                                                            (remove)="processInvestigation('HIV')"
                                                                                            (close)="processInvestigation('HIV')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    HEPATITIS B
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="hepatitisBItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="hepatitisB"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('HEPATITIS B',$event)"
                                                                                            (remove)="processInvestigation('HEPATITIS B')"
                                                                                            (close)="processInvestigation('HEPATITIS B')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    HEPATITIS C
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="hepatitisCItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="hepatitisC"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('HEPATITIS C',$event)"
                                                                                            (remove)="processInvestigation('HEPATITIS C')"
                                                                                            (close)="processInvestigation('HEPATITIS C')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    HEPATITIS A
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="hepatitisAItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="hepatitisA"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('HEPATITIS A',$event)"
                                                                                            (remove)="processInvestigation('HEPATITIS A')"
                                                                                            (close)="processInvestigation('HEPATITIS A')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    HEPATITIS E
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="hepatitisEItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="hepatitisE"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('HEPATITIS E',$event)"
                                                                                            (remove)="processInvestigation('HEPATITIS E')"
                                                                                            (close)="processInvestigation('HEPATITIS E')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    HBC (CORE ANTIBODIES)
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select [items]="hbcItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="hbc"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('HBC (CORE ANTIBODIES)',$event)"
                                                                                            (remove)="processInvestigation('HBC (CORE ANTIBODIES)')"
                                                                                            (close)="processInvestigation('HBC (CORE ANTIBODIES)')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    OTHER DIAGNOSTIC TESTS
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="otherDiagnosticTestsItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="otherDiagnosticTest"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('OTHER DIAGNOSTIC TESTS',$event)"
                                                                                            (remove)="processInvestigation('OTHER DIAGNOSTIC TESTS')"
                                                                                            (close)="processInvestigation('OTHER DIAGNOSTIC TESTS')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row mt-4">
                                                                                <div class="col-md-6 invest-col">
                                                                                    RADIOLOGY & OTHER DIAGNOSTIC TESTS
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">
                                                                                        <ng-select
                                                                                            [items]="radiologyAndOtherDiagnosticTestsItems"
                                                                                            bindLabel="investigation_name"
                                                                                            formControlName="radiologyAndOtherDiagnostics"
                                                                                            bindValue="uuid"
                                                                                            placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                                            appendTo="body"
                                                                                            multiple="true"
                                                                                            [clearable]="false"
                                                                                            [addTag]="true"
                                                                                            (change)="updateInvestigation('RADIOLOGY & OTHER DIAGNOSTIC TESTS',$event)"
                                                                                            (remove)="processInvestigation('RADIOLOGY & OTHER DIAGNOSTIC TESTS')"
                                                                                            (close)="processInvestigation('RADIOLOGY & OTHER DIAGNOSTIC TESTS')">
                                                                                        </ng-select>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- </td> -->
                                                                                <!-- <td> -->
                                                                                <div class="col-md-6">
                                                                                    <div class="text-center mb-2"
                                                                                        style="width: 75%;">

                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class=" row mt-4">
                                                                                <div class="col-md-12 invest-col">
                                                                                    Special Instructions
                                                                                    <div
                                                                                        style="    padding: 12px 28px 0px 0px;">
                                                                                        <textarea class="col-md-11"
                                                                                            type="text"
                                                                                            formControlName="special_instructions"
                                                                                            (input)="getSpecialInstructionValue($event)"
                                                                                            (focusout)="processInvestigation('special_instructions')"></textarea>

                                                                                    </div>

                                                                                </div>




                                                                            </div>
                                                                            <!-- </table> -->
                                                                        </form>
                                                                        <!-- <div>
                                                                          HAEMATOLOGY
                                                                          <div class="text-center mb-2 ml-5 pl-4" style="width: 75%;">
                                                                              <ng-select [items]="items"
                                                                              bindLabel="name"
                                                                              placeholder="{{userType=='Doctor'?'Select item': ' '}}"
                                                                              appendTo="body"
                                                                              multiple="true"
                                                                              >
                                                                              </ng-select>
                                                                          </div>
                                                                      </div> -->
                                                                    </div>
                                                                    <!-- <button class="btn btn-primary float-right" *ngIf="!showAllTabes" (click)="downloadInvestigation()">Download</button> -->

                                                                </div>
                                                            </div>
                                                            <div id="slot_report" class="tab-pane fade">
                                                                <app-medical-report [patientUuid]="patient"
                                                                    [doctorUuid]="doctor"
                                                                    [consultationId]="consultationId"
                                                                    [videoAndData]="videoAndData"
                                                                    [patientVideoAndData]="patientVideoAndData">
                                                                </app-medical-report>
                                                            </div>

                                                            <div id="private_notes" class="tab-pane fade">
                                                                <div class="card border-info pb-2"
                                                                    style="overflow: scroll;"
                                                                    [ngClass]="{'videoAndData_pane':videoAndData, 'onlyData_pane':(!videoAndData&&userType=='Doctor'),'onlyData_pane_patient':(!videoAndData&&userType=='Patient')}">
                                                                    <!-- <h5 class="text-danger text-center mt-4 pl-2">Medical History</h5> -->
                                                                    <table class="mt-3" style="width: 100%;">
                                                                        <tr>
                                                                            <td class="pl-2">
                                                                                Private Notes
                                                                                <textarea
                                                                                    formControlName="private_notes"
                                                                                    name="private_notes"
                                                                                    (focusout)="consultationDataDirectSave('private_notes','private_notes', $event)"
                                                                                    class="form-control mb-3" id="notes"
                                                                                    cols="20" rows="5"
                                                                                    style="width: 85%;"></textarea>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                            <div id="slot_prescription_data" class="tab-pane fade">
                                                                <div class="text-success prescrip">
                                                                    <div class="col-md-12 float-right mb-3">
                                                                        <button
                                                                            class="btn btn-primary btn-sm float-right"
                                                                            [disabled]="disabledDownloadPrescriptionBtn"
                                                                            *ngIf="userType=='Patient'&&showPrescriptionForPatient"
                                                                            (click)="downloadPrescription()">Download</button>
                                                                    </div>
                                                                    <div class="card border-info pb-2">
                                                                        <div class="mt-0">
                                                                            <form [formGroup]="prescriptionForm">
                                                                                <div formArrayName="prescriptionArray">
                                                                                    <ng-container>
                                                                                        <table
                                                                                            class="table table-responsive prescrip"
                                                                                            style="overflow-y: auto;"
                                                                                            [ngClass]="{'prescrptionData_pane_Doctor':userType=='Doctor', 'prescrptionData_pane_Patient':userType=='Patient'}"
                                                                                            *ngIf="!loadingPrescreption">
                                                                                            <thead>
                                                                                                <tr class="text-center">
                                                                                                    <th>Sl.no</th>
                                                                                                    <th>Form<span
                                                                                                            class="text-danger">*</span>
                                                                                                    </th>
                                                                                                    <th>Medicine<span
                                                                                                            class="text-danger">*</span>
                                                                                                    </th>
                                                                                                    <th>Strength<span
                                                                                                            class="text-danger">*</span>
                                                                                                    </th>
                                                                                                    <th>Dose<span
                                                                                                            class="text-danger">*</span>
                                                                                                    </th>
                                                                                                    <th>Morning</th>
                                                                                                    <th>Afternoon</th>
                                                                                                    <th>Evening</th>
                                                                                                    <th>Night</th>
                                                                                                    <th>Instruction<span
                                                                                                            class="text-danger">*</span>
                                                                                                    </th>
                                                                                                    <th>Duration<span
                                                                                                            class="text-danger">*</span>
                                                                                                    </th>
                                                                                                    <th
                                                                                                        *ngIf="userType=='Doctor'">
                                                                                                        Action</th>

                                                                                                </tr>
                                                                                            </thead>
                                                                                            <tbody>
                                                                                                <tr *ngFor="let data of this.prescriptionForm.controls.prescriptionArray.value; let i=index; trackBy:trackFn"
                                                                                                    [formGroupName]="i">
                                                                                                    <td>{{i+1}}</td>
                                                                                                    <td>
                                                                                                        <ng-select
                                                                                                            class="medicine_type"
                                                                                                            *ngIf="userType=='Doctor'"
                                                                                                            [items]="medicineType"
                                                                                                            bindLabel="code"
                                                                                                            bindValue="code"
                                                                                                            [clearable]="false"
                                                                                                            formControlName="medicine_type"
                                                                                                            (focusout)="onPrescription()"
                                                                                                            placeholder="Select item"
                                                                                                            appendTo="body"
                                                                                                            addTagText="Create New"
                                                                                                            [ngClass]="{'remove-td':videoAndData,'add-width':onlyData}">
                                                                                                        </ng-select>
                                                                                                        <input
                                                                                                            type="text"
                                                                                                            *ngIf="userType=='Patient'"
                                                                                                            class="form-control"
                                                                                                            name="medicine_type"
                                                                                                            id="medicine_type{{i}}"
                                                                                                            formControlName="medicine_type"
                                                                                                            disabled />

                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <ng-select
                                                                                                            class="brand_name"
                                                                                                            *ngIf="userType=='Doctor'"
                                                                                                            [items]="drugs"
                                                                                                            bindLabel="name"
                                                                                                            bindValue="name"
                                                                                                            [addTag]="true"
                                                                                                            appendTo="body"
                                                                                                            addTagText="add new"
                                                                                                            [clearable]="false"
                                                                                                            formControlName="brand_name"
                                                                                                            (focusout)="onPrescription()"
                                                                                                            placeholder="Select item"
                                                                                                            [multiple]="true"
                                                                                                            [maxSelectedItems]="1"
                                                                                                            [ngClass]="{'remove-td':videoAndData,'add-width':onlyData}">

                                                                                                        </ng-select>
                                                                                                        <input
                                                                                                            type="text"
                                                                                                            *ngIf="userType=='Patient'"
                                                                                                            class="form-control"
                                                                                                            name="medicine"
                                                                                                            id="medicine{{i}}"
                                                                                                            formControlName="brand_name"
                                                                                                            disabled />

                                                                                                    </td>

                                                                                                    <td><input
                                                                                                            class="form-control"
                                                                                                            formControlName="strength"
                                                                                                            *ngIf="userType=='Patient'"
                                                                                                            disabled>
                                                                                                        <input
                                                                                                            class="form-control"
                                                                                                            formControlName="strength"
                                                                                                            *ngIf="userType=='Doctor'">
                                                                                                    </td>

                                                                                                    <td>
                                                                                                        <ng-select
                                                                                                            class="dosage"
                                                                                                            *ngIf="userType=='Doctor'"
                                                                                                            [items]="dose"
                                                                                                            bindLabel="name"
                                                                                                            bindValue="name"
                                                                                                            formControlName="dosage"
                                                                                                            [clearable]="false"
                                                                                                            (focusout)="onPrescription()"
                                                                                                            placeholder="Select item"
                                                                                                            [ngClass]="{'remove-td':videoAndData,'dose-width':onlyData}"
                                                                                                            (change)="getDoseValues($event,i)">
                                                                                                        </ng-select>
                                                                                                        <input
                                                                                                            type="text"
                                                                                                            *ngIf="userType=='Patient'"
                                                                                                            class="form-control"
                                                                                                            name="dosage"
                                                                                                            id="dosage{{i}}"
                                                                                                            formControlName="dosage"
                                                                                                            disabled />
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <input
                                                                                                            class="form-control"
                                                                                                            *ngIf="userType=='Doctor'"
                                                                                                            formControlName="morning"
                                                                                                            [ngStyle]="videoAndData?{'width':'70px'}:''">
                                                                                                        <input
                                                                                                            class="form-control"
                                                                                                            *ngIf="userType=='Patient'"
                                                                                                            disabled
                                                                                                            formControlName="morning"
                                                                                                            [ngStyle]="videoAndData?{'width':'70px'}:''">
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <input
                                                                                                            class="form-control"
                                                                                                            *ngIf="userType=='Doctor'"
                                                                                                            formControlName="afternoon"
                                                                                                            [ngStyle]="videoAndData?{'width':'70px'}:''">
                                                                                                        <input
                                                                                                            class="form-control"
                                                                                                            *ngIf="userType=='Patient'"
                                                                                                            disabled
                                                                                                            formControlName="afternoon"
                                                                                                            [ngStyle]="videoAndData?{'width':'70px'}:''">
                                                                                                    </td>
                                                                                                    <td><input
                                                                                                            class="form-control"
                                                                                                            *ngIf="userType=='Doctor'"
                                                                                                            formControlName="evening"
                                                                                                            [ngStyle]="videoAndData?{'width':'70px'}:''">
                                                                                                        <input
                                                                                                            class="form-control"
                                                                                                            *ngIf="userType=='Patient'"
                                                                                                            disabled
                                                                                                            formControlName="evening"
                                                                                                            [ngStyle]="videoAndData?{'width':'70px'}:''">
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <input
                                                                                                            class="form-control"
                                                                                                            *ngIf="userType=='Doctor'"
                                                                                                            formControlName="night"
                                                                                                            [ngStyle]="videoAndData?{'width':'70px'}:''">
                                                                                                        <input
                                                                                                            class="form-control"
                                                                                                            *ngIf="userType=='Patient'"
                                                                                                            disabled
                                                                                                            formControlName="night"
                                                                                                            [ngStyle]="videoAndData?{'width':'70px'}:''">
                                                                                                    </td>

                                                                                                    <td><textarea
                                                                                                            type="text"
                                                                                                            *ngIf="userType=='Doctor'"
                                                                                                            name="notes"
                                                                                                            id="notes{{i}}"
                                                                                                            formControlName="notes"
                                                                                                            (focusout)="onPrescription()"
                                                                                                            class="form-control"
                                                                                                            style="height: 20px;width: 200px;"
                                                                                                            maxlength="100"
                                                                                                            autocomplete="off"></textarea>
                                                                                                        <textarea
                                                                                                            type="text"
                                                                                                            *ngIf="userType=='Patient'"
                                                                                                            disabled
                                                                                                            name="notes"
                                                                                                            id="notes{{i}}"
                                                                                                            formControlName="notes"
                                                                                                            (focusout)="onPrescription()"
                                                                                                            class="form-control"
                                                                                                            style="height: 20px;width: 200px;"
                                                                                                            maxlength="100"
                                                                                                            autocomplete="off"></textarea>
                                                                                                    </td>
                                                                                                    <td><input
                                                                                                            class="days form-control"
                                                                                                            type="text"
                                                                                                            *ngIf="userType=='Doctor'"
                                                                                                            type="text"
                                                                                                            name="days"
                                                                                                            id="days{{i}}"
                                                                                                            (focusout)="onPrescription()"
                                                                                                            formControlName="days"
                                                                                                            autocomplete="off"
                                                                                                            min="1">
                                                                                                        <input
                                                                                                            class="days form-control"
                                                                                                            type="text"
                                                                                                            *ngIf="userType=='Patient'"
                                                                                                            disabled
                                                                                                            name="days"
                                                                                                            id="days{{i}}"
                                                                                                            (focusout)="onPrescription()"
                                                                                                            formControlName="days"
                                                                                                            autocomplete="off"
                                                                                                            min="1">
                                                                                                    </td>
                                                                                                    <td> <i class="fa fa-trash-alt"
                                                                                                            (click)="delPrescription(i)"
                                                                                                            *ngIf="(prescriptionForm.valid || prescriptionArray.length>1)&&!detailView"></i>
                                                                                                    </td>

                                                                                                </tr>
                                                                                                <tr
                                                                                                    *ngIf="userType=='Doctor'">
                                                                                                    <td colspan="3"
                                                                                                        *ngIf="!detailView">
                                                                                                        <span>
                                                                                                            <p (click)="createPrescriptionForm(null)"
                                                                                                                class="add-pres">
                                                                                                                <i class="fa fa-plus-circle"
                                                                                                                    aria-hidden="true"></i>&nbsp;Add
                                                                                                                medicine
                                                                                                            </p>
                                                                                                        </span>
                                                                                                    </td>
                                                                                                    <td colspan="3"
                                                                                                        *ngIf="!detailView && isPrescriptionComplete=='false'">
                                                                                                        <span>
                                                                                                            <p (click)="NoPrescriptionForm()"
                                                                                                                class="add-pres">
                                                                                                                <i class="fa fa-ban"
                                                                                                                    aria-hidden="true"></i>&nbsp;No
                                                                                                                Prescription
                                                                                                            </p>
                                                                                                        </span>
                                                                                                    </td>
                                                                                                    <td></td>
                                                                                                    <td></td>
                                                                                                    <td></td>
                                                                                                    <td></td>
                                                                                                    <td></td>

                                                                                                    <td><button
                                                                                                            *ngIf="!detailView  && userType=='Doctor'"
                                                                                                            class="btn btn-primary btn-md sv-presc"
                                                                                                            [disabled]="!prescriptionForm.valid"
                                                                                                            (click)="onPrescriptionComplete()">Complete</button>
                                                                                                    </td>
                                                                                                </tr>

                                                                                            </tbody>
                                                                                        </table>
                                                                                        <div *ngIf="userType=='Doctor'">
                                                                                            <div *ngIf="prescriptionArray.length==0"
                                                                                                class="col-md-12 text-center">
                                                                                                <h6>No Data</h6>

                                                                                            </div>
                                                                                        </div>

                                                                                        <div
                                                                                            *ngIf="loadingPrescreption">
                                                                                            <div class="container ">
                                                                                                <div class="col-md-12 text-center mt-5 mb-5 ml-5 mr-5"
                                                                                                    style="height:300px">
                                                                                                    <div class="spinner-border text-info"
                                                                                                        role="status">
                                                                                                        <span
                                                                                                            class="sr-only">Loading...</span>
                                                                                                    </div>
                                                                                                </div>


                                                                                            </div>



                                                                                        </div>
                                                                                    </ng-container>

                                                                                </div>

                                                                            </form>


                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div id="recording" class="tab-pane fade">
                                                                <div class="schedule-header">
                                                                    <div class="row">
                                                                        <h5>Consultation Video</h5>
                                                                        <div class="col-md-12">
                                                                            <div class="day-slot">
                                                                                <ul>

                                                                                    <li *ngFor="let record of recordingData"
                                                                                        style="cursor: pointer;">
                                                                                        <a
                                                                                            (click)="openVideo(record.file)">
                                                                                            <span id="day-days"> <i
                                                                                                    class="fa fa-video"></i></span>
                                                                                            <span id="day-mediumDate"
                                                                                                class="slot-date">{{record.name}}
                                                                                            </span>
                                                                                            <span></span>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li
                                                                                        *ngIf="recordingData.length===0 ">
                                                                                        <p>No Video Data</p>
                                                                                    </li>


                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                        <h5>Screenshot Image</h5>
                                                                        <div class="col-md-12">
                                                                            <div class="day-slot">
                                                                                <ul>

                                                                                    <li *ngFor="let img of screenshot"
                                                                                        style="cursor: pointer;">
                                                                                        <a
                                                                                            (click)="openVideo(img.file)">
                                                                                            <span id="day-days"> <i
                                                                                                    class="fa fa-image"></i></span>
                                                                                            <span id="day-mediumDate"
                                                                                                class="slot-date">{{img.file_name}}
                                                                                            </span>
                                                                                            <span></span>
                                                                                        </a>
                                                                                    </li>
                                                                                    <li
                                                                                        *ngIf=" screenshot.length===0  ">
                                                                                        <p>No image Data</p>
                                                                                    </li>
                                                                                </ul>
                                                                            </div>

                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div id="slot_referal" class="tab-pane fade">
                                                                <div class="card border-info pb-2"
                                                                    style="overflow-y: auto; min-height: 343px;">
                                                                    <div class="col-md-12 mt-2">
                                                                        <div class="row">
                                                                            <div class="col-md-6" *ngIf="userType!='Patient'">
                                                                                Doctor Type
                                                                                <select
                                                                                    class="form-control input-field-border select"
                                                                                    name="doctorType" id="doctorType"
                                                                                    (change)="setDoctorType($event.target.value)"
                                                                                    formControlName="doctorType" >
                                                                                    <option value="0" >Select
                                                                                        Doctor Type</option>
                                                                                    <option value="1" translate>
                                                                                        Individual</option>
                                                                                    <option value="2" translate>
                                                                                        Hospital-Based</option>
                                                                                </select>
                                                                                
                                                                            </div>
                                                                            <div class="col-md-6"
                                                                                *ngIf="consultForm.value.doctorType==2">
                                                                                Hospital
                                                                                <select
                                                                                    class="form-control input-field-border select"
                                                                                    name="referHospital"
                                                                                    id="referHospital"
                                                                                    (change)="getSOM($event.target.value)"
                                                                                    formControlName="hospitalId"  *ngIf="userType!='Patient'">
                                                                                    <option disabled value="null">Select
                                                                                        Hospital</option>
                                                                                    <option
                                                                                        *ngFor="let data of hospitalList"
                                                                                        [value]="data.uuid">
                                                                                        {{ data.name}}
                                                                                    </option>
                                                                                </select>
                                                                                
                                                                            </div>
                                                                            <div class="col-md-6" *ngIf="userType=='Patient'">
                                                                                Hospital
                                                                            <input type="text" class="form-control"
                                                                                     formControlName="hospitalId" readonly>
                                                                                </div>
                                                                            <div class="col-md-6">
                                                                                System of Medicine
                                                                                <select
                                                                                    class="form-control input-field-border select"
                                                                                    name="referDepartment"
                                                                                    id="referDepartment"
                                                                                    (change)="getSpecificSpeciality($event.target.value)"
                                                                                    formControlName="deptName" *ngIf="userType!='Patient'">
                                                                                    <option disabled value="null">Select
                                                                                        System of Medicine</option>
                                                                                    <option
                                                                                        *ngFor="let data of getSOMList"
                                                                                        [value]="data">
                                                                                        {{ data }}
                                                                                    </option>
                                                                                </select>
                                                                                <input type="text" class="form-control"
                                                                                *ngIf="userType=='Patient'" formControlName="deptName" readonly>
                                                                            </div>
                                                                            <div class="col-md-6"
                                                                                *ngIf="specificDepartment && consultForm.value.doctorType==2">
                                                                                Department
                                                                                <select
                                                                                    class="form-control input-field-border select"
                                                                                    name="referSpeciality"
                                                                                    id="referSpeciality"
                                                                                    (change)="setSpecificDepartment($event.target.value)"
                                                                                    formControlName="deptId">
                                                                                    <option disabled value="null">Select
                                                                                        Department</option>
                                                                                    <option
                                                                                        *ngFor="let data of specificDepartment[0]"
                                                                                        [value]="data.department_uuid">
                                                                                        {{ data.value }}
                                                                                    </option>
                                                                                </select>
                                                                            </div>
                                                                            <div class="col-md-6"
                                                                            *ngIf="specificSpeciality">
                                                                                Speciality
                                                                                <select
                                                                                    class="form-control input-field-border select"
                                                                                    name="referSpeciality"
                                                                                    id="referSpeciality"
                                                                                    (change)="setSpecificSpeciality($event.target.value)"
                                                                                    formControlName="specId" *ngIf="userType!='Patient'">
                                                                                    <option disabled value="null">Select
                                                                                        Speciality</option>
                                                                                    <option
                                                                                        *ngFor="let data of specificSpeciality"
                                                                                        [value]="data.spec_code">
                                                                                        {{ data.spec_name }}
                                                                                    </option>
                                                                                </select>
                                                                                
                                                                            </div>
                                                                            <div class="col-md-6" *ngIf="userType=='Patient'">
                                                                                Speciality
                                                                                <input type="text" class="form-control"
                                                                                 formControlName="specId" readonly>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                Doctor
                                                                                <select
                                                                                    class="form-control input-field-border select"
                                                                                    name="referHospital"
                                                                                    id="referDoctor"
                                                                                    formControlName="doctorId"
                                                                                    *ngIf="userType!='Patient'">
                                                                                    <option disabled value="null">Select
                                                                                        Doctor</option>
                                                                                    <option
                                                                                        *ngFor="let data of doctorList"
                                                                                        [value]="data.uuid">
                                                                                        {{ data.username}}
                                                                                    </option>
                                                                                </select>
                                                                                <input type="text" class="form-control"
                                                                                *ngIf="userType=='Patient'" formControlName="doctorId" readonly>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                Reason for Refer:
                                                                                <input
                                                                                    class="form-control input-field-border select"
                                                                                    type="text"
                                                                                    formControlName="referReason">
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-12" *ngIf="userType=='DoctorAssistant'||userType=='Doctor'">
                                                                        <div class="row">
                                                                            <div class="col-md-12 mt-2">
                                                                                <button
                                                                                    class="btn btn-primary btn-sm btn-msg mt-2"
                                                                                    (click)="referPatient()">Refer</button>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="container">
                                                                <div class="col-md-12 mb-2 text-center text-danger">
                                                                    <h5 class="text-info">
                                                                        <!-- <div>
                                                                          <button class="btn btn-info btn-lg" *ngIf="!patientHistory" (click)="onShowPatientHistory()"><i class="fas fa-file-medical"></i> Show Patient History </button>
                                                                          <button class="btn btn-info btn-lg" *ngIf="patientHistory" (click)="onHidePatientHistory()"><i class="fas fa-file-medical"></i> Hide Patient History </button>
                                                                      </div> -->
                                                                    </h5>
                                                                    <!-- <button type="submit" (click)="onCaseHistory()" class="btn btn-secondary btn-lg mb-2 pl-10 ml-3">Case History</button> -->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <hr>


                            </div>
                        </div>
                    </div>

                </div>

            </form>
            <hr>

        </div>
        <div *ngIf="!detailView" class="text-center mt-4"
            [ngClass]="{'onlyData_Btn_list':videoAndData,'onlyData_Btn_list':onlyData}">

            <!-- <button class="btn btn-info btn-lg mr-3 mb-3" *ngIf="!currentSummary" (click)="onShowCurrentSummary()" id="Show-current-summary"><i class="fas fa-file-medical"></i> Show Current Summary </button> -->
            <!-- <button class="btn btn-lg mr-3 mb-3" *ngIf="currentSummary" (click)="onHideCurrentSummary()" style="background-color: #ce9733; color: azure;"><i class="fas fa-file-medical"></i> Hide Current Summary </button> -->

            <button class="btn btn-info btn-lg mr-3 mb-3" *ngIf="!patientHistory" (click)="onShowPatientHistory()"
                id="onShowPatientHistory"><i class="fas fa-file-medical"></i> Show Patient History </button>
            <button class="btn bg-gradient-warning btn-lg mr-3 mb-3" *ngIf="patientHistory"
                (click)="onHidePatientHistory()" id="onHidePatientHistory"
                style="background-color:#ce9733; color: azure;"><i class="fas fa-file-medical"></i> Hide Patient
                History </button>
            <!-- <button class="btn btn-info btn-lg mr-3 mb-3"><i class="fas fa-file-medical"></i> Reports </button> -->
            <button class="btn btn-info btn-lg mr-3 mb-3" id="onSuspendInvestigationsPending"
                (click)="setSuspensionType('Investigation')" data-toggle="modal" data-target="#suspendModal"><i
                    class="fas fa-file-medical"></i> Suspend For Investigations </button>
            <button class="btn btn-info btn-lg mr-3 mb-3" id="onSuspendReviewPending"
                (click)="setSuspensionType('Review')" data-toggle="modal" data-target="#suspendModal"><i
                    class="fas fa-file-medical"></i> Suspend For Review </button>
            <button class="btn btn-info btn-lg mr-3 mb-3" id="onComplete" data-toggle="modal" *ngIf="!followUpBooking"
                (click)="onComplete()"><i class="fas fa-file-medical"></i>
                End Consultation
            </button>
            <button class="btn btn-info btn-lg mr-3 mb-3" id="onComplete" data-toggle="modal" *ngIf="followUpBooking"
                (click)="onFollowComplete()"><i class="fas fa-file-medical"></i>
                End Consultation
            </button>
        </div>
        <div *ngIf="userType=='Patient' || userType=='Partner'">
            <div class="col-md-12 text-center">
                <button type="button" printSectionId="print-section" class="btn btn-danger btn-lg mr-3 mb-3"
                    (click)="openPatientDashboard()">Leave Consultation</button>
            </div>
        </div>
        <!-- <app-doctor-messages></app-doctor-messages> -->

        <div class="col-md-12 text-center mt-3" *ngIf="currentSummary">
            <h3 class="mt-3 text-success text-center">Current Summary</h3>
            <app-case-history-detailed (close)="onBackCaseHistoryDetailed()"></app-case-history-detailed>
        </div>

    </div>
    <!-- <div class="card">
        <div class="card-header">
            <div class="row">
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <h4 class="card-title mb-0"> Investigation</h4>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6  float-right">
                    <button class="btn btn-primary float-right" (click)="getConsultaionDocuments('investigation')">Download</button>
                </div>
            </div>
        </div>
        <div class="col-md-12 col-lg-12  col-xl-12">
            <div class="row mt-4">

                <div class="col-md-4 col-lg-4  col-xl-4">
                    <h6 class="text-success ml-1">PATHOLOGY</h6>
                    <div class="form-group">
                        <input type="text" class="form-control" maxlength="50" disabled>
                    </div>
                </div>
            </div>
        </div>
    </div> -->
    <div class="modal" id="suspendModal" tabindex="-1" role="dialog" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="suspendModalLabel">Suspend Consultation</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <h5>Note: Invoice will be generated and the patient will be charged. But there will be no charge for
                        his next consultation</h5>
                </div>
                <div class="modal-body">
                    <h5>Message for {{this.suspendType}}</h5>
                    <textarea name="reason" [(ngModel)]="suspendReason" id="suspend-reason" cols="30"
                        rows="10"></textarea>
                    <p *ngIf="suspendReason.length == 0" class="text-danger">*Message should not be empty</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" (click)="processSuspension()"
                        [disabled]="suspendReason.length == 0" data-dismiss="modal">Ok</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal" id="completeModal" tabindex="-1" role="dialog" data-keyboard="false" data-backdrop="static"
        *ngIf="isRefundComplete=='false' && parent_consultation_uuid==null">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="completeModalLabel">Consultation Refund</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <h5>Select Refund Type</h5>
                    <select [(ngModel)]="refundType" #refund (change)="onChange(refund.value)" class="col-md-12 mb-2">
                        <option value="no refund">No Refund</option>
                        <option value="Custom Refund">Custom Refund</option>
                        <option value="full refund">Full Refund</option>
                    </select>
                    <p *ngIf="refundType==null" class="text-danger">*Please select refund type</p>
                    <div *ngIf="isCustomRefund">
                        <h5>Enter Custom Refund Amount</h5>
                        <input type="number" [(ngModel)]="customAmount" class="col-md-12">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" (click)="refundSubmit()"
                        [disabled]="refundDisableCheck()" data-dismiss="modal">Submit</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--add new medicine Modal  -->
<div class="modal fade" id="new-medicine" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Modal title</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="col-md-6 col-lg-6  col-xl-6">
                    <h6 class="text-success ml-1">New Drug</h6>
                    <div class="form-group">
                        <input type="text" class="form-control" [(ngModel)]="newDrug" maxlength="50">
                    </div>
                </div>
            </div>
            <div class="modal-footer tex-center">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" (click)="addDrugFn(newDrug)">Add</button>
            </div>
        </div>
    </div>
</div>
<div class="col-md-12 mt-3" *ngIf="showCaseHistory">
    <!-- <h4 class="mt-3 text-info text-center">Patient History</h4> -->
    <!-- <div *ngIf="!caseHistoryDetailed"> -->
    <!-- <app-case-history-visit-dates (visitDate)="onVisitDate()"></app-case-history-visit-dates> -->
    <!-- </div> -->
    <!-- <app-consult-history [patientUuid]="patient" [doctorUuid]="doctor" [consultationId]="this.consultationId">
    </app-consult-history> -->
    <app-consultation-history [patientUuid]="patientUuid" [doctorUuid]="doctorUuid" [consultationId]="consultationId"
        [isOnConsult]="showCaseHistory"></app-consultation-history>

    <div *ngIf="caseHistoryDetailed">
        <!-- <h3 class="pointer text-info d-flex justify-content-between"><i class="fas fa-chevron-circle-left" (click)="onBackCaseHistoryDetailed()"></i></h3> -->
        <app-case-history-detailed (close)="onBackCaseHistoryDetailed()"></app-case-history-detailed>
    </div>
    <!-- <app-case-history-detailed></app-case-history-detailed> -->
</div>