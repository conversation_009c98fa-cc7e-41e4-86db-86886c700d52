import { ToastrService } from 'ngx-toastr';
// import { timeStamp } from 'console';
import {
  Component,
  OnInit,
  ViewEncapsulation,
  Input,
  EventEmitter,
  Output,
} from '@angular/core';
import {
  FormGroup,
  FormControl,
  FormArray,
  FormBuilder,
  Validators,
} from '@angular/forms';
import { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
// import { CaseHistoryComponent } from '../../onsult/case-history/case-history.component';
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';
import { DocumentModalComponent } from 'src/app/shared/document-modal/document-modal.component';
import { TeleConsultService } from '../tele-consult.service';
import { DoctorService } from '../../doctor/doctor.service';
import { SuspendModalComponent } from './suspend-modal/suspend-modal.component';
import { NONE_TYPE } from '@angular/compiler';
import * as moment from 'moment';
import { AuthService } from '../../auth/auth.service';
import { SharedService } from '../../shared/shared.service';
import * as Settings from '../../config/settings';
import { __await } from 'tslib';
import { HospitalService } from 'src/app/hospital-admin/hospital-admin.service';
declare var $: any;
@Component({
  selector: 'app-consultation',
  templateUrl: './consultation.component.html',
  styleUrls: ['./consultation.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class ConsultationComponent implements OnInit {
  @Input() patientVideoAndData: boolean;
  @Input() patientVideoAndDataShow: boolean;
  @Input() onlyData: boolean;
  @Output()
  consultaionFullFilmentStaus: EventEmitter<boolean> = new EventEmitter();
  // @Output()doctorEvent : EventEmitter<string> = new EventEmitter();
  suspendReviewModalForm = new FormGroup({
    //Medical History
    suspendReviewMessage: new FormControl(),
  });

  consultForm = this.formBuilder.group({
    //Medical History
    immunization_history: new FormControl(),
    past_medical_history: new FormControl(),
    appetite: new FormControl(),
    diet_history: new FormControl(),
    thirst: new FormControl(),
    sleep: new FormControl(),
    habits: new FormControl(),
    smoking: new FormControl(),
    alcohol: new FormControl(),
    drugs: new FormControl(),
    sexual_history: new FormControl(),
    additional_notes: new FormControl(),
    notes: new FormControl(),
    gender: new FormControl(),
    gynaecological_history: new FormControl(),
    age_of_menarche: new FormControl(),
    menstrual_history: new FormControl(),
    last_menstrual_period: new FormControl(null),
    number_of_pregnancy: new FormControl(),
    gravida: new FormControl(),
    para: new FormControl(),
    abortions: new FormControl(),
    social_history: new FormControl(),
    blood_pressure_systolic: new FormControl(),
    blood_pressure_diastolic: new FormControl(),
    pulse_rate: new FormControl(),
    spo2: new FormControl(),
    auscultation: new FormControl(),
    temperature: new FormControl(),
    ecg: new FormControl(),
    other_observations: new FormControl(),
    medicine: new FormControl(),
    diet: new FormControl(),
    recommendations: new FormControl(),
    chief_complaint: new FormControl(),
    history_of_present_illness: new FormControl(),
    //Physical Examination
    weight: new FormControl(NaN),
    height: new FormControl(NaN),
    bmi: new FormControl(),
    built: new FormControl(),
    nutrition: new FormControl(),
    clubbing_of_fingers: new FormControl(),
    nail_changes: new FormControl(),
    cyanosis: new FormControl(),
    icterus_jaundice: new FormControl(),
    pallor: new FormControl(),
    lymph_nodes: new FormControl(),
    oedema: new FormControl(),
    sclera: new FormControl(),
    // otherObservations: new FormControl(),
    //Systemic Examination
    respiratory_system: new FormControl(),
    gastro_intestinal_system: new FormControl(),
    cardio_vascular_system: new FormControl(),
    genitourinary_system: new FormControl(),
    musculoskeletal_system: new FormControl(),
    central_nervous_system: new FormControl(),
    eye: new FormControl(),
    ear: new FormControl(),
    nose: new FormControl(),
    mouth: new FormControl(),
    throat: new FormControl(),
    neck: new FormControl(),
    skin: new FormControl(),
    psychiatric_history: new FormControl(),
    //Diagnosis
    primary_diagnosis: new FormControl(),
    secondary_diagnosis: new FormControl(),
    differential_diagnosis: new FormControl(),
    final_diagnosis: new FormControl(),
    icd_10_codes: new FormControl(),
    //Investigation
    //private Notes
    private_notes: new FormControl(),
    //referral
    hospitalId: new FormControl(),
    doctorId: new FormControl(),
    deptId: new FormControl(),
    referReason: new FormControl(),
    doctorType: new FormControl(),
    deptName: new FormControl(),
    specId: new FormControl(),
  });
  investigationForm: FormGroup;
  patientHistory = false;
  currentSummary = false;
  public female: boolean;
  nofemale = false;
  investigationsTaxonomy: any;
  investigations = [];
  haematologyItems = [];
  haemIdList = [];
  biochemistryItems = [];
  bioChemIdList = [];
  microbiologyItems = [];
  microBioIdList = [];
  clinicalPathologyItems = [];
  clinicPathIdList = [];
  pathologyItems = [];
  pathIdList = [];
  serologyItems = [];
  serIdList = [];
  malariaItems = [];
  malIdList = [];
  filariasisItems = [];
  filIdList = [];
  dengueItems = [];
  denIdList = [];
  japaneseEncephalitisItems = [];
  japanEncIdList = [];
  chikungunyaItems = [];
  chikIdList = [];
  scrubTyphusItems = [];
  scrupTyphIdList = [];
  leptospirosisItems = [];
  leptIdList = [];
  brucellosisItems = [];
  bruceId = [];
  BRUCELLOSIS;

  tuberculosisItems = [];
  tuberIdList = [];
  hivItems = [];
  hivIdList = [];
  hepatitisBItems = [];
  hepBIdList = [];
  hepatitisCItems = [];
  hepCIdList = [];
  hepatitisAItems = [];
  hepAIdList = [];
  hepatitisEItems = [];
  hepEIdList = [];
  hbcItems = [];
  hbcIdList = [];
  otherDiagnosticTestsItems = [];
  odtIdList = [];
  radiologyAndOtherDiagnosticTestsItems = [];
  radDiaIdList = [];
  selected = [
    { id: 2, name: 'Clinical Pathology' },
    { id: 8, name: 'Leptrospirosis' },
  ];
  save = false;
  showCaseHistory = false;
  caseHistoryDetailed = false;
  screenOptions: NgbModalOptions = {
    keyboard: false,
    centered: true,
    size: 'lg',
    windowClass: 'modal-xl',
  };
  drugs = [];
  prescription = {};
  appointmentId = '';
  consultationId = '';
  drugId;
  investigationId = '';
  sub: any;
  joinedVideo = false;
  deviceVideo = false;
  videoLoaded = false;
  onlyVideo = false;

  suspendInvestigationMessage = '';
  suspendReviewMessage = '';
  name = '';
  prescriptionForm: FormGroup;
  prescriptionArray: FormArray;
  addedInvestigationCategories = [];
  investigationsPayload = [];
  savedData = {};
  detailView = false;
  loading = false;
  public addDrugTagNowRef: (name) => void;
  lmpDate: Date;
  userType: string;
  showAllTabes: boolean;

  consultationDocuments: any;
  disabledDownloadPrescriptionBtn: boolean;
  suspendType = '';
  suspendReason = '';
  doctor = '';
  patient = '';
  public maxDate: Date;
  public showAddMedicine = true;
  hidePrescriptionCompleteBtn: boolean;
  public showReports = false;
  showPrescription: boolean;
  profilePicture = 'assets/img/doctors/doctor-thumb-02.png';
  showProfilePic = true;
  personalInfo = {};
  showDoctorProfilePic: boolean;
  practiceLocations: any;
  degreeList: any = [];
  degreeString: string = '';
  doctorProfilePicture: any;
  doctorInfo: any;
  videoAndData = false;
  videoSize: number;
  dose: any = [
    { name: 'QID' },
    { name: 'TID' },
    { name: 'STAT' },
    { name: 'OD' },
    { name: 'BID/BD' },
    { name: 'HS' },
  ];
  medicineType: any = [];
  system_of_medicine: any;
  public newDrug: String;
  loadingPrescreption: boolean;
  special_instructions = null;
  closeVideoSession: boolean = true;
  screenshot: any = [];
  recordingData: any = [];
  public patientJoinedVideo = false;
  public doctorJoinedVideo = false;
  showPrescriptionForPatient = false;
  showWarningNotification: boolean;
  investigationFormValue: any[];
  patientInvestigationsPayload: any = [];
  prescriptionRefeshBtnDisabled = false;
  showHeaderLinks = true;
  refundType = null;
  isCustomRefund = false;
  customAmount = null;
  isRefundComplete = 'false';
  isPrescriptionComplete = 'false';
  parent_consultation_uuid = null;
  hospitalList: any;
  deptList: any;
  doctorList: any[]=[];
  doctorType: any;
  currentHsptId: string;
  selectedSpeciality: any;
  specificSpeciality: any;
  specificSpecialityData: any = [];
  patientUuid: string;
  followUpBooking: boolean = false;
  getSOMList: any = [];
  selectedSOM: any;
  specificDepartment: any;
  specificDepartmentList: any;
  specificDepartmentData: any;
  selectedDepartment: string;
  selectedDoctor: any;
  freeBooking: boolean = false;
  isInstantConsult: boolean = false;
  instantConsultAmount: any;
  constructor(
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private router: Router,
    private formBuilder: FormBuilder,
    private teleConsultService: TeleConsultService,
    private notificationService: ToastrService,
    private doctorService: DoctorService,
    private userService: AuthService,
    private sharedService: SharedService,
    private hospitalService: HospitalService,
  ) {
    this.addDrugTagNowRef = (name) => this.addDrugFn(name);
  }

  ngOnInit(): void {
    console.log(localStorage.getItem('isRefundComplete'))
    if (localStorage.getItem('isRefundComplete') != null) {
      this.isRefundComplete = localStorage.getItem('isRefundComplete');
    }
    if (localStorage.getItem('isPrescriptionComplete') != null) {
      this.isPrescriptionComplete = localStorage.getItem('isPrescriptionComplete');
    }
    if (localStorage.getItem('parent_consultation_uuid') != null) {
      this.parent_consultation_uuid = localStorage.getItem('parent_consultation_uuid');
    }
    localStorage.removeItem('doctor_joined_msg');
    localStorage.removeItem('patient_joined_msg');
    var consultation_type = localStorage.getItem("doc_consultation_type");
    if (consultation_type == "physical") {
      this.dataOnly();
      this.showHeaderLinks = false;
    }
    this.maxDate = new Date();
    this.loading = true;
    this.userType = localStorage.getItem('user_type');
    if (this.userType == 'Doctor') {
      this.patientVideoAndData = false;
      this.patientVideoAndDataShow = false;
      this.videoAndData = true;
      this.videoSize = 0;
      this.patientUuid = localStorage.getItem('consult_patient_id');
    }
    this.getMedicineType();
    this.route.queryParams.subscribe(
      (params) => {
        // console.log('CONSULTATION PARAMS');
        console.log(params);
        this.consultationId = params['consultationId'];
        localStorage.setItem('sess', this.consultationId);
        this.userService.getUserDetail().subscribe(
          (data) => {
            this.sharedService.setUserName(data['username']);
            if (data['profile_picture'] !== null) {
              this.sharedService.setPicture(data['profile_picture']);
            }
          },
          (error) => {
            console.log(error);
          }
        );
        if (!!this.userType && this.userType == 'Doctor') {
          this.showAllTabes = true;
        } else {
          this.showAllTabes = true;
        }
      },
      (err) => {
        console.log('ERROR:' + err);
      }
    );
    this.teleConsultService
      .getConsultationData(this.consultationId)
      .subscribe((data) => {
        this.patient = data['patient_uuid'];
        this.patientUuid = data['patient_uuid'];
        this.doctor = data['doctor_uuid'];
        this.isInstantConsult = data['is_instant_consult'];
        if(this.isInstantConsult){
          this.instantConsultAmount = data['instant_request'][0]['amount'];
        }       
        this.getDoctorDetails(data['doctor_uuid']);
        this.getPatientProfile(this.patient);
        const appointment_type = data['book_user_json']['appointment_type'];
        if (appointment_type == 'hospital_complimentry_appointment') {
          this.followUpBooking = true;
        }
      });
    if (localStorage.getItem('user_type') == 'Doctor') {
      this.doctorService.getDoctorProfile().subscribe(
        (data) => {
          // console.log('Doctor Data');
          this.name = 'Dr.' + data['user'].username; //+ ' ' + data['user'].last_name;
          // console.log(this.name);
        },
        (err) => {
          console.log(err);
        }
      );
      this.getAppointment();
    }
    this.getDrugs();

    //investigation
    this.investigationForm = this.formBuilder.group({
      keyAdvice: new FormControl(),
      others: new FormControl(),
      haematology: [[], Validators.required],
      biochemistryAndImmunoassay: [[], Validators.required],
      microbiology: [[], Validators.required],
      clinicalPathology: [[], Validators.required],
      pathology: [[], Validators.required],
      serology: [[], Validators.required],
      malaria: [[], Validators.required],
      filariasis: [[], Validators.required],
      dengue: [[], Validators.required],
      japaneseEncephalitis: [[], Validators.required],
      chikungunya: [[], Validators.required],
      scrubTyphus: [[], Validators.required],
      leptospirosis: [[], Validators.required],
      brucellosis: [[], Validators.required],
      tuberculosis: [[], Validators.required],
      hiv: [[], Validators.required],
      hepatitisB: [[], Validators.required],
      hepatitisC: [[], Validators.required],
      hepatitisA: [[], Validators.required],
      hepatitisE: [[], Validators.required],
      hbc: [[], Validators.required],
      otherDiagnosticTest: [[], Validators.required],
      radiologyAndOtherDiagnostics: [[], Validators.required],
      special_instructions: [[], Validators.required],
    });
    if (this.userType == 'Doctor') {
      this.teleConsultService.getInvestigationsTaxonomy().subscribe((data) => {
        this.investigationDataRender(data);
        this.loading = true;
        this.getConsultationData();
      });
    } else {
      this.doctor = localStorage.getItem('doctor_id');
      const query = 'doctor_id=' + this.doctor;
      this.teleConsultService.getInvestigationsTaxonomyData(query).subscribe((data) => {
        this.investigationDataRender(data);
        this.loading = true;
        this.getConsultationData();
      });
    }

    this.addPrescriptionForm();
    if (this.userType == 'Patient' || this.userType == 'Doctor') {
      this.sharedService.webSocketAvailable.subscribe((data) => {
        if (data['message_type'] == 'Adding Consultation data') {
          const consultData = JSON.parse(data['data']);
          console.log(consultData);
          if (consultData['vital_signs']) {
            if (consultData['vital_signs']['blood_pressure_systolic']) {
              this.consultForm
                .get('blood_pressure_systolic')
                .setValue(consultData['vital_signs']['blood_pressure_systolic']);
            } else if (consultData['vital_signs']['blood_pressure_diastolic']) {
              this.consultForm
                .get('blood_pressure_diastolic')
                .setValue(consultData['vital_signs']['blood_pressure_diastolic']);
            } else if (consultData['vital_signs']['temperature']) {
              this.consultForm
                .get('temperature')
                .setValue(consultData['vital_signs']['temperature']);
            } else if (consultData['vital_signs']['pulse_rate']) {
              this.consultForm
                .get('pulse_rate')
                .setValue(consultData['vital_signs']['pulse_rate']);
            } else if (consultData['vital_signs']['auscultation']) {
              this.consultForm
                .get('auscultation')
                .setValue(consultData['vital_signs']['auscultation']);
            } else if (consultData['vital_signs']['ecg']) {
              this.consultForm
                .get('ecg')
                .setValue(consultData['vital_signs']['ecg']);
            } else if (consultData['vital_signs']['spo2']) {
              this.consultForm
                .get('spo2')
                .setValue(consultData['vital_signs']['spo2']);
            } else if (consultData['vital_signs']['additional_notes']) {
              this.consultForm
                .get('additional_notes')
                .setValue(consultData['vital_signs']['additional_notes']);
            }
          } else
            if (consultData['physical_examination']) {
              if (consultData['physical_examination']['weight']) {
                this.consultForm
                  .get('weight')
                  .setValue(consultData['physical_examination']['weight']);
              } else if (consultData['physical_examination']['height']) {
                this.consultForm
                  .get('height')
                  .setValue(consultData['physical_examination']['height']);
              } else if (consultData['physical_examination']['bmi']) {
                this.consultForm
                  .get('bmi')
                  .setValue(consultData['physical_examination']['bmi']);
              } else if (consultData['physical_examination']['nutrition']) {
                this.consultForm
                  .get('nutrition')
                  .setValue(consultData['physical_examination']['nutrition']);
              } else if (consultData['physical_examination']['nail_changes']) {
                this.consultForm
                  .get('nail_changes')
                  .setValue(consultData['physical_examination']['nail_changes']);
              } else if (
                consultData['physical_examination']['clubbing_of_fingers']
              ) {
                this.consultForm
                  .get('clubbing_of_fingers')
                  .setValue(consultData['physical_examination']['clubbing_of_fingers']);
              } else if (consultData['physical_examination']['cyanosis']) {
                this.consultForm
                  .get('cyanosis')
                  .setValue(consultData['physical_examination']['cyanosis']);
              } else if (consultData['physical_examination']['icterus_jaundice']) {
                this.consultForm
                  .get('icterus_jaundice')
                  .setValue(consultData['physical_examination']['icterus_jaundice']);
              } else if (consultData['physical_examination']['pallor']) {
                this.consultForm
                  .get('pallor')
                  .setValue(consultData['physical_examination']['pallor']);
              } else if (consultData['physical_examination']['lymph_nodes']) {
                this.consultForm
                  .get('lymph_nodes')
                  .setValue(consultData['physical_examination']['lymph_nodes']);
              } else if (consultData['physical_examination']['oedema']) {
                this.consultForm
                  .get('oedema')
                  .setValue(consultData['physical_examination']['oedema']);
              } else if (consultData['physical_examination']['sclera']) {
                this.consultForm
                  .get('sclera')
                  .setValue(consultData['physical_examination']['sclera']);
              }
            } else
              if (consultData['systemic_examination']) {
                if (consultData['systemic_examination']['respiratory_system']) {
                  this.consultForm
                    .get('respiratory_system')
                    .setValue(consultData['systemic_examination']['respiratory_system']);
                } else if (consultData['systemic_examination']['gastro_intestinal_system']) {
                  this.consultForm
                    .get('gastro_intestinal_system')
                    .setValue(consultData['systemic_examination']['gastro_intestinal_system']);
                } else if (consultData['systemic_examination']['cardio_vascular_system']) {
                  this.consultForm
                    .get('cardio_vascular_system')
                    .setValue(consultData['systemic_examination']['cardio_vascular_system']);
                } else if (consultData['systemic_examination']['genitourinary_system']) {
                  this.consultForm
                    .get('genitourinary_system')
                    .setValue(consultData['systemic_examination']['genitourinary_system']);
                } else if (consultData['systemic_examination']['musculoskeletal_system']) {
                  this.consultForm
                    .get('musculoskeletal_system')
                    .setValue(consultData['systemic_examination']['musculoskeletal_system']);
                } else if (
                  consultData['systemic_examination']['central_nervous_system']
                ) {
                  this.consultForm
                    .get('central_nervous_system')
                    .setValue(consultData['systemic_examination']['central_nervous_system']);
                } else if (consultData['systemic_examination']['eye']) {
                  this.consultForm
                    .get('eye')
                    .setValue(consultData['systemic_examination']['eye']);
                } else if (consultData['systemic_examination']['nose']) {
                  this.consultForm
                    .get('nose')
                    .setValue(consultData['systemic_examination']['nose']);
                } else if (consultData['systemic_examination']['ear']) {
                  this.consultForm
                    .get('ear')
                    .setValue(consultData['systemic_examination']['ear']);
                } else if (consultData['systemic_examination']['mouth']) {
                  this.consultForm
                    .get('mouth')
                    .setValue(consultData['systemic_examination']['mouth']);
                } else if (consultData['systemic_examination']['throat']) {
                  this.consultForm
                    .get('throat')
                    .setValue(consultData['systemic_examination']['throat']);
                } else if (consultData['systemic_examination']['neck']) {
                  this.consultForm
                    .get('neck')
                    .setValue(consultData['systemic_examination']['neck']);
                } else if (consultData['systemic_examination']['skin']) {
                  this.consultForm
                    .get('skin')
                    .setValue(consultData['systemic_examination']['skin']);
                } else if (consultData['systemic_examination']['psychiatric_history']) {
                  this.consultForm
                    .get('psychiatric_history')
                    .setValue(consultData['systemic_examination']['psychiatric_history']);
                }
              } else
                if (consultData['diagnosis']) {
                  if (consultData['diagnosis']['primary_diagnosis']) {
                    this.consultForm
                      .get('primary_diagnosis')
                      .setValue(consultData['diagnosis']['primary_diagnosis']);
                  } else if (consultData['diagnosis']['secondary_diagnosis']) {
                    this.consultForm
                      .get('secondary_diagnosis')
                      .setValue(consultData['diagnosis']['secondary_diagnosis']);
                  } else if (consultData['diagnosis']['differential_diagnosis']) {
                    this.consultForm
                      .get('differential_diagnosis')
                      .setValue(consultData['diagnosis']['differential_diagnosis']);
                  } else if (consultData['diagnosis']['final_diagnosis']) {
                    this.consultForm
                      .get('final_diagnosis')
                      .setValue(consultData['diagnosis']['final_diagnosis']);
                  } else if (consultData['diagnosis']['icd_10_codes']) {
                    this.consultForm
                      .get('icd_10_codes')
                      .setValue(consultData['diagnosis']['icd_10_codes']);
                  }
                } else
                  if (consultData['medical_history']) {
                    console.log('medic history')
                    if (consultData['medical_history']['immunization_history']) {
                      this.consultForm
                        .get('immunization_history')
                        .setValue(consultData['medical_history']['immunization_history']);
                    } else if (consultData['medical_history']['past_medical_history']) {
                      this.consultForm
                        .get('past_medical_history')
                        .setValue(consultData['medical_history']['past_medical_history']);
                    } else if (consultData['medical_history']['appetite']) {
                      this.consultForm
                        .get('appetite')
                        .setValue(consultData['medical_history']['appetite']);
                    } else if (consultData['medical_history']['social_history']) {
                      this.consultForm
                        .get('social_history')
                        .setValue(consultData['medical_history']['social_history']);
                    } else if (consultData['medical_history']['diet']) {
                      this.consultForm.get('diet').setValue(consultData['medical_history']['diet']);
                    } else if (consultData['medical_history']['thirst']) {
                      this.consultForm
                        .get('thirst')
                        .setValue(consultData['medical_history']['thirst']);
                    } else if (consultData['medical_history']['sleep']) {
                      this.consultForm
                        .get('sleep')
                        .setValue(consultData['medical_history']['sleep']);
                    } else if (consultData['medical_history']['smoking']) {
                      this.consultForm
                        .get('smoking')
                        .setValue(consultData['medical_history']['smoking']);
                    } else if (consultData['medical_history']['alcohol']) {
                      this.consultForm
                        .get('alcohol')
                        .setValue(consultData['medical_history']['alcohol']);
                    } else if (consultData['medical_history']['drugs']) {
                      this.consultForm
                        .get('drugs')
                        .setValue(consultData['medical_history']['drugs']);
                    } else if (consultData['medical_history']['sexual_history']) {
                      this.consultForm
                        .get('sexual_history')
                        .setValue(consultData['medical_history']['sexual_history']);
                    } else if (consultData['medical_history']['other_observations']) {
                      this.consultForm
                        .get('other_observations')
                        .setValue(consultData['medical_history']['other_observations']);
                    } else if (consultData['medical_history']['age_of_menarche']) {
                      this.female = true;
                      this.consultForm
                        .get('age_of_menarche')
                        .setValue(consultData['medical_history']['age_of_menarche']);
                    } else if (consultData['medical_history']['menstrual_history']) {
                      this.female = true;
                      this.consultForm
                        .get('menstrual_history')
                        .setValue(consultData['medical_history']['menstrual_history']);
                    } else if (consultData['medical_history']['number_of_pregnancy']) {
                      this.female = true;
                      this.consultForm
                        .get('number_of_pregnancy')
                        .setValue(consultData['medical_history']['number_of_pregnancy']);
                    } else if (consultData['medical_history']['gravida']) {
                      this.female = true;
                      this.consultForm
                        .get('gravida')
                        .setValue(consultData['medical_history']['gravida']);
                    } else if (consultData['medical_history']['para']) {
                      this.female = true;
                      this.consultForm
                        .get('para')
                        .setValue(consultData['medical_history']['para']);
                    } else if (consultData['medical_history']['abortions']) {
                      this.female = true;
                      this.consultForm
                        .get('abortions')
                        .setValue(consultData['medical_history']['abortions']);
                    } else if (consultData['medical_history']['history_of_present_illness']) {
                      this.consultForm
                        .get('history_of_present_illness')
                        .setValue(consultData['medical_history']['history_of_present_illness']);
                    } else if (consultData['medical_history']['chief_complaint']) {
                      this.consultForm
                        .get('chief_complaint')
                        .setValue(consultData['medical_history']['chief_complaint']);
                    } else if (consultData['medical_history']['gynaecological_history']) {
                      this.female = true;
                      this.consultForm
                        .get('gynaecological_history')
                        .setValue(consultData['medical_history']['gynaecological_history']);
                    } else if (consultData['medical_history']['female']) {
                      this.female = consultData['medical_history']['female'];
                    } else if (consultData['medical_history']['last_menstrual_period'] !== null) {
                      this.female = true;
                      this.consultForm
                        .get('last_menstrual_period')
                        .setValue(
                          moment(consultData['medical_history']['last_menstrual_period']).format(
                            'DD-MM-YYYY'
                          )
                        );
                    }

                  } else if (consultData['prescription']) {
                    if (consultData['prescription']['is_draft'] == false) {
                      this.showPrescriptionForPatient = true;
                      this.prescription =
                        consultData['prescription']['prescription']['drugs_prescribed'];
                      this.createPrescriptionForm(this.prescription);
                    }

                  } else if (consultData['investigation']) {
                    if (consultData['investigation']['special_instructions']) {
                      this.special_instructions = consultData['investigation']['special_instructions'];
                      this.investigationForm
                        .get('special_instructions')
                        .setValue(this.special_instructions);
                    } else if (consultData['investigation']['tests_prescribed']?.['tests']) {
                      this.getInvestigation();
                      this.patientInvestigationsPayload =
                        consultData['investigation']['tests_prescribed']['tests'];
                      if (this.patientInvestigationsPayload.length > 0) {
                        const lastElement = this.patientInvestigationsPayload[this.patientInvestigationsPayload.length - 1];
                        const category = lastElement['category'];
                        const investigation_name = lastElement['investigation_name'];
                        if (!lastElement['uuid']) {
                          const query = `owned_by=${this.doctor}&investigation_name=${investigation_name}&category=${category}`;
                          this.teleConsultService.getInvestigationsTaxonomyData(query).subscribe((data) => {

                            const uuid = data[category][0]['uuid'];
                            this.patientInvestigationsPayload.splice(-1, 1);
                            const val = { category: category, investigation_name: investigation_name, uuid: uuid };
                            this.patientInvestigationsPayload.push(val);
                            this.renderInvestigations(this.patientInvestigationsPayload);
                          });
                        } else {
                          this.renderInvestigations(this.patientInvestigationsPayload);
                        }
                      }


                   
                  } }else if(consultData['refer_data']){
                    this.consultForm.get('doctorType').setValue(consultData['refer_data']['referred_to_doctorType']);
                    this.consultForm.get('hospitalId').setValue(consultData['refer_data']['referred_to_hospitalName']);
                    this.consultForm.get('deptName').setValue(consultData['refer_data']['referring_departmentName']);
                    this.consultForm.get('doctorId').setValue(consultData['refer_data']['referred_to_doctorName']);
                    this.consultForm.get('specId').setValue(consultData['refer_data']['referring_specialityName']);
                    this.consultForm.get('referReason').setValue(consultData['refer_data']['reason_for_referral']);
                  }
        } else if (data['message_type'] == "Doctor Ended Consultation") {
          this.notificationService.warning('Doctor Ended Consultation');
        } else if (data['message_type'] == "Suspended For Investigation") {
          this.notificationService.warning('Suspended For Investigation');
        } else if (data['message_type'] == "Suspended For Review") {
          this.notificationService.warning('Suspended For Review');
        }
      });
      this.sub = this.sharedService.getMessages().subscribe();
    }
  }

  viewPrescription() {
    console.log('refresh work');
    // this.showPrescriptionForPatient=false;
    // this.loading= true;
    this.prescriptionRefeshBtnDisabled = true;
    this.teleConsultService.getConsultationData(this.consultationId).subscribe(
      (data) => {
        // this.showPrescriptionForPatient=true;
        this.prescriptionRefeshBtnDisabled = false;
        console.log(data);
        this.consultForm.patchValue({
          blood_pressure_systolic: data['vitalsigns']['blood_pressure_systolic'],
          blood_pressure_diastolic: data['vitalsigns']['blood_pressure_diastolic'],
          pulse_rate: data['vitalsigns']['pulse_rate'],
          spo2: data['vitalsigns']['spo2'],
          auscultation: data['vitalsigns']['auscultation'],
          temperature: data['vitalsigns']['temperature'],
          ecg: data['vitalsigns']['ecg'],
          additional_notes: data['vitalsigns']['additional_notes']
        });
        // this.loading= false;
        //  this.medicalHistory.push(data);
      },
      (error) => {
        this.prescriptionRefeshBtnDisabled = true;
        console.log(error);
        // this.loading= false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );

  }

  ngOnDestroy() {
    // if(this.userType =='Patient'){
    // this.sub.unsubscribe();
    // }
    localStorage.removeItem("doc_consultation_type");
    localStorage.removeItem('isPrescriptionComplete');
    localStorage.removeItem('isRefundComplete');
    localStorage.removeItem('parent_consultation_uuid');
  }
  onCaseHistory(event) {
    // this.modalService.dismissAll();
    // this.modalService.open(CaseHistoryComponent, this.screenOptions).result.then((result) => {
    // });
    if (event.target.checked) {
      this.showCaseHistory = true;
    } else {
      this.showCaseHistory = false;
      this.caseHistoryDetailed = false;
    }
  }

  onShowPatientHistory() {
    // this.patientEvent.emit(this.patient);
    // this.doctorEvent.emit(this.doctor);
    this.showCaseHistory = true;
    this.patientHistory = true;
    this.currentSummary = false;
  }

  onHidePatientHistory() {
    this.showCaseHistory = false;
    this.caseHistoryDetailed = false;
    this.patientHistory = false;
    this.currentSummary = false;
  }

  onShowCurrentSummary() {
    // this.teleConsultService.showCurrentSummary(this.consultationId).subscribe( data => {

    // },
    // err =>{
    //   console.log('ERROR:' + err.message);
    // });
    this.currentSummary = true;
    this.showCaseHistory = false;
    this.caseHistoryDetailed = false;
    this.patientHistory = false;
  }

  onHideCurrentSummary() {
    this.currentSummary = false;
  }

  onReports() { }

  onSave() {
    this.save = true;
  }

  onFormSubmit() { }

  onTabClick(event: any) {
    console.log(event);
    console.log(event.tab.textLabel);
    this.save = false;
  }
  activeTabIndex = 0;
  public isShow = false;
  public toggleDiv() {
    this.isShow = !this.isShow;
  }

  openDoctorDashboard() {
    if (this.userType == 'Doctor') {
      if (this.showAllTabes) {
        this.router.navigateByUrl('/doctor/dashboard');
        this.router.events.subscribe((val) => {
          const nvigationEnd = val instanceof NavigationEnd;
          if (!!nvigationEnd) {
            location.reload();
          }
        });
      }
    } else if (this.userType == 'Patient') {
      this.router.navigateByUrl('/patient/dashboard');
    } else if (this.userType == 'DoctorAssistant') {
      this.router.navigateByUrl('hadashboard');
    }
  }

  onAddRow(prescriptionTable) {
    let tableRef = <HTMLTableElement>document.getElementById(prescriptionTable);
    // let newRow = tableR.insertBefore(-1)
    // TSection.insertBefore();
    let newRow = tableRef.insertRow(-1);
    console.log(newRow);
    // Insert a cell in the row at index 0
    let medicine = newRow.insertCell(0);
    medicine.innerHTML = "<td> <input type='text'> </td>";

    let morningDosage = newRow.insertCell(1);
    morningDosage.innerHTML = "<td> <input type='checkbox'> </td>";

    let afternoonDosage = newRow.insertCell(2);
    afternoonDosage.innerHTML = "<td> <input type='checkbox'> </td>";

    let nightDosage = newRow.insertCell(3);
    nightDosage.innerHTML = "<td> <input type='checkbox'> </td>";

    let beforeFood = newRow.insertCell(4);
    beforeFood.innerHTML = "<td> <input type='checkbox'> </td>";

    let days = newRow.insertCell(5);
    days.innerHTML =
      "<td> <input type='number' name='days' style='width: 50%;'> </td>";

    let remarks = newRow.insertCell(6);
    remarks.innerHTML = "<td> <input type='text' name='remarks'> </td>";

    // Append a text node to the cell
    // let newText = document.createTextNode('New bottom row');
    // newCell.appendChild(newText);
  }

  onGenderFemale() {
    this.female = true;
  }

  onGenderNoFemale() {
    this.female = false;
  }

  onVisitDate() {
    this.caseHistoryDetailed = true;
  }

  onReport() {
    this.modalService.open(DocumentModalComponent, {
      windowClass: 'modalSize',
    });
  }

  CreateNew() {
    $('#new-medicine').modal('show');
  }

  onBackCaseHistoryDetailed() {
    this.caseHistoryDetailed = false;
  }

  onKey(sectionName, fieldName, event) {
    this.teleConsultService
      .updateConsultation(
        this.consultationId,
        sectionName,
        fieldName,
        event.target.value
      )
      .subscribe(
        (data) => {
          // this.consultationId = data['uuid'];
        },
        (err) => {
          console.log('ERROR:' + err.message);
        }
      );
  }

  onKeyPrescription(fieldName, event) {
    console.log(fieldName, event.target.value);
    // let obj = {fieldName: ''};
    // this.prescription[this.prescription.length+1];
  }

  onPrescription() {
    const valid = this.prescriptionForm.valid;
    if (!!valid) {
      this.showAddMedicine = true;
      const data = this.prescriptionForm.get('prescriptionArray').value;
      console.log('origindata', data);
      this.processPrescriptionData(data);
      this.teleConsultService
        .addPrescription(this.consultationId, this.prescription)
        .subscribe(
          (data) => {
            this.getDrugs();
            //   if(data['prescription']['is_draft'] === true){
            //}else{
            //    this.hidePrescriptionCompleteBtn= true;
            //   this.showAddMedicine= false;
            //}
          },
          (error) => {
            this.notificationService.error(error?.['error']?.['error_message']);
            console.log(error);
          }
        );
    }
  }

  joinVideo() {
    this.joinedVideo = true;
  }

  joinDeviceVideo() {
    this.deviceVideo = true;
  }

  videoOnly() {
    this.onlyVideo = true;
    this.onlyData = false;
    this.videoAndData = false;
    $('#videoZoomButton').click();
  }

  dataOnly() {
    this.onlyData = true;
    this.onlyVideo = false;
    this.videoAndData = false;
    this.closeVideoSession = false;
  }

  dataAndVideo() {
    this.videoAndData = true;
    this.onlyVideo = false;
    this.onlyData = false;
    $('#videoZoomButton').click();
  }
  refundSubmit() {
    console.log(this.refundType);
    console.log(this.customAmount);
    var data = {};
    if (this.refundType == "Custom Refund") {
      data = {
        fees: this.customAmount
      }
    } else {
      data = {
        fees: this.refundType
      }
    }
    this.teleConsultService.refundConsultation(this.consultationId, data).subscribe(
      (response) => {
        console.log(response)
        if (response.status == 201 || response.status == 200) {
          this.teleConsultService.completeConsultation(this.consultationId).subscribe(
            (response) => {
              console.log(response)
              if (response.status == 204) {
                this.notificationService.error(response.statusText);
                this.isRefundComplete = 'true';
                localStorage.setItem('isRefundComplete', 'true');
              }
              else {
                this.consultaionFullFilmentStaus.emit(true);
                this.router.navigateByUrl('/doctor/dashboard');
                this.router.events.subscribe((val) => {
                  const nvigationEnd = val instanceof NavigationEnd;
                  if (!!nvigationEnd) {
                    location.reload();
                  }
                });
              }
            },
            (err) => {
              console.log('ERROR:' + err);
              const status = err['status'];
              if (status == 400) {
                this.notificationService.error(`${err['error']['error_message']}`, 'Med.Bot');
              } else {
                this.notificationService.error(`${err['statusText']}`, 'Med.Bot');
              }
            }
          );
        }
        else {
        }
      },
      (err) => {
        console.log('ERROR:' + err.message);
      }
    );
  }

  onChange(refundType) {
    if (refundType == "Custom Refund") {
      this.isCustomRefund = true;
    }
    else {
      this.isCustomRefund = false;
    }
    this.refundType = refundType;
    console.log(refundType);
  }
  refundDisableCheck() {
    if (this.isCustomRefund) {
      if (this.customAmount != null && this.refundType != null) {
        return false;
      } else {
        return true;
      }
    }
    else {
      if (this.refundType != null) {
        return false;
      } else {
        return true;
      }
    }
  }
  onSuspendInvestigationsPending() {
    const activeModal = this.modalService.open(SuspendModalComponent);
    activeModal.componentInstance.suspendType = 'investigations';
    activeModal.componentInstance.consultationId = this.consultationId;
    // this.teleConsultService.suspendConsultationInvestigationsPending(this.consultationId, this.suspendInvestigationMessage).subscribe( data => {
    //   this.router.navigateByUrl('/doctor/dashboard');
    // },
    // err =>{
    //   console.log('ERROR:' + err.message);
    // });
  }

  onSuspendReviewPending() {
    const activeModal = this.modalService.open(SuspendModalComponent);
    activeModal.componentInstance.suspendType = 'review';
    activeModal.componentInstance.consultationId = this.consultationId;
    // this.teleConsultService.suspendConsultationReviewPending(this.consultationId, this.suspendReviewMessage).subscribe( data => {
    //   this.router.navigateByUrl('/doctor/dashboard');
    // },
    // err =>{
    //   console.log('ERROR:' + err.message);
    // });
  }

  onComplete() {
    console.log(this.parent_consultation_uuid);
  
    if(this.isInstantConsult){
      this.freeBooking = this.instantConsultAmount == '0.00' ? true : false;
    }

    if (this.isRefundComplete == "true" || this.parent_consultation_uuid != null || this.freeBooking == true) {
      $('#completeModal').modal('hide');
      this.teleConsultService.completeConsultation(this.consultationId).subscribe(
        (response) => {
          console.log(response)
          if (response.status == 204) {
            this.notificationService.error(response.statusText);
          }
          else {
            this.consultaionFullFilmentStaus.emit(true);
            this.router.navigateByUrl('/doctor/dashboard');
            this.router.events.subscribe((val) => {
              const nvigationEnd = val instanceof NavigationEnd;
              if (!!nvigationEnd) {
                location.reload();
              }
            });
          }
        },
        (err) => {
          console.log(err);
          const status = err['status'];
          if (status == 400) {
            this.notificationService.error(`${err['error']['error_message']}`, 'Med.Bot');
          } else {
            this.notificationService.error(`${err['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
    else {
      $('#completeModal').modal('show');
    }
  }
  onFollowComplete() {
    this.teleConsultService.completeConsultation(this.consultationId).subscribe(
      (response) => {
        if (response.status == 204) {
          this.notificationService.error(response.statusText);
        }
        else {
          this.consultaionFullFilmentStaus.emit(true);
          this.router.navigateByUrl('/doctor/dashboard');
          this.router.events.subscribe((val) => {
            const nvigationEnd = val instanceof NavigationEnd;
            if (!!nvigationEnd) {
              location.reload();
            }
          });
        }
      },
      (err) => {
        console.log(err);
        const status = err['status'];
        if (status == 400) {
          this.notificationService.error(`${err['error']['error_message']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${err['statusText']}`, 'Med.Bot');
        }
      }
    );
  }

  consultationOnlyStyle() {
    if (this.onlyData) {
      return { 'margin-left': '25%', width: '100%' };
    }
  }

  addDrugFn(name) {
    const drugData = {
      name: name,
      manufacturer: null,
      generic_name: null,
      dosage_form: null,
      description: null,
      system_of_medicine: this.system_of_medicine,
    };
    this.drugs.unshift(drugData);
  }

  addPrescriptionForm() {
    this.prescriptionForm = this.formBuilder.group({
      prescriptionArray: this.formBuilder.array([]),
    });
  }
  NoPrescriptionForm() {
    this.prescriptionArray.clear();

  }
  createPrescriptionForm(data) {
    this.prescriptionArray = this.prescriptionForm.get(
      'prescriptionArray'
    ) as FormArray;
    if (data == null) {
      this.showAddMedicine = false;
      console.log('data inavailable');
      this.prescriptionArray.push(
        this.formBuilder.group({
          uuid: null,
          brand_name: new FormControl('', Validators.required),
          medicine_type: new FormControl(null, Validators.required),
          strength: new FormControl(null, Validators.required),
          dosage: new FormControl('', Validators.required),
          before_food: new FormControl(false),
          morning: new FormControl(''),
          afternoon: new FormControl(''),
          evening: new FormControl(''),
          night: new FormControl(''),
          days: new FormControl('', Validators.required),
          notes: new FormControl('', Validators.required),
        })
      );
    } else {
      console.log('data available', data);
      this.prescriptionArray.clear();
      if (this.userType == 'Doctor') {
        data.forEach((prescriptionData) => {
          this.prescriptionArray.push(
            this.formBuilder.group({
              uuid: prescriptionData['uuid'],
              brand_name: new FormControl(
                [prescriptionData['brand_name']],
                Validators.required
              ),
              medicine_type: new FormControl(
                prescriptionData['medicine_type'],
                Validators.required
              ),
              strength: new FormControl(
                prescriptionData['strength'],
                Validators.required
              ),
              dosage: new FormControl(
                prescriptionData['administration_instructions']?.dosage,
                Validators.required
              ),
              before_food: new FormControl(prescriptionData['before_food']),
              morning: new FormControl(
                prescriptionData['administration_instructions']?.morning
              ),
              afternoon: new FormControl(
                prescriptionData['administration_instructions']?.afternoon
              ),
              evening: new FormControl(
                prescriptionData['administration_instructions']?.evening
              ),
              night: new FormControl(
                prescriptionData['administration_instructions']?.night
              ),
              days: new FormControl(
                prescriptionData['administration_instructions']?.duration_days,
                Validators.required
              ),
              notes: new FormControl(
                prescriptionData['administration_instructions']?.notes,
                Validators.required
              ),
            })
          );
        });
      } else if (this.showPrescriptionForPatient) {
        data.forEach((prescriptionData) => {
          this.prescriptionArray.push(
            this.formBuilder.group({
              uuid: prescriptionData['uuid'],
              brand_name: new FormControl(prescriptionData['brand_name']),
              medicine_type: new FormControl(
                prescriptionData['medicine_type'],
                Validators.required
              ),
              strength: new FormControl(
                prescriptionData['strength'],
                Validators.required
              ),
              dosage: new FormControl(
                prescriptionData['administration_instructions']?.dosage
              ),
              before_food: new FormControl(prescriptionData['before_food']),
              morning: new FormControl(
                prescriptionData['administration_instructions']?.morning
              ),
              afternoon: new FormControl(
                prescriptionData['administration_instructions']?.afternoon
              ),
              evening: new FormControl(
                prescriptionData['administration_instructions']?.evening
              ),
              night: new FormControl(
                prescriptionData['administration_instructions']?.night
              ),
              days: new FormControl(
                prescriptionData['administration_instructions']?.duration_days
              ),
              notes: new FormControl(
                prescriptionData['administration_instructions']?.notes
              ),
            })
          );
        });
      }
    }
  }

  trackFn(index: any) {
    return index;
  }

  processPrescriptionData(data) {

    let prescription_consultation_data = [];
    data.forEach((element) => {
      let days = [];
      let prescription = element?.brand_name;
      if (prescription.length > 0) {
        prescription.forEach((drug) => {
          const selectedDrug = this.drugs.filter(
            (selectedDrug) => selectedDrug.name == drug
          );
          console.log(selectedDrug);
          let preciseData = {
            uuid: selectedDrug?.[0]?.['uuid'] || null,
            brand_name: drug['name'] || prescription[0],
            medicine_type: element?.medicine_type || 'medicine',
            generic_name: drug['generic_name'] || null,
            before_food: element['before_food'],
            strength: element?.strength || null,
            system_of_medicine: this.system_of_medicine,
            administration_instructions: {
              // parts_of_day: days,
              morning: element?.morning || ' ',
              afternoon: element?.afternoon || ' ',
              evening: element?.evening || ' ',
              night: element?.night || ' ',
              duration_days: element['days'],
              sos: false,
              dosage: element?.dosage || null,
              notes: element['notes'],
            },
          };
          if (selectedDrug.length != 0) {
            preciseData['uuid'] = selectedDrug[0]['uuid'];
          } else {
            preciseData['uuid'] = null;
          }
          console.log(preciseData);
          prescription_consultation_data.push(preciseData);
        });
      } else {
        let pres_drug = this.drugs.filter(
          (drug) => drug.name == prescription[0]
        );
        pres_drug.forEach((drug) => {
          let preciseData = {
            uuid: drug['uuid'] || null,
            brand_name: drug['name'] || prescription[0],
            medicine_type: element?.medicine_type || 'medicine',
            generic_name: drug['generic_name'] || null,
            before_food: element['before_food'],
            strength: element?.strength || null,
            system_of_medicine: this.system_of_medicine,
            administration_instructions: {
              // parts_of_day: days,
              morning: element?.morning || ' ',
              afternoon: element?.afternoon || ' ',
              evening: element?.evening || ' ',
              night: element?.night || ' ',
              duration_days: element['days'],
              sos: false,
              dosage: element?.dosage || null,
              notes: element['notes'],
            },
          };
          console.log(preciseData);
          prescription_consultation_data.push(preciseData);
        });
      }
    });
    this.prescription = {
      prescription: {
        prescription: {
          drugs_prescribed: prescription_consultation_data,
        },
      },
    };
    // console.log(this.prescription);
  }

  getConsultationData() {
    this.teleConsultService
      .getConsultationDataHa(this.consultationId)
      .subscribe((data) => {

        this.savedData = data;
        const consultation_status = data['fulfilment_status'];
        if (data['prescription']['is_draft'] == false) {
          this.showPrescriptionForPatient = true;
          this.hidePrescriptionCompleteBtn = true;
        }
        if (data['prescription']['prescription']) {
          this.prescription =
            data['prescription']['prescription']['drugs_prescribed'];
          this.createPrescriptionForm(this.prescription);
        } else {
          if (this.userType == 'Doctor') {
            this.createPrescriptionForm(null);
          }
        }

        this.special_instructions =
          data['investigation']['special_instructions'];
        this.investigationForm
          .get('special_instructions')
          .setValue(this.special_instructions);
        if (data['investigation']['tests_prescribed']?.['tests']) {
          this.investigationsPayload =
            data['investigation']['tests_prescribed']['tests'];
          this.renderInvestigations(this.investigationsPayload);
          if (this.detailView) {
            this.investigationForm.disable();
          }
        } else {
          if (this.detailView) {
            this.investigationForm.disable();
          }
        }
        if (data['vitalsigns']) {
          this.consultForm
            .get('blood_pressure_systolic')
            .setValue(data['vitalsigns']['blood_pressure_systolic']);
          this.consultForm
            .get('blood_pressure_diastolic')
            .setValue(data['vitalsigns']['blood_pressure_diastolic']);
          this.consultForm
            .get('temperature')
            .setValue(data['vitalsigns']['temperature']);
          this.consultForm
            .get('pulse_rate')
            .setValue(data['vitalsigns']['pulse_rate']);
          this.consultForm
            .get('auscultation')
            .setValue(data['vitalsigns']['auscultation']);
          this.consultForm.get('ecg').setValue(data['vitalsigns']['ecg']);
          this.consultForm.get('spo2').setValue(data['vitalsigns']['spo2']);
          this.consultForm
            .get('additional_notes')
            .setValue(data['vitalsigns']['additional_notes']);
        }
        if (data['medicalhistory']) {
          this.female = data['medicalhistory']['female'];
          console.log(data['medicalhistory']['last_menstrual_period']);
          this.consultForm
            .get('immunization_history')
            .setValue(data['medicalhistory']['immunization_history']);
          this.consultForm
            .get('past_medical_history')
            .setValue(data['medicalhistory']['past_medical_history']);
          this.consultForm
            .get('appetite')
            .setValue(data['medicalhistory']['appetite']);
          this.consultForm
            .get('social_history')
            .setValue(data['medicalhistory']['social_history']);
          this.consultForm.get('diet').setValue(data['medicalhistory']['diet']);
          this.consultForm
            .get('thirst')
            .setValue(data['medicalhistory']['thirst']);
          this.consultForm
            .get('sleep')
            .setValue(data['medicalhistory']['sleep']);
          this.consultForm
            .get('smoking')
            .setValue(data['medicalhistory']['smoking']);
          this.consultForm
            .get('alcohol')
            .setValue(data['medicalhistory']['alcohol']);
          this.consultForm
            .get('drugs')
            .setValue(data['medicalhistory']['drugs']);
          this.consultForm
            .get('sexual_history')
            .setValue(data['medicalhistory']['sexual_history']);
          this.consultForm
            .get('other_observations')
            .setValue(data['medicalhistory']['other_observations']);
          this.consultForm
            .get('age_of_menarche')
            .setValue(data['medicalhistory']['age_of_menarche']);
          this.consultForm
            .get('menstrual_history')
            .setValue(data['medicalhistory']['menstrual_history']);
          if (data['medicalhistory']['last_menstrual_period'] !== null) {
            this.consultForm
              .get('last_menstrual_period')
              .setValue(
                moment(data['medicalhistory']['last_menstrual_period']).format(
                  'DD-MM-YYYY'
                )
              );
            this.lmpDate = data['medicalhistory']['last_menstrual_period'];
          }

          this.consultForm
            .get('number_of_pregnancy')
            .setValue(data['medicalhistory']['number_of_pregnancy']);
          this.consultForm
            .get('gravida')
            .setValue(data['medicalhistory']['gravida']);
          this.consultForm.get('para').setValue(data['medicalhistory']['para']);
          this.consultForm
            .get('abortions')
            .setValue(data['medicalhistory']['abortions']);

          this.consultForm
            .get('history_of_present_illness')
            .setValue(data['medicalhistory']['history_of_present_illness']);
          this.consultForm
            .get('chief_complaint')
            .setValue(data['medicalhistory']['chief_complaint']);
          this.consultForm
            .get('gynaecological_history')
            .setValue(data['medicalhistory']['gynaecological_history']);
          //  this.consultForm.get('gender').setValue(data['medicalhistory']['female']);
        }
        if (data['physicalexamination']) {
          this.consultForm
            .get('weight')
            .setValue(data['physicalexamination']['weight']);
          this.consultForm
            .get('height')
            .setValue(data['physicalexamination']['height']);
          this.consultForm
            .get('bmi')
            .setValue(data['physicalexamination']['bmi']);
          this.consultForm
            .get('nutrition')
            .setValue(data['physicalexamination']['nutrition']);
          this.consultForm
            .get('nail_changes')
            .setValue(data['physicalexamination']['nail_changes']);
          this.consultForm
            .get('clubbing_of_fingers')
            .setValue(data['physicalexamination']['clubbing_of_fingers']);
          this.consultForm
            .get('cyanosis')
            .setValue(data['physicalexamination']['cyanosis']);
          this.consultForm
            .get('icterus_jaundice')
            .setValue(data['physicalexamination']['icterus_jaundice']);
          this.consultForm
            .get('pallor')
            .setValue(data['physicalexamination']['pallor']);
          this.consultForm
            .get('lymph_nodes')
            .setValue(data['physicalexamination']['lymph_nodes']);
          this.consultForm
            .get('oedema')
            .setValue(data['physicalexamination']['oedema']);
          this.consultForm
            .get('sclera')
            .setValue(data['physicalexamination']['sclera']);
        }
        if (data['systemicexamination']) {
          this.consultForm
            .get('respiratory_system')
            .setValue(data['systemicexamination']['respiratory_system']);
          this.consultForm
            .get('gastro_intestinal_system')
            .setValue(data['systemicexamination']['gastro_intestinal_system']);
          this.consultForm
            .get('cardio_vascular_system')
            .setValue(data['systemicexamination']['cardio_vascular_system']);
          this.consultForm
            .get('genitourinary_system')
            .setValue(data['systemicexamination']['genitourinary_system']);
          this.consultForm
            .get('musculoskeletal_system')
            .setValue(data['systemicexamination']['musculoskeletal_system']);
          this.consultForm
            .get('central_nervous_system')
            .setValue(data['systemicexamination']['central_nervous_system']);
          this.consultForm
            .get('eye')
            .setValue(data['systemicexamination']['eye']);
          this.consultForm
            .get('ear')
            .setValue(data['systemicexamination']['ear']);
          this.consultForm
            .get('nose')
            .setValue(data['systemicexamination']['nose']);
          this.consultForm
            .get('mouth')
            .setValue(data['systemicexamination']['mouth']);
          this.consultForm
            .get('throat')
            .setValue(data['systemicexamination']['throat']);
          this.consultForm
            .get('neck')
            .setValue(data['systemicexamination']['neck']);
          this.consultForm
            .get('skin')
            .setValue(data['systemicexamination']['skin']);
          this.consultForm
            .get('psychiatric_history')
            .setValue(data['systemicexamination']['psychiatric_history']);

        }
        if (data['diagnosis']) {
          this.consultForm
            .get('primary_diagnosis')
            .setValue(data['diagnosis']['primary_diagnosis']);
          this.consultForm
            .get('secondary_diagnosis')
            .setValue(data['diagnosis']['secondary_diagnosis']);
          this.consultForm
            .get('differential_diagnosis')
            .setValue(data['diagnosis']['differential_diagnosis']);
          this.consultForm
            .get('final_diagnosis')
            .setValue(data['diagnosis']['final_diagnosis']);
          this.consultForm
            .get('icd_10_codes')
            .setValue(data['diagnosis']['icd_10_codes']);
        }

        // if (data['referal']) {
        //   this.consultForm
        //     .get('doctorType')
        //     .setValue(data['referal']['doctorType']);
        //   if (this.consultForm.value.doctorType == 2) {
        //     this.consultForm
        //       .get('hospitalId')
        //       .setValue(data['referal']['hospitalId']);
        //   }
        //   this.consultForm
        //     .get('deptName')
        //     .setValue(data['referal']['deptName']);
        //   this.consultForm
        //     .get('deptId')
        //     .setValue(data['referal']['deptId']);
        //   this.consultForm
        //     .get('doctorId')
        //     .setValue(data['referal']['doctorId']);
        //   this.consultForm
        //     .get('referReason')
        //     .setValue(data['referal']['referReason']);
        // }

        if (
          (consultation_status == 'Not Started' ||
            consultation_status == 'Started') &&
          this.userType == 'Doctor'
        ) {
          this.detailView = false;
          this.loading = false;
        } else {
          this.detailView = true;
          this.dataOnly();
          this.investigationForm.disable();
          // this.consultForm.disable();
          this.consultForm.controls['immunization_history'].disable();
          this.consultForm.controls['past_medical_history'].disable();
          this.consultForm.controls['appetite'].disable();
          this.consultForm.controls['diet_history'].disable();
          this.consultForm.controls['thirst'].disable();
          this.consultForm.controls['sleep'].disable();
          this.consultForm.controls['habits'].disable();
          this.consultForm.controls['smoking'].disable();
          this.consultForm.controls['alcohol'].disable();
          this.consultForm.controls['drugs'].disable();
          this.consultForm.controls['sexual_history'].disable();
          this.consultForm.controls['notes'].disable();
          this.consultForm.controls['gender'].disable();
          this.consultForm.controls['gynaecological_history'].disable();
          this.consultForm.controls['age_of_menarche'].disable();
          this.consultForm.controls['menstrual_history'].disable();
          this.consultForm.controls['last_menstrual_period'].disable();
          this.consultForm.controls['number_of_pregnancy'].disable();
          this.consultForm.controls['gravida'].disable();
          this.consultForm.controls['para'].disable();
          this.consultForm.controls['abortions'].disable();
          this.consultForm.controls['social_history'].disable();
          this.consultForm.controls['other_observations'].disable();
          this.consultForm.controls['medicine'].disable();
          this.consultForm.controls['diet'].disable();
          this.consultForm.controls['recommendations'].disable();
          this.consultForm.controls['chief_complaint'].disable();
          this.consultForm.controls['history_of_present_illness'].disable();
          this.consultForm.controls['weight'].disable();
          this.consultForm.controls['height'].disable();
          this.consultForm.controls['bmi'].disable();
          this.consultForm.controls['built'].disable();
          this.consultForm.controls['nutrition'].disable();
          this.consultForm.controls['clubbing_of_fingers'].disable();
          this.consultForm.controls['nail_changes'].disable();
          this.consultForm.controls['cyanosis'].disable();
          this.consultForm.controls['icterus_jaundice'].disable();
          this.consultForm.controls['pallor'].disable();
          this.consultForm.controls['lymph_nodes'].disable();
          this.consultForm.controls['oedema'].disable();
          this.consultForm.controls['sclera'].disable();
          this.consultForm.controls['respiratory_system'].disable();
          this.consultForm.controls['gastro_intestinal_system'].disable();
          this.consultForm.controls['cardio_vascular_system'].disable();
          this.consultForm.controls['genitourinary_system'].disable();
          this.consultForm.controls['musculoskeletal_system'].disable();
          this.consultForm.controls['central_nervous_system'].disable();
          this.consultForm.controls['eye'].disable();
          this.consultForm.controls['ear'].disable();
          this.consultForm.controls['nose'].disable();
          this.consultForm.controls['mouth'].disable();
          this.consultForm.controls['throat'].disable();
          this.consultForm.controls['neck'].disable();
          this.consultForm.controls['skin'].disable();
          this.consultForm.controls['psychiatric_history'].disable();
          this.consultForm.controls['primary_diagnosis'].disable();
          this.consultForm.controls['secondary_diagnosis'].disable();
          this.consultForm.controls['differential_diagnosis'].disable();
          this.consultForm.controls['final_diagnosis'].disable();
          this.consultForm.controls['icd_10_codes'].disable();
          this.consultForm.controls['blood_pressure_systolic'].enable();
          this.consultForm.controls['blood_pressure_diastolic'].enable();
          this.consultForm.controls['pulse_rate'].enable();
          this.consultForm.controls['spo2'].enable();
          this.consultForm.controls['temperature'].enable();
          this.consultForm.controls['auscultation'].enable();
          this.consultForm.controls['ecg'].enable();
          this.consultForm.controls['additional_notes'].enable();
          this.prescriptionForm.disable();
          this.prescriptionForm.disable();

          this.consultForm.controls['doctorType'].disable();
          this.consultForm.controls['hospitalId'].disable();
          this.consultForm.controls['deptName'].disable();
          this.consultForm.controls['deptId'].disable();
          this.consultForm.controls['doctorId'].disable();
          this.consultForm.controls['referReason'].disable();

          this.loading = false;
        }
        this.loading = false;
      });
  }
  //Investigation functionalities
  updateInvestigation(category, event) {

    if (!this.addedInvestigationCategories.includes(category)) {
      this.addedInvestigationCategories.push(category);
    }
    this.investigations[category] = event;
    console.log(event);
  }
  getSpecialInstructionValue(event) {
    this.special_instructions = this.investigationForm.get(
      'special_instructions'
    ).value;
  }

  processInvestigation(category) {
    if (category !== 'special_instructions') {
      const investigations_added = this.investigations[category];
      this.investigationsPayload = this.investigationsPayload.filter(
        (investig) => investig.category !== category
      );
      if (investigations_added.length > 0) {
        const inst = this.investigationsTaxonomy[category];
        console.log(inst);
        investigations_added.forEach((element) => {
          const value = inst.filter(
            (obj) => obj.investigation_name == element['investigation_name']
          );
          // console.log(value);
          const investData = {
            uuid: value?.[0]?.['uuid'] || null,
            category: category,
            investigation_name: element['investigation_name'],
          };

          this.investigationsPayload.push(investData);
          console.log(investData);

        });
        console.log(this.investigationsPayload);
      }
    }

    const data = {
      investigation: {
        key_advice: 'Test_description',
        other_observations: 'Test_description',
        special_instructions: this.special_instructions,
        tests_prescribed: {
          tests: this.investigationsPayload,
        },
      },
    };
    this.teleConsultService
      .addPrescription(this.consultationId, data)
      .subscribe(
        (data) => {
          if (this.investigationsPayload.length > 0) {
            const lastElement = this.investigationsPayload[this.investigationsPayload.length - 1];
            if (!lastElement['uuid']) {
              this.getInvestigation();
              this.investigationsPayload = data['investigation']['tests_prescribed']['tests'];
            }

          }

        },
        (error) => {
          const validationErrorMsg = error?.['error']?.['error_message'];
          if (validationErrorMsg.includes('already exists')) {
            const lastElement = this.investigationsPayload[this.investigationsPayload.length - 1];
            let category = lastElement['category'];
            const investigation_name = lastElement['investigation_name'];
            console.log(investigation_name, category);
            if (category == 'HAEMATOLOGY') {
              this.haematologyItems = this.haematologyItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'BIOCHEMISTRY AND IMMUNOASSAYS') {
              this.biochemistryItems = this.biochemistryItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'MICROBIOLOGY') {
              this.microbiologyItems = this.microbiologyItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'CLINICAL PATHOLOGY') {
              this.clinicalPathologyItems = this.clinicalPathologyItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'PATHOLOGY') {
              this.pathologyItems = this.pathologyItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'SEROLOGY') {
              this.serologyItems = this.serologyItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'MALARIA') {
              this.malariaItems = this.malariaItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'FILARIASIS') {
              this.filariasisItems = this.filariasisItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'dengue') {
              this.dengueItems = this.dengueItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'JAPANESE ENCEPHALITIS') {
              this.japaneseEncephalitisItems = this.japaneseEncephalitisItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'CHIKUNGUNYA') {
              this.chikungunyaItems = this.chikungunyaItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'SCRUB TYPHUS') {
              this.scrubTyphusItems = this.scrubTyphusItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'LEPTOSPIROSIS') {
              this.leptospirosisItems = this.leptospirosisItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'BRUCELLOSIS') {
              this.brucellosisItems = this.brucellosisItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'TUBERCULOSIS') {
              this.tuberculosisItems = this.tuberculosisItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'HIV') {
              this.hivItems = this.hivItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'HEPATITIS B') {
              this.hepatitisBItems = this.hepatitisBItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'HEPATITIS C') {
              this.hepatitisCItems = this.hepatitisCItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'HEPATITIS A') {
              this.hepatitisAItems = this.hepatitisAItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'HEPATITIS E') {
              this.hepatitisEItems = this.hepatitisEItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'HBC (CORE ANTIBODIES)') {
              this.hbcItems = this.hbcItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'OTHER DIAGNOSTIC TESTS') {
              this.otherDiagnosticTestsItems = this.otherDiagnosticTestsItems.filter(obj => obj.investigation_name !== investigation_name)
            } else if (category == 'RADIOLOGY & OTHER DIAGNOSTIC TESTS') {
              this.radiologyAndOtherDiagnosticTestsItems = this.radiologyAndOtherDiagnosticTestsItems.filter(obj => obj.investigation_name !== investigation_name)
            }
            this.resetInvestigationFormValue(category);
          }
          console.log(error);
          this.notificationService.error(error?.['error']?.['error_message']);
        }
      );


  }

  resetInvestigationFormValue(category) {
    this.investigationFormValue = [];
    for (let val of this.investigationsPayload) {
      if (category == val.category) {
        this.investigationFormValue.push(val.uuid);
      }
    }
    console.log(this.investigationFormValue);
    if (this.investigationFormValue.length > 0) {
      if (category === 'HAEMATOLOGY') {
        this.investigationForm.controls[`haematology`].setValue(this.investigationFormValue);
      } else if (category === 'BIOCHEMISTRY AND IMMUNOASSAYS') {
        this.investigationForm.controls[`biochemistryAndImmunoassay`].setValue(this.investigationFormValue);
      } else if (category === 'MICROBIOLOGY') {
        this.investigationForm.controls[`microbiology`].setValue(this.investigationFormValue);
      } else if (category === 'CLINICAL PATHOLOGY') {
        this.investigationForm.controls[`clinicalPathology`].setValue(this.investigationFormValue);
      } else if (category === 'PATHOLOGY') {
        this.investigationForm.controls[`pathology`].setValue(this.investigationFormValue);
      } else if (category === 'SEROLOGY') {
        this.investigationForm.controls[`serology`].setValue(this.investigationFormValue);
      } else if (category === 'MALARIA') {
        this.investigationForm.controls[`malaria`].setValue(this.investigationFormValue);
      } else if (category === 'FILARIASIS') {
        this.investigationForm.controls[`filariasis`].setValue(this.investigationFormValue);
      } else if (category === 'DENGUE') {
        this.investigationForm.controls[`dengue`].setValue(this.investigationFormValue);
      } else if (category === 'JAPANESE ENCEPHALITIS') {
        this.investigationForm.controls[`japaneseEncephalitis`].setValue(this.investigationFormValue);
      } else if (category === 'CHIKUNGUNYA') {
        this.investigationForm.controls[`chikungunya`].setValue(this.investigationFormValue);
      } else if (category === 'SCRUB TYPHUS') {
        this.investigationForm.controls[`scrubTyphus`].setValue(this.investigationFormValue);
      } else if (category === 'LEPTOSPIROSIS') {
        this.investigationForm.controls[`leptospirosis`].setValue(this.investigationFormValue);
      } else if (category === 'BRUCELLOSIS') {
        this.investigationForm.controls[`brucellosis`].setValue(this.investigationFormValue);
      } else if (category === 'TUBERCULOSIS') {
        this.investigationForm.controls[`tuberculosis`].setValue(this.investigationFormValue);
      } else if (category === 'HIV') {
        this.investigationForm.controls[`hiv`].setValue(this.investigationFormValue);
      } else if (category === 'HEPATITIS B') {
        this.investigationForm.controls[`hepatitisB`].setValue(this.investigationFormValue);
      } else if (category === 'HEPATITIS C') {
        this.investigationForm.controls[`hepatitisC`].setValue(this.investigationFormValue);
      } else if (category === 'HEPATITIS A') {
        this.investigationForm.controls[`hepatitisA`].setValue(this.investigationFormValue);
      } else if (category === 'HEPATITIS E') {
        this.investigationForm.controls[`hepatitisE`].setValue(this.investigationFormValue);
      } else if (category === 'HBC (CORE ANTIBODIES)') {
        this.investigationForm.controls[`hbc`].setValue(this.investigationFormValue);
      } else if (category === 'OTHER DIAGNOSTIC TESTS') {
        this.investigationForm.controls[`otherDiagnosticTest`].setValue(this.investigationFormValue);
      } else if (category === 'RADIOLOGY & OTHER DIAGNOSTIC TESTS') {
        this.investigationForm.controls[`radiologyAndOtherDiagnostics`].setValue(this.investigationFormValue);
      }
      this.investigationsPayload.splice(-1, 1);
    } else {
      this.setInvestigationValues(category);
    }

  }
  setInvestigationValues(category) {
    if (category === 'HAEMATOLOGY') {
      this.investigationForm.controls[`haematology`].setValue(this.investigationFormValue);
    } else if (category === 'BIOCHEMISTRY AND IMMUNOASSAYS') {
      this.investigationForm.controls[`biochemistryAndImmunoassay`].setValue(this.investigationFormValue);
    } else if (category === 'MICROBIOLOGY') {
      this.investigationForm.controls[`microbiology`].setValue(this.investigationFormValue);
    } else if (category === 'CLINICAL PATHOLOGY') {
      this.investigationForm.controls[`clinicalPathology`].setValue(this.investigationFormValue);
    } else if (category === 'PATHOLOGY') {
      this.investigationForm.controls[`pathology`].setValue(this.investigationFormValue);
    } else if (category === 'SEROLOGY') {
      this.investigationForm.controls[`serology`].setValue(this.investigationFormValue);
    } else if (category === 'MALARIA') {
      this.investigationForm.controls[`malaria`].setValue(this.investigationFormValue);
    } else if (category === 'FILARIASIS') {
      this.investigationForm.controls[`filariasis`].setValue(this.investigationFormValue);
    } else if (category === 'DENGUE') {
      this.investigationForm.controls[`dengue`].setValue(this.investigationFormValue);
    } else if (category === 'JAPANESE ENCEPHALITIS') {
      this.investigationForm.controls[`japaneseEncephalitis`].setValue(this.investigationFormValue);
    } else if (category === 'CHIKUNGUNYA') {
      this.investigationForm.controls[`chikungunya`].setValue(this.investigationFormValue);
    } else if (category === 'SCRUB TYPHUS') {
      this.investigationForm.controls[`scrubTyphus`].setValue(this.investigationFormValue);
    } else if (category === 'LEPTOSPIROSIS') {
      this.investigationForm.controls[`leptospirosis`].setValue(this.investigationFormValue);
    } else if (category === 'BRUCELLOSIS') {
      this.investigationForm.controls[`brucellosis`].setValue(this.investigationFormValue);
    } else if (category === 'TUBERCULOSIS') {
      this.investigationForm.controls[`tuberculosis`].setValue(this.investigationFormValue);
    } else if (category === 'HIV') {
      this.investigationForm.controls[`hiv`].setValue(this.investigationFormValue);
    } else if (category === 'HEPATITIS B') {
      this.investigationForm.controls[`hepatitisB`].setValue(this.investigationFormValue);
    } else if (category === 'HEPATITIS C') {
      this.investigationForm.controls[`hepatitisC`].setValue(this.investigationFormValue);
    } else if (category === 'HEPATITIS A') {
      this.investigationForm.controls[`hepatitisA`].setValue(this.investigationFormValue);
    } else if (category === 'HEPATITIS E') {
      this.investigationForm.controls[`hepatitisE`].setValue(this.investigationFormValue);
    } else if (category === 'HBC (CORE ANTIBODIES)') {
      this.investigationForm.controls[`hbc`].setValue(this.investigationFormValue);
    } else if (category === 'OTHER DIAGNOSTIC TESTS') {
      this.investigationForm.controls[`otherDiagnosticTest`].setValue(this.investigationFormValue);
    } else if (category === 'RADIOLOGY & OTHER DIAGNOSTIC TESTS') {
      this.investigationForm.controls[`radiologyAndOtherDiagnostics`].setValue(this.investigationFormValue);
    }
    this.investigationsPayload.splice(-1, 1);
  }

  renderInvestigations(data) {
    if (this.userType == 'Doctor') {
      data.forEach((invObject) => {
        if (invObject.category == 'HAEMATOLOGY') {
          let haem = this.investigationForm.get('haematology').value;
          if (!this.haemIdList.includes(invObject['uuid'])) {
            this.haematologyItems.push(invObject);
          }
          if (!haem.includes(invObject['uuid'])) {
            haem.push(invObject['uuid']);
            this.investigationForm.get('haematology').patchValue(haem);
          }
        }

        if (invObject.category == 'BIOCHEMISTRY AND IMMUNOASSAYS') {
          let bioc = this.investigationForm.get('biochemistryAndImmunoassay')
            .value;
          if (!this.bioChemIdList.includes(invObject['uuid'])) {
            this.bioChemIdList.push(invObject);
          }
          if (!bioc.includes(invObject['uuid'])) {
            bioc.push(invObject['uuid']);
            this.investigationForm
              .get('biochemistryAndImmunoassay')
              .patchValue(bioc);
          }
        }

        if (invObject.category == 'MICROBIOLOGY') {
          let micbio = this.investigationForm.get('microbiology').value;
          if (!this.microBioIdList.includes(invObject['uuid'])) {
            this.microbiologyItems.push(invObject);
          }
          if (!micbio.includes(invObject['uuid'])) {
            micbio.push(invObject['uuid']);
            this.investigationForm.get('microbiology').patchValue(micbio);
          }
        }

        if (invObject.category == 'CLINICAL PATHOLOGY') {
          let clinicPath = this.investigationForm.get('clinicalPathology').value;
          if (!this.clinicPathIdList.includes(invObject['uuid'])) {
            this.clinicalPathologyItems.push(invObject);
          }
          if (!clinicPath.includes(invObject['uuid'])) {
            clinicPath.push(invObject['uuid']);
            this.investigationForm
              .get('clinicalPathology')
              .patchValue(clinicPath);
          }
        }

        if (invObject.category == 'PATHOLOGY') {
          let path = this.investigationForm.get('pathology').value;
          if (!this.pathIdList.includes(invObject['uuid'])) {
            this.pathologyItems.push(invObject);
          }
          if (!path.includes(invObject['uuid'])) {
            path.push(invObject['uuid']);
            this.investigationForm.get('pathology').patchValue(path);
          }
        }

        if (invObject.category == 'SEROLOGY') {
          let sero = this.investigationForm.get('serology').value;
          if (!this.serIdList.includes(invObject['uuid'])) {
            this.serologyItems.push(invObject);
          }
          if (!sero.includes(invObject['uuid'])) {
            sero.push(invObject['uuid']);
            this.investigationForm.get('serology').patchValue(sero);
          }
        }

        if (invObject.category == 'MALARIA') {
          let mal = this.investigationForm.get('malaria').value;
          if (!this.malIdList.includes(invObject['uuid'])) {
            this.malariaItems.push(invObject);
          }
          if (!mal.includes(invObject['uuid'])) {
            mal.push(invObject['uuid']);
            this.investigationForm.get('malaria').patchValue(mal);
          }
        }

        if (invObject.category == 'FILARIASIS') {
          let fil = this.investigationForm.get('filariasis').value;
          if (!this.filIdList.includes(invObject['uuid'])) {
            this.filariasisItems.push(invObject);
          }
          if (!fil.includes(invObject['uuid'])) {
            fil.push(invObject['uuid']);
            this.investigationForm.get('filariasis').patchValue(fil);
          }
        }

        if (invObject.category == 'DENGUE') {
          let den = this.investigationForm.get('dengue').value;
          if (!this.denIdList.includes(invObject['uuid'])) {
            this.dengueItems.push(invObject);
          }
          if (!den.includes(invObject['uuid'])) {
            den.push(invObject['uuid']);
            this.investigationForm.get('dengue').patchValue(den);
          }
        }

        if (invObject.category == 'JAPANESE ENCEPHALITIS') {
          let jp = this.investigationForm.get('japaneseEncephalitis').value;
          if (!this.japanEncIdList.includes(invObject['uuid'])) {
            this.japaneseEncephalitisItems.push(invObject);
          }
          if (!jp.includes(invObject['uuid'])) {
            jp.push(invObject['uuid']);
            this.investigationForm.get('japaneseEncephalitis').patchValue(jp);
          }
        }

        if (invObject.category == 'CHIKUNGUNYA') {
          let chik = this.investigationForm.get('chikungunya').value;
          if (!this.chikIdList.includes(invObject['uuid'])) {
            this.chikungunyaItems.push(invObject);
          }
          if (!chik.includes(invObject['uuid'])) {
            chik.push(invObject['uuid']);
            this.investigationForm.get('chikungunya').patchValue(chik);
          }
        }

        if (invObject.category == 'SCRUB TYPHUS') {
          let scrubTyp = this.investigationForm.get('scrubTyphus').value;
          if (!this.scrupTyphIdList.includes(invObject['uuid'])) {
            this.scrubTyphusItems.push(invObject);
          }
          if (!scrubTyp.includes(invObject['uuid'])) {
            scrubTyp.push(invObject['uuid']);
            this.investigationForm.get('scrubTyphus').patchValue(scrubTyp);
          }
        }

        if (invObject.category == 'LEPTOSPIROSIS') {
          let lepto = this.investigationForm.get('leptospirosis').value;
          if (!this.leptIdList.includes(invObject['uuid'])) {
            this.leptospirosisItems.push(invObject);
          }
          if (!lepto.includes(invObject['uuid'])) {
            lepto.push(invObject['uuid']);
            this.investigationForm.get('leptospirosis').patchValue(lepto);
          }
        }

        if (invObject.category == 'BRUCELLOSIS') {
          let bruce = this.investigationForm.get('brucellosis').value;
          if (!this.bruceId.includes(invObject['uuid'])) {
            this.brucellosisItems.push(invObject);
          }
          if (!bruce.includes(invObject['uuid'])) {
            bruce.push(invObject['uuid']);
            this.investigationForm.get('brucellosis').patchValue(bruce);
          }
        }

        if (invObject.category == 'TUBERCULOSIS') {
          let tb = this.investigationForm.get('tuberculosis').value;
          if (!this.tuberIdList.includes(invObject['uuid'])) {
            this.tuberculosisItems.push(invObject);
          }
          if (!tb.includes(invObject['uuid'])) {
            tb.push(invObject['uuid']);
            this.investigationForm.get('tuberculosis').patchValue(tb);
          }
        }

        if (invObject.category == 'HIV') {
          let hiv = this.investigationForm.get('hiv').value;
          if (!this.hivIdList.includes(invObject['uuid'])) {
            this.hivItems.push(invObject);
          }
          if (!hiv.includes(invObject['uuid'])) {
            hiv.push(invObject['uuid']);
            this.investigationForm.get('hiv').patchValue(hiv);
          }
        }

        if (invObject.category == 'HEPATITIS B') {
          let hepB = this.investigationForm.get('hepatitisB').value;
          if (!this.hepBIdList.includes(invObject['uuid'])) {
            this.hepatitisBItems.push(invObject);
          }
          if (!hepB.includes(invObject['uuid'])) {
            hepB.push(invObject['uuid']);
            this.investigationForm.get('hepatitisB').patchValue(hepB);
          }
        }

        if (invObject.category == 'HEPATITIS C') {
          let hepC = this.investigationForm.get('hepatitisC').value;
          if (!this.hepCIdList.includes(invObject['uuid'])) {
            this.hepatitisCItems.push(invObject);
          }
          if (!hepC.includes(invObject['uuid'])) {
            hepC.push(invObject['uuid']);
            this.investigationForm.get('hepatitisC').patchValue(hepC);
          }
        }

        if (invObject.category == 'HEPATITIS A') {
          let hepA = this.investigationForm.get('hepatitisA').value;
          if (!this.hepAIdList.includes(invObject['uuid'])) {
            this.hepatitisAItems.push(invObject);
          }
          if (!hepA.includes(invObject['uuid'])) {
            hepA.push(invObject['uuid']);
            this.investigationForm.get('hepatitisA').patchValue(hepA);
          }
        }

        if (invObject.category == 'HEPATITIS E') {
          let hepE = this.investigationForm.get('hepatitisE').value;
          if (!this.hepEIdList.includes(invObject['uuid'])) {
            this.hepatitisEItems.push(invObject);
          }
          if (!hepE.includes(invObject['uuid'])) {
            hepE.push(invObject['uuid']);
            this.investigationForm.get('hepatitisE').patchValue(hepE);
          }
        }

        if (invObject.category == 'HBC (CORE ANTIBODIES)') {
          let hbc = this.investigationForm.get('hbc').value;
          if (!this.hbcIdList.includes(invObject['uuid'])) {
            this.hbcItems.push(invObject);
          }
          if (!hbc.includes(invObject['uuid'])) {
            hbc.push(invObject['uuid']);
            this.investigationForm.get('hbc').patchValue(hbc);
          }
        }

        if (invObject.category == 'OTHER DIAGNOSTIC TESTS') {
          let odt = this.investigationForm.get('otherDiagnosticTest').value;
          if (!this.odtIdList.includes(invObject['uuid'])) {
            this.otherDiagnosticTestsItems.push(invObject);
          }
          if (!odt.includes(invObject['uuid'])) {
            odt.push(invObject['uuid']);
            this.investigationForm.get('otherDiagnosticTest').patchValue(odt);
          }
        }

        if (invObject.category == 'RADIOLOGY & OTHER DIAGNOSTIC TESTS') {
          let rodt = this.investigationForm.get('radiologyAndOtherDiagnostics')
            .value;
          if (!this.radDiaIdList.includes(invObject['uuid'])) {
            this.radiologyAndOtherDiagnosticTestsItems.push(invObject);
          }
          if (!rodt.includes(invObject['uuid'])) {
            rodt.push(invObject['uuid']);
            this.investigationForm
              .get('radiologyAndOtherDiagnostics')
              .patchValue(rodt);
          }
        }
      });
    } else if (this.userType == 'Patient') {
      this.investigationForm.reset();
      data.forEach((invObject) => {
        if (invObject.category == 'HAEMATOLOGY') {
          this.investigationForm.controls[`haematology`].setValue([invObject['uuid']]);
        }

        if (invObject.category == 'BIOCHEMISTRY AND IMMUNOASSAYS') {
          this.investigationForm.controls[`biochemistryAndImmunoassay`].setValue([invObject['uuid']]);


        }

        if (invObject.category == 'MICROBIOLOGY') {
          this.investigationForm.controls[`microbiology`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'CLINICAL PATHOLOGY') {
          this.investigationForm.controls[`clinicalPathology`].setValue([invObject['uuid']]);
        }

        if (invObject.category == 'PATHOLOGY') {
          this.investigationForm.controls[`pathology`].setValue([invObject['uuid']]);
        }

        if (invObject.category == 'SEROLOGY') {
          this.investigationForm.controls[`serology`].setValue([invObject['uuid']]);
        }

        if (invObject.category == 'MALARIA') {
          this.investigationForm.controls[`malaria`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'FILARIASIS') {
          this.investigationForm.controls[`filariasis`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'DENGUE') {
          this.investigationForm.controls[`dengue`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'JAPANESE ENCEPHALITIS') {
          this.investigationForm.controls[`japaneseEncephalitis`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'CHIKUNGUNYA') {
          this.investigationForm.controls[`chikungunya`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'SCRUB TYPHUS') {
          this.investigationForm.controls[`scrubTyphus`].setValue([invObject['uuid']]);
        }

        if (invObject.category == 'LEPTOSPIROSIS') {
          this.investigationForm.controls[`leptospirosis`].setValue([invObject['uuid']]);
        }

        if (invObject.category == 'BRUCELLOSIS') {
          this.investigationForm.controls[`brucellosis`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'TUBERCULOSIS') {
          this.investigationForm.controls[`tuberculosis`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'HIV') {
          this.investigationForm.controls[`hiv`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'HEPATITIS B') {
          this.investigationForm.controls[`hepatitisB`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'HEPATITIS C') {

          this.investigationForm.controls[`hepatitisC`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'HEPATITIS A') {
          this.investigationForm.controls[`hepatitisA`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'HEPATITIS E') {
          this.investigationForm.controls[`hepatitisE`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'HBC (CORE ANTIBODIES)') {
          this.investigationForm.controls[`hbc`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'OTHER DIAGNOSTIC TESTS') {

          this.investigationForm.controls[`otherDiagnosticTest`].setValue([invObject['uuid']]);

        }

        if (invObject.category == 'RADIOLOGY & OTHER DIAGNOSTIC TESTS') {
          this.investigationForm.controls[`radiologyAndOtherDiagnostics`].setValue([invObject['uuid']]);
        }
      });
    }

  }

  consultationDataDirectSave(category, type, event) {
    const touched = this.consultForm.get(`${type}`).touched;
    const dirty = this.consultForm.get(`${type}`).dirty;
    console.log(touched, dirty);
    if (dirty) {
      // if(this.userType=='Doctor' &&dirty){
      if (
        category == 'physical_examination' &&
        (type == 'weight' || type == 'height')
      ) {
        if (
          (
            this.consultForm.get('weight').value !== null) &&
          this.consultForm.get('height').value !== null
        ) {
          console.log('saveBmi');
          this.saveBmi(category);
        }
      }

      let sdata: any;
      if (type == 'last_menstrual_period') {
        // console.log(this.lmpDate);
        sdata = event; //moment(event).format('YYYY-MM-DD');
        console.log(sdata);
      } else {
        if (event.target.value) {
          sdata = event.target.value;
        } else {
          sdata = null;
        }
      }
      let daat = {};
      daat[type] = sdata;
      let consultData = {};
      consultData[category] = daat;
      console.log(consultData);
      this.teleConsultService
        .addPrescription(this.consultationId, consultData)
        .subscribe(
          (data) => {
            // console.log(type);
            // this.consultForm.get(type).setValue(sdata);
          },
          (err) => {
            console.log('ERROR:' + err.message);
          }
        );
    }
  }

  delPrescription(i) {
    this.loadingPrescreption = true;
    this.prescriptionArray.removeAt(i);
    const value = this.prescriptionArray.value;
    this.prescriptionArray.clear();
    setTimeout(() => this.loadPrescriptionForm(value, 'Delete'), 100);
    console.log(this.prescriptionArray.length);
  }
  loadPrescriptionForm(value, event) {
    console.log(value);
    if (event == 'Delete') {
      for (let prescriptionData of value) {
        this.prescriptionArray.push(
          this.formBuilder.group({
            uuid: prescriptionData['uuid'],
            brand_name: new FormControl(
              [prescriptionData?.['brand_name'][0]],
              Validators.required
            ),
            medicine_type: new FormControl(
              prescriptionData?.['medicine_type'],
              Validators.required
            ),
            strength: new FormControl(
              prescriptionData?.['strength'],
              Validators.required
            ),
            dosage: new FormControl(
              prescriptionData?.dosage,
              Validators.required
            ),
            before_food: new FormControl(prescriptionData?.['before_food']),
            morning: new FormControl(prescriptionData?.morning),
            afternoon: new FormControl(prescriptionData?.afternoon),
            evening: new FormControl(prescriptionData?.evening),
            night: new FormControl(prescriptionData?.night),
            days: new FormControl(prescriptionData?.days, Validators.required),
            notes: new FormControl(
              prescriptionData?.notes,
              Validators.required
            ),
          })
        );
      }
    } else {
      for (let prescriptionData of value) {
        this.prescriptionArray.push(
          this.formBuilder.group({
            uuid: prescriptionData['uuid'],
            brand_name: new FormControl(
              [prescriptionData['brand_name']],
              Validators.required
            ),
            medicine_type: new FormControl(
              prescriptionData['medicine_type'],
              Validators.required
            ),
            strength: new FormControl(
              prescriptionData['strength'],
              Validators.required
            ),
            dosage: new FormControl(
              prescriptionData['administration_instructions']?.dosage,
              Validators.required
            ),
            before_food: new FormControl(prescriptionData['before_food']),
            morning: new FormControl(
              prescriptionData['administration_instructions']?.morning
            ),
            afternoon: new FormControl(
              prescriptionData['administration_instructions']?.afternoon
            ),
            evening: new FormControl(
              prescriptionData['administration_instructions']?.evening
            ),
            night: new FormControl(
              prescriptionData['administration_instructions']?.night
            ),
            days: new FormControl(
              prescriptionData['administration_instructions']?.duration_days,
              Validators.required
            ),
            notes: new FormControl(
              prescriptionData['administration_instructions']?.notes,
              Validators.required
            ),
          })
        );
      }
    }

    this.loadingPrescreption = false;
    console.log(this.prescriptionArray.length);
  }
  loadFemData(value) {
    console.log(value);
    this.female = value;
    let daat = {};
    if (!!value) {
      daat['female'] = true;
    } else {
      daat['female'] = false;
    }
    let consultData = {};
    consultData['medical_history'] = daat;
    this.teleConsultService
      .addPrescription(this.consultationId, consultData)
      .subscribe(
        (data) => {

        },
        (err) => {
          console.log('ERROR:' + err.message);
        }
      );
    // this.consultForm.get('last_menstrual_period').setValue(moment(new Date(this.savedData['medicalhistory']['last_menstrual_period'])).format('DD-MM-YYYY'));
    // console.log(moment(this.savedData['medicalhistory']['last_menstrual_period']).format('DD-MM-YYYY'));
  }

  getConsultaionDocuments(data) {
    if (data == 'Prescription') {
      this.teleConsultService.downloadPdf(this.consultationId, data).subscribe(result => {
        this.sharedService.downloadPdf(result, data, this.consultationId);
      })
    } else {
      this.teleConsultService.getDocument(this.consultationId, data).subscribe(
        (data) => {

          // if(data){
          //   this.openFile(data[0]['file']);
          // }else {
          //     this.notificationService.error('Pdf file not found')
          //   }
          const result = data['results'];
          if (result.length > 0) {
            window.open(result[0]['file']);
          } else {
            this.notificationService.error('No files found', 'Med.Bot');
          }
          this.consultationDocuments = data['results'];
          //  this.medicalHistory.push(data);
        },
        (error) => {
          console.log(error);
          this.disabledDownloadPrescriptionBtn = false;
          const status = error['status'];
          if (status == 400) {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          } else {
            this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
          }
        }
      );
    }
  }

  openFile(url) {
    const popUp = window.open(url);
    if (popUp == null || typeof (popUp) == 'undefined') {
      this.notificationService.warning('Please disable your pop-up blocker and download again.');
    }
    else {
      popUp.focus();
    }
  }

  downloadPrescription() {
    const data = 'Prescription';
    this.getConsultaionDocuments(data);
  }
  downloadInvestigation() {
    const data = 'Investigation';
    this.getConsultaionDocuments(data);
  }

  setSuspensionType(data) {
    this.suspendType = data;
  }

  processSuspension() {
    if (this.suspendType == 'Investigation') {
      this.teleConsultService
        .suspendConsultationInvestigationsPending(
          this.consultationId,
          this.suspendReason
        )
        .subscribe(
          (data) => {
            this.router.navigateByUrl('/doctor/dashboard');
            this.router.events.subscribe((val) => {
              const nvigationEnd = val instanceof NavigationEnd;
              if (!!nvigationEnd) {
                location.reload();
              }
            });
          },
          (err) => {
            console.log('ERROR:' + err.message);
            this.notificationService.error(err?.['error']?.['error_message']);
          }
        );
    }
    if (this.suspendType == 'Review') {
      this.teleConsultService
        .suspendConsultationReviewPending(
          this.consultationId,
          this.suspendReason
        )
        .subscribe(
          (data) => {
            this.router.navigateByUrl('/doctor/dashboard');
            this.router.events.subscribe((val) => {
              const nvigationEnd = val instanceof NavigationEnd;
              if (!!nvigationEnd) {
                location.reload();
              }
            });
          },
          (err) => {
            console.log('ERROR:' + err.message);
            this.notificationService.error(err?.['error']?.['error_message']);
          }
        );
    }
  }
  onPrescriptionComplete() {

    this.hidePrescriptionCompleteBtn = true;
    const data = this.prescriptionForm.get('prescriptionArray').value;
    this.completePrescriptionData(data);
    this.teleConsultService
      .addPrescription(this.consultationId, this.prescription)
      .subscribe(
        (data) => {
          this.showAddMedicine = true;
          this.isPrescriptionComplete = 'true';
          localStorage.setItem('isPrescriptionComplete', 'true');
          let prescriptionData =
            data['prescription']['prescription']['drugs_prescribed'];
          this.prescriptionArray.clear();
          this.loadPrescriptionForm(prescriptionData, 'Save');
          this.prescriptionArray.get;
          this.notificationService.success('Prescription Completed');
        },
        (error) => {
          this.notificationService.error(error?.['error']?.['error_message']);
          console.log(error);
        }
      );
  }
  completePrescriptionData(data) {
    console.log(data, 'ssh');
    let prescription_consultation_data = [];
    data.forEach((element) => {
      let days = [];
      let prescription = element?.brand_name;
      if (prescription.length > 0) {
        prescription.forEach((drug) => {
          const selectedDrug = this.drugs.filter(
            (selectedDrug) => selectedDrug.name == drug
          );
          console.log(selectedDrug);
          let preciseData = {
            uuid: selectedDrug?.[0]?.['uuid'] || null,
            brand_name: drug['name'] || prescription[0],
            medicine_type: element?.medicine_type || 'medicine',
            generic_name: drug['generic_name'] || null,
            before_food: element['before_food'],
            strength: element?.strength || null,
            system_of_medicine: this.system_of_medicine,
            administration_instructions: {
              // parts_of_day: days,
              morning: element?.morning || ' ',
              afternoon: element?.afternoon || ' ',
              evening: element?.evening || ' ',
              night: element?.night || ' ',
              duration_days: element['days'],
              sos: false,
              dosage: element?.dosage || null,
              notes: element['notes'],
            },
          };
          if (selectedDrug.length != 0) {
            preciseData['uuid'] = selectedDrug[0]['uuid'];
          } else {
            preciseData['uuid'] = null;
          }
          console.log(preciseData);
          prescription_consultation_data.push(preciseData);
        });
      } else {
        let pres_drug = this.drugs.filter(
          (drug) => drug.name == prescription[0]
        );
        pres_drug.forEach((drug) => {
          const selectedDrug = this.drugs.filter(
            (selectedDrug) => selectedDrug.name == drug
          );
          let preciseData = {
            uuid: drug['uuid'] || null,
            brand_name: drug['name'] || prescription[0],
            medicine_type: element?.medicine_type || 'medicine',
            generic_name: drug['generic_name'] || null,
            before_food: element['before_food'],
            strength: element?.strength || null,
            system_of_medicine: this.system_of_medicine,
            administration_instructions: {
              // parts_of_day: days,
              morning: element?.morning || ' ',
              afternoon: element?.afternoon || ' ',
              evening: element?.evening || ' ',
              night: element?.night || ' ',
              duration_days: element['days'],
              sos: false,
              dosage: element?.dosage || null,
              notes: element['notes'],
            },
          };
          if (selectedDrug.length != 0) {
            preciseData['uuid'] = selectedDrug[0]['uuid'];
          } else {
            preciseData['uuid'] = null;
          }
          console.log(preciseData);
          prescription_consultation_data.push(preciseData);
        });
      }
    });
    this.prescription = {
      prescription: {
        prescription: {
          drugs_prescribed: prescription_consultation_data,
        },
        is_draft: false,
      },
    };
    console.log(this.prescription);
  }

  deletePresCription(i) { }
  showPrescriptionTab() {
    this.showPrescription = true;
  }
  openPatientDashboard() {
    const data = { patient_exited: true }
    this.teleConsultService.leaveConsultation(this.consultationId, data).subscribe(data => {
      if (this.userType = 'Patient') {
        this.router.navigateByUrl('/patient/dashboard');
      } else if (this.userType == 'Partner') {
        this.hospitalService.setHospitalDetails();
      }
      this.router.events.subscribe((val) => {
        const nvigationEnd = val instanceof NavigationEnd;
        if (!!nvigationEnd) {
          location.reload();
        }
      });
    })

  }
  getDoctorDetails(id) {
    this.showDoctorProfilePic = false;
    this.doctorService.getdoctorProfileById(id).subscribe(
      (data) => {
        this.doctorInfo = data['user'];
        this.system_of_medicine = data['system_of_medicine'];
        if (this.doctorInfo['profile_picture']) {
          this.doctorProfilePicture = this.doctorInfo['profile_picture'];
        } else {
          this.doctorProfilePicture = 'assets/img/doctors/doctor-thumb-02.png';
        }

        data['qualifications'].map((qual) =>
          this.degreeList.push(qual['name'])
        );

        this.practiceLocations = data['practicelocations'];
        console.log(this.practiceLocations);
        for (let i = this.degreeList.length; i > 0; i--) {
          this.degreeString = this.degreeString + ', ' + this.degreeList[i - 1];
          this.degreeString = this.degreeString.substring(1);
        }
        setTimeout(() => {
          this.showDoctorProfilePic = true;
        }, 1000);
      },
      (error) => {
        console.log(error);
        this.showDoctorProfilePic = false;

        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        } else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );
  }
  getPatientProfile(id) {
    this.teleConsultService.getPatientProfile(id).subscribe(
      (data) => {
        this.personalInfo = data;
        if (this.personalInfo['profile_picture']) {
          this.profilePicture = this.personalInfo['profile_picture'];
        }
        setTimeout(() => {
          this.showProfilePic = true;
        }, 1000);
      },
      (error) => {
        this.showProfilePic = true;
        console.log(error);
      }
    );
  }
  changeVideoSize(value) {
    if (value === '3') {
      this.videoSize = 3;
    } else if (value === '2') {
      this.videoSize = 2;
    } else if (value === '1') {
      this.videoSize = 1;
    } else if (value === '4') {
      this.videoSize = 0;
    }
  }
  getDoseValues(data, i) {
    console.log(data.name);
    const presData = this.prescriptionArray.at(i);
    if (data.name === 'QID') {
      presData.get('morning').setValue('1');
      presData.get('afternoon').setValue('1');
      presData.get('evening').setValue('1');
      presData.get('night').setValue('1');
    } else if (data.name === 'TID') {
      presData.get('morning').setValue('1');
      presData.get('afternoon').setValue('1');
      presData.get('evening').setValue(' ');
      presData.get('night').setValue('1');
    } else if (data.name === 'STAT') {
      presData.get('morning').setValue(' ');
      presData.get('afternoon').setValue(' ');
      presData.get('evening').setValue(' ');
      presData.get('night').setValue(' ');
    } else if (data.name === 'OD') {
      presData.get('morning').setValue('1');
      presData.get('afternoon').setValue(' ');
      presData.get('evening').setValue(' ');
      presData.get('night').setValue('');
    } else if (data.name === 'BID/BD') {
      presData.get('morning').setValue('1');
      presData.get('afternoon').setValue(' ');
      presData.get('evening').setValue(' ');
      presData.get('night').setValue('1');
    } else if (data.name === 'HS') {
      presData.get('morning').setValue(' ');
      presData.get('afternoon').setValue(' ');
      presData.get('evening').setValue(' ');
      presData.get('night').setValue('1');
    }
  }
  getMedicineType() {
    this.teleConsultService.getMedicineType().subscribe(
      (data) => {
        this.medicineType = data;
      },
      (error) => {
        console.log(error);
      }
    );
  }
  getRecording() {
    let result = [];
    const purpose = 'Screenshot';
    this.teleConsultService.getRecordingData(this.consultationId).subscribe(
      (data) => {
        result = Object.values(data);
        console.log(result);
        this.recordingData = result.filter(obj => obj.file_status == 'Available');
        console.log(this.recordingData);

      },
      (error) => {
        console.log(error);
      }
    );
    this.teleConsultService.getDocument(this.consultationId, purpose).subscribe(
      (data) => {
        this.screenshot = data['results'];
        // if(this.screenshot.length===0){
        //   this.notificationService.error(' image not found ');
        // }
      },
      (error) => {
        console.log(error);
        this.disabledDownloadPrescriptionBtn = false;
        // const status = error['status'];
        // if (status == 400) {
        //   this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        // } else {
        //   this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        // }
      }
    );
  }
  openVideo(videoFile) {
    if (videoFile !== null) {
      window.open(videoFile);
    } else {
      this.notificationService.error('Video file not found');
    }
  }
  getDrugs() {
    this.teleConsultService.getDrugsTaxonomy().subscribe((data) => {
      this.drugs = Object.values(data);
    });
  }

  getInvestigation() {
    if (this.userType == 'Doctor') {
      this.teleConsultService.getInvestigationsTaxonomy().subscribe((data) => {
        this.investigationDataRender(data)
      });
    } else if (this.userType == 'Patient') {
      const query = 'doctor_id=' + this.doctor;
      this.teleConsultService.getInvestigationsTaxonomyData(query).subscribe((data) => {
        this.investigationDataRender(data)
      });
    }

  }
  investigationDataRender(data) {
    this.investigationsTaxonomy = data;
    Object.entries(this.investigationsTaxonomy).forEach(
      ([key, value]) => (this.investigations[key] = [])
    );
    this.haematologyItems = this.investigationsTaxonomy['HAEMATOLOGY'];
    this.haematologyItems.forEach((item) => {
      this.haemIdList.push(item.uuid);
    });
    this.biochemistryItems = this.investigationsTaxonomy[
      'BIOCHEMISTRY AND IMMUNOASSAYS'
    ];
    this.microbiologyItems = this.investigationsTaxonomy['MICROBIOLOGY'];
    this.clinicalPathologyItems = this.investigationsTaxonomy[
      'CLINICAL PATHOLOGY'
    ];
    this.pathologyItems = this.investigationsTaxonomy['PATHOLOGY'];
    this.serologyItems = this.investigationsTaxonomy['SEROLOGY'];
    this.malariaItems = this.investigationsTaxonomy['MALARIA'];
    this.filariasisItems = this.investigationsTaxonomy['FILARIASIS'];
    this.dengueItems = this.investigationsTaxonomy['DENGUE'];
    this.japaneseEncephalitisItems = this.investigationsTaxonomy[
      'JAPANESE ENCEPHALITIS'
    ];
    this.chikungunyaItems = this.investigationsTaxonomy['CHIKUNGUNYA'];
    this.scrubTyphusItems = this.investigationsTaxonomy['SCRUB TYPHUS'];
    this.leptospirosisItems = this.investigationsTaxonomy['LEPTOSPIROSIS'];
    this.brucellosisItems = this.investigationsTaxonomy['BRUCELLOSIS'];
    this.tuberculosisItems = this.investigationsTaxonomy['TUBERCULOSIS'];
    this.hivItems = this.investigationsTaxonomy['HIV'];
    this.hepatitisBItems = this.investigationsTaxonomy['HEPATITIS B'];
    this.hepatitisCItems = this.investigationsTaxonomy['HEPATITIS C'];
    this.hepatitisAItems = this.investigationsTaxonomy['HEPATITIS A'];
    this.hepatitisEItems = this.investigationsTaxonomy['HEPATITIS E'];
    this.hbcItems = this.investigationsTaxonomy['HBC (CORE ANTIBODIES)'];
    this.otherDiagnosticTestsItems = this.investigationsTaxonomy[
      'OTHER DIAGNOSTIC TESTS'
    ];
    this.radiologyAndOtherDiagnosticTestsItems = this.investigationsTaxonomy[
      'RADIOLOGY & OTHER DIAGNOSTIC TESTS'
    ];
  }
  showJoinVideoBtn(event) {
    this.joinedVideo = event;
  }
  getAppointment() {
    const date = moment(new Date()).format('YYYY-MM-DD');
    const todaySearchParams =
      '?start_datetime=' + date + '&end_datetime=' + date;
    this.doctorService.getDoctorAppointments(todaySearchParams).subscribe(
      (data) => {
        const results = data['results'];
        this.showWarningNotification = true;
        results.forEach(element => {
          if(element.uuid==this.consultationId){
            this.freeBooking = element.free_booking;
          }
        });(
          (obj) => obj.uuid == 'Not Started'
        )
        const appointment = results.filter(
          (obj) =>
            obj.fulfilment_status == 'Not Started' && obj.status == 'Booked'
        );
        console.log(appointment);
        setInterval(
          () => this.todayNextAppointmentNotification(appointment),
          60000
        );
      },
      (error) => {
        console.log(error);
      }
    );
  }
  todayNextAppointmentNotification(appointment) {
    console.log(appointment);
    if (appointment.length > 0) {
      const currentDate = moment();
      const day = moment(appointment?.[0]?.start_datetime);
      let minutes = currentDate.diff(day, 'minutes');
      console.log(minutes);
      const endTime = moment(appointment?.[0]?.end_datetime);
      let maxTime = endTime.diff(day, 'minutes');
      console.log(maxTime);
      if (minutes > -4 && maxTime >= minutes && this.showWarningNotification) {
        this.showWarningNotification = false;
        this.notificationService.warning(
          'Next appointment will be start in 3 minutes'
        );
      }
    }
  }
  saveBmi(category) {
    const unitConversion =
      parseFloat(this.consultForm.get('height').value) / 100;
    const meter = unitConversion * unitConversion;
    console.log(unitConversion, meter);
    const bmi = parseFloat(this.consultForm.get('weight').value) / meter;
    let bmi_value = parseFloat(bmi.toFixed(1));
    console.log('bmi_value', typeof (bmi_value))
    if (bmi_value) {
      this.consultForm.get('bmi').setValue(bmi_value);

    } else {
      this.consultForm.get('bmi').setValue(null);
      bmi_value = null;
    }
    console.log(bmi_value);

    let bmi_data = { bmi: bmi_value };
    let consultData = {};
    consultData[category] = bmi_data;
    console.log(consultData);
    this.teleConsultService
      .addPrescription(this.consultationId, consultData)
      .subscribe(
        (data) => {
          // console.log(type);
          // this.consultForm.get(type).setValue(sdata);
        },
        (err) => {
          console.log('ERROR:' + err.message);
        }
      );
    return null;
  }
  setDoctorType(event: any) {
    this.doctorList = [];
    this.deptList = [];
    this.consultForm.value.doctorType = event;
    this.doctorType = event;
    switch (event) {
      case '1':
        this.getDoctorDepartment(this.currentHsptId);
        break;
      case '2':
        this.getHospitalList();
        break;
    }
  }
  getDoctorDepartment(id?: string) {
    this.consultForm.value.hospitalId = id;
    if (id == undefined || id == null) {
      this.getDoctorList();
      this.getSOM();
    }
    else {
      this.getDoctorList(id);
      this.getSOM(id);
    }
  }
  getDoctorList(id?: string) {
    this.doctorList = [];
    this.sharedService.getDoctorList(id).subscribe((data: any) => {
      data.forEach(element => {
        this.doctorList.push(element);
      });
    });
  }
  getDeptList(id?: string) {
    this.doctorService.getSystemOfMedicine().subscribe((data: any) => {
      this.specificDepartmentData = data;
      Object.keys(data).forEach((element: any) => {
        this.deptList.push(element);
      });
    });
  }
  // getSOM(id?: string) {
  //   if(id&&id!=undefined&&id!=null){
  //     this.doctorService.getParticularHospitalDepartment(id).subscribe((data: any) => {
  //       this.specificSpecialityData = data;
  //       Object.keys(data).forEach((element: any) => {
  //         this.getSOMList.push(element);
  //       });
  //     });
  //   }else{
  //     this.doctorService.getSystemOfMedicine().subscribe((data: any) => {
  //       this.specificSpecialityData = data;
  //       Object.keys(data).forEach((element: any) => {
  //         this.getSOMList.push(element);
  //       });
  //     });
  //   }
  // }
  getSOM(id?: string) {
    if(id!=undefined&&id!=null){
      this.consultForm.value.hospitalId = id;
    }
    this.getSOMList = [];
    this.doctorService.getSystemOfMedicine().subscribe((data: any) => {
      this.specificDepartmentData = data;
      Object.keys(data).forEach((element: any) => {
        this.getSOMList.push(element);
      });
    });
    this.specificSpecialityData = [];
    if (id) {
      this.getDoctorList(id);
      this.doctorService.getParticularHospitalSpeciality(id).subscribe((data: any) => {
        Object.keys(data).forEach((key) => {
          const category = data[key];
          this.specificSpecialityData.push({ key, category });
        });
      });
    }
    this.consultForm.value.deptId = null;
    this.consultForm.value.specId = null;
  }
  getHospitalList() {
    this.sharedService.getHospitalList().subscribe((data: any) => {
      this.hospitalList = [];
      data.forEach(element => {
        this.hospitalList.push(element);
      });
    });
  }
  getSpecificSpeciality(data) {
    this.specificDepartment = [];
    this.specificSpeciality = [];
    this.selectedSOM = data;
   
    Object.keys(this.specificDepartmentData).forEach(category => {
      if (category == data) {
        this.specificDepartment.push(this.specificDepartmentData[category]['departments']);
      }
    });
    if (this.specificSpecialityData.length > 0) {
      this.specificSpecialityData.forEach(item => {
        if (item.key == data) {
          item.category.forEach(element => {
            this.specificSpeciality.push({spec_code:element.spec_code,spec_name:element.spec_name});
          })
          
        }
      });
      console.log(this.specificSpeciality);
    }
    if(this.doctorType=='1'){
      let doctorList = [];
      this.specificSpeciality=[]
      //  this.doctorList.forEach((item:any) => 
      //   {
      //     if(item.som == data){
      //       doctorList.push(item);
      //       this.specificSpeciality.push({spec_code:item.speciality[0].label,spec_name:item.speciality[0].value});
      //   }
      // });
      this.doctorList.forEach((item: any) => {
        if (item.som === data) {
          doctorList.push(item);
      
          const specialityExists = this.specificSpeciality.some(
            speciality => speciality.spec_code === item.speciality[0].label
          );
      
          if (!specialityExists) {
            this.specificSpeciality.push({
              spec_code: item.speciality[0].label,
              spec_name: item.speciality[0].value
            });
          }
        }
      });
      this.doctorList = [];
      this.doctorList = doctorList;
    }
  }
  setSpecificDepartment(val: string) {
    this.selectedDepartment = val;
  }
  setSpecificSpeciality(val: string) {
    this.selectedSpeciality = val;
    let hspid = this.consultForm.value.hospitalId;
    this.sharedService.getDoctorListByDept(this.selectedSOM, this.selectedSpeciality).subscribe((data: any) => {
      data['results'].forEach(element => {
        this.doctorList.push({ uuid: element.user.uuid, username: element.user.username });
      });
    });
  }

  referPatient() {
    let doctorType;
    if (this.consultForm.value.doctorType == 1) {
      doctorType = 'individual';
    }
    else if (this.consultForm.value.doctorType == 2) {
      doctorType = 'hospitalbased'
    }
    const formData = new FormData();
    if (this.consultForm.value.hospitalId != null && this.consultForm.value.hospitalId != undefined) {
      formData.append('referred_to_hospital', this.consultForm.value.hospitalId);
      this.hospitalList.forEach(element => {
        if (element.uuid == this.consultForm.value.hospitalId) {
          formData.append('referred_to_hospitalName', element.name);
        }
      })
    }
    formData.append('referred_to_doctorType', doctorType);
    formData.append('referred_to_doctor', this.consultForm.value.doctorId);

    let selectedDoctor = '';
    this.doctorList.forEach(element => {
      if (element.uuid == this.consultForm.value.doctorId) {
        selectedDoctor = element.username;
      }
    })
    formData.append('referred_to_doctorName', selectedDoctor);

    if(this.consultForm.value.deptId != null && this.consultForm.value.deptId != undefined){
      formData.append('referring_department', this.consultForm.value.deptId);
    }
    formData.append('referring_departmentName', this.consultForm.value.deptName);
    formData.append('referring_specialityName', this.consultForm.value.specId);
    formData.append('reason_for_referral', this.consultForm.value.referReason);
    formData.append('consultation_id', this.consultationId);
    if (this.userType == 'DoctorAssistant') {
      formData.append('referring_hospital', localStorage.getItem('hstId'));
    }
    this.sharedService.setReferral(formData).subscribe(data => {
      this.notificationService.success(data['message']);
    },
      (error) => {
        console.log(error);
      });
  }
 
}
