<!-- <div class="modal-dialog" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="instantConsultModalLabel">Suspension reason / Notes</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
        </div>
        <div class="modal-body text-center" id="inst-appt-modal-body">
            An instant appointment request has arrived.
        </div>
        <div class="modal-footer text-center">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Ok</button>
            <button type="button" class="btn btn-primary" (click)="cancelAppointment()" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        </div>
    </div>
</div> -->
<h5 class="modal-title" id="instantConsultModalLabel">Suspension reason / Notes</h5>
<input type="textarea" name="reason" [(ngModel)] = "reason">
<div>
    <button type="button" class="btn btn-secondary" (click)="onClose()">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="onSuspend()" class="btn btn-secondary" data-dismiss="modal">Ok</button>
</div>
