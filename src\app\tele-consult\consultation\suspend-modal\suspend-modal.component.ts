import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TeleConsultService } from '../../tele-consult.service';
import { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-suspend-modal',
  templateUrl: './suspend-modal.component.html',
  styleUrls: ['./suspend-modal.component.css']
})
export class SuspendModalComponent implements OnInit {
  suspendInvestigationMessage = '';
  suspendType = '';
  consultationId = '';
  reason = '';
  constructor(private router: Router,private teleConsultService: TeleConsultService, private modalService: NgbModal ) {}

  ngOnInit(): void {
  }

  onSuspend(){
    if(this.suspendType=='investigations'){
      this.teleConsultService.suspendConsultationInvestigationsPending(this.consultationId, this.reason).subscribe( data => {
        this.router.navigateByUrl('/doctor/dashboard');
      },
      err =>{
        console.log('ERROR:' + err.message);
      });
    }
    if(this.suspendType=='review'){
          this.teleConsultService.suspendConsultationReviewPending(this.consultationId, this.reason).subscribe( data => {
      this.router.navigateByUrl('/doctor/dashboard');
    },
    err =>{
      console.log('ERROR:' + err.message);
    });
    }

  }

  onClose(){
    this.modalService.dismissAll();
  }
}
