#join {
    text-align: center;
  }
  
  #join,
  #session {
    position: absolute;
    margin: auto;
    top: 100px;
    bottom: 0;
    left: 0;
    right: 0;
    height: 70%;
    width: 70%;
  }
/* Multi-Camera Layout */
.multi-camera-container {
  position: absolute;
  margin: auto;
  top: 100px;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70%;
  width: 70%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.camera-grid {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 15px;
  width: 100%;
  height: 100%;
}

.camera-wrapper {
  flex: 1;
  position: relative;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.camera-wrapper:hover {
  transform: scale(1.02);
}

.primary-camera {
  flex: 2;
}

.secondary-camera {
  flex: 1;
}

.camera-label {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
}

.video-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.camera-controls {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.camera-wrapper:hover .camera-controls {
  opacity: 1;
}

.camera-controls button {
  font-size: 11px;
  padding: 4px 8px;
}

/* Camera Selection Panel */
.camera-selection-panel {
  background: rgba(255, 255, 255, 0.95);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.camera-selection-panel h6 {
  margin-bottom: 10px;
  color: #333;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.device-item.active {
  background: #e3f2fd;
  border-color: #2196f3;
}

.device-item span {
  flex: 1;
  font-size: 14px;
}

/* Camera Mode Toggle */
.camera-mode-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 15px;
  border-radius: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.camera-count {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}
