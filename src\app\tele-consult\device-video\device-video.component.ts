import { Component, Input, OnInit, ViewChild, ɵConsole } from '@angular/core';
import { throwError as observableThrowError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {OpenviduSessionComponent, StreamEvent, Session, UserModel, OpenViduLayout, OvSettings, OpenViduLayoutOptions, SessionDisconnectedEvent, Publisher} from 'openvidu-angular';
import { TeleConsultService } from '../tele-consult.service';
import { ActivatedRoute } from '@angular/router';
import * as Settings from '../../config/settings';
@Component({
  selector: 'app-device-video',
  templateUrl: './device-video.component.html',
  styleUrls: ['./device-video.component.css']
})
export class DeviceVideoComponent implements OnInit {

    OPENVIDU_SERVER_URL = Settings.OPENVIDU_SERVER_URL;//'https://openvidu.bookconnect.in';
    OPENVIDU_SERVER_SECRET = Settings.OPENVIDU_SECRET;//'X!jt9pk@H';
    mySessionId: string;
    myUserName = 'Participant' + Math.floor(Math.random() * 100);
    tokens: string[] = [];
    deviceTokens: string[] = [];
    session = false;
    ovSession: Session;
    ovLocalUsers: UserModel[];
    ovLayout: OpenViduLayout;
    ovLayoutOptions: OpenViduLayoutOptions;

    @Input() consultationId: any;
    videoToken = '';
    appointmentId = '';
    sessionJoined = false;

    @ViewChild('ovSessionComponent')
    public ovSessionComponent: OpenviduSessionComponent;
    sub: any;

    constructor(
      private httpClient: HttpClient,
      private route: ActivatedRoute,
      private teleConsultService: TeleConsultService
      )
      {
        this.mySessionId = this.consultationId;
      }

    ngOnInit(){
        this.joinDeviceSession();
     }

  async  joinDeviceSession(){
      console.log('Consulting device video cons id::' + this.consultationId)
      this.teleConsultService.getDeviceVideoToken(this.consultationId).subscribe((data) => {
        this.videoToken = data['token'];
        this.sessionJoined = true;
        this.deviceTokens.push(this.videoToken);
        this.session = true;
      },
      (err) =>{
        console.log('ERROR:' + err);
      });
    }

    handlerSessionCreatedEvent(session: Session): void {
      console.log('SESSION CREATED EVENT', session);
      session.on('streamCreated', (event: StreamEvent) => {
        console.log('SESSION STREAM CREATED');
      });
      session.on('streamDestroyed', (event: StreamEvent) => {
        console.log('SESSION STREAM DESTROYED');
      });

      session.on('sessionDisconnected', (event: SessionDisconnectedEvent) => {
        this.session = false;
        this.tokens = [];
      });

      this.myMethod();

    }

    handlerPublisherCreatedEvent(publisher: Publisher) {
      publisher.on('streamCreated', (e) => {
        console.log('Publisher streamCreated', e);
      });
    }

    handlerErrorEvent(event): void {
      console.log(event);
    }

    myMethod() {
      this.ovLocalUsers = this.ovSessionComponent.getLocalUsers();
      this.ovLayout = this.ovSessionComponent.getOpenviduLayout();
      this.ovLayoutOptions = this.ovSessionComponent.getOpenviduLayoutOptions();
    }

    /**
     * --------------------------
     * SERVER-SIDE RESPONSIBILITY
     * --------------------------
     * This method retrieve the mandatory user token from OpenVidu Server,
     * in this case making use Angular http API.
     * This behavior MUST BE IN YOUR SERVER-SIDE IN PRODUCTION. In this case:
     *   1) Initialize a session in OpenVidu Server	 (POST /api/sessions)
     *   2) Generate a token in OpenVidu Server		   (POST /api/tokens)
     *   3) The token must be consumed in Session.connect() method of OpenVidu Browser
     */

    getToken(): Promise<string> {
      return this.createSession(this.consultationId).then((sessionId) => {
        return this.createToken(sessionId);
      });
    }

    createSession(sessionId) {
      return new Promise((resolve, reject) => {
        const body = JSON.stringify({ customSessionId: sessionId });
        const options = {
          headers: new HttpHeaders({
            Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET),
            'Content-Type': 'application/json',
          }),
        };
        return this.httpClient
          .post(this.OPENVIDU_SERVER_URL + '/api/sessions', body, options)
          .pipe(
            catchError((error) => {
              if (error.status === 409) {
                resolve(sessionId);
              } else {
                console.log('ERROR STATUS CODE::'+error.status);
                console.log('ERRROR DETAIL::'+error);
                console.warn('No connection to OpenVidu Server. This may be a certificate error at ' + this.OPENVIDU_SERVER_URL);
                if (
                  window.confirm(
                    'No connection to OpenVidu Server. This may be a certificate error at "' +
                      this.OPENVIDU_SERVER_URL +
                      '"\n\nClick OK to navigate and accept it. If no certificate warning is shown, then check that your OpenVidu Server' +
                      'is up and running at "' +
                      this.OPENVIDU_SERVER_URL +
                      '"',
                  )
                ) {
                  // location.assign(this.OPENVIDU_SERVER_URL + '/accept-certificate');
                }
              }
              return observableThrowError(error);
            }),
          )
          .subscribe((response) => {
            console.log(response);
            resolve(response['id']);
          });
      });
    }

    createToken(sessionId): Promise<string> {
      return new Promise((resolve, reject) => {
        const body = JSON.stringify({ session: sessionId });
        const options = {
          headers: new HttpHeaders({
            Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET),
            'Content-Type': 'application/json',
          }),
        };
        return this.httpClient
          .post(this.OPENVIDU_SERVER_URL + '/api/tokens', body, options)
          .pipe(
            catchError((error) => {
              reject(error);
              return observableThrowError(error);
            }),
          )
          .subscribe((response) => {
            console.log(response);
            resolve(response['token']);
          });
      });
    }

  }
