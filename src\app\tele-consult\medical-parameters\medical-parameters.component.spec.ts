import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { MedicalParametersComponent } from './medical-parameters.component';

describe('MedicalParametersComponent', () => {
  let component: MedicalParametersComponent;
  let fixture: ComponentFixture<MedicalParametersComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ MedicalParametersComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MedicalParametersComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
