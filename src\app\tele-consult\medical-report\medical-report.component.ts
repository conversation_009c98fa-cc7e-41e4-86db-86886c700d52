import { Component, OnInit, Input } from '@angular/core';
import { PatientService } from '../../patient/patient.service';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { TeleConsultService } from '../tele-consult.service';
import { FormControl, FormGroup } from '@angular/forms';
import { AuthService } from '../../auth/auth.service';
import { SharedService } from '../../shared/shared.service';
import * as moment from 'moment';
import { TranslateModule } from '@ngx-translate/core';
@Component({
  selector: 'app-medical-report',
  templateUrl: './medical-report.component.html',
  styleUrls: ['./medical-report.component.css']
})
export class MedicalReportComponent implements OnInit {
  @Input() patientUuid: any;
  @Input() doctorUuid: any;
  @Input() consultationId: any;
  @Input() history: string;
  @Input() videoAndData: boolean;
  @Input() patientVideoAndData: boolean;
  selectedDiagnosticReportName = new FormControl(null);
  appointmentId: any;
  reportName: any;
  reportFile: any = null;
  showReports: any;
  public userType: string;
  reportTypes = [
    { id: 'Pathology Test', testType: 'Pathology Test' },
    { id: 'X-Ray', testType: 'X-Ray' },
    { id: 'Ultra Sound Scan', testType: 'Ultra Sound Scan' },
    { id: 'CT Scan', testType: 'CT Scan' },
    { id: 'MRI Scan', testType: 'MRI Scan' },
    { id: 'Prescription', testType: 'Prescription' },
    { id: 'Other', testType: 'Other' },
  ];
  reportFiles: any = [];
  reportDate: any = null;
  public pastAppointments: any = [];
  selectedConsultationDate: FormControl = new FormControl('null');
  consultationData: any;
  showUploadTab: boolean;
  reportTotalPage: number;
  reportCurrentPage: any;
  reportSerialNumber: number = 0;
  maxDate: Date;
  minDate: Date;
  public searchForm: FormGroup;
  queryParms: string;
  shwoFilter: boolean = true;
  showAllDocuments: boolean = false;
  showUploading: boolean = false;
  constructor(private patientService: PatientService,
    private activatedRoute: ActivatedRoute,
    private notificationService: ToastrService,
    private teleConsultService: TeleConsultService,
    private userService: AuthService,
    private sharedService: SharedService) { }

  ngOnInit(): void {
    this.maxDate = new Date();
    this.minDate = new Date();
    this.minDate.setDate(this.minDate.getDate() - 36500);
    this.userType = localStorage.getItem('user_type');
    this.activatedRoute.params.subscribe(params => {
      if (params['patient'] !== undefined) {
        this.patientUuid = params['patient'];
        this.doctorUuid = params['doctor'];
        if (this.history !== 'history') {
          this.consultationId = params['consultid'];
        }
      }

      const data = 'consultation=' + this.consultationId;
      this.queryParms = `?consultation=${this.consultationId}`;
      this.getDocument(data);
      this.consultationAppointment();
      this.userService.getUserDetail().subscribe(
        (data) => {
          this.sharedService.setUserName(data['username']);
          if (data['profile_picture'] !== null) {
            this.sharedService.setPicture(data['profile_picture']);
          }
        },
        (error) => {
          console.log(error);
        }
      );
    })
    this.searchFormCOntrol();
    if (this.userType == 'Doctor') {
      this.sharedService.webSocketAvailable.subscribe((data) => {
        if (data['message_type'] == 'Adding Report') {
          const data = 'consultation=' + this.consultationId;
          this.getDocument(data);
          this.notificationService.warning('New report uploaded by patient');
        }
      });
    }
  }

  searchFormCOntrol() {
    this.searchForm = new FormGroup({
      reportType: new FormControl(null),
      reportGeneratedOn: new FormControl(null),
      reportUpdatedOn: new FormControl(null)
    })
  }

  getDocument(data) {
    this.patientService.getMedicalReportsById(data).subscribe(
      data => {
        this.reportFiles = Object.values(data['results']);
        console.log(this.reportFiles);
      }, error => {
        console.log(error);
      }
    );
  }

  medicalReports(event) {
    console.log(event);
    this.reportFile = event.target.files[0];
    this.reportName = event.target.files[0]?.name;
  }

  showMedicalReports(status) {
    this.showReports = status;
  }

  consultationAppointment() {
    this.teleConsultService.getConsultationData(this.consultationId)
      .subscribe((data) => {
        data;
        const status = data['fulfilment_status'];
        if (status == 'Suspended') {
          this.selectedConsultationDate.setValue(data['start_datetime']);
          this.showUploadTab = true;
        } else {
          this.showUploadTab = false;
        }
        console.log(this.pastAppointments);
      }, error => {
        console.log(error);
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
      )
  }

  getConsultationId(event, id) {
    this.searchForm.reset();
    this.showAllDocuments = event.target.checked;
    console.log(event.target.checked)
    if (id == 'All' && event.target.checked) {
      const data = 'patient=' + this.patientUuid;
      this.showUploadTab = false;
      this.getDocument(data)
    } else {
      const data = 'consultation=' + this.consultationId;
      this.getDocument(data);
    }
  }

  saveMedicalReport() {
    this.showUploading = true;
    const file = this.reportFile;
    const type = this.selectedDiagnosticReportName.value;
    if (this.userType === 'Patient') {
      const data = { 'medical_report_type': type, 'appointment': this.consultationId, 'consultation': this.consultationId, 'report_generated_on': this.reportDate, 'ongoing': true, 'userType': this.userType };
      this.patientService.postMedicalReport(file, data).subscribe(
        data => {
          this.showUploading = false;
          this.reportFiles.unshift(data);
          this.reportDate = null;
          this.reportName = null;
          this.reportFile = null;
          this.selectedDiagnosticReportName.setValue(null);
          this.notificationService.success('Report updated', 'Med.Bot');
        }, error => {
          console.log(error);
        }
      );
    } else if (this.userType == 'Doctor') {
      const data = {
        'medical_report_type': type, 'appointment': this.consultationId, 'consultation': this.consultationId,
        'report_generated_on': this.reportDate, 'patient': this.patientUuid,
      };
      this.patientService.postMedicalReportById(file, data).subscribe(
        data => {
          this.reportFiles.unshift(data);
          this.showUploading = false;
          this.reportDate = null;
          this.reportName = null;
          this.reportFile = null;
          this.selectedDiagnosticReportName.setValue(null);
          this.notificationService.success('Report updated', 'Med.Bot')
        }, error => {
          console.log(error);
        }
      );
    } else if (this.userType === 'Partner') {
      const data = { 'medical_report_type': type, 'appointment': this.consultationId, 'consultation': this.consultationId, 'report_generated_on': this.reportDate };
      this.patientService.postMedicalReportHospital(file, data, this.patientUuid).subscribe(
        data => {
          this.showUploading = false;
          this.reportFiles.unshift(data);
          this.reportDate = null;
          this.reportName = null;
          this.reportFile = null;
          this.selectedDiagnosticReportName.setValue(null);
          this.notificationService.success('Report updated', 'Med.Bot');
        }, error => {
          console.log(error);
        }
      );
    }
  }

  openFile(file) {
    window.open(file);
  }
  getReportType(event, appointmentId = null) {
    // this.selectedIndex = index;
    this.selectedDiagnosticReportName.setValue(event.testType);
  }
  reportNextPageList() {
    this.reportCurrentPage = this.reportCurrentPage + 1;
    if (this.reportTotalPage >= this.reportCurrentPage) {
      this.queryParms = this.queryParms + `&page=${this.reportCurrentPage}`;
      this.getreportList(this.queryParms);
      this.reportSerialNumber = (this.reportCurrentPage - 1) * 10;
    } else {
      this.reportCurrentPage = this.reportCurrentPage - 1;
    }
  }
  reportLastPageList() {
    this.reportSerialNumber = (this.reportTotalPage - 1) * 10;
    this.queryParms = this.queryParms + `&page=${this.reportTotalPage}`;
    this.getreportList(this.queryParms);

  }
  reportFirstPageList() {
    this.reportCurrentPage = 1;
    this.reportSerialNumber = 0;
    this.queryParms = this.queryParms + `&page=${this.reportCurrentPage}`
    this.getreportList(this.queryParms);
  }
  reportPreviousPageList() {
    this.reportCurrentPage = this.reportCurrentPage - 1;
    if (this.reportTotalPage >= this.reportCurrentPage && this.reportCurrentPage > 0) {
      this.queryParms = this.queryParms + `&page=${this.reportCurrentPage}`
      this.getreportList(this.queryParms);
      this.reportSerialNumber = (this.reportCurrentPage - 1) * 10;
    } else {
      this.reportCurrentPage = this.reportCurrentPage + 1;
    }
  }
  getreportList(data) {
    this.reportTotalPage = 0;
    this.patientService.getMedicalReportData(data).subscribe(
      data => {
        this.reportFiles = data['results'];
        this.reportTotalPage = data['total_pages'];
        this.reportCurrentPage = data['page_number'];
        //this.reportLoading=false;


        console.log(this.reportFiles);
      }
    );
  }
  findReports() {
    let searchQueryParms = [];
    const reportType = this.searchForm.controls[`reportType`].value;
    const reportGeneratedOn = this.searchForm.controls[`reportGeneratedOn`].value;
    const reportUpdatedOn = this.searchForm.controls[`reportUpdatedOn`].value;
    if (reportType) {
      const value = 'medical_report_type=' + reportType;
      searchQueryParms.push(value);
    }
    if (reportGeneratedOn) {
      const value = 'generated_date=' + moment(reportGeneratedOn).format('YYYY-MM-DD');
      searchQueryParms.push(value);
    }
    if (reportUpdatedOn) {
      const value = 'created_date=' + moment(reportUpdatedOn).format('YYYY-MM-DD');
      searchQueryParms.push(value);
    }
    if (this.showAllDocuments) {
      this.queryParms = `?patient=${this.patientUuid}&`;
    } else {
      this.queryParms = `?consultation=${this.consultationId}&`;
    }
    for (let i = 0; i < searchQueryParms.length; i++) {
      if (i === searchQueryParms.length - 1) {
        this.queryParms = this.queryParms + searchQueryParms[i];
      } else {
        this.queryParms = this.queryParms + searchQueryParms[i] + '&';
      }

    }
    this.getreportList(this.queryParms);
  }
  showFilterFields() {
    if (this.shwoFilter == true) {
      this.shwoFilter = true;
    } else {
      this.shwoFilter = true;
    }

  }
  resetSearchForm() {
    this.searchForm.reset();
    if (this.showAllDocuments) {
      this.queryParms = `?patient=${this.patientUuid}`;
    } else {
      this.queryParms = `?consultation=${this.consultationId}`;
    }
    this.getreportList(this.queryParms);
  }
}
