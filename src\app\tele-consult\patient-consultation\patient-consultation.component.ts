import { SharedService } from './../../shared/shared.service';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router,NavigationEnd } from '@angular/router';
import { PatientService } from '../../patient/patient.service';
import{ToastrService} from 'ngx-toastr';
import {TeleConsultService} from '../tele-consult.service';
import {Location} from '@angular/common';
import {AuthService} from '../../auth/auth.service';
import { HospitalService } from 'src/app/hospital-admin/hospital-admin.service';
declare var $;

@Component({
  selector: 'app-patient-consultation',
  templateUrl: './patient-consultation.component.html',
  styleUrls: ['./patient-consultation.component.css']
})
export class PatientConsultationComponent implements OnInit {

  patientConsultForm = new FormGroup({
    //Medical History
    immunizationHistory: new FormControl(),
    personalHistory: new FormControl(),
    appetite: new FormControl(),
    dietHistory: new FormControl(),
    thirst: new FormControl(),
    sleep: new FormControl(),
    habits: new FormControl(),
    smoking: new FormControl(),
    alcohol: new FormControl(),
    drugs: new FormControl(),
    sexualHistory: new FormControl(),
    notes: new FormControl(),
    gender: new FormControl(),
    gynaecologicalHistory: new FormControl(),
    ageOfMenarch: new FormControl(),
    menstrualHistory: new FormControl(),
    lastMenstrualPeriod: new FormControl(),
    numberOfPregnancy: new FormControl(),
    gravida: new FormControl(),
    para: new FormControl(),
    abortions: new FormControl(),
    bloodPressure: new FormControl(),
    pulseRate: new FormControl(),
    saturation: new FormControl(),
    auscultation: new FormControl(),
    temperature: new FormControl(),
    ecg: new FormControl(),
    otherObservation: new FormControl(),
    medicine: new FormControl(),
    diet: new FormControl(),
    recommendations: new FormControl(),
    //Physical Examination
    weight: new FormControl(),
    height: new FormControl(),
    bmi: new FormControl(),
    built: new FormControl(),
    nutrition: new FormControl(),
    clubbingOfFingers: new FormControl(),
    nailChanges: new FormControl(),
    cyanosis: new FormControl(),
    icterusJaundice: new FormControl(),
    pallor: new FormControl(),
    lymphNodes: new FormControl(),
    oedema: new FormControl(),
    sclera: new FormControl(),
    otherObservations: new FormControl(),
    //Systemic Examination
    respiratorySystem: new FormControl(),
    gastroIntestinal: new FormControl(),
    cardioVascular: new FormControl(),
    genitoUrinary: new FormControl(),
    musculoSkeletal: new FormControl(),
    centralNervous: new FormControl(),
    eye: new FormControl(),
    ear: new FormControl(),
    nose: new FormControl(),
    mouth: new FormControl(),
    throat: new FormControl(),
    neck: new FormControl(),
    skin: new FormControl(),
    psychiatricHistory: new FormControl(),
    //Diagnosis
    primary: new FormControl(),
    secondary: new FormControl(),
    differentialDiagnosis: new FormControl(),
    finalDiagnosis: new FormControl(),
    ICD10Codes: new FormControl(),
    //Investigation
    keyAdvice: new FormControl(),
    others: new FormControl(),
    haematology: new FormControl(),
    biochemistryAndImmunoassay: new FormControl(),
    clinicalPathology: new FormControl(),
    pathology: new FormControl(),
    serology: new FormControl(),
    malaria: new FormControl(),
    filaria: new FormControl(),
    dengu: new FormControl(),
    japaneseEncephalitis: new FormControl(),
    chikungunya: new FormControl(),
    scrubTyphus: new FormControl(),
    leptrospirosis: new FormControl(),
    brucellosis: new FormControl(),
    tuberculosis: new FormControl(),
    hiv: new FormControl(),
    hepatitisB: new FormControl(),
    hepatitisC: new FormControl(),
    hepatitisA: new FormControl(),
    hepatitisE: new FormControl(),
    hbc: new FormControl(),
    otherDiagnosticTest: new FormControl(),
    radioloyAndOtherDiagnostics: new FormControl()
  });

  joinedVideo = false;
  deviceVideo = false;
  consultationId='';
  sub: any;
  participantName = 'Patient Consult';
  prescriptionData: any =[];
  prescriptionRefeshBtnDisabled= false;
  public isLoading= false;
  public investigationData= [];
  consultationDocuments: any;
  reportFiles: any =[];
  reportTypes = [
    { id: 'Pathology Test', testType: 'Pathology Test' },
    { id: 'X-Ray', testType: 'X-Ray' },
    { id: 'Ultra Sound Scan', testType: 'Ultra Sound Scan' },
    { id: 'CT Scan', testType: 'CT Scan' },
    { id: 'MRI Scan', testType: 'MRI Scan' },
    { id: 'Other', testType: 'Other' },
  ];
  selectedDiagnosticReportName=new FormControl(null) ;
  reportName: any;
  reportFile: any;
  showReports: any;
  patient: any;
  doctor: any;
  showConsultation= true;
  showProfilePic= false;
  profilePicture='assets/img/doctors/doctor-thumb-02.png';
  personalInfo={}
  doctorInfo={};
  doctorProfilePicture: any;
  practiceLocations: any;
  degreeList: any=[];
  degreeString: string=' ';
  showDoctorProfilePic: boolean;
  onlyVideo: boolean;
  onlyData: boolean;
  videoAndData: boolean;
  videoSize: number=0;
  tabSize: number;
  messageLoading: boolean;
  messageTotalPage: number;
  messageData: any;
  messageCurrentPage: any;
  userType: string;
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private patientService :PatientService,
    private notificationService: ToastrService,
    private teleConsultService:TeleConsultService,
    private sharedService: SharedService,
    private location:Location,
    private userService:AuthService,
    private hospitalService:HospitalService
    ) { }

  ngOnInit(): void {
    this.videoAndData=true;
    this.onlyData= false;
    this.onlyVideo=false;
    this.userType=localStorage.getItem('user_type');
    this.userService.getUserDetail().subscribe(
      (data) => {
        this.personalInfo = data;
        // console.log(data,'check');
        this.participantName = data['username'];
        if( this.personalInfo['profile_picture'] ){
          this.profilePicture=this.personalInfo['profile_picture'];
        }
        setTimeout(()=>{
          this.showProfilePic= true;
        },1000);

      },error=>{
        this.showProfilePic= true;
        console.log(error);
      })
    this.sub = this.route
    .queryParams
    .subscribe(params => {
      this.consultationId = params['consultationId'];
      const data='consultation='+this.consultationId;
      this.getConsultationData();
     // this.getDocument( data);
      console.log('PATIENT NGONINIT CONSULTATION ID' + this.consultationId);
    },
    err =>{
      console.log('ERROR:' + err);
    });

  }

  ngOnDestroy(){
    this.sub.unsubscribe();
  }
  getConsultationData(){
    this.teleConsultService.getConsultationData(this.consultationId).subscribe(
      data=>{
        this.patient=data['patient_uuid'];
        this.doctor=data['doctor_uuid'];
        if(data['doctor_uuid']=='Started'||'Not Started'){

        }else{
          this.openPatientDashboard(true)
        }
        // this.getDoctorDetails(this.doctor);
        // console.log(this.reportFiles);
      },error=>{
        console.log(error);
      }
    );
  }

  openPatientDashboard(event){
  //   if(event){
  //     // this.notificationService.warning('Your consultation has ended','Med.Bot');
  //     this.router.navigateByUrl('/patient/dashboard');
  //     this.router.events.subscribe((val) => {
  //       const nvigationEnd=val instanceof NavigationEnd;
  //       if(!!nvigationEnd){
  //         location.reload();
  //       }
  //   });
  // }
  const data = { patient_exited: true }
    this.teleConsultService.leaveConsultation(this.consultationId, data).subscribe(data => {
      if(this.userType='Patient'){
        this.router.navigateByUrl('/patient/dashboard');
      }else if (this.userType == 'Partner') {
        this.hospitalService.setHospitalDetails();
      }
      this.router.events.subscribe((val) => {
        const nvigationEnd = val instanceof NavigationEnd;
        if (!!nvigationEnd) {
          location.reload();
        }
      });
    })

  }

  onAddRow(prescriptionTable){
    let tableRef = <HTMLTableElement >document.getElementById(prescriptionTable);
    // let newRow = tableR.insertBefore(-1)
        // TSection.insertBefore();
    let newRow = tableRef.insertRow(-1);

    // Insert a cell in the row at index 0
    let medicine = newRow.insertCell(0);
    medicine.innerHTML = "<td> <input type='text'> </td>";

    let morningDosage = newRow.insertCell(1);
    morningDosage.innerHTML = "<td> <input type='checkbox'> </td>";

    let afternoonDosage = newRow.insertCell(2);
    afternoonDosage.innerHTML = "<td> <input type='checkbox'> </td>";

    let nightDosage = newRow.insertCell(3);
    nightDosage.innerHTML = "<td> <input type='checkbox'> </td>";

    let beforeFood = newRow.insertCell(4);
    beforeFood.innerHTML = "<td> <input type='checkbox'> </td>";

    let days = newRow.insertCell(5);
    days.innerHTML = "<td> <input type='number' name='days' style='width: 50%;'> </td>";

    let remarks = newRow.insertCell(6);
    remarks.innerHTML = "<td> <input type='text' name='remarks'> </td>";
  }

  joinVideo(){
    this.joinedVideo=true;
  }

  onFormSubmit(){

  }

  joinDeviceVideo(){
    this.deviceVideo=true;
  }
  viewPrescription() {
    this.showConsultation=false;
    this.isLoading= true;
    this.prescriptionRefeshBtnDisabled= true;
    this.patientService.getConsultationData(this.consultationId).subscribe(
      (data) => {
        this.showConsultation=true;
        this.prescriptionRefeshBtnDisabled= false;
        this.isLoading= false;
        const consultation= data['fulfilment_status']
        if(consultation =='Suspended'|| consultation =='Completed'){
          this.notificationService.warning('Your consultation has ended','Med.Bot');
          this.openPatientDashboard(true)
        }else if(consultation =='Doctor Missed'){
          this.notificationService.warning('Doctor missed your consultation ','Med.Bot');
          this.openPatientDashboard(true)
        }else if(consultation =='Patient Missed'||consultation =='Both Missed'){
          this.notificationService.warning('You missed your consultation ','Med.Bot');
          this.openPatientDashboard(true)
        }else{



        }
        //  this.medicalHistory.push(data);
      },
      (error) => {
        this.prescriptionRefeshBtnDisabled= true;
        console.log(error);
        this.isLoading= false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );
    // this.router.navigate(['/patient/prescription', id]);
  }
  getConsultaionDocuments(data){

    this.teleConsultService.getDocument(this.consultationId,data).subscribe(
      (data) => {
        // if(data){
        //   this.openFile(data[0]['file']);
        // }else {
        //     this.notificationService.error('Pdf file not found')
        //   }
        const results=data['results'];
        if(results.length>0){
         window.open(results[0]['file']) ;
        }else{
          this.notificationService.warning('No')
        }

      },
      (error) => {
        console.log(error);

        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        } else {
          this.notificationService.error(
            `${error['statusText']}`,
            'Med.Bot'
          );
        }
      }
    );


  }
  downloadPrescription(){
    this.getConsultaionDocuments('Prescription');
  }
  downloadInvestigation(){
    let investigation= false;
    this.getConsultaionDocuments('Investigation');
    for(let data of  this.consultationDocuments){
            if(data.purpose=='Investigation'){
              investigation= true;
              window.open(data.file)
            }
    }
    if(!investigation){

      this.notificationService.warning(
        `No investigation data`,
        'Med.Bot'
      );
    }

  }
  getReportType(event, appointmentId=null) {
    // this.selectedIndex = index;
    this.selectedDiagnosticReportName.setValue(event.testType) ;
  }
  medicalReports(event){
    console.log(event);
    this.reportFile = event.target.files[0];
    this.reportName = event.target.files[0]?.name;
   }
   showMedicalReports(status){
     this.showReports = status;
   }
   saveMedicalReport(){
    const file = this.reportFile;
    const type = this.selectedDiagnosticReportName.value;
    const data = {'medical_report_type':type,'appointment':this.consultationId,'consultation':this.consultationId};
    this.patientService.postMedicalReport(file,data).subscribe(
      data=>{
        this.reportName=null;
        this.reportFile=null;
        this.selectedDiagnosticReportName.setValue('null');
        this.reportFiles.push(data);
        this.notificationService.success('Report updated','Med.Bot')
          ;
      },error=>{
        console.log(error);

      }
    );
  }
  openFile(data){
    window.open(data)
  }
  back() {
    this.location.back();
  }
  getDoctorDetails(id){
    this.showDoctorProfilePic= false;
    this.patientService.getdoctorProfile(id).subscribe((data) => {
      this.doctorInfo = data['user'];
      if(this.doctorInfo['profile_picture']){
        this.doctorProfilePicture = this.doctorInfo['profile_picture'];
      }else{
        this.doctorProfilePicture =  'assets/img/doctors/doctor-thumb-02.png'
      }

      data['qualifications'].map((qual) =>
        this.degreeList.push(qual['name'])
      );

      this.practiceLocations = data['practicelocations'];
      console.log(this.practiceLocations);
      for (let i = this.degreeList.length; i > 0; i--) {
        this.degreeString = this.degreeString + ', ' + this.degreeList[i - 1];
        this.degreeString = this.degreeString.substring(1);
      }
      setTimeout(()=>{
        this.showDoctorProfilePic = true;
      },1000);

    },error=>{
      console.log(error);
      this.showDoctorProfilePic= false;
      this.isLoading =false;
      const status = error['status'];
      if(status == 400){
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      else{
        this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
      }
    });
   }
   videoOnly(){
    this.onlyVideo=true;
    this.onlyData=false;
    this.videoAndData=false;
    $( "#videoZoomButton" ).click();
    // this.joinedVideo=false;
  }

  dataOnly(){
    this.onlyData=true;
    this.onlyVideo=false;
    this.videoAndData=false;
    // this.joinedVideo=false;
  }

  dataAndVideo(){
    this.videoAndData=true;
    this.onlyVideo=false;
    this.onlyData=false;
    $( "#videoZoomButton" ).click();
    // this.joinedVideo=false;
  }
  changeVideoSize(value){
    if(value==='3'){
      this.videoSize=3;

    }else if(value==='2'){
      this.videoSize=2;

    }else if(value==='1'){
      this.videoSize=1;

    }else if(value==='4'){
      this.videoSize=0;

    }
  }

  resizeScreen(){
   $('video:first').trigger('dbclick');
  }
  showJoinVideoBtn(event){
    this.joinedVideo=event;
  }
  getmessageList(page) {
    this.messageLoading = true;
    this.messageTotalPage = 0;
    this.patientService.getMessage().subscribe(
      (data) => {
        this.messageData = data['results'];
        this.messageTotalPage = data['total_pages'];
        this.messageCurrentPage = data['page_number'];
        this.messageLoading = false;

      },
      (error) => error => {
        this.messageLoading = false;
        const status = error['status'];
        if (status == 400) {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
        else {
          this.notificationService.error(`${error['statusText']}`, 'Med.Bot');
        }
      }
    );

  }
}
