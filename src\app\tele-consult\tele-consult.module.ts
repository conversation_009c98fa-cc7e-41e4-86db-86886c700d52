import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
// doctor components
import { RouterModule } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { AngularFileUploaderModule } from "angular-file-uploader";
import {MatTabsModule} from '@angular/material/tabs';
import { NgxPrintModule } from 'ngx-print';


import { ConsultationComponent } from './consultation/consultation.component';
import { ConsultingVideoComponent } from './consulting-video/consulting-video.component';
import { FileUploadComponent } from './file-upload/file-upload.component';
import { MedicalParametersComponent } from './medical-parameters/medical-parameters.component';
import { TeleConsultComponent } from './tele-consult.component';
import {MatGridListModule} from '@angular/material/grid-list';
import {MatTableModule} from '@angular/material/table';
import { PatientModule } from '../patient/patient.module';
import { PatientConsultationComponent } from './patient-consultation/patient-consultation.component';
import { OpenviduSessionModule } from 'openvidu-angular';
import { DoctorModule } from '../doctor/doctor.module';
import { DeviceVideoComponent } from './device-video/device-video.component';
import { SuspendModalComponent } from './consultation/suspend-modal/suspend-modal.component';
import { ConsultHistoryComponent } from './consult-history/consult-history.component';
import { MedicalReportComponent } from './medical-report/medical-report.component';
import { DaConsultingVideoComponent } from './da-consulting-video/da-consulting-video.component';
@NgModule({
    declarations: [
        TeleConsultComponent,
        ConsultationComponent,
        ConsultingVideoComponent,
        FileUploadComponent,
        MedicalParametersComponent,
        PatientConsultationComponent,
        DeviceVideoComponent,
        SuspendModalComponent,
        ConsultHistoryComponent,
        MedicalReportComponent,
        DaConsultingVideoComponent
    ],
    imports: [
      RouterModule,
      FormsModule,
      CommonModule,
      ReactiveFormsModule,
      NgSelectModule,
      NgMultiSelectDropDownModule,
      AngularFileUploaderModule,
      MatGridListModule,
      MatTableModule,
      MatTabsModule,
      PatientModule,
      NgxPrintModule,
      OpenviduSessionModule,
      DoctorModule,
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient]
        }
      }),
      BsDatepickerModule.forRoot(),
    ],
    exports: [
      AngularFileUploaderModule,
      MatGridListModule,
      MatTableModule,
      OpenviduSessionModule,
      MedicalReportComponent,
      ConsultingVideoComponent
    ],
    schemas: [ CUSTOM_ELEMENTS_SCHEMA ]
  })
  export class TeleConsultModule {}

  export function HttpLoaderFactory(httpClient: HttpClient) {
    return new TranslateHttpLoader(httpClient);
  }
