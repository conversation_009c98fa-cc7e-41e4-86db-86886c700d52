import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { delay } from 'rxjs/operators';
import * as Settings from './../config/settings';
import { Observable } from 'rxjs';
// import * as Settings from './../config/settings';
// import { delay } from 'rxjs/operators';
// import { settings } from 'cluster';

@Injectable({
  providedIn: 'root',
})
export class TeleConsultService {
  constructor(private httpClient: HttpClient) {}


  createConsultation(appointment_uuid){
    let body = {};
    return this.httpClient.post(`${Settings.API_CONSULTATION_URL_PREFIX}/api/doctor/me/appointments/${appointment_uuid}/consultations/`, body);
    // .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  createDrug(){

    let body = {
      "name":"",
      "manufacturer":"",
      "generic_name":"",
      "dosage_form":"",
      "description":"",
    };
    return this.httpClient.post(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/drug_taxonomy/`, body);
  }

  createInvestigation(){
    let body = {
      "category":"",
      "investigation_name":"",
    };
    return this.httpClient.post(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/investigations_taxonomy/`, body);
  }



  updateConsultation(consultation_uuid, updateSection, updateField, updateValue){
    console.log('consultation_uuid ' + consultation_uuid);
    console.log('updateSection ' + updateSection);
    console.log('updateField ' + updateField);
    console.log('updateValue ' + updateValue);
    let dataDetail = {
      [updateSection]: {
        [updateField]: updateValue
      }
    };

    // let dataDetail = {
    //   `${updateSection}`: {
    //     `${updateField}`: updateValue
    //   }
    // };

    // let key1 = updateSection;
    // let key2 = updateField;
    // let jsonData = { };
    // jsonData[key1][key2] = updateValue;
  //  console.log('body..' + dataDetail[updateSection][updateField]);
    return this.httpClient.patch(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/data/`, dataDetail).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getVideoToken(consultation_uuid){
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/vc/token/`)
  }

  getDeviceVideoToken(consultation_uuid){
    let queryParams = '?token_user_type=Device1'
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/vc/token` + queryParams);
  }

  suspendConsultationInvestigationsPending(consultation_uuid, message){
    // let updateDetail = {
    //   fulfilment_status: 'Suspended',
    //   suspension_reason: 'Investigations Pending',
    //   suspension_message: message
    // };
    let updateDetail = {
      suspension_message: message
    };
    return this.httpClient.patch(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/suspend_for_investigations/`, updateDetail).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  suspendConsultationReviewPending(consultation_uuid, message){
    // let updateDetail = {
    //   fulfilment_status: 'Suspended',
    //   suspension_reason: 'Review Pending',
    //   suspension_message: message
    // };
    let updateDetail = {
      suspension_message: message
    };
    return this.httpClient.patch(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/suspend_for_review/`, updateDetail);
  }

  completeConsultation(consultation_uuid){
    // let updateDetail = {fulfilment_status: 'Completed'};
    let updateDetail = {};
    return this.httpClient.patch(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/complete/`, updateDetail,{observe: 'response'}).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  refundConsultation(consultation_uuid,data){  
    return this.httpClient.post(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/refund/`,data,{observe: 'response'}).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getConsultationData(consultation_uuid){
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/data/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsultationDataHa(consultation_uuid){
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/data/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  addPrescription(consultation_uuid, prescription){
    return this.httpClient.patch(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/data/`,
    prescription).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getDrugsTaxonomy(){
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/drug_taxonomy/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  postDrugTaxonomy(data){
    return this.httpClient.post(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/drug_taxonomy/`, data
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getInvestigationsTaxonomy(){
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/investigations_taxonomy/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getInvestigationsTaxonomyHa(){
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/investigations_taxonomy/`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getInvestigationsTaxonomyData(query){
    return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/investigations_taxonomy/?${query}`
    ).pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getDocument(id,data){
    return this
    .httpClient
    .get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${id}/documents/?purpose=${data}`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getConsutlHistory(patient_id,doctor_id,page){
    return this
    .httpClient
    .get(`${Settings.API_PATIENT_URL_PREFIX}/api/c/consultations/?patient_user_uuid=${patient_id}&doctor_uuid=${doctor_id}&page=${page}&fulfilment_status=Suspended%2BCompleted`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }

  getAllConsultationHistory(patient_id,doctor_id){
    return this
    .httpClient
    .get(`${Settings.API_PATIENT_URL_PREFIX}/api/c/consultations/data/?patient_user_uuid=${patient_id}&doctor_uuid=${doctor_id}&fulfilment_status=Suspended%2BCompleted`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getPatientConsultationHistory(patient_id,){
    return this
    .httpClient
    .get(`${Settings.API_PATIENT_URL_PREFIX}/api/c/consultations/data/?patient_uuid=${patient_id}&fulfilment_status=Suspended%2BCompleted`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getConsutlReport(patient_id,doctor_id){
    return this
    .httpClient
    .get(`${Settings.API_PATIENT_URL_PREFIX}/api/c/consultations/?patient_user_uuid=${patient_id}&doctor_uuid=${doctor_id}&fulfilment_status=Suspended%2BCompleted`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));
  }
  getPatientProfile(id){
      return this
      .httpClient
      .get(`${Settings.API_PATIENT_URL_PREFIX}/api/p/profile/${id}/`)
      .pipe(delay(Settings.REQUEST_DELAY_TIME));

  }
  getMedicineType(){
    return this
    .httpClient
    .get(`${Settings.API_PATIENT_URL_PREFIX}/api/doctor/l/medicine_types/`)
    .pipe(delay(Settings.REQUEST_DELAY_TIME));

}
getRecordingData(id){
  return this
  .httpClient
  .get(`${Settings.API_PATIENT_URL_PREFIX}/api/c/consultations/${id}/vc/recording/`)
  .pipe(delay(Settings.REQUEST_DELAY_TIME));
}
checkOpenConsultation(doctorId){
  return this.httpClient.get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/?doctor_uuid=${doctorId}&&fulfilment_status=Started`).pipe(delay(Settings.REQUEST_DELAY_TIME));


}
getConsutlHistoryDocument(patient_id,doctor_id){
  return this
  .httpClient
  .get(`${Settings.API_PATIENT_URL_PREFIX}/api/c/consultations/${doctor_id}/${patient_id}/consultation-history/?purpose=ConsultationHistory`)
  .pipe(delay(Settings.REQUEST_DELAY_TIME));
}
leaveConsultation(consultation_uuid ,data){
  return this.httpClient.patch(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${consultation_uuid}/patient_exit/`,
  data).pipe(delay(Settings.REQUEST_DELAY_TIME));
}
downloadPdf(uuid,type): Observable<Blob>{
  let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this
      .httpClient
      .get(`${Settings.API_CONSULTATION_URL_PREFIX}/api/c/consultations/${uuid}/documents/download/?purpose=${type}`, {headers: headers, observe: 'body', responseType: 'blob' })
      .pipe(delay(Settings.REQUEST_DELAY_TIME));
}

}
