@page {
    size: 7in 9.25in;
    margin: 27mm 16mm 27mm 16mm;
}

.container {
    margin-top: 50px;
    left: 30%;
    right: 20%;
}

.top {
    display: inline;
}

p {
    font-family: sans-serif;
    margin-left: 5px;
}

p.top {
    margin: 0px;
}

.clinicname {
    margin-top: 2px;
    margin-bottom: 2px;
    position: absolute;
}

.address {
    margin-top: 20px;
    position: absolute;
}

hr {
    /* color: #00b050; */
    height: 5px !important;
}

table {
    margin-bottom: 40px !important;
}

.text-right {
    position: relative;
    left: 75%;
}